{"ast": null, "code": "import { ref } from \"vue\";\nimport { findList } from \"../../../api/point/record\";\nimport Page from \"../../../components/Page\";\nimport { formatDate } from \"@/util/dateUtils\";\nexport default {\n  name: \"PointRecordIndex\",\n  components: {\n    Page\n  },\n  setup() {\n    const typeMap = {\n      increase: \"增加积分\",\n      decrease: \"消耗积分\",\n      fallback: \"回退积分\",\n      recycle: \"回收积分\"\n    };\n    const shortcuts = [{\n      text: \"最近一周\",\n      value: (() => {\n        const end = new Date();\n        const start = new Date();\n        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\n        return [start, end];\n      })()\n    }, {\n      text: \"最近一个月\",\n      value: (() => {\n        const end = new Date();\n        const start = new Date();\n        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\n        return [start, end];\n      })()\n    }, {\n      text: \"最近三个月\",\n      value: (() => {\n        const end = new Date();\n        const start = new Date();\n        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);\n        return [start, end];\n      })()\n    }];\n    const list = ref([]);\n    const total = ref(0);\n    const datetime = ref(null);\n    const dataLoading = ref(true);\n    const searchParam = ref({\n      startDate: \"\",\n      endDate: \"\",\n      keyword: \"\",\n      type: \"\",\n      size: 20,\n      current: 1\n    });\n    const datetimeChange = value => {\n      if (value && value.length) {\n        searchParam.value.startDate = formatDate(value[0]);\n        searchParam.value.endDate = formatDate(value[1]);\n      } else {\n        searchParam.value.startDate = null;\n        searchParam.value.endDate = null;\n      }\n      loadList();\n    };\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true;\n      findList(searchParam.value, res => {\n        dataLoading.value = false;\n        if (!res) {\n          return;\n        }\n        list.value = res.list;\n        total.value = res.total;\n      });\n    };\n    loadList();\n    const currentChange = currentPage => {\n      searchParam.value.current = currentPage;\n      loadList();\n    };\n    const sizeChange = s => {\n      searchParam.value.size = s;\n      loadList();\n    };\n    // 搜索\n    const search = () => {\n      loadList();\n    };\n    return {\n      list,\n      total,\n      searchParam,\n      search,\n      currentChange,\n      sizeChange,\n      typeMap,\n      dataLoading,\n      datetime,\n      shortcuts,\n      datetimeChange\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "findList", "Page", "formatDate", "name", "components", "setup", "typeMap", "increase", "decrease", "fallback", "recycle", "shortcuts", "text", "value", "end", "Date", "start", "setTime", "getTime", "list", "total", "datetime", "dataLoading", "searchParam", "startDate", "endDate", "keyword", "type", "size", "current", "datetimeChange", "length", "loadList", "res", "currentChange", "currentPage", "sizeChange", "s", "search"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/point/record/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"header\">\n      <el-form :inline=\"true\" :model=\"searchParam\" class=\"demo-form-inline\">\n        <el-form-item>\n          <el-date-picker size=\"mini\" v-model=\"datetime\" @change=\"datetimeChange\" type=\"datetimerange\" :shortcuts=\"shortcuts\" range-separator=\"至\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" align=\"right\"></el-date-picker>\n        </el-form-item>\n        <el-form-item>\n          <el-input size=\"mini\" class=\"search-input\" v-model=\"searchParam.keyword\" placeholder=\"请输入关键字\">\n            <template #append>\n              <el-button size=\"mini\" class=\"search-btn\" type=\"primary\" @click=\"search\">搜索</el-button>\n            </template>\n          </el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-select size=\"mini\" v-model=\"searchParam.type\" @change=\"search\" placeholder=\"请选择类型\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"增加积分\" value=\"increase\"></el-option>\n            <el-option label=\"消耗积分\" value=\"decrease\"></el-option>\n            <el-option label=\"回退积分\" value=\"fallback\"></el-option>\n            <el-option label=\"回收积分\" value=\"recycle\"></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"content\">\n      <div class=\"content-list\">\n        <el-table v-loading=\"dataLoading\" :data=\"list\" size=\"small\" style=\"width: 100%;\">\n          <el-table-column prop=\"pointId\" label=\"积分ID\" width=\"60\"/>\n          <el-table-column prop=\"pointNum\" label=\"积分个数\"/>\n          <el-table-column label=\"类型\">\n            <template #default=\"scope\">\n              {{typeMap[scope.row.type]}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"memberId\" label=\"会员ID\"/>\n          <el-table-column prop=\"mobile\" label=\"手机号\"/>\n          <el-table-column prop=\"createTime\" label=\"发放/消耗时间\"/>\n          <el-table-column prop=\"remark\" label=\"发放/消耗原因\"/>\n        </el-table>\n      </div>\n    </div>\n    <page style=\"margin-top: 20px;\" :total=\"total\" :current-change=\"currentChange\" :size-change=\"sizeChange\" :page-size=\"searchParam.size\"></page>\n  </div>\n</template>\n\n<script>\n  import {ref} from \"vue\"\n  import {findList} from \"../../../api/point/record\"\n  import Page from \"../../../components/Page\"\n  import {formatDate} from \"@/util/dateUtils\";\n\n  export default {\n    name: \"PointRecordIndex\",\n    components: {\n      Page\n    },\n    setup() {\n      const typeMap = {\n        increase: \"增加积分\",\n        decrease: \"消耗积分\",\n        fallback: \"回退积分\",\n        recycle: \"回收积分\"\n      }\n      const shortcuts = [{\n        text: \"最近一周\",\n        value: (() => {\n          const end = new Date();\n          const start = new Date();\n          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\n          return [start, end]\n        })()\n      }, {\n        text: \"最近一个月\",\n        value: (() => {\n          const end = new Date();\n          const start = new Date();\n          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\n          return [start, end]\n        })()\n      }, {\n        text: \"最近三个月\",\n        value: (() => {\n          const end = new Date();\n          const start = new Date();\n          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);\n          return [start, end]\n        })()\n      }]\n      const list = ref([])\n      const total = ref(0)\n      const datetime = ref(null)\n      const dataLoading = ref(true)\n      const searchParam = ref({\n        startDate: \"\",\n        endDate: \"\",\n        keyword: \"\",\n        type: \"\",\n        size: 20,\n        current: 1\n      })\n      const datetimeChange = (value) => {\n        if (value && value.length) {\n          searchParam.value.startDate = formatDate(value[0])\n          searchParam.value.endDate = formatDate(value[1])\n        } else {\n          searchParam.value.startDate = null\n          searchParam.value.endDate = null\n        }\n        loadList();\n      }\n      // 加载列表\n      const loadList = () => {\n        dataLoading.value = true\n        findList(searchParam.value, (res) => {\n          dataLoading.value = false\n          if (!res) {return;}\n          list.value = res.list;\n          total.value = res.total;\n        })\n      }\n      loadList();\n      const currentChange = (currentPage) => {\n        searchParam.value.current = currentPage;\n        loadList();\n      }\n      const sizeChange = (s) => {\n        searchParam.value.size = s;\n        loadList();\n      }\n      // 搜索\n      const search = () => {\n        loadList();\n      }\n      return {\n        list,\n        total,\n        searchParam,\n        search,\n        currentChange,\n        sizeChange,\n        typeMap,\n        dataLoading,\n        datetime,\n        shortcuts,\n        datetimeChange\n      };\n    }\n  };\n</script>\n\n<style lang=\"scss\">\n  .header {\n    .el-form {\n      .el-form-item {\n        .el-form-item__content {\n          line-height: 28px;\n          .search-btn {\n            &:hover {\n              color: $--color-primary;\n            }\n          }\n        }\n      }\n    }\n  }\n</style>\n<style scoped lang=\"scss\">\n  .app-container {\n    margin: 20px;\n    .content-list {\n      margin: 0;\n      padding: 0;\n      border: 0;\n      font: inherit;\n      vertical-align: baseline;\n    }\n    .search-input {\n      width: 242px;\n    }\n  }\n</style>\n"], "mappings": "AA+CE,SAAQA,GAAG,QAAO,KAAI;AACtB,SAAQC,QAAQ,QAAO,2BAA0B;AACjD,OAAOC,IAAG,MAAO,0BAAyB;AAC1C,SAAQC,UAAU,QAAO,kBAAkB;AAE3C,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,UAAU,EAAE;IACVH;EACF,CAAC;EACDI,KAAKA,CAAA,EAAG;IACN,MAAMC,OAAM,GAAI;MACdC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE;IACX;IACA,MAAMC,SAAQ,GAAI,CAAC;MACjBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,CAAC,MAAM;QACZ,MAAMC,GAAE,GAAI,IAAIC,IAAI,EAAE;QACtB,MAAMC,KAAI,GAAI,IAAID,IAAI,EAAE;QACxBC,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,EAAC,GAAI,IAAG,GAAI,IAAG,GAAI,EAAC,GAAI,CAAC,CAAC;QACrD,OAAO,CAACF,KAAK,EAAEF,GAAG;MACpB,CAAC;IACH,CAAC,EAAE;MACDF,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,CAAC,MAAM;QACZ,MAAMC,GAAE,GAAI,IAAIC,IAAI,EAAE;QACtB,MAAMC,KAAI,GAAI,IAAID,IAAI,EAAE;QACxBC,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,EAAC,GAAI,IAAG,GAAI,IAAG,GAAI,EAAC,GAAI,EAAE,CAAC;QACtD,OAAO,CAACF,KAAK,EAAEF,GAAG;MACpB,CAAC;IACH,CAAC,EAAE;MACDF,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,CAAC,MAAM;QACZ,MAAMC,GAAE,GAAI,IAAIC,IAAI,EAAE;QACtB,MAAMC,KAAI,GAAI,IAAID,IAAI,EAAE;QACxBC,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,EAAC,GAAI,IAAG,GAAI,IAAG,GAAI,EAAC,GAAI,EAAE,CAAC;QACtD,OAAO,CAACF,KAAK,EAAEF,GAAG;MACpB,CAAC;IACH,CAAC;IACD,MAAMK,IAAG,GAAIpB,GAAG,CAAC,EAAE;IACnB,MAAMqB,KAAI,GAAIrB,GAAG,CAAC,CAAC;IACnB,MAAMsB,QAAO,GAAItB,GAAG,CAAC,IAAI;IACzB,MAAMuB,WAAU,GAAIvB,GAAG,CAAC,IAAI;IAC5B,MAAMwB,WAAU,GAAIxB,GAAG,CAAC;MACtByB,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC;IACD,MAAMC,cAAa,GAAKjB,KAAK,IAAK;MAChC,IAAIA,KAAI,IAAKA,KAAK,CAACkB,MAAM,EAAE;QACzBR,WAAW,CAACV,KAAK,CAACW,SAAQ,GAAItB,UAAU,CAACW,KAAK,CAAC,CAAC,CAAC;QACjDU,WAAW,CAACV,KAAK,CAACY,OAAM,GAAIvB,UAAU,CAACW,KAAK,CAAC,CAAC,CAAC;MACjD,OAAO;QACLU,WAAW,CAACV,KAAK,CAACW,SAAQ,GAAI,IAAG;QACjCD,WAAW,CAACV,KAAK,CAACY,OAAM,GAAI,IAAG;MACjC;MACAO,QAAQ,EAAE;IACZ;IACA;IACA,MAAMA,QAAO,GAAIA,CAAA,KAAM;MACrBV,WAAW,CAACT,KAAI,GAAI,IAAG;MACvBb,QAAQ,CAACuB,WAAW,CAACV,KAAK,EAAGoB,GAAG,IAAK;QACnCX,WAAW,CAACT,KAAI,GAAI,KAAI;QACxB,IAAI,CAACoB,GAAG,EAAE;UAAC;QAAO;QAClBd,IAAI,CAACN,KAAI,GAAIoB,GAAG,CAACd,IAAI;QACrBC,KAAK,CAACP,KAAI,GAAIoB,GAAG,CAACb,KAAK;MACzB,CAAC;IACH;IACAY,QAAQ,EAAE;IACV,MAAME,aAAY,GAAKC,WAAW,IAAK;MACrCZ,WAAW,CAACV,KAAK,CAACgB,OAAM,GAAIM,WAAW;MACvCH,QAAQ,EAAE;IACZ;IACA,MAAMI,UAAS,GAAKC,CAAC,IAAK;MACxBd,WAAW,CAACV,KAAK,CAACe,IAAG,GAAIS,CAAC;MAC1BL,QAAQ,EAAE;IACZ;IACA;IACA,MAAMM,MAAK,GAAIA,CAAA,KAAM;MACnBN,QAAQ,EAAE;IACZ;IACA,OAAO;MACLb,IAAI;MACJC,KAAK;MACLG,WAAW;MACXe,MAAM;MACNJ,aAAa;MACbE,UAAU;MACV9B,OAAO;MACPgB,WAAW;MACXD,QAAQ;MACRV,SAAS;MACTmB;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}