{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport axios from \"axios\";\nimport { getToken } from \"@/util/tokenUtils\";\nimport { error } from \"./tipsUtils\";\nimport { loginOut } from \"@/api/login\";\nimport router from \"@/router\";\nconst qs = require(\"qs\");\n\n// create an axios instance\nconst service = axios.create({\n  baseURL: process.env.VUE_APP_BASE_API,\n  // url = base url + request url\n  withCredentials: true,\n  // send cookies when cross-domain requests\n  timeout: 300000 // request timeout\n});\n\n// request interceptor\nservice.interceptors.request.use(config => {\n  if (config.method === \"get\") {\n    console.log(config);\n    config.url = config.url + \"?\" + qs.stringify(config.params, {\n      arrayFormat: \"repeat\"\n    });\n    config.params = {};\n    // config.paramsSerializer = function(params, b) {\n    //   return qs.stringify(params, { arrayFormat: \"repeat\" })\n    // }\n  }\n  // do something before request is sent\n  if (getToken()) {\n    // let each request carry token\n    // [\"X-Token\"] is a custom headers key\n    // please modify it according to the actual situation\n    config.headers[\"Authorization\"] = \"Bearer \" + getToken();\n  }\n  return config;\n}, error => {\n  // do something with request error\n  console.log(error); // for debug\n  return Promise.reject(error);\n});\n\n// response interceptor\nservice.interceptors.response.use(response => {\n  const code = response.status;\n  if (code < 200 || code > 300) {\n    error(response.message);\n    return Promise.reject(\"error\");\n  } else {\n    return response.data;\n  }\n}, e => {\n  let code = 0;\n  try {\n    code = e.response.status;\n  } catch (e) {\n    if (e.toString().indexOf(\"Error: timeout\") !== -1) {\n      error(\"网络请求超时\");\n      return Promise.reject(e);\n    }\n    if (e.toString().indexOf(\"Error: Network Error\") !== -1) {\n      error(\"网络请求错误\");\n      return Promise.reject(e);\n    }\n  }\n  if (code === 401) {\n    error(\"认证失败\");\n    loginOut();\n    router.push(\"/login\");\n  } else {\n    const errorMsg = e.response.data.msg || e.response.data.message || e.response.data.error_description || e.response.data.error || e.response.data;\n    if (errorMsg !== undefined) {\n      error(errorMsg);\n    }\n  }\n  return Promise.reject(e);\n});\nexport default service;", "map": {"version": 3, "names": ["axios", "getToken", "error", "loginOut", "router", "qs", "require", "service", "create", "baseURL", "process", "env", "VUE_APP_BASE_API", "withCredentials", "timeout", "interceptors", "request", "use", "config", "method", "console", "log", "url", "stringify", "params", "arrayFormat", "headers", "Promise", "reject", "response", "code", "status", "message", "data", "e", "toString", "indexOf", "push", "errorMsg", "msg", "error_description", "undefined"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/util/remoteUtils.js"], "sourcesContent": ["import axios from \"axios\";\nimport { getToken } from \"@/util/tokenUtils\";\nimport { error } from \"./tipsUtils\";\nimport {loginOut} from \"@/api/login\";\nimport router from \"@/router\";\nconst qs = require(\"qs\");\n\n// create an axios instance\nconst service = axios.create({\n  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url\n  withCredentials: true, // send cookies when cross-domain requests\n  timeout: 300000, // request timeout\n});\n\n// request interceptor\nservice.interceptors.request.use(\n  config => {\n    if (config.method === \"get\") {\n      console.log(config)\n      config.url = config.url + \"?\" + qs.stringify(config.params, { arrayFormat: \"repeat\" })\n      config.params = {}\n      // config.paramsSerializer = function(params, b) {\n      //   return qs.stringify(params, { arrayFormat: \"repeat\" })\n      // }\n    }\n    // do something before request is sent\n    if (getToken()) {\n      // let each request carry token\n      // [\"X-Token\"] is a custom headers key\n      // please modify it according to the actual situation\n      config.headers[\"Authorization\"] = \"Bearer \" + getToken();\n    }\n    return config;\n  },\n  error => {\n    // do something with request error\n    console.log(error); // for debug\n    return Promise.reject(error);\n  }\n);\n\n// response interceptor\nservice.interceptors.response.use(\n  response => {\n    const code = response.status;\n    if (code < 200 || code > 300) {\n      error(response.message);\n      return Promise.reject(\"error\");\n    } else {\n      return response.data;\n    }\n  },\n  e => {\n    let code = 0;\n    try {\n      code = e.response.status;\n    } catch (e) {\n      if (e.toString().indexOf(\"Error: timeout\") !== -1) {\n        error(\"网络请求超时\");\n        return Promise.reject(e);\n      }\n      if (e.toString().indexOf(\"Error: Network Error\") !== -1) {\n        error(\"网络请求错误\");\n        return Promise.reject(e);\n      }\n    }\n    if (code === 401) {\n      error(\"认证失败\");\n      loginOut()\n      router.push(\"/login\")\n    } else {\n      const errorMsg = e.response.data.msg || e.response.data.message || e.response.data.error_description || e.response.data.error || e.response.data;\n      if (errorMsg !== undefined) {\n        error(errorMsg);\n      }\n    }\n    return Promise.reject(e);\n  }\n);\n\nexport default service;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,KAAK,QAAQ,aAAa;AACnC,SAAQC,QAAQ,QAAO,aAAa;AACpC,OAAOC,MAAM,MAAM,UAAU;AAC7B,MAAMC,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC;;AAExB;AACA,MAAMC,OAAO,GAAGP,KAAK,CAACQ,MAAM,CAAC;EAC3BC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAgB;EAAE;EACvCC,eAAe,EAAE,IAAI;EAAE;EACvBC,OAAO,EAAE,MAAM,CAAE;AACnB,CAAC,CAAC;;AAEF;AACAP,OAAO,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9BC,MAAM,IAAI;EACR,IAAIA,MAAM,CAACC,MAAM,KAAK,KAAK,EAAE;IAC3BC,OAAO,CAACC,GAAG,CAACH,MAAM,CAAC;IACnBA,MAAM,CAACI,GAAG,GAAGJ,MAAM,CAACI,GAAG,GAAG,GAAG,GAAGjB,EAAE,CAACkB,SAAS,CAACL,MAAM,CAACM,MAAM,EAAE;MAAEC,WAAW,EAAE;IAAS,CAAC,CAAC;IACtFP,MAAM,CAACM,MAAM,GAAG,CAAC,CAAC;IAClB;IACA;IACA;EACF;EACA;EACA,IAAIvB,QAAQ,EAAE,EAAE;IACd;IACA;IACA;IACAiB,MAAM,CAACQ,OAAO,CAAC,eAAe,CAAC,GAAG,SAAS,GAAGzB,QAAQ,EAAE;EAC1D;EACA,OAAOiB,MAAM;AACf,CAAC,EACDhB,KAAK,IAAI;EACP;EACAkB,OAAO,CAACC,GAAG,CAACnB,KAAK,CAAC,CAAC,CAAC;EACpB,OAAOyB,OAAO,CAACC,MAAM,CAAC1B,KAAK,CAAC;AAC9B,CAAC,CACF;;AAED;AACAK,OAAO,CAACQ,YAAY,CAACc,QAAQ,CAACZ,GAAG,CAC/BY,QAAQ,IAAI;EACV,MAAMC,IAAI,GAAGD,QAAQ,CAACE,MAAM;EAC5B,IAAID,IAAI,GAAG,GAAG,IAAIA,IAAI,GAAG,GAAG,EAAE;IAC5B5B,KAAK,CAAC2B,QAAQ,CAACG,OAAO,CAAC;IACvB,OAAOL,OAAO,CAACC,MAAM,CAAC,OAAO,CAAC;EAChC,CAAC,MAAM;IACL,OAAOC,QAAQ,CAACI,IAAI;EACtB;AACF,CAAC,EACDC,CAAC,IAAI;EACH,IAAIJ,IAAI,GAAG,CAAC;EACZ,IAAI;IACFA,IAAI,GAAGI,CAAC,CAACL,QAAQ,CAACE,MAAM;EAC1B,CAAC,CAAC,OAAOG,CAAC,EAAE;IACV,IAAIA,CAAC,CAACC,QAAQ,EAAE,CAACC,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE;MACjDlC,KAAK,CAAC,QAAQ,CAAC;MACf,OAAOyB,OAAO,CAACC,MAAM,CAACM,CAAC,CAAC;IAC1B;IACA,IAAIA,CAAC,CAACC,QAAQ,EAAE,CAACC,OAAO,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC,EAAE;MACvDlC,KAAK,CAAC,QAAQ,CAAC;MACf,OAAOyB,OAAO,CAACC,MAAM,CAACM,CAAC,CAAC;IAC1B;EACF;EACA,IAAIJ,IAAI,KAAK,GAAG,EAAE;IAChB5B,KAAK,CAAC,MAAM,CAAC;IACbC,QAAQ,EAAE;IACVC,MAAM,CAACiC,IAAI,CAAC,QAAQ,CAAC;EACvB,CAAC,MAAM;IACL,MAAMC,QAAQ,GAAGJ,CAAC,CAACL,QAAQ,CAACI,IAAI,CAACM,GAAG,IAAIL,CAAC,CAACL,QAAQ,CAACI,IAAI,CAACD,OAAO,IAAIE,CAAC,CAACL,QAAQ,CAACI,IAAI,CAACO,iBAAiB,IAAIN,CAAC,CAACL,QAAQ,CAACI,IAAI,CAAC/B,KAAK,IAAIgC,CAAC,CAACL,QAAQ,CAACI,IAAI;IAChJ,IAAIK,QAAQ,KAAKG,SAAS,EAAE;MAC1BvC,KAAK,CAACoC,QAAQ,CAAC;IACjB;EACF;EACA,OAAOX,OAAO,CAACC,MAAM,CAACM,CAAC,CAAC;AAC1B,CAAC,CACF;AAED,eAAe3B,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}