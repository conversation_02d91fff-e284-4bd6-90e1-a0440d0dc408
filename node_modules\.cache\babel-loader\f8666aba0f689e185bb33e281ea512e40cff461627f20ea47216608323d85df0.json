{"ast": null, "code": "import { get, post, del } from \"../../util/requestUtils\";\n\n// 发表评论\nexport function saveComment(data, success) {\n  return post(\"/comment/auth-api/comment\", data, success);\n}\n\n// 删除评论\nexport function deleteComment(data, success) {\n  return del(\"/comment/auth-api/comment\", data, success);\n}\n\n// 评论列表\nexport function getCommentList(params, success) {\n  return get(\"/comment/public-api/comment/list\", params, success);\n}\n\n// 获取当前用户的评论列表\nexport function getCurrentMemberCommentList(params, success) {\n  return get(\"/comment/auth-api/current-member/comment/list\", params, success);\n}\n\n// 发表回复\nexport function saveReplyComment(data, success) {\n  return post(\"/comment/auth-api/reply/comment\", data, success);\n}\n\n// 删除回复评论\nexport function deleteReplyComment(data, success) {\n  return del(\"/comment/auth-api/reply/comment\", data, success);\n}\nimport * as lessonApi from \"../../api/learn/lesson\";\nimport * as newsApi from \"../../api/content/news\";\nimport * as articleApi from \"../../api/content/article\";\nimport * as questionApi from \"../../api/ask/question\";\nimport * as answerApi from \"../../api/ask/answer\";\nimport * as dynamicApi from \"../../api/circle/dynamic\";\nimport * as channelApi from \"../../api/live/channel\";\nimport * as resourceApi from \"../../api/resource\";\nexport function findTopicList(params, success) {\n  switch (params.topicType) {\n    case \"lesson\":\n      return lessonApi.findList(params, success);\n    case \"news\":\n      return newsApi.findList(params, success);\n    case \"article\":\n      return articleApi.findList(params, success);\n    case \"question\":\n      return questionApi.findList(params, success);\n    case \"answer\":\n      return answerApi.findList(params, success);\n    case \"dynamic\":\n      return dynamicApi.findList(params, success);\n    case \"channel\":\n      return channelApi.findList(params, success);\n    case \"resource\":\n      return resourceApi.findList(params, success);\n  }\n}", "map": {"version": 3, "names": ["get", "post", "del", "saveComment", "data", "success", "deleteComment", "getCommentList", "params", "getCurrentMemberCommentList", "saveReplyComment", "deleteReplyComment", "lessonApi", "newsApi", "articleApi", "questionA<PERSON>", "answerApi", "dynamicApi", "channelApi", "resourceApi", "findTopicList", "topicType", "findList"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/api/comment/index.js"], "sourcesContent": ["import { get, post, del } from \"../../util/requestUtils\"\n\n// 发表评论\nexport function saveComment(data, success) {\n  return post(\"/comment/auth-api/comment\", data, success)\n}\n\n// 删除评论\nexport function deleteComment(data, success) {\n  return del(\"/comment/auth-api/comment\", data, success)\n}\n\n// 评论列表\nexport function getCommentList(params, success) {\n  return get(\"/comment/public-api/comment/list\", params, success)\n}\n\n// 获取当前用户的评论列表\nexport function getCurrentMemberCommentList(params, success) {\n  return get(\"/comment/auth-api/current-member/comment/list\", params, success)\n}\n\n// 发表回复\nexport function saveReplyComment(data, success) {\n  return post(\"/comment/auth-api/reply/comment\", data, success)\n}\n\n// 删除回复评论\nexport function deleteReplyComment(data, success) {\n  return del(\"/comment/auth-api/reply/comment\", data, success)\n}\n\nimport * as lessonApi from \"../../api/learn/lesson\";\nimport * as newsApi from \"../../api/content/news\";\nimport * as articleApi from \"../../api/content/article\";\nimport * as questionApi from \"../../api/ask/question\";\nimport * as answerApi from \"../../api/ask/answer\";\nimport * as dynamicApi from \"../../api/circle/dynamic\";\nimport * as channelApi from \"../../api/live/channel\";\nimport * as resourceApi from \"../../api/resource\";\nexport function findTopicList(params, success) {\n  switch (params.topicType) {\n  case \"lesson\":\n    return lessonApi.findList(params, success)\n  case \"news\":\n    return newsApi.findList(params, success)\n  case \"article\":\n    return articleApi.findList(params, success)\n  case \"question\":\n    return questionApi.findList(params, success)\n  case \"answer\":\n    return answerApi.findList(params, success)\n  case \"dynamic\":\n    return dynamicApi.findList(params, success)\n  case \"channel\":\n    return channelApi.findList(params, success)\n  case \"resource\":\n    return resourceApi.findList(params, success)\n  }\n}\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,GAAG,QAAQ,yBAAyB;;AAExD;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACzC,OAAOJ,IAAI,CAAC,2BAA2B,EAAEG,IAAI,EAAEC,OAAO,CAAC;AACzD;;AAEA;AACA,OAAO,SAASC,aAAaA,CAACF,IAAI,EAAEC,OAAO,EAAE;EAC3C,OAAOH,GAAG,CAAC,2BAA2B,EAAEE,IAAI,EAAEC,OAAO,CAAC;AACxD;;AAEA;AACA,OAAO,SAASE,cAAcA,CAACC,MAAM,EAAEH,OAAO,EAAE;EAC9C,OAAOL,GAAG,CAAC,kCAAkC,EAAEQ,MAAM,EAAEH,OAAO,CAAC;AACjE;;AAEA;AACA,OAAO,SAASI,2BAA2BA,CAACD,MAAM,EAAEH,OAAO,EAAE;EAC3D,OAAOL,GAAG,CAAC,+CAA+C,EAAEQ,MAAM,EAAEH,OAAO,CAAC;AAC9E;;AAEA;AACA,OAAO,SAASK,gBAAgBA,CAACN,IAAI,EAAEC,OAAO,EAAE;EAC9C,OAAOJ,IAAI,CAAC,iCAAiC,EAAEG,IAAI,EAAEC,OAAO,CAAC;AAC/D;;AAEA;AACA,OAAO,SAASM,kBAAkBA,CAACP,IAAI,EAAEC,OAAO,EAAE;EAChD,OAAOH,GAAG,CAAC,iCAAiC,EAAEE,IAAI,EAAEC,OAAO,CAAC;AAC9D;AAEA,OAAO,KAAKO,SAAS,MAAM,wBAAwB;AACnD,OAAO,KAAKC,OAAO,MAAM,wBAAwB;AACjD,OAAO,KAAKC,UAAU,MAAM,2BAA2B;AACvD,OAAO,KAAKC,WAAW,MAAM,wBAAwB;AACrD,OAAO,KAAKC,SAAS,MAAM,sBAAsB;AACjD,OAAO,KAAKC,UAAU,MAAM,0BAA0B;AACtD,OAAO,KAAKC,UAAU,MAAM,wBAAwB;AACpD,OAAO,KAAKC,WAAW,MAAM,oBAAoB;AACjD,OAAO,SAASC,aAAaA,CAACZ,MAAM,EAAEH,OAAO,EAAE;EAC7C,QAAQG,MAAM,CAACa,SAAS;IACxB,KAAK,QAAQ;MACX,OAAOT,SAAS,CAACU,QAAQ,CAACd,MAAM,EAAEH,OAAO,CAAC;IAC5C,KAAK,MAAM;MACT,OAAOQ,OAAO,CAACS,QAAQ,CAACd,MAAM,EAAEH,OAAO,CAAC;IAC1C,KAAK,SAAS;MACZ,OAAOS,UAAU,CAACQ,QAAQ,CAACd,MAAM,EAAEH,OAAO,CAAC;IAC7C,KAAK,UAAU;MACb,OAAOU,WAAW,CAACO,QAAQ,CAACd,MAAM,EAAEH,OAAO,CAAC;IAC9C,KAAK,QAAQ;MACX,OAAOW,SAAS,CAACM,QAAQ,CAACd,MAAM,EAAEH,OAAO,CAAC;IAC5C,KAAK,SAAS;MACZ,OAAOY,UAAU,CAACK,QAAQ,CAACd,MAAM,EAAEH,OAAO,CAAC;IAC7C,KAAK,SAAS;MACZ,OAAOa,UAAU,CAACI,QAAQ,CAACd,MAAM,EAAEH,OAAO,CAAC;IAC7C,KAAK,UAAU;MACb,OAAOc,WAAW,CAACG,QAAQ,CAACd,MAAM,EAAEH,OAAO,CAAC;EAAA;AAEhD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}