{"ast": null, "code": "import { ref } from \"vue\";\nimport { deleteResource, findList, publishedResource } from \"@/api/resource/index\";\nimport Page from \"@/components/Page\";\nimport { confirm, info, success } from \"@/util/tipsUtils\";\nimport { findCategoryList, toTree } from \"@/api/resource/category\";\nimport CommentDrawer from \"@/views/comment/commentDrawer\";\nexport default {\n  name: \"ResourceList\",\n  components: {\n    CommentDrawer,\n    Page\n  },\n  setup() {\n    const statusMap = {\n      \"draft\": \"草稿\",\n      \"published\": \"已发布\"\n    };\n    const list = ref([]);\n    const total = ref(0);\n    const dataLoading = ref(true);\n    const searchParam = ref({\n      keyword: \"\",\n      status: \"\",\n      cid: \"\",\n      size: 20,\n      current: 1\n    });\n    const selectCidList = ref([]);\n    const categoryOptions = ref([]);\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true;\n      findList(searchParam.value, res => {\n        dataLoading.value = false;\n        if (!res) {\n          return;\n        }\n        for (const listElement of res.list) {\n          listElement.chapterList = [];\n        }\n        list.value = res.list;\n        total.value = res.total;\n      });\n    };\n    loadList();\n    // 加载分类\n    const loadCategory = () => {\n      findCategoryList(0, true, res => {\n        if (res) {\n          categoryOptions.value = toTree(res);\n        }\n      });\n    };\n    loadCategory();\n    // 搜索\n    const search = () => {\n      if (selectCidList.value && selectCidList.value.length > 0) {\n        searchParam.value.cid = selectCidList.value[selectCidList.value.length - 1];\n      }\n      loadList();\n    };\n    // 删除\n    const remove = item => {\n      confirm(\"确认删除资源 \" + item.title + \" 吗？\", \"提示\", () => {\n        deleteResource(item.id, () => {\n          success(\"删除成功\");\n          loadList();\n        });\n      }, () => {});\n    };\n    const currentChange = currentPage => {\n      searchParam.value.current = currentPage;\n      loadList();\n    };\n    const sizeChange = s => {\n      searchParam.value.size = s;\n      loadList();\n    };\n    const selectTopic = ref({});\n    const drawer = ref(false);\n    const drawerClose = done => {\n      drawer.value = false;\n      done();\n    };\n    const commentView = item => {\n      drawer.value = true;\n      selectTopic.value = item;\n    };\n    const published = item => {\n      const p = {\n        status: \"published\",\n        id: item.id\n      };\n      if (item.status === \"published\") {\n        p.status = \"draft\";\n      }\n      publishedResource(p, () => {\n        success(item.status === \"published\" ? \"取消发布成功\" : \"发布成功\");\n        loadList();\n      });\n    };\n    const downloadView = () => {\n      info(\"敬请期待\");\n    };\n    return {\n      list,\n      total,\n      searchParam,\n      search,\n      currentChange,\n      sizeChange,\n      remove,\n      commentView,\n      selectTopic,\n      drawer,\n      drawerClose,\n      statusMap,\n      selectCidList,\n      categoryOptions,\n      published,\n      downloadView,\n      dataLoading\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "deleteResource", "findList", "publishedResource", "Page", "confirm", "info", "success", "findCategoryList", "toTree", "CommentDrawer", "name", "components", "setup", "statusMap", "list", "total", "dataLoading", "searchParam", "keyword", "status", "cid", "size", "current", "selectCidList", "categoryOptions", "loadList", "value", "res", "listElement", "chapterList", "loadCategory", "search", "length", "remove", "item", "title", "id", "currentChange", "currentPage", "sizeChange", "s", "selectTopic", "drawer", "drawerClose", "done", "commentView", "published", "p", "downloadView"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/resource/list/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"header\">\n      <el-form :inline=\"true\" :model=\"searchParam\" class=\"demo-form-inline\">\n        <el-form-item label=\"\">\n          <el-input size=\"mini\" class=\"search-input\" v-model=\"searchParam.keyword\" placeholder=\"请输入关键字\"></el-input>\n          <el-button size=\"mini\" class=\"search-btn\" type=\"primary\" @click=\"search\">搜索</el-button>\n        </el-form-item>\n        <el-form-item label=\"状态\" class=\"status\">\n          <el-select size=\"mini\" v-model=\"searchParam.status\" @change=\"search\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"草稿\" value=\"draft\"></el-option>\n            <el-option label=\"已发布\" value=\"published\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"分类\">\n          <el-cascader size=\"mini\" v-model=\"selectCidList\" :options=\"categoryOptions\" :props=\"{ checkStrictly: true }\" @change=\"search\" clearable></el-cascader>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"content\" v-loading=\"dataLoading\">\n      <div class=\"content-list\">\n        <el-empty v-if=\"!list || !list.length\"/>\n        <div class=\"content-item\" v-for=\"item in list\" :key=\"item.id + ''\">\n          <div class=\"content-item-warp\">\n            <a class=\"image\">\n              <img :src=\"item.image\">\n            </a>\n            <div class=\"article-card-bone\">\n              <div class=\"title-wrap\">\n                <a class=\"title\">{{item.title}}</a>\n                <span class=\"label create-time\">{{item.createTime}}</span>\n              </div>\n              <div class=\"abstruct\">\n                <div class=\"status\">{{statusMap[item.status]}}</div>\n                <div class=\"divider\"></div>\n                <div class=\"status\">\n                  <img :src=\"item.member.avatar\" style=\"width: 20px;vertical-align: text-top;border-radius: 10px;\"/>\n                  {{item.member.name}}\n                </div>\n              </div>\n              <div class=\"count-wrapper\">\n                <ul class=\"count\">\n                  <li>下载 {{item.downloadNum || 0}}</li>\n                  <li>收藏 {{item.favoriteNum || 0}}</li>\n                  <li>点赞 {{item.likeNum || 0}}</li>\n                  <li>评论 {{item.commentNum || 0}}</li>\n                </ul>\n                <div class=\"article-action-list\">\n                  <span class=\"icon-label\" @click=\"downloadView\">下载用户</span>\n                  <span class=\"icon-label\" @click=\"commentView(item)\">查看评论</span>\n                  <span class=\"icon-label\" @click=\"published(item)\">{{item.status === 'published' ? '取消发布' : '发布'}}</span>\n                  <span class=\"icon-label\" @click=\"remove(item)\">删除</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <comment-drawer :topic=\"selectTopic\" :show-drawer=\"drawer\" topic-type=\"resource\" :drawer-close=\"drawerClose\"/>\n    <page :total=\"total\" :current-change=\"currentChange\" :size-change=\"sizeChange\"></page>\n  </div>\n</template>\n\n<script>\n  import {ref} from \"vue\"\n  import {deleteResource, findList, publishedResource} from \"@/api/resource/index\"\n  import Page from \"@/components/Page\"\n  import {confirm, info, success} from \"@/util/tipsUtils\";\n  import {findCategoryList, toTree} from \"@/api/resource/category\";\n  import CommentDrawer from \"@/views/comment/commentDrawer\";\n\n  export default {\n    name: \"ResourceList\",\n  components: {\n    CommentDrawer,\n    Page\n  },\n  setup() {\n    const statusMap = {\n      \"draft\": \"草稿\",\n      \"published\": \"已发布\"\n    }\n    const list = ref([])\n    const total = ref(0)\n    const dataLoading = ref(true)\n    const searchParam = ref({\n      keyword: \"\",\n      status: \"\",\n      cid: \"\",\n      size: 20,\n      current: 1\n    })\n    const selectCidList = ref([])\n    const categoryOptions = ref([])\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true\n      findList(searchParam.value, (res) => {\n        dataLoading.value = false\n        if (!res) {return;}\n        for (const listElement of res.list) {\n          listElement.chapterList = [];\n        }\n        list.value = res.list;\n        total.value = res.total;\n      })\n    }\n    loadList();\n    // 加载分类\n    const loadCategory = () => {\n      findCategoryList(0, true, (res) => {if (res) {categoryOptions.value = toTree(res);}})\n    }\n    loadCategory();\n    // 搜索\n    const search = () => {\n      if (selectCidList.value && selectCidList.value.length > 0) {\n        searchParam.value.cid = selectCidList.value[selectCidList.value.length - 1];\n      }\n      loadList();\n    }\n    // 删除\n    const remove = (item) => {\n      confirm(\"确认删除资源 \" + item.title + \" 吗？\", \"提示\", () => {\n        deleteResource(item.id, () => {\n          success(\"删除成功\")\n          loadList()\n        })\n      }, () => {\n      })\n    }\n    const currentChange = (currentPage) => {\n      searchParam.value.current = currentPage;\n      loadList();\n    }\n    const sizeChange = (s) => {\n      searchParam.value.size = s;\n      loadList();\n    }\n    const selectTopic = ref({})\n    const drawer = ref(false)\n    const drawerClose = (done) => {\n      drawer.value = false\n      done()\n    }\n    const commentView = (item) => {\n      drawer.value = true\n      selectTopic.value = item\n    }\n    const published = (item) => {\n      const p = {status: \"published\", id: item.id}\n      if(item.status === \"published\") {\n        p.status = \"draft\"\n      }\n      publishedResource(p, () => {\n        success(item.status === \"published\" ? \"取消发布成功\" : \"发布成功\")\n        loadList();\n      })\n    }\n    const downloadView = () => {\n      info(\"敬请期待\")\n    }\n    return {\n      list,\n      total,\n      searchParam,\n      search,\n      currentChange,\n      sizeChange,\n      remove,\n      commentView,\n      selectTopic,\n      drawer,\n      drawerClose,\n      statusMap,\n      selectCidList,\n      categoryOptions,\n      published,\n      downloadView,\n      dataLoading\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n  .app-container {\n    margin: 20px;\n    .content-list {\n      margin: 0;\n      padding: 0;\n      border: 0;\n      font: inherit;\n      vertical-align: baseline;\n      .content-item {\n        padding: 24px 12px;\n        line-height: 1;\n        font-size: 14px;\n        color: #666;\n        border-bottom: 1px solid #e8e8e8;\n        position: relative;\n        background: #ffffff;\n        &:last-child {\n          border-bottom: 0;\n        }\n        .content-item-warp {\n          position: relative;\n          display: flex;\n          .image {\n            width: 168px;\n            min-width: 168px;\n            height: 108px;\n            margin-right: 24px;\n            position: relative;\n            overflow: hidden;\n            border-radius: 4px;\n            border: 1px solid #e8e8e8;\n            img {\n              width: 100%;\n              height: 100%;\n              transition: all .5s ease-out .1s;\n              -o-object-fit: cover;\n              object-fit: cover;\n              -o-object-position: center;\n              object-position: center;\n              &:hover {\n                transform: matrix(1.04,0,0,1.04,0,0);\n                -webkit-backface-visibility: hidden;\n                backface-visibility: hidden;\n              }\n            }\n          }\n          .article-card-bone {\n            width: 100%;\n            display: flex;\n            flex-direction: column;\n            min-width: 0;\n            .title-wrap {\n              display: flex;\n              justify-content: space-between;\n              margin-top: 0;\n              .title {\n                font-size: 16px;\n                overflow: hidden;\n                white-space: nowrap;\n                text-overflow: ellipsis;\n                line-height: 24px;\n                font-weight: 600;\n                display: block;\n                color: #222;\n                &:hover {\n                  color: $--color-primary;\n                }\n              }\n              .create-time {\n                color: #999;\n                line-height: 24px;\n                margin-left: 12px;\n                flex-shrink: 0;\n              }\n            }\n            .abstruct {\n              line-height: 20px;\n              margin-top: 20px;\n              height: 20px;\n              display: flex;\n              align-items: flex-end;\n              .status {\n                color: #999;\n                border: none;\n                background-color: #f5f5f5;\n                padding: 0 8px;\n                line-height: 20px;\n                font-size: 12px;\n                border-radius: 2px;\n                white-space: nowrap;\n                display: inline-block;\n                box-sizing: border-box;\n                transition: all .3s;\n                margin-right: 8px;\n              }\n              .article-card .byte-tag-simple {\n                margin-right: 8px;\n              }\n              .divider {\n                width: 1px;\n                height: 12px;\n                margin: 4px 10px 4px 4px;\n                background: #bfbfbf;\n              }\n              .icon {\n                margin-right: 8px;\n                svg {\n                  vertical-align: bottom;\n                  &:focus {\n                    outline: none;\n                  }\n                }\n              }\n            }\n            .count-wrapper {\n              margin-top: 24px;\n              display: flex;\n              justify-content: space-between;\n              .count {\n                line-height: 20px;\n                position: relative;\n                li {\n                  display: inline-block;\n                  margin-right: 24px;\n                  &:after {\n                    content: \"\\ff65\";\n                    font-size: 20px;\n                    margin: 0 8px;\n                    line-height: 0;\n                    position: absolute;\n                    top: 10px;\n                    color: #666;\n                  }\n                  &:last-child:after {\n                    content: \"\"\n                  }\n                }\n              }\n              .article-action-list {\n                display: flex;\n                line-height: 20px;\n                flex: 1 0 auto;\n                justify-content: flex-end;\n                .icon-label {\n                  cursor: pointer;\n                  font-size: 14px;\n                  line-height: 20px;\n                  display: flex;\n                  color: #222;\n                  font-weight: 400;\n                  margin-left: 24px;\n                  &:first-child {\n                    margin-left: 0;\n                  }\n                  &:hover {\n                    color: $--color-primary;\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    .search-input {\n      width: 242px;\n    }\n  }\n</style>\n"], "mappings": "AAkEE,SAAQA,GAAG,QAAO,KAAI;AACtB,SAAQC,cAAc,EAAEC,QAAQ,EAAEC,iBAAiB,QAAO,sBAAqB;AAC/E,OAAOC,IAAG,MAAO,mBAAkB;AACnC,SAAQC,OAAO,EAAEC,IAAI,EAAEC,OAAO,QAAO,kBAAkB;AACvD,SAAQC,gBAAgB,EAAEC,MAAM,QAAO,yBAAyB;AAChE,OAAOC,aAAY,MAAO,+BAA+B;AAEzD,eAAe;EACbC,IAAI,EAAE,cAAc;EACtBC,UAAU,EAAE;IACVF,aAAa;IACbN;EACF,CAAC;EACDS,KAAKA,CAAA,EAAG;IACN,MAAMC,SAAQ,GAAI;MAChB,OAAO,EAAE,IAAI;MACb,WAAW,EAAE;IACf;IACA,MAAMC,IAAG,GAAIf,GAAG,CAAC,EAAE;IACnB,MAAMgB,KAAI,GAAIhB,GAAG,CAAC,CAAC;IACnB,MAAMiB,WAAU,GAAIjB,GAAG,CAAC,IAAI;IAC5B,MAAMkB,WAAU,GAAIlB,GAAG,CAAC;MACtBmB,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,GAAG,EAAE,EAAE;MACPC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC;IACD,MAAMC,aAAY,GAAIxB,GAAG,CAAC,EAAE;IAC5B,MAAMyB,eAAc,GAAIzB,GAAG,CAAC,EAAE;IAC9B;IACA,MAAM0B,QAAO,GAAIA,CAAA,KAAM;MACrBT,WAAW,CAACU,KAAI,GAAI,IAAG;MACvBzB,QAAQ,CAACgB,WAAW,CAACS,KAAK,EAAGC,GAAG,IAAK;QACnCX,WAAW,CAACU,KAAI,GAAI,KAAI;QACxB,IAAI,CAACC,GAAG,EAAE;UAAC;QAAO;QAClB,KAAK,MAAMC,WAAU,IAAKD,GAAG,CAACb,IAAI,EAAE;UAClCc,WAAW,CAACC,WAAU,GAAI,EAAE;QAC9B;QACAf,IAAI,CAACY,KAAI,GAAIC,GAAG,CAACb,IAAI;QACrBC,KAAK,CAACW,KAAI,GAAIC,GAAG,CAACZ,KAAK;MACzB,CAAC;IACH;IACAU,QAAQ,EAAE;IACV;IACA,MAAMK,YAAW,GAAIA,CAAA,KAAM;MACzBvB,gBAAgB,CAAC,CAAC,EAAE,IAAI,EAAGoB,GAAG,IAAK;QAAC,IAAIA,GAAG,EAAE;UAACH,eAAe,CAACE,KAAI,GAAIlB,MAAM,CAACmB,GAAG,CAAC;QAAC;MAAC,CAAC;IACtF;IACAG,YAAY,EAAE;IACd;IACA,MAAMC,MAAK,GAAIA,CAAA,KAAM;MACnB,IAAIR,aAAa,CAACG,KAAI,IAAKH,aAAa,CAACG,KAAK,CAACM,MAAK,GAAI,CAAC,EAAE;QACzDf,WAAW,CAACS,KAAK,CAACN,GAAE,GAAIG,aAAa,CAACG,KAAK,CAACH,aAAa,CAACG,KAAK,CAACM,MAAK,GAAI,CAAC,CAAC;MAC7E;MACAP,QAAQ,EAAE;IACZ;IACA;IACA,MAAMQ,MAAK,GAAKC,IAAI,IAAK;MACvB9B,OAAO,CAAC,SAAQ,GAAI8B,IAAI,CAACC,KAAI,GAAI,KAAK,EAAE,IAAI,EAAE,MAAM;QAClDnC,cAAc,CAACkC,IAAI,CAACE,EAAE,EAAE,MAAM;UAC5B9B,OAAO,CAAC,MAAM;UACdmB,QAAQ,EAAC;QACX,CAAC;MACH,CAAC,EAAE,MAAM,CACT,CAAC;IACH;IACA,MAAMY,aAAY,GAAKC,WAAW,IAAK;MACrCrB,WAAW,CAACS,KAAK,CAACJ,OAAM,GAAIgB,WAAW;MACvCb,QAAQ,EAAE;IACZ;IACA,MAAMc,UAAS,GAAKC,CAAC,IAAK;MACxBvB,WAAW,CAACS,KAAK,CAACL,IAAG,GAAImB,CAAC;MAC1Bf,QAAQ,EAAE;IACZ;IACA,MAAMgB,WAAU,GAAI1C,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAM2C,MAAK,GAAI3C,GAAG,CAAC,KAAK;IACxB,MAAM4C,WAAU,GAAKC,IAAI,IAAK;MAC5BF,MAAM,CAAChB,KAAI,GAAI,KAAI;MACnBkB,IAAI,EAAC;IACP;IACA,MAAMC,WAAU,GAAKX,IAAI,IAAK;MAC5BQ,MAAM,CAAChB,KAAI,GAAI,IAAG;MAClBe,WAAW,CAACf,KAAI,GAAIQ,IAAG;IACzB;IACA,MAAMY,SAAQ,GAAKZ,IAAI,IAAK;MAC1B,MAAMa,CAAA,GAAI;QAAC5B,MAAM,EAAE,WAAW;QAAEiB,EAAE,EAAEF,IAAI,CAACE;MAAE;MAC3C,IAAGF,IAAI,CAACf,MAAK,KAAM,WAAW,EAAE;QAC9B4B,CAAC,CAAC5B,MAAK,GAAI,OAAM;MACnB;MACAjB,iBAAiB,CAAC6C,CAAC,EAAE,MAAM;QACzBzC,OAAO,CAAC4B,IAAI,CAACf,MAAK,KAAM,WAAU,GAAI,QAAO,GAAI,MAAM;QACvDM,QAAQ,EAAE;MACZ,CAAC;IACH;IACA,MAAMuB,YAAW,GAAIA,CAAA,KAAM;MACzB3C,IAAI,CAAC,MAAM;IACb;IACA,OAAO;MACLS,IAAI;MACJC,KAAK;MACLE,WAAW;MACXc,MAAM;MACNM,aAAa;MACbE,UAAU;MACVN,MAAM;MACNY,WAAW;MACXJ,WAAW;MACXC,MAAM;MACNC,WAAW;MACX9B,SAAS;MACTU,aAAa;MACbC,eAAe;MACfsB,SAAS;MACTE,YAAY;MACZhC;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}