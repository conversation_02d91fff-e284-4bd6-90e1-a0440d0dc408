{"ast": null, "code": "import { ref } from \"vue\";\nimport Page from \"@/components/Page\";\nimport { Search } from \"@element-plus/icons-vue\";\nexport default {\n  name: \"LearnReportSignUpIndex\",\n  components: {\n    Search,\n    Page\n  },\n  setup() {\n    const params = ref({\n      current: 1,\n      size: 20,\n      status: ''\n    });\n    const selectCidList = ref([]);\n    const categoryOptions = ref([]);\n    const loadList = () => {};\n    const total = ref(0);\n    const currentChange = c => {\n      params.value.current = c;\n      loadList();\n    };\n    const sizeChange = s => {\n      params.value.size = s;\n      loadList();\n    };\n    const search = () => {\n      params.current = 1;\n      loadList();\n    };\n    return {\n      params,\n      total,\n      currentChange,\n      sizeChange,\n      search\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "Page", "Search", "name", "components", "setup", "params", "current", "size", "status", "selectCidList", "categoryOptions", "loadList", "total", "currentChange", "c", "value", "sizeChange", "s", "search"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/learn/report/signup/index.vue"], "sourcesContent": ["<template>\n  <div class=\"report\">\n    <div class=\"header\">\n      <el-form :inline=\"true\" :model=\"params\" class=\"form-inline\">\n        <el-form-item label=\"课程名称\">\n          <el-input size=\"small\" @keydown.enter=\"search\" class=\"search-input\" v-model=\"params.keyword\" placeholder=\"请输入关键字\">\n            <template #suffix>\n              <i @click=\"search\" class=\"el-input__icon el-icon-search search-btn\"></i>\n            </template>\n          </el-input>\n        </el-form-item>\n        <el-form-item label=\"课程状态\" class=\"select\">\n          <el-select size=\"small\" v-model=\"params.status\" @change=\"search\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"未发布\" value=\"unpublished\"></el-option>\n            <el-option label=\"已发布\" value=\"published\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"课程分类\" class=\"select\">\n          <el-cascader size=\"small\" v-model=\"selectCidList\" :options=\"categoryOptions\" :props=\"{ checkStrictly: true }\" @change=\"search\" clearable></el-cascader>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" type=\"primary\" @click=\"edit()\">\n            <el-icon style=\"vertical-align: middle\">\n              <Search />\n            </el-icon>\n            <span style=\"vertical-align: middle\">搜索</span>\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"report-main\">\n      <el-table>\n        <el-table-column label=\"名称\" prop=\"name\"></el-table-column>\n        <el-table-column label=\"今日报名人数\" prop=\"name\"></el-table-column>\n        <el-table-column label=\"今日报名次数\" prop=\"name\"></el-table-column>\n        <el-table-column label=\"总报名人数\" prop=\"name\"></el-table-column>\n        <el-table-column label=\"总报名次数\" prop=\"name\"></el-table-column>\n        <el-table-column label=\"取消报名数\" prop=\"name\"></el-table-column>\n      </el-table>\n      <page :total=\"total\" :size-change=\"sizeChange\" :current-change=\"currentChange\" :page-size=\"params.size\"/>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {ref} from \"vue\"\nimport Page from \"@/components/Page\";\nimport {Search} from \"@element-plus/icons-vue\";\nexport default {\n  name: \"LearnReportSignUpIndex\",\n  components: {Search, Page},\n  setup() {\n    const params = ref({\n      current: 1,\n      size: 20,\n      status: ''\n    })\n    const selectCidList = ref([])\n    const categoryOptions = ref([])\n    \n    const loadList = () => {\n\n    }\n    const total = ref(0)\n    const currentChange = (c) => {\n      params.value.current = c;\n      loadList();\n    }\n    const sizeChange = (s) => {\n      params.value.size = s;\n      loadList();\n    }\n    const search = () => {\n      params.current = 1\n      loadList()\n    }\n    return {\n      params,\n      total,\n      currentChange,\n      sizeChange,\n      search\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n  .report {\n    margin: 20px;\n    font-size: 12px;\n    .report-main {\n      ::v-deep .el-table {\n        font-size: 12px;\n        .el-table__empty-block {\n          line-height: 400px;\n          .el-table__empty-text {\n            line-height: 400px;\n          }\n        }\n        th, td {\n          padding: 6px 0;\n        }\n      }\n    }\n  }\n</style>\n"], "mappings": "AA8CA,SAAQA,GAAG,QAAO,KAAI;AACtB,OAAOC,IAAG,MAAO,mBAAmB;AACpC,SAAQC,MAAM,QAAO,yBAAyB;AAC9C,eAAe;EACbC,IAAI,EAAE,wBAAwB;EAC9BC,UAAU,EAAE;IAACF,MAAM;IAAED;EAAI,CAAC;EAC1BI,KAAKA,CAAA,EAAG;IACN,MAAMC,MAAK,GAAIN,GAAG,CAAC;MACjBO,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE;IACV,CAAC;IACD,MAAMC,aAAY,GAAIV,GAAG,CAAC,EAAE;IAC5B,MAAMW,eAAc,GAAIX,GAAG,CAAC,EAAE;IAE9B,MAAMY,QAAO,GAAIA,CAAA,KAAM,CAEvB;IACA,MAAMC,KAAI,GAAIb,GAAG,CAAC,CAAC;IACnB,MAAMc,aAAY,GAAKC,CAAC,IAAK;MAC3BT,MAAM,CAACU,KAAK,CAACT,OAAM,GAAIQ,CAAC;MACxBH,QAAQ,EAAE;IACZ;IACA,MAAMK,UAAS,GAAKC,CAAC,IAAK;MACxBZ,MAAM,CAACU,KAAK,CAACR,IAAG,GAAIU,CAAC;MACrBN,QAAQ,EAAE;IACZ;IACA,MAAMO,MAAK,GAAIA,CAAA,KAAM;MACnBb,MAAM,CAACC,OAAM,GAAI;MACjBK,QAAQ,EAAC;IACX;IACA,OAAO;MACLN,MAAM;MACNO,KAAK;MACLC,aAAa;MACbG,UAAU;MACVE;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}