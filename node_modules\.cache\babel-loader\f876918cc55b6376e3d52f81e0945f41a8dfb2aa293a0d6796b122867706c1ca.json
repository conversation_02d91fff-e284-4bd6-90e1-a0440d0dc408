{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref } from \"vue\";\nimport { findCategoryList, toTree } from \"@/api/exam/paper/category\";\nimport { findList, delPaper } from \"@/api/exam/paper\";\nimport Page from \"@/components/Page\";\nimport { confirm, success } from \"@/util/tipsUtils\";\nimport router from \"@/router\";\nexport default {\n  name: \"PaperList\",\n  components: {\n    Page\n  },\n  props: {\n    isComponent: {\n      type: Boolean,\n      default: false\n    },\n    selectionChangeCallback: {\n      type: Function,\n      default: a => {\n        console.log(a);\n      }\n    },\n    componentCid: {\n      type: Number,\n      default: 0\n    },\n    hideComponent: {\n      type: Function,\n      default: () => {}\n    }\n  },\n  setup(props) {\n    const selectCidList = ref([]);\n    const commodityIdList = ref([]);\n    const categoryOptions = ref([]);\n    const list = ref([]);\n    const total = ref(0);\n    const params = ref({\n      keyword: \"\",\n      cid: \"\",\n      type: \"\",\n      size: 20,\n      current: 1\n    });\n    const colors = [\"#99A9BF\", \"#F7BA2A\", \"#FF9900\"];\n    const paperTypeMap = {\n      \"normal\": \"静态试卷\",\n      \"random\": \"随机试卷\",\n      \"mock\": \"模拟试卷\"\n    };\n    const statusMap = {\n      \"draft\": \"草稿\",\n      \"published\": \"已发布\",\n      \"deleted\": \"已删除\"\n    };\n    // 加载分类\n    const loadCategory = () => {\n      findCategoryList(0, true, res => {\n        if (res) {\n          categoryOptions.value = toTree(res);\n        }\n      });\n    };\n    loadCategory();\n    // 加载列表\n    const loadList = () => {\n      if (props.isComponent) {\n        params.value.cid = props.componentCid;\n      }\n      findList(params.value, res => {\n        if (!res) {\n          return;\n        }\n        list.value = res.list;\n        total.value = res.total;\n      });\n    };\n    loadList();\n    // 搜索\n    const search = () => {\n      if (selectCidList.value && selectCidList.value.length) {\n        params.value.cid = selectCidList.value[selectCidList.value.length - 1];\n      }\n      loadList();\n    };\n    // 编辑\n    const edit = item => {\n      router.push({\n        path: \"/exam/paper/\" + item.type.replace(\"_\", \"-\"),\n        query: {\n          id: item.id\n        }\n      });\n    };\n    const remove = id => {\n      confirm(\"确认删除试卷?\", \"提示\", () => {\n        delPaper(id, () => {\n          success(\"删除成功\");\n          loadList();\n        });\n      });\n    };\n    const pageChange = c => {\n      params.value.current = c;\n      loadList();\n    };\n    const sizeChange = function (size) {\n      params.value.size = size;\n      loadList();\n    };\n    const expandChange = (row, expandedRows) => {\n      // 展开\n      if (expandedRows.length > 0) {\n        console.log(row, expandedRows);\n      }\n    };\n    // 选择列表项\n    const selectItem = val => {\n      commodityIdList.value = [];\n      if (val && val.length > 0) {\n        for (const valElement of val) {\n          commodityIdList.value.push(valElement.id);\n        }\n      }\n    };\n    return {\n      colors,\n      paperTypeMap,\n      statusMap,\n      selectCidList,\n      commodityIdList,\n      categoryOptions,\n      list,\n      total,\n      params,\n      search,\n      selectItem,\n      edit,\n      remove,\n      pageChange,\n      sizeChange,\n      expandChange\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "findCategoryList", "toTree", "findList", "delPaper", "Page", "confirm", "success", "router", "name", "components", "props", "isComponent", "type", "Boolean", "default", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Function", "a", "console", "log", "componentCid", "Number", "hideComponent", "setup", "selectCidList", "commodityIdList", "categoryOptions", "list", "total", "params", "keyword", "cid", "size", "current", "colors", "paperTypeMap", "statusMap", "loadCategory", "res", "value", "loadList", "search", "length", "edit", "item", "push", "path", "replace", "query", "id", "remove", "pageChange", "c", "sizeChange", "expandChange", "row", "expandedRows", "selectItem", "val", "valElement"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/exam/paper/index.vue"], "sourcesContent": ["<template>\n  <div>\n    <div class=\"container\">\n      <div class=\"header\">\n        <el-form :inline=\"true\" :model=\"params\" class=\"demo-form-inline\">\n          <el-form-item label=\"\">\n            <el-input size=\"small\" class=\"search-input\" v-model=\"params.keyword\" placeholder=\"请输入关键字\"></el-input>\n            <el-button size=\"small\" class=\"search-btn\" type=\"primary\" @click=\"search\">搜索</el-button>\n          </el-form-item>\n          <el-form-item label=\"类型\" class=\"status\">\n            <el-select size=\"small\" v-model=\"params.type\" @change=\"search\">\n              <el-option label=\"全部\" value=\"\"></el-option>\n              <el-option :label=\"key\" :value=\"value\" v-for=\"(key, value) in paperTypeMap\" :key=\"value\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"状态\" class=\"status\">\n            <el-select size=\"small\" v-model=\"params.status\" @change=\"search\">\n              <el-option label=\"全部\" value=\"\"></el-option>\n              <el-option :label=\"key\" :value=\"value\" v-for=\"(key, value) in statusMap\" :key=\"value\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"分类\" v-if=\"!isComponent\">\n            <el-cascader size=\"small\" v-model=\"selectCidList\" :options=\"categoryOptions\" :props=\"{ checkStrictly: true }\" @change=\"search\" clearable></el-cascader>\n          </el-form-item>\n        </el-form>\n      </div>\n      <div class=\"content\">\n        <el-table ref=\"multipleTable\" @selection-change=\"selectItem\" :data=\"list\" style=\"width: 100%;\" @expand-change=\"expandChange\">\n          <el-table-column v-if=\"isComponent\" type=\"selection\" width=\"30\"></el-table-column>\n          <el-table-column type=\"expand\">\n            <template #default=\"scope\">\n              <el-card class=\"box-card\">\n                <template #header>\n                  <div class=\"clearfix\">\n                    <span>基础信息</span>\n                  </div>\n                </template>\n                <div class=\"table-wrapper\">\n                  <table class=\"fl-table\" style=\"width: 100%;\">\n                    <tr><td>试卷名称：</td><td>{{scope.row.title}}</td></tr>\n                    <tr><td width=\"120\">创建时间：</td><td>{{scope.row.createTime}}</td></tr>\n                    <tr><td>试卷描述：</td><td>{{scope.row.note || \"无\"}}</td></tr>\n                  </table>\n                </div>\n              </el-card>\n              <el-card style=\"margin-top: 20px;\">\n                <template #header>\n                  <div class=\"clearfix\">\n                    <span>题目</span>\n                  </div>\n                </template>\n                <div class=\"fl-table\">\n                  <el-table :data=\"scope.row.questionList\" v-if=\"scope.row.questionList && scope.row.type !== 'random'\" :show-header=\"false\" style=\"width: 100%;\">\n                    <el-table-column type=\"index\" width=\"50\"></el-table-column>\n                    <el-table-column prop=\"title\"></el-table-column>\n                  </el-table>\n                  <div v-else>\n                    根据抽题条件随机抽取题目组成试卷\n                  </div>\n                </div>\n              </el-card>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"id\" label=\"ID\" width=\"50\"></el-table-column>\n          <el-table-column label=\"试卷类型\" width=\"80\">\n            <template #default=\"scope\">\n              {{paperTypeMap[scope.row.type]}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"title\" label=\"试卷名称\"></el-table-column>\n          <el-table-column prop=\"score\" label=\"总分\" width=\"80\"></el-table-column>\n          <el-table-column prop=\"passScore\" label=\"合格分数\" width=\"80\"></el-table-column>\n          <el-table-column prop=\"difficulty\" label=\"难度\" width=\"140\">\n            <template #default=\"scope\">\n              <el-rate :disabled=\"true\" v-model=\"scope.row.difficulty\" :colors=\"colors\"></el-rate>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"difficulty\" label=\"状态\" width=\"80\">\n            <template #default=\"scope\">\n              {{statusMap[scope.row.status]}}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" v-if=\"!isComponent\" width=\"100\">\n            <template #default=\"scope\">\n              <el-button class=\"right-btn\" type=\"text\" @click=\"edit(scope.row)\" size=\"small\">编辑</el-button>\n              <el-button class=\"right-btn\" type=\"text\" @click=\"remove(scope.row.id)\" size=\"mini\">删除</el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n      <page :total=\"total\" :page-size=\"params.size\" :current-change=\"pageChange\" :size-change=\"sizeChange\"></page>\n    </div>\n    <template v-if=\"isComponent\">\n      <div class=\"dialog-footer\" style=\"text-align: right;margin-top: 30px;\">\n        <el-button size=\"mini\" @click=\"hideComponent\">取 消</el-button>\n        <el-button size=\"mini\" type=\"primary\" @click=\"selectionChangeCallback(commodityIdList)\">确 定</el-button>\n      </div>\n    </template>\n  </div>\n</template>\n\n<script>\nimport {ref} from \"vue\"\nimport {findCategoryList, toTree} from \"@/api/exam/paper/category\"\nimport {findList, delPaper} from \"@/api/exam/paper\"\nimport Page from \"@/components/Page\"\nimport {confirm, success} from \"@/util/tipsUtils\";\nimport router from \"@/router\";\n\nexport default {\n  name: \"PaperList\",\n  components: {\n    Page\n  },\n  props: {\n    isComponent: {\n      type: Boolean,\n      default: false\n    },\n    selectionChangeCallback: {\n      type: Function,\n      default: (a) => {\n        console.log(a)\n      }\n    },\n    componentCid: {\n      type: Number,\n      default: 0\n    },\n    hideComponent: {\n      type: Function,\n      default: () => {\n      }\n    }\n  },\n  setup(props) {\n    const selectCidList = ref([])\n    const commodityIdList = ref([])\n    const categoryOptions = ref([])\n    const list = ref([])\n    const total = ref(0)\n    const params = ref({\n      keyword: \"\",\n      cid: \"\",\n      type: \"\",\n      size: 20,\n      current: 1\n    })\n    const colors = [\"#99A9BF\", \"#F7BA2A\", \"#FF9900\"]\n    const paperTypeMap = {\n      \"normal\": \"静态试卷\",\n      \"random\": \"随机试卷\",\n      \"mock\": \"模拟试卷\",\n    }\n    const statusMap = {\n      \"draft\": \"草稿\",\n      \"published\": \"已发布\",\n      \"deleted\": \"已删除\"\n    }\n    // 加载分类\n    const loadCategory = () => {\n      findCategoryList(0, true, (res) => {\n        if (res) {\n          categoryOptions.value = toTree(res);\n        }\n      })\n    }\n    loadCategory()\n    // 加载列表\n    const loadList = () => {\n      if (props.isComponent) {\n        params.value.cid = props.componentCid;\n      }\n      findList(params.value, (res) => {\n        if (!res) {return;}\n        list.value = res.list;\n        total.value = res.total;\n      })\n    }\n    loadList()\n    // 搜索\n    const search = () => {\n      if (selectCidList.value && selectCidList.value.length) {\n        params.value.cid = selectCidList.value[selectCidList.value.length - 1];\n      }\n      loadList();\n    }\n    // 编辑\n    const edit = (item) => {\n      router.push({path: \"/exam/paper/\" + item.type.replace(\"_\", \"-\"), query: { id : item.id }})\n    }\n    const remove = (id) => {\n      confirm(\"确认删除试卷?\", \"提示\", () => {\n        delPaper(id, () => {\n          success(\"删除成功\")\n          loadList()\n        })\n      })\n    }\n    const pageChange = (c) => {\n      params.value.current = c;\n      loadList();\n    }\n    const sizeChange =function(size){\n      params.value.size = size;\n      loadList();\n    }\n    const expandChange = (row, expandedRows) => {\n      // 展开\n      if(expandedRows.length>0) {\n        console.log(row, expandedRows)\n      }\n    }\n    // 选择列表项\n    const selectItem = (val) => {\n      commodityIdList.value = [];\n      if (val && val.length > 0) {\n        for (const valElement of val) {\n          commodityIdList.value.push(valElement.id);\n        }\n      }\n    }\n    return {\n      colors,\n      paperTypeMap,\n      statusMap,\n      selectCidList,\n      commodityIdList,\n      categoryOptions,\n      list,\n      total,\n      params,\n      search,\n      selectItem,\n      edit,\n      remove,\n      pageChange,\n      sizeChange,\n      expandChange\n    }\n  }\n};\n</script>\n\n<style  scoped lang=\"scss\">\n  .container {\n    margin: 20px;\n  }\n  .image {\n    height: 60px;\n    display: inline-block;\n  }\n  .right-btn{\n    margin: 5px 10px 5px 0;\n  }\n  .search-input {\n    width: 242px;\n  }\n  ::v-deep .el-table-column--selection .cell{\n    padding-left: 14px;\n    padding-right: 14px;\n  }\n  ::v-deep .el-table tbody tr:hover > td {\n    background-color: transparent;\n  }\n  .fl-table {\n    tr:last-child, ::v-deep tr:last-child {\n      td {\n        border: 0;\n      }\n    }\n  }\n  .dialog-footer {\n    text-align: center;\n    margin-top: 40px;\n  }\n</style>\n"], "mappings": ";AAsGA,SAAQA,GAAG,QAAO,KAAI;AACtB,SAAQC,gBAAgB,EAAEC,MAAM,QAAO,2BAA0B;AACjE,SAAQC,QAAQ,EAAEC,QAAQ,QAAO,kBAAiB;AAClD,OAAOC,IAAG,MAAO,mBAAkB;AACnC,SAAQC,OAAO,EAAEC,OAAO,QAAO,kBAAkB;AACjD,OAAOC,MAAK,MAAO,UAAU;AAE7B,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAE;IACVL;EACF,CAAC;EACDM,KAAK,EAAE;IACLC,WAAW,EAAE;MACXC,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDC,uBAAuB,EAAE;MACvBH,IAAI,EAAEI,QAAQ;MACdF,OAAO,EAAGG,CAAC,IAAK;QACdC,OAAO,CAACC,GAAG,CAACF,CAAC;MACf;IACF,CAAC;IACDG,YAAY,EAAE;MACZR,IAAI,EAAES,MAAM;MACZP,OAAO,EAAE;IACX,CAAC;IACDQ,aAAa,EAAE;MACbV,IAAI,EAAEI,QAAQ;MACdF,OAAO,EAAEA,CAAA,KAAM,CACf;IACF;EACF,CAAC;EACDS,KAAKA,CAACb,KAAK,EAAE;IACX,MAAMc,aAAY,GAAIzB,GAAG,CAAC,EAAE;IAC5B,MAAM0B,eAAc,GAAI1B,GAAG,CAAC,EAAE;IAC9B,MAAM2B,eAAc,GAAI3B,GAAG,CAAC,EAAE;IAC9B,MAAM4B,IAAG,GAAI5B,GAAG,CAAC,EAAE;IACnB,MAAM6B,KAAI,GAAI7B,GAAG,CAAC,CAAC;IACnB,MAAM8B,MAAK,GAAI9B,GAAG,CAAC;MACjB+B,OAAO,EAAE,EAAE;MACXC,GAAG,EAAE,EAAE;MACPnB,IAAI,EAAE,EAAE;MACRoB,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC;IACD,MAAMC,MAAK,GAAI,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;IAC/C,MAAMC,YAAW,GAAI;MACnB,QAAQ,EAAE,MAAM;MAChB,QAAQ,EAAE,MAAM;MAChB,MAAM,EAAE;IACV;IACA,MAAMC,SAAQ,GAAI;MAChB,OAAO,EAAE,IAAI;MACb,WAAW,EAAE,KAAK;MAClB,SAAS,EAAE;IACb;IACA;IACA,MAAMC,YAAW,GAAIA,CAAA,KAAM;MACzBrC,gBAAgB,CAAC,CAAC,EAAE,IAAI,EAAGsC,GAAG,IAAK;QACjC,IAAIA,GAAG,EAAE;UACPZ,eAAe,CAACa,KAAI,GAAItC,MAAM,CAACqC,GAAG,CAAC;QACrC;MACF,CAAC;IACH;IACAD,YAAY,EAAC;IACb;IACA,MAAMG,QAAO,GAAIA,CAAA,KAAM;MACrB,IAAI9B,KAAK,CAACC,WAAW,EAAE;QACrBkB,MAAM,CAACU,KAAK,CAACR,GAAE,GAAIrB,KAAK,CAACU,YAAY;MACvC;MACAlB,QAAQ,CAAC2B,MAAM,CAACU,KAAK,EAAGD,GAAG,IAAK;QAC9B,IAAI,CAACA,GAAG,EAAE;UAAC;QAAO;QAClBX,IAAI,CAACY,KAAI,GAAID,GAAG,CAACX,IAAI;QACrBC,KAAK,CAACW,KAAI,GAAID,GAAG,CAACV,KAAK;MACzB,CAAC;IACH;IACAY,QAAQ,EAAC;IACT;IACA,MAAMC,MAAK,GAAIA,CAAA,KAAM;MACnB,IAAIjB,aAAa,CAACe,KAAI,IAAKf,aAAa,CAACe,KAAK,CAACG,MAAM,EAAE;QACrDb,MAAM,CAACU,KAAK,CAACR,GAAE,GAAIP,aAAa,CAACe,KAAK,CAACf,aAAa,CAACe,KAAK,CAACG,MAAK,GAAI,CAAC,CAAC;MACxE;MACAF,QAAQ,EAAE;IACZ;IACA;IACA,MAAMG,IAAG,GAAKC,IAAI,IAAK;MACrBrC,MAAM,CAACsC,IAAI,CAAC;QAACC,IAAI,EAAE,cAAa,GAAIF,IAAI,CAAChC,IAAI,CAACmC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;QAAEC,KAAK,EAAE;UAAEC,EAAC,EAAIL,IAAI,CAACK;QAAG;MAAC,CAAC;IAC3F;IACA,MAAMC,MAAK,GAAKD,EAAE,IAAK;MACrB5C,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM;QAC7BF,QAAQ,CAAC8C,EAAE,EAAE,MAAM;UACjB3C,OAAO,CAAC,MAAM;UACdkC,QAAQ,EAAC;QACX,CAAC;MACH,CAAC;IACH;IACA,MAAMW,UAAS,GAAKC,CAAC,IAAK;MACxBvB,MAAM,CAACU,KAAK,CAACN,OAAM,GAAImB,CAAC;MACxBZ,QAAQ,EAAE;IACZ;IACA,MAAMa,UAAS,GAAG,SAAAA,CAASrB,IAAI,EAAC;MAC9BH,MAAM,CAACU,KAAK,CAACP,IAAG,GAAIA,IAAI;MACxBQ,QAAQ,EAAE;IACZ;IACA,MAAMc,YAAW,GAAIA,CAACC,GAAG,EAAEC,YAAY,KAAK;MAC1C;MACA,IAAGA,YAAY,CAACd,MAAM,GAAC,CAAC,EAAE;QACxBxB,OAAO,CAACC,GAAG,CAACoC,GAAG,EAAEC,YAAY;MAC/B;IACF;IACA;IACA,MAAMC,UAAS,GAAKC,GAAG,IAAK;MAC1BjC,eAAe,CAACc,KAAI,GAAI,EAAE;MAC1B,IAAImB,GAAE,IAAKA,GAAG,CAAChB,MAAK,GAAI,CAAC,EAAE;QACzB,KAAK,MAAMiB,UAAS,IAAKD,GAAG,EAAE;UAC5BjC,eAAe,CAACc,KAAK,CAACM,IAAI,CAACc,UAAU,CAACV,EAAE,CAAC;QAC3C;MACF;IACF;IACA,OAAO;MACLf,MAAM;MACNC,YAAY;MACZC,SAAS;MACTZ,aAAa;MACbC,eAAe;MACfC,eAAe;MACfC,IAAI;MACJC,KAAK;MACLC,MAAM;MACNY,MAAM;MACNgB,UAAU;MACVd,IAAI;MACJO,MAAM;MACNC,UAAU;MACVE,UAAU;MACVC;IACF;EACF;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}