{"ast": null, "code": "import { nextTick, onMounted, ref, watch } from \"vue\";\nimport { toBase64 } from \"@/api/oss/oss\";\nexport default {\n  name: \"CertificatePreview\",\n  props: {\n    certificate: {\n      type: Object\n    },\n    download: {\n      type: Boolean\n    }\n  },\n  setup(props) {\n    const isLoading = ref(true);\n    const downloadFlag = ref(false);\n    const certificateInfo = ref(props.certificate);\n    const certificateCanvas = ref(null);\n    const canvasContainer = ref(null);\n\n    // 背景图的固定宽高比\n    const backgroundWidth = 2894;\n    const backgroundHeight = 4093;\n\n    // 计算容器的高度\n    const setContainerHeight = () => {\n      const container = canvasContainer.value;\n      const containerWidth = container.clientWidth;\n\n      // 根据背景图的宽高比计算容器的高度\n      const calculatedHeight = containerWidth * (backgroundHeight / backgroundWidth);\n      container.style.height = `${calculatedHeight}px`;\n    };\n\n    // 获取并设置Canvas尺寸\n    const setCanvasSize = () => {\n      const container = canvasContainer.value;\n      const canvas = certificateCanvas.value;\n      const containerWidth = container.clientWidth;\n      const containerHeight = container.clientHeight;\n      canvas.width = containerWidth;\n      canvas.height = containerHeight;\n\n      // 重新生成证书内容\n      generateCertificate(certificateInfo.value);\n    };\n\n    // 加载图片的工具方法，返回一个Promise\n    const loadImage = url => {\n      return new Promise((resolve, reject) => {\n        // toBase64(url, res => {\n        const img = new Image();\n        img.src = res;\n        img.crossOrigin = 'anonymous';\n        img.onload = () => resolve(img);\n        img.onerror = err => reject(err);\n        // }).catch((err) => {\n        //   reject(err)\n        // })\n      });\n    };\n\n    // 传入日期字符串 '2025-02-01 00:39:29'\n    const getYear = dateString => {\n      if (!dateString) {\n        return '';\n      }\n      return dateString.split(' ')[0].split('-')[0]; // 拆分获取年份\n    };\n\n    const getMonth = dateString => {\n      if (!dateString) {\n        return '';\n      }\n      return dateString.split(' ')[0].split('-')[1]; // 拆分获取月份\n    };\n\n    const getDay = dateString => {\n      if (!dateString) {\n        return '';\n      }\n      return dateString.split(' ')[0].split('-')[2]; // 拆分获取日期\n    };\n\n    // 根据证书对象生成证书\n    const generateCertificate = async certificateInfo => {\n      const canvas = certificateCanvas.value;\n      const ctx = canvas.getContext('2d');\n      try {\n        // 同时加载背景图和头像图\n        const [backgroundImage, avatar] = await Promise.all([loadImage(certificateInfo.design), loadImage(certificateInfo.member && certificateInfo.member.idPhoto).catch(() => null) // 捕获头像加载失败\n        ]);\n\n        // 计算缩放比例\n        const imageRatio = backgroundImage.width / backgroundImage.height;\n        const canvasRatio = canvas.width / canvas.height;\n        let drawWidth,\n          drawHeight,\n          offsetX = 0,\n          offsetY = 0;\n        if (canvasRatio > imageRatio) {\n          // 如果Canvas宽高比大于背景图宽高比，限制Canvas高度，宽度自适应\n          drawHeight = canvas.height;\n          drawWidth = drawHeight * imageRatio;\n          offsetX = (canvas.width - drawWidth) / 2; // 水平居中\n        } else {\n          // 如果Canvas宽高比小于背景图宽高比，限制Canvas宽度，高度自适应\n          drawWidth = canvas.width;\n          drawHeight = drawWidth / imageRatio;\n          offsetY = (canvas.height - drawHeight) / 2; // 垂直居中\n        }\n\n        // 将背景图绘制到Canvas，背景图根据 Canvas 尺寸缩放并居中\n        ctx.drawImage(backgroundImage, 0, 0, backgroundImage.width, backgroundImage.height, offsetX, offsetY, drawWidth, drawHeight);\n\n        // 绘制背景图\n        // ctx.drawImage(backgroundImage, 0, 0, canvas.width, canvas.height);\n\n        // 如果头像存在且加载成功，则绘制头像\n        if (avatar) {\n          let avatarSize = drawWidth * 0.175; // 头像大小占画布宽度的 17.5%\n          let avatarYSize = drawHeight * 0.165; // 头像大小占画布宽度的 20%\n          let avatarX = drawWidth * 0.66; // 居中\n          let avatarY = drawHeight * 0.382; // 头像位置在画布上方\n          ctx.drawImage(avatar, avatarX, avatarY, avatarSize, avatarYSize); // 头像位置和大小\n          // } else {\n          //   // 头像加载失败或者没有提供，绘制默认的“无头像”占位符\n          //   ctx.fillStyle = 'gray';\n          //   ctx.fillRect(50, 50, 100, 100); // 绘制灰色方块作为占位符\n          //   ctx.fillStyle = 'white';\n          //   ctx.font = '24px Arial';\n          //   ctx.fillText('无头像', 55, 110); // 标注“无头像”\n        }\n\n        // 绘制文字\n        ctx.font = '30px Arial';\n        ctx.fillStyle = \"#000\";\n        ctx.fillText(`${certificateInfo.code}`, drawWidth * 0.17, drawHeight * 0.49);\n        ctx.fillText(`${certificateInfo.member.realname || this.certificateInfo.member.name}`, drawWidth * 0.2, drawHeight * 0.59);\n        ctx.fillText(`${certificateInfo.description}`, drawWidth * 0.42, drawHeight * 0.59);\n        ctx.fillText(`${getYear(certificateInfo.awardDate)}`, drawWidth * 0.56, drawHeight * 0.79);\n        ctx.fillText(`${getMonth(certificateInfo.awardDate)}`, drawWidth * 0.69, drawHeight * 0.79);\n        ctx.fillText(`${getDay(certificateInfo.awardDate)}`, drawWidth * 0.77, drawHeight * 0.79);\n        downloadFlag.value = true;\n        isLoading.value = false;\n      } catch (error) {\n        console.error('图片加载失败', error);\n        isLoading.value = false;\n      }\n    };\n\n    // 在组件挂载后生成证书\n    onMounted(() => {\n      nextTick(() => {\n        setContainerHeight(); // 更新容器高度\n        setCanvasSize(); // 更新Canvas尺寸\n      });\n    });\n\n    // 监听容器尺寸变化\n    watch(canvasContainer, () => {\n      setContainerHeight(); // 更新容器高度\n      setCanvasSize(); // 更新Canvas尺寸\n    });\n\n    // 下载证书\n    const downloadCertificate = () => {\n      const canvas = certificateCanvas.value;\n      const imageUrl = canvas.toDataURL('image/png'); // 转换为PNG图片\n      const link = document.createElement('a');\n      link.href = imageUrl;\n      link.download = 'certificate.png'; // 下载文件名\n      link.click();\n    };\n    return {\n      isLoading,\n      downloadFlag,\n      canvasContainer,\n      certificateCanvas,\n      downloadCertificate,\n      certificateInfo\n    };\n  }\n};", "map": {"version": 3, "names": ["nextTick", "onMounted", "ref", "watch", "toBase64", "name", "props", "certificate", "type", "Object", "download", "Boolean", "setup", "isLoading", "downloadFlag", "certificateInfo", "certificateCanvas", "canvasContainer", "backgroundWidth", "backgroundHeight", "setContainerHeight", "container", "value", "containerWidth", "clientWidth", "calculatedHeight", "style", "height", "setCanvasSize", "canvas", "containerHeight", "clientHeight", "width", "generateCertificate", "loadImage", "url", "Promise", "resolve", "reject", "img", "Image", "src", "res", "crossOrigin", "onload", "onerror", "err", "getYear", "dateString", "split", "getMonth", "getDay", "ctx", "getContext", "backgroundImage", "avatar", "all", "design", "member", "idPhoto", "catch", "imageRatio", "canvasRatio", "drawWidth", "drawHeight", "offsetX", "offsetY", "drawImage", "avatarSize", "avatarYSize", "avatarX", "avatarY", "font", "fillStyle", "fillText", "code", "realname", "description", "awardDate", "error", "console", "downloadCertificate", "imageUrl", "toDataURL", "link", "document", "createElement", "href", "click"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/certificate/preview/index.vue"], "sourcesContent": ["<template>\n  <div>\n    <transition name=\"fade\">\n      <div v-if=\"isLoading\" class=\"loading-overlay\">\n        正在加载...\n      </div>\n    </transition>\n  </div>\n  <div>\n    <el-button v-if=\"downloadFlag\" type=\"primary\" size=\"large\" @click=\"downloadCertificate\">下载证书</el-button>\n    <div class=\"certificate-coat\">\n      <div class=\"certificate-rotate-wrap\" ref=\"canvasContainer\">\n        <canvas ref=\"certificateCanvas\"></canvas>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {nextTick, onMounted, ref, watch} from \"vue\";\nimport {toBase64} from \"@/api/oss/oss\";\n\nexport default {\n  name: \"CertificatePreview\",\n  props: {\n    certificate: {\n      type: Object\n    },\n    download: {\n      type: Boolean\n    }\n  },\n  setup(props) {\n    const isLoading = ref(true)\n    const downloadFlag = ref(false)\n    const certificateInfo = ref(props.certificate)\n\n    const certificateCanvas = ref(null);\n    const canvasContainer = ref(null);\n\n    // 背景图的固定宽高比\n    const backgroundWidth = 2894;\n    const backgroundHeight = 4093;\n\n    // 计算容器的高度\n    const setContainerHeight = () => {\n      const container = canvasContainer.value;\n      const containerWidth = container.clientWidth;\n\n      // 根据背景图的宽高比计算容器的高度\n      const calculatedHeight = containerWidth * (backgroundHeight / backgroundWidth);\n\n      container.style.height = `${calculatedHeight}px`;\n    };\n\n    // 获取并设置Canvas尺寸\n    const setCanvasSize = () => {\n      const container = canvasContainer.value;\n      const canvas = certificateCanvas.value;\n\n      const containerWidth = container.clientWidth;\n      const containerHeight = container.clientHeight;\n\n      canvas.width = containerWidth;\n      canvas.height = containerHeight;\n\n      // 重新生成证书内容\n      generateCertificate(certificateInfo.value);\n    };\n\n\n    // 加载图片的工具方法，返回一个Promise\n    const loadImage = (url) => {\n      return new Promise((resolve, reject) => {\n        // toBase64(url, res => {\n          const img = new Image();\n          img.src = res;\n          img.crossOrigin = 'anonymous';\n          img.onload = () => resolve(img);\n          img.onerror = (err) => reject(err);\n        // }).catch((err) => {\n        //   reject(err)\n        // })\n      });\n    };\n\n    // 传入日期字符串 '2025-02-01 00:39:29'\n    const getYear = (dateString) => {\n      if (!dateString) {\n        return ''\n      }\n      return dateString.split(' ')[0].split('-')[0]; // 拆分获取年份\n    }\n\n    const getMonth = (dateString) =>  {\n      if (!dateString) {\n        return ''\n      }\n      return dateString.split(' ')[0].split('-')[1]; // 拆分获取月份\n    }\n\n    const getDay = (dateString) => {\n      if (!dateString) {\n        return ''\n      }\n      return dateString.split(' ')[0].split('-')[2]; // 拆分获取日期\n    }\n\n    // 根据证书对象生成证书\n    const generateCertificate = async (certificateInfo) => {\n      const canvas = certificateCanvas.value;\n      const ctx = canvas.getContext('2d');\n\n      try {\n        // 同时加载背景图和头像图\n        const [backgroundImage, avatar] = await Promise.all([\n          loadImage(certificateInfo.design),\n          loadImage(certificateInfo.member && certificateInfo.member.idPhoto).catch(() => null), // 捕获头像加载失败\n        ]);\n\n        // 计算缩放比例\n        const imageRatio = backgroundImage.width / backgroundImage.height;\n        const canvasRatio = canvas.width / canvas.height;\n\n        let drawWidth, drawHeight, offsetX = 0, offsetY = 0;\n\n        if (canvasRatio > imageRatio) {\n          // 如果Canvas宽高比大于背景图宽高比，限制Canvas高度，宽度自适应\n          drawHeight = canvas.height;\n          drawWidth = drawHeight * imageRatio;\n          offsetX = (canvas.width - drawWidth) / 2;  // 水平居中\n        } else {\n          // 如果Canvas宽高比小于背景图宽高比，限制Canvas宽度，高度自适应\n          drawWidth = canvas.width;\n          drawHeight = drawWidth / imageRatio;\n          offsetY = (canvas.height - drawHeight) / 2;  // 垂直居中\n        }\n\n        // 将背景图绘制到Canvas，背景图根据 Canvas 尺寸缩放并居中\n        ctx.drawImage(backgroundImage, 0, 0, backgroundImage.width, backgroundImage.height, offsetX, offsetY, drawWidth, drawHeight);\n\n\n        // 绘制背景图\n        // ctx.drawImage(backgroundImage, 0, 0, canvas.width, canvas.height);\n\n        // 如果头像存在且加载成功，则绘制头像\n        if (avatar) {\n          let avatarSize = drawWidth * 0.175; // 头像大小占画布宽度的 17.5%\n          let avatarYSize = drawHeight * 0.165; // 头像大小占画布宽度的 20%\n          let avatarX = drawWidth * 0.66; // 居中\n          let avatarY = drawHeight * 0.382; // 头像位置在画布上方\n          ctx.drawImage(avatar, avatarX, avatarY, avatarSize, avatarYSize); // 头像位置和大小\n        // } else {\n        //   // 头像加载失败或者没有提供，绘制默认的“无头像”占位符\n        //   ctx.fillStyle = 'gray';\n        //   ctx.fillRect(50, 50, 100, 100); // 绘制灰色方块作为占位符\n        //   ctx.fillStyle = 'white';\n        //   ctx.font = '24px Arial';\n        //   ctx.fillText('无头像', 55, 110); // 标注“无头像”\n        }\n\n        // 绘制文字\n        ctx.font = '30px Arial';\n        ctx.fillStyle = \"#000\";\n        ctx.fillText(`${certificateInfo.code}`, drawWidth * 0.17, drawHeight * 0.49);\n        ctx.fillText(`${certificateInfo.member.realname || this.certificateInfo.member.name}`, drawWidth * 0.2, drawHeight * 0.59);\n        ctx.fillText(`${certificateInfo.description}`, drawWidth * 0.42, drawHeight * 0.59);\n\n        ctx.fillText(`${getYear(certificateInfo.awardDate)}`, drawWidth * 0.56, drawHeight * 0.79);\n        ctx.fillText(`${getMonth(certificateInfo.awardDate)}`, drawWidth * 0.69, drawHeight * 0.79);\n        ctx.fillText(`${getDay(certificateInfo.awardDate)}`, drawWidth * 0.77, drawHeight * 0.79);\n\n\n        downloadFlag.value = true\n        isLoading.value = false\n\n      } catch (error) {\n        console.error('图片加载失败', error);\n        isLoading.value = false\n      }\n    };\n\n    // 在组件挂载后生成证书\n    onMounted(() => {\n      nextTick(() => {\n        setContainerHeight();  // 更新容器高度\n        setCanvasSize();  // 更新Canvas尺寸\n      });\n    });\n\n    // 监听容器尺寸变化\n    watch(canvasContainer, () => {\n      setContainerHeight();  // 更新容器高度\n      setCanvasSize();  // 更新Canvas尺寸\n    });\n\n    // 下载证书\n    const downloadCertificate = () => {\n      const canvas = certificateCanvas.value;\n      const imageUrl = canvas.toDataURL('image/png'); // 转换为PNG图片\n      const link = document.createElement('a');\n      link.href = imageUrl;\n      link.download = 'certificate.png'; // 下载文件名\n      link.click();\n    };\n\n    return {\n      isLoading,\n      downloadFlag,\n      canvasContainer,\n      certificateCanvas,\n      downloadCertificate,\n      certificateInfo\n    };\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.el-button {\n  font-size: 20px;\n  border-radius: 4px;\n  margin: 10px;\n}\n.certificate-coat {\n  box-sizing: border-box;\n}\n.certificate-rotate {\n  transform: rotate(90deg);\n  margin-top: 165px!important;\n  margin-left: -165px!important;\n}\n.fade-enter-active, .fade-leave-active {\n  transition: opacity 0.1s;\n}\n.fade-enter, .fade-leave-to {\n  opacity: 0;\n}\n.loading-overlay {\n  /* 加载动画样式 */\n}\n</style>\n"], "mappings": "AAmBA,SAAQA,QAAQ,EAAEC,SAAS,EAAEC,GAAG,EAAEC,KAAK,QAAO,KAAK;AACnD,SAAQC,QAAQ,QAAO,eAAe;AAEtC,eAAe;EACbC,IAAI,EAAE,oBAAoB;EAC1BC,KAAK,EAAE;IACLC,WAAW,EAAE;MACXC,IAAI,EAAEC;IACR,CAAC;IACDC,QAAQ,EAAE;MACRF,IAAI,EAAEG;IACR;EACF,CAAC;EACDC,KAAKA,CAACN,KAAK,EAAE;IACX,MAAMO,SAAQ,GAAIX,GAAG,CAAC,IAAI;IAC1B,MAAMY,YAAW,GAAIZ,GAAG,CAAC,KAAK;IAC9B,MAAMa,eAAc,GAAIb,GAAG,CAACI,KAAK,CAACC,WAAW;IAE7C,MAAMS,iBAAgB,GAAId,GAAG,CAAC,IAAI,CAAC;IACnC,MAAMe,eAAc,GAAIf,GAAG,CAAC,IAAI,CAAC;;IAEjC;IACA,MAAMgB,eAAc,GAAI,IAAI;IAC5B,MAAMC,gBAAe,GAAI,IAAI;;IAE7B;IACA,MAAMC,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,MAAMC,SAAQ,GAAIJ,eAAe,CAACK,KAAK;MACvC,MAAMC,cAAa,GAAIF,SAAS,CAACG,WAAW;;MAE5C;MACA,MAAMC,gBAAe,GAAIF,cAAa,IAAKJ,gBAAe,GAAID,eAAe,CAAC;MAE9EG,SAAS,CAACK,KAAK,CAACC,MAAK,GAAK,GAAEF,gBAAiB,IAAG;IAClD,CAAC;;IAED;IACA,MAAMG,aAAY,GAAIA,CAAA,KAAM;MAC1B,MAAMP,SAAQ,GAAIJ,eAAe,CAACK,KAAK;MACvC,MAAMO,MAAK,GAAIb,iBAAiB,CAACM,KAAK;MAEtC,MAAMC,cAAa,GAAIF,SAAS,CAACG,WAAW;MAC5C,MAAMM,eAAc,GAAIT,SAAS,CAACU,YAAY;MAE9CF,MAAM,CAACG,KAAI,GAAIT,cAAc;MAC7BM,MAAM,CAACF,MAAK,GAAIG,eAAe;;MAE/B;MACAG,mBAAmB,CAAClB,eAAe,CAACO,KAAK,CAAC;IAC5C,CAAC;;IAGD;IACA,MAAMY,SAAQ,GAAKC,GAAG,IAAK;MACzB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC;QACE,MAAMC,GAAE,GAAI,IAAIC,KAAK,EAAE;QACvBD,GAAG,CAACE,GAAE,GAAIC,GAAG;QACbH,GAAG,CAACI,WAAU,GAAI,WAAW;QAC7BJ,GAAG,CAACK,MAAK,GAAI,MAAMP,OAAO,CAACE,GAAG,CAAC;QAC/BA,GAAG,CAACM,OAAM,GAAKC,GAAG,IAAKR,MAAM,CAACQ,GAAG,CAAC;QACpC;QACA;QACA;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMC,OAAM,GAAKC,UAAU,IAAK;MAC9B,IAAI,CAACA,UAAU,EAAE;QACf,OAAO,EAAC;MACV;MACA,OAAOA,UAAU,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;IACjD;;IAEA,MAAMC,QAAO,GAAKF,UAAU,IAAM;MAChC,IAAI,CAACA,UAAU,EAAE;QACf,OAAO,EAAC;MACV;MACA,OAAOA,UAAU,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;IACjD;;IAEA,MAAME,MAAK,GAAKH,UAAU,IAAK;MAC7B,IAAI,CAACA,UAAU,EAAE;QACf,OAAO,EAAC;MACV;MACA,OAAOA,UAAU,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;IACjD;;IAEA;IACA,MAAMhB,mBAAkB,GAAI,MAAOlB,eAAe,IAAK;MACrD,MAAMc,MAAK,GAAIb,iBAAiB,CAACM,KAAK;MACtC,MAAM8B,GAAE,GAAIvB,MAAM,CAACwB,UAAU,CAAC,IAAI,CAAC;MAEnC,IAAI;QACF;QACA,MAAM,CAACC,eAAe,EAAEC,MAAM,IAAI,MAAMnB,OAAO,CAACoB,GAAG,CAAC,CAClDtB,SAAS,CAACnB,eAAe,CAAC0C,MAAM,CAAC,EACjCvB,SAAS,CAACnB,eAAe,CAAC2C,MAAK,IAAK3C,eAAe,CAAC2C,MAAM,CAACC,OAAO,CAAC,CAACC,KAAK,CAAC,MAAM,IAAI,CAAC,CAAE;QAAA,CACxF,CAAC;;QAEF;QACA,MAAMC,UAAS,GAAIP,eAAe,CAACtB,KAAI,GAAIsB,eAAe,CAAC3B,MAAM;QACjE,MAAMmC,WAAU,GAAIjC,MAAM,CAACG,KAAI,GAAIH,MAAM,CAACF,MAAM;QAEhD,IAAIoC,SAAS;UAAEC,UAAU;UAAEC,OAAM,GAAI,CAAC;UAAEC,OAAM,GAAI,CAAC;QAEnD,IAAIJ,WAAU,GAAID,UAAU,EAAE;UAC5B;UACAG,UAAS,GAAInC,MAAM,CAACF,MAAM;UAC1BoC,SAAQ,GAAIC,UAAS,GAAIH,UAAU;UACnCI,OAAM,GAAI,CAACpC,MAAM,CAACG,KAAI,GAAI+B,SAAS,IAAI,CAAC,EAAG;QAC7C,OAAO;UACL;UACAA,SAAQ,GAAIlC,MAAM,CAACG,KAAK;UACxBgC,UAAS,GAAID,SAAQ,GAAIF,UAAU;UACnCK,OAAM,GAAI,CAACrC,MAAM,CAACF,MAAK,GAAIqC,UAAU,IAAI,CAAC,EAAG;QAC/C;;QAEA;QACAZ,GAAG,CAACe,SAAS,CAACb,eAAe,EAAE,CAAC,EAAE,CAAC,EAAEA,eAAe,CAACtB,KAAK,EAAEsB,eAAe,CAAC3B,MAAM,EAAEsC,OAAO,EAAEC,OAAO,EAAEH,SAAS,EAAEC,UAAU,CAAC;;QAG5H;QACA;;QAEA;QACA,IAAIT,MAAM,EAAE;UACV,IAAIa,UAAS,GAAIL,SAAQ,GAAI,KAAK,EAAE;UACpC,IAAIM,WAAU,GAAIL,UAAS,GAAI,KAAK,EAAE;UACtC,IAAIM,OAAM,GAAIP,SAAQ,GAAI,IAAI,EAAE;UAChC,IAAIQ,OAAM,GAAIP,UAAS,GAAI,KAAK,EAAE;UAClCZ,GAAG,CAACe,SAAS,CAACZ,MAAM,EAAEe,OAAO,EAAEC,OAAO,EAAEH,UAAU,EAAEC,WAAW,CAAC,EAAE;UACpE;UACA;UACA;UACA;UACA;UACA;UACA;QACA;;QAEA;QACAjB,GAAG,CAACoB,IAAG,GAAI,YAAY;QACvBpB,GAAG,CAACqB,SAAQ,GAAI,MAAM;QACtBrB,GAAG,CAACsB,QAAQ,CAAE,GAAE3D,eAAe,CAAC4D,IAAK,EAAC,EAAEZ,SAAQ,GAAI,IAAI,EAAEC,UAAS,GAAI,IAAI,CAAC;QAC5EZ,GAAG,CAACsB,QAAQ,CAAE,GAAE3D,eAAe,CAAC2C,MAAM,CAACkB,QAAO,IAAK,IAAI,CAAC7D,eAAe,CAAC2C,MAAM,CAACrD,IAAK,EAAC,EAAE0D,SAAQ,GAAI,GAAG,EAAEC,UAAS,GAAI,IAAI,CAAC;QAC1HZ,GAAG,CAACsB,QAAQ,CAAE,GAAE3D,eAAe,CAAC8D,WAAY,EAAC,EAAEd,SAAQ,GAAI,IAAI,EAAEC,UAAS,GAAI,IAAI,CAAC;QAEnFZ,GAAG,CAACsB,QAAQ,CAAE,GAAE3B,OAAO,CAAChC,eAAe,CAAC+D,SAAS,CAAE,EAAC,EAAEf,SAAQ,GAAI,IAAI,EAAEC,UAAS,GAAI,IAAI,CAAC;QAC1FZ,GAAG,CAACsB,QAAQ,CAAE,GAAExB,QAAQ,CAACnC,eAAe,CAAC+D,SAAS,CAAE,EAAC,EAAEf,SAAQ,GAAI,IAAI,EAAEC,UAAS,GAAI,IAAI,CAAC;QAC3FZ,GAAG,CAACsB,QAAQ,CAAE,GAAEvB,MAAM,CAACpC,eAAe,CAAC+D,SAAS,CAAE,EAAC,EAAEf,SAAQ,GAAI,IAAI,EAAEC,UAAS,GAAI,IAAI,CAAC;QAGzFlD,YAAY,CAACQ,KAAI,GAAI,IAAG;QACxBT,SAAS,CAACS,KAAI,GAAI,KAAI;MAExB,EAAE,OAAOyD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;QAC9BlE,SAAS,CAACS,KAAI,GAAI,KAAI;MACxB;IACF,CAAC;;IAED;IACArB,SAAS,CAAC,MAAM;MACdD,QAAQ,CAAC,MAAM;QACboB,kBAAkB,EAAE,EAAG;QACvBQ,aAAa,EAAE,EAAG;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACAzB,KAAK,CAACc,eAAe,EAAE,MAAM;MAC3BG,kBAAkB,EAAE,EAAG;MACvBQ,aAAa,EAAE,EAAG;IACpB,CAAC,CAAC;;IAEF;IACA,MAAMqD,mBAAkB,GAAIA,CAAA,KAAM;MAChC,MAAMpD,MAAK,GAAIb,iBAAiB,CAACM,KAAK;MACtC,MAAM4D,QAAO,GAAIrD,MAAM,CAACsD,SAAS,CAAC,WAAW,CAAC,EAAE;MAChD,MAAMC,IAAG,GAAIC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAG,GAAIL,QAAQ;MACpBE,IAAI,CAAC1E,QAAO,GAAI,iBAAiB,EAAE;MACnC0E,IAAI,CAACI,KAAK,EAAE;IACd,CAAC;IAED,OAAO;MACL3E,SAAS;MACTC,YAAY;MACZG,eAAe;MACfD,iBAAiB;MACjBiE,mBAAmB;MACnBlE;IACF,CAAC;EACH;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}