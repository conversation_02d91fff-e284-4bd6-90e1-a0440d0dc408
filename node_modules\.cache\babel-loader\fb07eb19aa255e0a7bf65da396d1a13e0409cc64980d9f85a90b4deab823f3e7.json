{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-6f6e4ca0\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"app-container\"\n};\nconst _hoisted_2 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", {\n  class: \"upload-image-tips\"\n}, \"图片建议：尺寸 1920 x 1200 像素，大小7M以下\", -1 /* HOISTED */));\nconst _hoisted_3 = {\n  style: {\n    \"margin\": \"50px auto\",\n    \"text-align\": \"center\"\n  }\n};\nconst _hoisted_4 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_upload = _resolveComponent(\"upload\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\"    <el-table-column label=\\\"背景图\\\" prop=\\\"desgin\\\">\"), _createCommentVNode(\"      <template #default=\\\"scope\\\">\"), _createCommentVNode(\"        <img :src=\\\"scope.row.design\\\" />\"), _createCommentVNode(\"      </template>\"), _createCommentVNode(\"    </el-table-column>\"), _createCommentVNode(\"    <el-table-column label=\\\"证书名称\\\" prop=\\\"name\\\"></el-table-column>\"), _createCommentVNode(\"    <el-table-column label=\\\"证书描述\\\" prop=\\\"description\\\"></el-table-column>\"), _createCommentVNode(\"    <el-table-column label=\\\"颁发机构\\\" prop=\\\"awardingOrganization\\\"></el-table-column>\"), _createCommentVNode(\"    <el-table-column label=\\\"颁发人员\\\" prop=\\\"awarderName\\\"></el-table-column>\"), _createCommentVNode(\"    <el-table-column label=\\\"颁发条件\\\" prop=\\\"awardConditions\\\"></el-table-column>\"), _createCommentVNode(\"    <el-table-column label=\\\"到期策略\\\" prop=\\\"validityPolicy\\\"></el-table-column>\"), _createCommentVNode(\"    <el-table-column label=\\\"状态\\\" prop=\\\"statusName\\\"></el-table-column>\"), _createElementVNode(\"div\", null, [_createVNode(_component_el_form, {\n    model: _ctx.certificateTemplate,\n    rules: _ctx.certificateTemplateRules,\n    ref: \"certificateTemplateRef\",\n    \"label-width\": \"120px\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      label: \"证书名称：\",\n      prop: \"name\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"small\",\n        modelValue: _ctx.certificateTemplate.name,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => _ctx.certificateTemplate.name = $event),\n        placeholder: \"请输入证书名称\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"证书描述：\",\n      prop: \"name\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"small\",\n        modelValue: _ctx.certificateTemplate.description,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => _ctx.certificateTemplate.description = $event),\n        placeholder: \"请输入证书描述\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"颁发机构：\",\n      prop: \"phrase\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"small\",\n        modelValue: _ctx.certificateTemplate.awardingOrganization,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => _ctx.certificateTemplate.awardingOrganization = $event),\n        placeholder: \"请输入颁发机构\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"颁发人员：\",\n      prop: \"price\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"small\",\n        modelValue: _ctx.certificateTemplate.awarderName,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => _ctx.certificateTemplate.awarderName = $event),\n        placeholder: \"请输入颁发人员\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"颁发条件：\",\n      prop: \"introduction\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"small\",\n        modelValue: _ctx.certificateTemplate.awardConditions,\n        \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => _ctx.certificateTemplate.awardConditions = $event),\n        placeholder: \"请输入颁发条件\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"到期策略：\",\n      prop: \"introduction\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"small\",\n        modelValue: _ctx.certificateTemplate.validityPolicy,\n        \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => _ctx.certificateTemplate.validityPolicy = $event),\n        placeholder: \"请输入到期策略\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"启用状态：\",\n      prop: \"introduction\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_select, {\n        modelValue: _ctx.certificateTemplate.status,\n        \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => _ctx.certificateTemplate.status = $event),\n        class: \"m-2\",\n        placeholder: \"Select\",\n        size: \"small\",\n        style: {\n          \"width\": \"240px\"\n        }\n      }, {\n        default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.statusOptions, item => {\n          return _openBlock(), _createBlock(_component_el_option, {\n            key: item.value,\n            label: item.label,\n            value: item.value\n          }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n        }), 128 /* KEYED_FRAGMENT */))]),\n\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"证书背景图：\",\n      prop: \"name\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_upload, {\n        class: _normalizeClass({\n          'no-plus': _ctx.certificateTemplate.desgin\n        }),\n        \"on-upload-success\": _ctx.onUploadImageSuccess,\n        \"on-upload-remove\": _ctx.onUploadImageRemove,\n        files: _ctx.uploadData.files,\n        \"upload-url\": _ctx.uploadData.url,\n        limit: 1,\n        accept: \"image/jpeg,image/gif,image/png\"\n      }, null, 8 /* PROPS */, [\"class\", \"on-upload-success\", \"on-upload-remove\", \"files\", \"upload-url\"]), _hoisted_2]),\n      _: 1 /* STABLE */\n    }), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n      size: \"small\",\n      onClick: _ctx.preview\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"预览\")]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      size: \"small\",\n      onClick: _ctx.submitBaseInfo\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"提交\")]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]), _createVNode(_component_el_dialog, {\n    title: \"编辑章节\",\n    modelValue: _ctx.showChapterDialog,\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => _ctx.showChapterDialog = $event),\n    \"before-close\": _ctx.hideChapter\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_button, {\n      size: \"small\",\n      onClick: _ctx.hideChapter\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"取 消\")]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      size: \"small\",\n      type: \"primary\",\n      onClick: _ctx.submitChapter\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"确 定\")]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"before-close\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementVNode", "style", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createVNode", "_component_el_form", "model", "_ctx", "certificateTemplate", "rules", "certificateTemplateRules", "ref", "_component_el_form_item", "label", "prop", "_component_el_input", "size", "name", "$event", "placeholder", "description", "awardingOrganization", "awarder<PERSON><PERSON>", "awardConditions", "validityPolicy", "_component_el_select", "status", "_Fragment", "_renderList", "statusOptions", "item", "_createBlock", "_component_el_option", "key", "value", "_component_upload", "_normalizeClass", "<PERSON><PERSON>", "onUploadImageSuccess", "onUploadImageRemove", "files", "uploadData", "url", "limit", "accept", "_hoisted_2", "_hoisted_3", "_component_el_button", "onClick", "preview", "submitBaseInfo", "_component_el_dialog", "title", "showChapterDialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "footer", "_withCtx", "_hoisted_4", "type", "submitChapter"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/certificate/template/edit/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n<!--    <el-table-column label=\"背景图\" prop=\"desgin\">-->\n<!--      <template #default=\"scope\">-->\n<!--        <img :src=\"scope.row.design\" />-->\n<!--      </template>-->\n<!--    </el-table-column>-->\n<!--    <el-table-column label=\"证书名称\" prop=\"name\"></el-table-column>-->\n<!--    <el-table-column label=\"证书描述\" prop=\"description\"></el-table-column>-->\n<!--    <el-table-column label=\"颁发机构\" prop=\"awardingOrganization\"></el-table-column>-->\n<!--    <el-table-column label=\"颁发人员\" prop=\"awarderName\"></el-table-column>-->\n<!--    <el-table-column label=\"颁发条件\" prop=\"awardConditions\"></el-table-column>-->\n<!--    <el-table-column label=\"到期策略\" prop=\"validityPolicy\"></el-table-column>-->\n<!--    <el-table-column label=\"状态\" prop=\"statusName\"></el-table-column>-->\n    <div>\n      <el-form :model=\"certificateTemplate\" :rules=\"certificateTemplateRules\" ref=\"certificateTemplateRef\" label-width=\"120px\">\n        <el-form-item label=\"证书名称：\" prop=\"name\">\n          <el-input size=\"small\" v-model=\"certificateTemplate.name\" placeholder=\"请输入证书名称\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"证书描述：\" prop=\"name\">\n          <el-input size=\"small\" v-model=\"certificateTemplate.description\" placeholder=\"请输入证书描述\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"颁发机构：\" prop=\"phrase\">\n          <el-input size=\"small\" v-model=\"certificateTemplate.awardingOrganization\" placeholder=\"请输入颁发机构\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"颁发人员：\" prop=\"price\">\n          <el-input size=\"small\" v-model=\"certificateTemplate.awarderName\" placeholder=\"请输入颁发人员\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"颁发条件：\" prop=\"introduction\">\n          <el-input size=\"small\" v-model=\"certificateTemplate.awardConditions\" placeholder=\"请输入颁发条件\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"到期策略：\" prop=\"introduction\">\n          <el-input size=\"small\" v-model=\"certificateTemplate.validityPolicy\" placeholder=\"请输入到期策略\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"启用状态：\" prop=\"introduction\">\n          <el-select v-model=\"certificateTemplate.status\" class=\"m-2\" placeholder=\"Select\" size=\"small\" style=\"width: 240px\">\n            <el-option v-for=\"item in statusOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\"/>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"证书背景图：\" prop=\"name\">\n          <upload\n              :class=\"{'no-plus': certificateTemplate.desgin}\"\n              :on-upload-success=\"onUploadImageSuccess\"\n              :on-upload-remove=\"onUploadImageRemove\"\n              :files=\"uploadData.files\"\n              :upload-url=\"uploadData.url\"\n              :limit=\"1\"\n              accept=\"image/jpeg,image/gif,image/png\">\n          </upload>\n          <span class=\"upload-image-tips\">图片建议：尺寸 1920 x 1200 像素，大小7M以下</span>\n        </el-form-item>\n        <div style=\"margin:50px auto;text-align: center;\">\n          <el-button size=\"small\" @click=\"preview\">预览</el-button>\n          <el-button size=\"small\" @click=\"submitBaseInfo\">提交</el-button>\n        </div>\n      </el-form>\n    </div>\n    <el-dialog title=\"编辑章节\" v-model=\"showChapterDialog\" :before-close=\"hideChapter\">\n<!--      <el-form :model=\"certificateTemplateChapter\" :rules=\"certificateTemplateChapterRules\" ref=\"certificateTemplateChapterRef\">-->\n<!--        <el-form-item label=\"标题：\" label-width=\"120px\" prop=\"title\">-->\n<!--          <el-input size=\"small\" v-model=\"certificateTemplateChapter.title\" placeholder=\"请输入标题\" autocomplete=\"off\"></el-input>-->\n<!--        </el-form-item>-->\n<!--        <el-form-item label=\"简介：\" label-width=\"120px\" prop=\"phrase\">-->\n<!--          <el-input size=\"small\" v-model=\"certificateTemplateChapter.phrase\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入简介\"></el-input>-->\n<!--        </el-form-item>-->\n<!--      </el-form>-->\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button size=\"small\" @click=\"hideChapter\">取 消</el-button>\n          <el-button size=\"small\" type=\"primary\" @click=\"submitChapter\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n<script>\n// import router from \"@/router\"\nimport Upload from \"@/components/Uplaod\"\nimport {ref} from \"vue\"\nimport {useRoute} from \"vue-router\"\n// import {success, confirm, error} from \"@/util/tipsUtils\"\n// import {findCategoryList, toTree, getAllParent} from \"@/api/learn/category\"\n// import {saveBaseInfo, updateBaseInfo, getBaseInfo, publishLesson, unPublishLesson,\n//     saveLessonChapter, updateLessonChapter, deleteLessonChapter, getLessonChapterList, updateSortOrder,\n//     saveLessonChapterSection, updateLessonChapterSection, deleteLessonChapterSection, saveHomework, updateHomework, getHomework} from \"@/api/learn/lesson\"\n\n  export default {\n  name: \"LearnLessonEdit\",\n    components:{\n      Upload\n    },\n    setup() {\n      const statusOptions = [\n        {\n          value: 'active',\n          label: '启用',\n        },\n        {\n          value: 'inactive',\n          label: '禁用',\n        }\n      ];\n\n      // const loadWangEditorFlag = ref(false)\n\n      const route = useRoute()\n      let isUpdate = !!route.query.id\n\n\n      let showStep = ref(\"\")\n      const steps = [\n        {key: \"base\", name: \"课程信息\"},\n        {key: \"content\", name: \"内容章节\"},\n        {key: \"homework\", name: \"课后作业\"},\n        {key: \"publish\", name: \"发布状态\"},\n      ]\n      const stepActive = ref(0)\n      const loadStepActiveArray = () => {\n        const stepActiveArray = [];\n        for (let i = 0; i < steps.length; i++) {\n          const step = steps[i];\n          stepActiveArray.push(step.key);\n          if (step.key === showStep.value) {\n            stepActive.value = i;\n            break;\n          }\n        }\n        if (isUpdate) {\n          stepActive.value = steps.length;\n        }\n        return stepActiveArray;\n      }\n      // 基本信息\n      const uploadData = ref({\n        url: process.env.VUE_APP_BASE_API + \"/oss/learn/lesson/image\",\n        files: []\n      })\n      const certificateTemplate = ref({\n        id: \"\",\n        name: \"\",\n        startTime: \"\",\n        endTime: \"\",\n        price: 0,\n        originalPrice: 0,\n        image: \"\",\n        cidList: [],\n        phrase: \"\",\n        introduction: \"\"\n      })\n          <!--    <el-table-column label=\"证书名称\" prop=\"name\"></el-table-column>-->\n          <!--    <el-table-column label=\"证书描述\" prop=\"description\"></el-table-column>-->\n          <!--    <el-table-column label=\"颁发机构\" prop=\"awardingOrganization\"></el-table-column>-->\n          <!--    <el-table-column label=\"颁发人员\" prop=\"awarderName\"></el-table-column>-->\n          <!--    <el-table-column label=\"颁发条件\" prop=\"awardConditions\"></el-table-column>-->\n          <!--    <el-table-column label=\"到期策略\" prop=\"validityPolicy\"></el-table-column>-->\n          <!--    <el-table-column label=\"状态\" prop=\"statusName\"></el-table-column>-->\n\n      const certificateTemplateRules = {\n        name: [{ required: true, message: \"请输入证书名称\", trigger: \"blur\" }],\n        description: [{ required: true, message: \"请输入证书描述\", trigger: \"blur\" }],\n        awardingOrganization: [{ required: true, message: \"请输入颁发机构\", trigger: \"blur\" }],\n        awarderName: [{ required: true, message: \"请输入颁发人员\", trigger: \"blur\" }],\n        awardConditions: [{ required: true, message: \"请输入颁发条件\", trigger: \"blur\" }],\n        validityPolicy: [{ required: true, message: \"请输入到期策略\", trigger: \"blur\" }],\n        design: [{ required: true, message: \"请选择海报\", trigger: \"change\" }],\n\n        startTime: [{ required: true, message: \"请选择时间\", trigger: \"change\" }],\n        endTime: [{ required: true, message: \"请选择时间\", trigger: \"change\" }],\n        cidList: [{ required: true, message: \"请选择分类\", trigger: \"change\" }],\n      }\n      // // 加载基本信息\n      // const loadBaseInfo = () => {\n      //   let id = route.query.id;\n      //   if (!id) {\n      //     loadWangEditorFlag.value = true;\n      //     return;\n      //   }\n      //   getBaseInfo(id, function (res) {\n      //     lesson.value = res;\n      //     selectCidList.value = getAllParent(categoryOptions.value, res.cidList);\n      //     lesson.value.cidList = []\n      //     uploadData.value.files = [\n      //       {\n      //         name: \"海报\",\n      //         url: lesson.value.image\n      //       }\n      //     ]\n      //     for (const valElement of selectCidList.value) {\n      //       lesson.value.cidList.push(valElement[valElement.length - 1])\n      //     }\n      //     loadWangEditorFlag.value = true;\n      //   })\n      // }\n      // // 获取分类\n      // const loadCategory = () => {\n      //   findCategoryList(0, true, (res) => {\n      //     if (res && res.length) {\n      //       categoryOptions.value = toTree(res);\n      //       loadBaseInfo();\n      //     }\n      //   })\n      // }\n      // // 选择分类\n      // const changeCategory = (val) => {\n      //   lesson.value.cidList = []\n      //   for (const valElement of val) {\n      //     lesson.value.cidList.push(valElement[valElement.length - 1])\n      //   }\n      // }\n      // // 选择时间\n      // const changeStartTime = (val) => {\n      //   lesson.value.startTime = val\n      // }\n      // // 选择时间\n      // const changeEndTime = (val) => {\n      //   lesson.value.endTime = val\n      // }\n      // // 上传图片成功\n      // const onUploadImageSuccess = (res) => {\n      //   lesson.value.image = res.data\n      // }\n      // // 删除图片\n      // const onUploadImageRemove = () => {\n      //   lesson.value.image = \"\"\n      //   uploadData.value.files = []\n      // }\n      // // 提交基本信息\n      // const lessonRef = ref(null)\n      // const submitBaseInfo = () => {\n      //   lessonRef.value.validate((valid) => {\n      //     if (!valid) { return false }\n      //     if (isUpdate) {\n      //       if(typeof lesson.value.startTime == \"string\") {\n      //         lesson.value.startTime = new Date(lesson.value.startTime);\n      //       }\n      //       if(typeof lesson.value.endTime == \"string\") {\n      //         lesson.value.endTime = new Date(lesson.value.endTime);\n      //       }\n      //       updateBaseInfo(lesson.value, function (res) {\n      //         if (res && res.id) {\n      //           lesson.value = res;\n      //           success(\"编辑成功\")\n      //           showStep.value = \"content\";\n      //           loadStepActiveArray()\n      //           let path = route.fullPath;\n      //           router.push({path, query: {id: lesson.value.id, step: \"content\"} });\n      //         }\n      //       })\n      //     } else {\n      //       saveBaseInfo(lesson.value, function (res) {\n      //         if (res && res.id) {\n      //           lesson.value = res;\n      //           success(\"新增成功\")\n      //           showStep.value = \"content\";\n      //           loadStepActiveArray()\n      //           let path = route.fullPath;\n      //           router.push({path, query: {id: lesson.value.id, step: \"content\"} });\n      //         }\n      //       })\n      //     }\n      //   })\n      // }\n\n\n      // 步骤条\n      const init = () => {\n        // // 初始化加载\n        // if (route.query.step) {\n        //   showStep.value = route.query.step;\n        // } else {\n        //   showStep.value = \"base\"\n        // }\n        // lesson.value.id = route.query.id || \"\"\n        // loadCategory();\n        // loadContent();\n      }\n      init()\n\n      // 返回参数与方法\n      return {\n        statusOptions,\n        certificateTemplate,\n        certificateTemplateRules,\n        // 基本信息\n        uploadData,\n        // lessonRef,\n        // changeCategory,\n        // changeStartTime,\n        // changeEndTime,\n        // onUploadImageSuccess,\n        // onUploadImageRemove,\n        // submitBaseInfo,\n        // 步骤条\n        steps,\n        stepActive,\n        showStep,\n      };\n    }\n  }\n</script>\n<style scoped lang=\"scss\">\n  .app-container {\n    margin: 20px;\n    .base {\n      .upload-image-tips {\n        font-size: 12px;\n        color: #999999;\n      }\n      ::v-deep .el-upload--picture-card,\n      ::v-deep .el-upload-list--picture-card .el-upload-list__item {\n        //width: 100%;\n        height: 62.5%;\n        border: none;\n        display: flex;\n        margin: 0;\n        min-height: 146px;\n        justify-content: center;\n        flex-direction: column;\n        max-height: 400px;\n        background-color: #ffffff;\n      }\n      .no-plus {\n        ::v-deep .el-upload--picture-card {\n          min-height: inherit;\n          justify-content: inherit;\n          flex-direction: inherit;\n          display: none;\n        }\n        img {\n          max-height: 460px;\n        }\n      }\n      .input-number {\n        margin-right: 20px;\n      }\n    }\n    .content {\n      position: relative;\n      min-height: 500px;\n      .content-header {\n        text-align: right;\n        ::v-deep .el-button {\n          border-color: #f3f5f8;\n        }\n      }\n      .tips {\n        font-size: 12px;\n        color: #999999;\n        padding: 15px 20px;\n      }\n    }\n    .publish {\n      .publish-box {\n        margin: 50px auto;\n        text-align: center;\n        .current-status {\n          margin: 0 auto 20px;\n          width: 180px;\n        }\n        .btn-list{\n          margin: 0 auto;\n          width: 180px;\n          text-align: center;\n        }\n      }\n    }\n  }\n  ::v-deep .el-input__inner, ::v-deep .el-input-number {\n    height: 34px;\n    line-height: 34px;\n    font-size: 12px;\n    border-color: #f3f5f8;\n    //border: none;\n    &:focus, &:hover {\n      border-color: #f3f5f8;\n    }\n    .el-input-number__decrease, .el-input-number__increase {\n      background: #FFFFFF;\n      line-height: 32px;\n      border: none;\n      &:focus, &:hover {\n        border-color: #f3f5f8;\n      }\n    }\n  }\n  ::v-deep .el-textarea__inner {\n    border-color: #f3f5f8;\n    &:focus, &:hover {\n      border-color: #f3f5f8;\n    }\n  }\n  ::v-deep .el-cascader .el-input .el-input__inner:focus {\n    border-color: #f3f5f8;\n  }\n  ::v-deep .el-input__icon {\n    line-height: 34px;\n    cursor: pointer;\n    &:hover {\n      color: $--color-primary;\n    }\n  }\n  ::v-deep .el-form-item__label {\n    font-size: 12px;\n  }\n  ::v-deep .el-table th,\n  ::v-deep .el-table td {\n    padding: 5px 0;\n    font-size: 12px;\n    color: #000000;\n  }\n  ::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td {\n    background-color: #FFFFFF;\n  }\n  ::v-deep .el-table__body tr.current-row > td {\n    background-color: #FFFFFF;\n  }\n  ::v-deep .el-button--text {\n    color: #999999;\n    font-size: 12px;\n    &:hover {\n      color: $--color-primary;\n    }\n  }\n  ::v-deep .el-cascader:not(.is-disabled):hover .el-input__inner {\n    cursor: pointer;\n    border-color: #f3f5f8;\n  }\n  .box-card {\n    padding: 0 30px 10px;\n    .el-card {\n      box-shadow: none;\n    }\n    ::v-deep .el-card__header {\n      padding: 5px 20px;\n      font-size: 12px;\n      border: 0;\n    }\n    ::v-deep .el-card__body {\n      padding: 0;\n      .table-wrapper {\n        display: none;\n        .video-box {\n          padding: 0 20px 15px;\n          display: flex;\n          justify-content: center;\n          video {\n            background: #000;\n            width: 320px;\n            height: 240px;\n          }\n        }\n      }\n      .show {\n        display: block;\n      }\n    }\n  }\n  .opt-btn {\n    float: right;\n    ::v-deep .el-button {\n      margin: 0;\n      padding: 5px;\n    }\n  }\n  .affix {\n    min-height: 720px;\n    .step-list {\n      padding: 10px 20px;\n      .title {\n        padding: 0 0 20px 0;\n        font-size: 12px;\n      }\n      .steps {\n        height: 120px;\n        padding-left: 10px;\n        ::v-deep .el-step__title {\n          font-size: 14px;\n        }\n        ::v-deep .el-step__icon {\n          width: 20px;\n          height: 20px;\n        }\n        ::v-deep .el-step.is-vertical .el-step__head {\n          width: 20px;\n        }\n        ::v-deep .el-step.is-vertical .el-step__title{\n          cursor:pointer;\n        }\n        ::v-deep .el-step.is-vertical .el-step__line {\n          width: 1px;\n          left: 10px;\n          top: 2px;\n        }\n        ::v-deep .el-step__icon.is-text {\n          border-width: 1px;\n          cursor:pointer;\n        }\n        ::v-deep .step-active .el-step__head.is-finish {\n          color: red;\n        }\n      }\n    }\n    .draggable {\n      padding: 10px 0 10px 10px;\n      .title {\n        padding: 10px 0 10px;\n        font-size: 12px;\n      }\n      .item-list {\n        padding: 0 0 0 10px;\n        .item {\n          font-size: 12px;\n          line-height: 20px;\n          padding: 5px 0;\n          .sub-item-list {\n            background: #ffffff;\n            padding: 0 10px;\n            border-radius: 4px;\n            margin-top: 5px;\n            .sub-item {\n              line-height: 20px;\n              padding: 5px 0;\n              color: #666666;\n              &:first-child {\n                padding-top: 10px;\n              }\n              &:last-child {\n                padding-bottom: 10px;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  ::v-deep .el-upload--text {\n    font-size: 12px;\n  }\n  ::v-deep .el-affix--fixed {\n    z-index: 98!important;\n  }\n  ::v-deep .el-table__empty-block {\n    line-height: 400px;\n    .el-table__empty-text {\n      line-height: 400px;\n    }\n  }\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAe;gEAgDlBC,mBAAA,CAAoE;EAA9DD,KAAK,EAAC;AAAmB,GAAC,+BAA6B;;EAE1DE,KAA4C,EAA5C;IAAA;IAAA;EAAA;AAA4C;;EAgB5CF,KAAK,EAAC;AAAe;;;;;;;;;;uBAlEhCG,mBAAA,CAwEM,OAxENC,UAwEM,GAvERC,mBAAA,uDAAsD,EACtDA,mBAAA,uCAAwC,EACxCA,mBAAA,6CAA8C,EAC9CA,mBAAA,qBAAwB,EACxBA,mBAAA,0BAA6B,EAC7BA,mBAAA,wEAAuE,EACvEA,mBAAA,+EAA8E,EAC9EA,mBAAA,wFAAuF,EACvFA,mBAAA,+EAA8E,EAC9EA,mBAAA,mFAAkF,EAClFA,mBAAA,kFAAiF,EACjFA,mBAAA,4EAA2E,EACvEJ,mBAAA,CA0CM,cAzCJK,YAAA,CAwCUC,kBAAA;IAxCAC,KAAK,EAAEC,IAAA,CAAAC,mBAAmB;IAAGC,KAAK,EAAEF,IAAA,CAAAG,wBAAwB;IAAEC,GAAG,EAAC,wBAAwB;IAAC,aAAW,EAAC;;sBAC/G,MAEe,CAFfP,YAAA,CAEeQ,uBAAA;MAFDC,KAAK,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAC/B,MAA2F,CAA3FV,YAAA,CAA2FW,mBAAA;QAAjFC,IAAI,EAAC,OAAO;oBAAUT,IAAA,CAAAC,mBAAmB,CAACS,IAAI;mEAAxBV,IAAA,CAAAC,mBAAmB,CAACS,IAAI,GAAAC,MAAA;QAAEC,WAAW,EAAC;;;QAExEf,YAAA,CAEeQ,uBAAA;MAFDC,KAAK,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAC/B,MAAkG,CAAlGV,YAAA,CAAkGW,mBAAA;QAAxFC,IAAI,EAAC,OAAO;oBAAUT,IAAA,CAAAC,mBAAmB,CAACY,WAAW;mEAA/Bb,IAAA,CAAAC,mBAAmB,CAACY,WAAW,GAAAF,MAAA;QAAEC,WAAW,EAAC;;;QAE/Ef,YAAA,CAEeQ,uBAAA;MAFDC,KAAK,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAC/B,MAA2G,CAA3GV,YAAA,CAA2GW,mBAAA;QAAjGC,IAAI,EAAC,OAAO;oBAAUT,IAAA,CAAAC,mBAAmB,CAACa,oBAAoB;mEAAxCd,IAAA,CAAAC,mBAAmB,CAACa,oBAAoB,GAAAH,MAAA;QAAEC,WAAW,EAAC;;;QAExFf,YAAA,CAEeQ,uBAAA;MAFDC,KAAK,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAC/B,MAAkG,CAAlGV,YAAA,CAAkGW,mBAAA;QAAxFC,IAAI,EAAC,OAAO;oBAAUT,IAAA,CAAAC,mBAAmB,CAACc,WAAW;mEAA/Bf,IAAA,CAAAC,mBAAmB,CAACc,WAAW,GAAAJ,MAAA;QAAEC,WAAW,EAAC;;;QAE/Ef,YAAA,CAEeQ,uBAAA;MAFDC,KAAK,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAC/B,MAAsG,CAAtGV,YAAA,CAAsGW,mBAAA;QAA5FC,IAAI,EAAC,OAAO;oBAAUT,IAAA,CAAAC,mBAAmB,CAACe,eAAe;mEAAnChB,IAAA,CAAAC,mBAAmB,CAACe,eAAe,GAAAL,MAAA;QAAEC,WAAW,EAAC;;;QAEnFf,YAAA,CAEeQ,uBAAA;MAFDC,KAAK,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAC/B,MAAqG,CAArGV,YAAA,CAAqGW,mBAAA;QAA3FC,IAAI,EAAC,OAAO;oBAAUT,IAAA,CAAAC,mBAAmB,CAACgB,cAAc;mEAAlCjB,IAAA,CAAAC,mBAAmB,CAACgB,cAAc,GAAAN,MAAA;QAAEC,WAAW,EAAC;;;QAElFf,YAAA,CAIeQ,uBAAA;MAJDC,KAAK,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAC/B,MAEY,CAFZV,YAAA,CAEYqB,oBAAA;oBAFQlB,IAAA,CAAAC,mBAAmB,CAACkB,MAAM;mEAA1BnB,IAAA,CAAAC,mBAAmB,CAACkB,MAAM,GAAAR,MAAA;QAAEpB,KAAK,EAAC,KAAK;QAACqB,WAAW,EAAC,QAAQ;QAACH,IAAI,EAAC,OAAO;QAAChB,KAAoB,EAApB;UAAA;QAAA;;0BACjF,MAA6B,E,kBAAxCC,mBAAA,CAAoG0B,SAAA,QAAAC,WAAA,CAA1ErB,IAAA,CAAAsB,aAAa,EAArBC,IAAI;+BAAtBC,YAAA,CAAoGC,oBAAA;YAA1DC,GAAG,EAAEH,IAAI,CAACI,KAAK;YAAGrB,KAAK,EAAEiB,IAAI,CAACjB,KAAK;YAAGqB,KAAK,EAAEJ,IAAI,CAACI;;;;;;;QAGhG9B,YAAA,CAWeQ,uBAAA;MAXDC,KAAK,EAAC,QAAQ;MAACC,IAAI,EAAC;;wBAChC,MAQS,CARTV,YAAA,CAQS+B,iBAAA;QAPJrC,KAAK,EAAAsC,eAAA;UAAA,WAAc7B,IAAA,CAAAC,mBAAmB,CAAC6B;QAAM;QAC7C,mBAAiB,EAAE9B,IAAA,CAAA+B,oBAAoB;QACvC,kBAAgB,EAAE/B,IAAA,CAAAgC,mBAAmB;QACrCC,KAAK,EAAEjC,IAAA,CAAAkC,UAAU,CAACD,KAAK;QACvB,YAAU,EAAEjC,IAAA,CAAAkC,UAAU,CAACC,GAAG;QAC1BC,KAAK,EAAE,CAAC;QACTC,MAAM,EAAC;0GAEXC,UAAoE,C;;QAEtE9C,mBAAA,CAGM,OAHN+C,UAGM,GAFJ1C,YAAA,CAAuD2C,oBAAA;MAA5C/B,IAAI,EAAC,OAAO;MAAEgC,OAAK,EAAEzC,IAAA,CAAA0C;;wBAAS,MAAE,C,iBAAF,IAAE,E;;oCAC3C7C,YAAA,CAA8D2C,oBAAA;MAAnD/B,IAAI,EAAC,OAAO;MAAEgC,OAAK,EAAEzC,IAAA,CAAA2C;;wBAAgB,MAAE,C,iBAAF,IAAE,E;;;;2CAIxD9C,YAAA,CAeY+C,oBAAA;IAfDC,KAAK,EAAC,MAAM;gBAAU7C,IAAA,CAAA8C,iBAAiB;+DAAjB9C,IAAA,CAAA8C,iBAAiB,GAAAnC,MAAA;IAAG,cAAY,EAAEX,IAAA,CAAA+C;;IAStDC,MAAM,EAAAC,QAAA,CACf,MAGM,CAHNzD,mBAAA,CAGM,OAHN0D,UAGM,GAFJrD,YAAA,CAA4D2C,oBAAA;MAAjD/B,IAAI,EAAC,OAAO;MAAEgC,OAAK,EAAEzC,IAAA,CAAA+C;;wBAAa,MAAG,C,iBAAH,KAAG,E;;oCAChDlD,YAAA,CAA6E2C,oBAAA;MAAlE/B,IAAI,EAAC,OAAO;MAAC0C,IAAI,EAAC,SAAS;MAAEV,OAAK,EAAEzC,IAAA,CAAAoD;;wBAAe,MAAG,C,iBAAH,KAAG,E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}