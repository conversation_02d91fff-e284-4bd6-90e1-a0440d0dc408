{"ast": null, "code": "import { ref } from \"vue\";\nimport { findList, updateChannel, saveChannel } from \"../../../api/point/channel\";\nimport Page from \"../../../components/Page\";\nimport { success } from \"../../../util/tipsUtils\";\nexport default {\n  name: \"PointChannelIndex\",\n  components: {\n    Page\n  },\n  setup() {\n    const statusMap = {\n      \"not_effect\": \"未生效\",\n      \"effect\": \"生效中\",\n      \"expired\": \"已失效\"\n    };\n    const list = ref([]);\n    const total = ref(0);\n    const dataLoading = ref(true);\n    const searchParam = ref({\n      keyword: \"\",\n      size: 20,\n      current: 1\n    });\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true;\n      findList(searchParam.value, res => {\n        dataLoading.value = false;\n        if (!res) {\n          return;\n        }\n        list.value = res.list;\n        total.value = res.total;\n      });\n    };\n    loadList();\n    const currentChange = currentPage => {\n      searchParam.value.current = currentPage;\n      loadList();\n    };\n    const sizeChange = s => {\n      searchParam.value.size = s;\n      loadList();\n    };\n    // 搜索\n    const search = () => {\n      loadList();\n    };\n    const pointChannelRules = {\n      name: [{\n        required: true,\n        message: \"请输入名称\",\n        trigger: \"blur\"\n      }],\n      memberReceiveNum: [{\n        required: true,\n        message: \"请输入会员每次发放积分数\",\n        trigger: \"blur\"\n      }],\n      changeRemind: [{\n        required: true,\n        message: \"请选择积分变动提醒\",\n        trigger: \"blur\"\n      }],\n      dayIssuedNum: [{\n        required: true,\n        message: \"请输入日发放积分数\",\n        trigger: \"blur\"\n      }],\n      dayMemberReceiveNum: [{\n        required: true,\n        message: \"请输入单用户日领取数\",\n        trigger: \"blur\"\n      }],\n      issuedNum: [{\n        required: true,\n        message: \"请输入总发放积分数\",\n        trigger: \"blur\"\n      }],\n      increaseRemindTips: [{\n        required: true,\n        message: \"请输入增加积分提醒\",\n        trigger: \"blur\"\n      }],\n      decreaseRemindTips: [{\n        required: true,\n        message: \"请输入减少积分提醒\",\n        trigger: \"blur\"\n      }]\n    };\n    const pointChannel = ref({});\n    const pointChannelRef = ref(null);\n    const showChannelFormDialog = ref(false);\n    const hideChannelForm = () => {\n      showChannelFormDialog.value = false;\n      pointChannel.value = {};\n    };\n    const add = () => {\n      showChannelFormDialog.value = true;\n    };\n    // 编辑\n    const edit = item => {\n      pointChannel.value = item;\n      showChannelFormDialog.value = true;\n    };\n    //提交\n    const submitChannel = () => {\n      pointChannelRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (pointChannel.value.id) {\n          updateChannel(pointChannel.value, () => {\n            success(\"修改成功\");\n            loadList();\n            hideChannelForm();\n          });\n        } else {\n          saveChannel(pointChannel.value, () => {\n            success(\"新增成功\");\n            loadList();\n            hideChannelForm();\n          });\n        }\n      });\n    };\n    return {\n      list,\n      total,\n      searchParam,\n      search,\n      currentChange,\n      sizeChange,\n      showChannelFormDialog,\n      add,\n      pointChannel,\n      pointChannelRef,\n      edit,\n      hideChannelForm,\n      submitChannel,\n      pointChannelRules,\n      statusMap,\n      dataLoading\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "findList", "updateChannel", "saveChannel", "Page", "success", "name", "components", "setup", "statusMap", "list", "total", "dataLoading", "searchParam", "keyword", "size", "current", "loadList", "value", "res", "currentChange", "currentPage", "sizeChange", "s", "search", "pointChannelRules", "required", "message", "trigger", "memberReceiveNum", "changeRemind", "dayIssuedNum", "dayMemberReceiveNum", "issuedNum", "increaseRemindTips", "decreaseRemindTips", "pointChannel", "pointChannelRef", "showChannelFormDialog", "hideChannelForm", "add", "edit", "item", "submitChannel", "validate", "valid", "id"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/point/channel/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"header\">\n      <el-form :inline=\"true\" :model=\"searchParam\" class=\"demo-form-inline\">\n        <el-form-item label=\"\">\n          <el-input size=\"small\" class=\"search-input\" v-model=\"searchParam.keyword\" placeholder=\"请输入关键字\">\n            <template #append>\n              <el-button size=\"small\" class=\"search-btn\" type=\"primary\" @click=\"search\">搜索</el-button>\n            </template>\n          </el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" type=\"primary\" @click=\"add\">创建积分渠道</el-button>\n        </el-form-item>\n        <el-form-item>\n          <p style=\"font-size: 10px;padding: 6px;line-height: 14px;background: #e2f7fe;border-radius: 5px;border: 1px solid #d5daf7;\">\n            <i class=\"el-icon-warning-outline\"></i>\n            温馨提示：建议针对积分渠道设置阈值，不限制将导致损失风险\n          </p>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"content\">\n      <div class=\"content-list\">\n        <el-table v-loading=\"dataLoading\" :data=\"list\" size=\"small\" style=\"width: 100%;\">\n          <el-table-column prop=\"id\" label=\"ID\" width=\"50\"/>\n          <el-table-column prop=\"name\" label=\"渠道名称\"/>\n          <el-table-column prop=\"status\" label=\"单用户每次发放积分数\">\n            <template #default=\"scope\">\n              {{scope.row.memberReceiveNum || 0}}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"日发放积分数（已发放/阈值）\">\n            <template #default=\"scope\">\n              {{(scope.row.hasBeenDayIssuedNum || 0) + \" / \" + (scope.row.dayIssuedNum || \"不限制\")}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"status\" label=\"单用户日发放积分数\">\n            <template #default=\"scope\">\n              {{scope.row.dayMemberReceiveNum || \"不限制\"}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"redemptionRatio\" label=\"总发放积分数（已发放/阈值）\">\n            <template #default=\"scope\">\n              {{(scope.row.hasBeenIssuedNum || 0) + \" / \" + (scope.row.issuedNum || \"不限制\")}}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"50\">\n            <template #default=\"scope\">\n              <el-button type=\"text\" size=\"small\" @click=\"edit(scope.row)\">编辑</el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n    </div>\n    <page style=\"margin-top: 20px;\" :total=\"total\" :current-change=\"currentChange\" :size-change=\"sizeChange\" :page-size=\"searchParam.size\"></page>\n    <el-dialog title=\"新增/编辑积分\" v-model=\"showChannelFormDialog\" :before-close=\"hideChannelForm\">\n      <el-form :model=\"pointChannel\" :rules=\"pointChannelRules\" ref=\"pointChannelRef\">\n        <el-form-item label=\"名称：\" label-width=\"150px\" prop=\"name\">\n          <el-input size=\"small\" v-model=\"pointChannel.name\" placeholder=\"请输入名称\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"会员每次发放积分数：\" label-width=\"150px\" prop=\"memberReceiveNum\">\n          <el-input size=\"small\" v-model=\"pointChannel.memberReceiveNum\" placeholder=\"请输入大于0的整数\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"日发放积分数：\" label-width=\"150px\" prop=\"dayIssuedNum\">\n          <el-input size=\"small\" v-model=\"pointChannel.dayIssuedNum\" placeholder=\"请输入大于0的整数，等于0则不限制\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"单用户日领取数：\" label-width=\"150px\" prop=\"dayMemberReceiveNum\">\n          <el-input size=\"small\" v-model=\"pointChannel.dayMemberReceiveNum\" placeholder=\"请输入大于0的整数，等于0则不限制\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"总发放积分数：\" label-width=\"150px\" prop=\"issuedNum\">\n          <el-input size=\"small\" v-model=\"pointChannel.issuedNum\" placeholder=\"请输入大于0的整数，等于0则不限制\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"积分变动提醒：\" label-width=\"150px\" prop=\"changeRemind\">\n          <el-switch v-model=\"pointChannel.changeRemind\" active-color=\"#07c160\" inactive-color=\"#cccccc\"></el-switch>\n        </el-form-item>\n        <el-form-item label=\"增加积分提醒：\" label-width=\"150px\" prop=\"increaseRemindTips\">\n          <el-input size=\"small\" v-model=\"pointChannel.increaseRemindTips\" placeholder=\"积分个数用{coin}表示\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"减少积分提醒：\" label-width=\"150px\" prop=\"decreaseRemindTips\">\n          <el-input size=\"small\" v-model=\"pointChannel.decreaseRemindTips\" placeholder=\"积分个数用{coin}表示\"></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button size=\"small\" @click=\"hideChannelForm\">取 消</el-button>\n          <el-button size=\"small\" type=\"primary\" @click=\"submitChannel\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import {ref} from \"vue\"\n  import {findList, updateChannel, saveChannel} from \"../../../api/point/channel\"\n  import Page from \"../../../components/Page\"\n  import {success} from \"../../../util/tipsUtils\";\n\n  export default {\n    name: \"PointChannelIndex\",\n    components: {\n      Page\n    },\n    setup() {\n      const statusMap = {\n        \"not_effect\": \"未生效\",\n        \"effect\": \"生效中\",\n        \"expired\": \"已失效\"\n      }\n      const list = ref([])\n      const total = ref(0)\n      const dataLoading = ref(true)\n      const searchParam = ref({\n        keyword: \"\",\n        size: 20,\n        current: 1\n      })\n      // 加载列表\n      const loadList = () => {\n        dataLoading.value = true\n        findList(searchParam.value, (res) => {\n          dataLoading.value = false\n          if (!res) {return;}\n          list.value = res.list;\n          total.value = res.total;\n        })\n      }\n      loadList();\n      const currentChange = (currentPage) => {\n        searchParam.value.current = currentPage;\n        loadList();\n      }\n      const sizeChange = (s) => {\n        searchParam.value.size = s;\n        loadList();\n      }\n      // 搜索\n      const search = () => {\n        loadList();\n      }\n      const pointChannelRules = {\n        name: [{ required: true, message: \"请输入名称\", trigger: \"blur\" }],\n        memberReceiveNum: [{ required: true, message: \"请输入会员每次发放积分数\", trigger: \"blur\" }],\n        changeRemind: [{ required: true, message: \"请选择积分变动提醒\", trigger: \"blur\" }],\n        dayIssuedNum: [{ required: true, message: \"请输入日发放积分数\", trigger: \"blur\" }],\n        dayMemberReceiveNum: [{ required: true, message: \"请输入单用户日领取数\", trigger: \"blur\" }],\n        issuedNum: [{ required: true, message: \"请输入总发放积分数\", trigger: \"blur\" }],\n        increaseRemindTips: [{ required: true, message: \"请输入增加积分提醒\", trigger: \"blur\" }],\n        decreaseRemindTips: [{ required: true, message: \"请输入减少积分提醒\", trigger: \"blur\" }],\n      }\n      const pointChannel = ref({})\n      const pointChannelRef = ref(null)\n      const showChannelFormDialog = ref(false)\n      const hideChannelForm = () => {\n        showChannelFormDialog.value = false;\n        pointChannel.value = {}\n      }\n      const add = () => {\n        showChannelFormDialog.value = true;\n      }\n      // 编辑\n      const edit = (item) => {\n        pointChannel.value = item\n        showChannelFormDialog.value = true;\n      }\n      //提交\n      const submitChannel = () => {\n        pointChannelRef.value.validate(valid => {\n          if (!valid) {\n            return false;\n          }\n          if (pointChannel.value.id) {\n            updateChannel(pointChannel.value, () => {\n              success(\"修改成功\")\n              loadList()\n              hideChannelForm()\n            });\n          } else {\n            saveChannel(pointChannel.value, () => {\n              success(\"新增成功\")\n              loadList()\n              hideChannelForm()\n            });\n          }\n        })\n      }\n      return {\n        list,\n        total,\n        searchParam,\n        search,\n        currentChange,\n        sizeChange,\n        showChannelFormDialog,\n        add,\n        pointChannel,\n        pointChannelRef,\n        edit,\n        hideChannelForm,\n        submitChannel,\n        pointChannelRules,\n        statusMap,\n        dataLoading,\n      };\n    }\n  };\n</script>\n<style lang=\"scss\">\n  .header {\n    .el-form {\n      .el-form-item {\n        .el-form-item__content {\n          line-height: 28px;\n          .search-btn {\n            &:hover {\n              color: $--color-primary;\n            }\n          }\n        }\n      }\n    }\n  }\n</style>\n<style scoped lang=\"scss\">\n  .app-container {\n    margin: 20px;\n    .content-list {\n      margin: 0;\n      padding: 0;\n      border: 0;\n      font: inherit;\n      vertical-align: baseline;\n    }\n    .search-input {\n      width: 242px;\n    }\n  }\n</style>\n"], "mappings": "AA8FE,SAAQA,GAAG,QAAO,KAAI;AACtB,SAAQC,QAAQ,EAAEC,aAAa,EAAEC,WAAW,QAAO,4BAA2B;AAC9E,OAAOC,IAAG,MAAO,0BAAyB;AAC1C,SAAQC,OAAO,QAAO,yBAAyB;AAE/C,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,UAAU,EAAE;IACVH;EACF,CAAC;EACDI,KAAKA,CAAA,EAAG;IACN,MAAMC,SAAQ,GAAI;MAChB,YAAY,EAAE,KAAK;MACnB,QAAQ,EAAE,KAAK;MACf,SAAS,EAAE;IACb;IACA,MAAMC,IAAG,GAAIV,GAAG,CAAC,EAAE;IACnB,MAAMW,KAAI,GAAIX,GAAG,CAAC,CAAC;IACnB,MAAMY,WAAU,GAAIZ,GAAG,CAAC,IAAI;IAC5B,MAAMa,WAAU,GAAIb,GAAG,CAAC;MACtBc,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC;IACD;IACA,MAAMC,QAAO,GAAIA,CAAA,KAAM;MACrBL,WAAW,CAACM,KAAI,GAAI,IAAG;MACvBjB,QAAQ,CAACY,WAAW,CAACK,KAAK,EAAGC,GAAG,IAAK;QACnCP,WAAW,CAACM,KAAI,GAAI,KAAI;QACxB,IAAI,CAACC,GAAG,EAAE;UAAC;QAAO;QAClBT,IAAI,CAACQ,KAAI,GAAIC,GAAG,CAACT,IAAI;QACrBC,KAAK,CAACO,KAAI,GAAIC,GAAG,CAACR,KAAK;MACzB,CAAC;IACH;IACAM,QAAQ,EAAE;IACV,MAAMG,aAAY,GAAKC,WAAW,IAAK;MACrCR,WAAW,CAACK,KAAK,CAACF,OAAM,GAAIK,WAAW;MACvCJ,QAAQ,EAAE;IACZ;IACA,MAAMK,UAAS,GAAKC,CAAC,IAAK;MACxBV,WAAW,CAACK,KAAK,CAACH,IAAG,GAAIQ,CAAC;MAC1BN,QAAQ,EAAE;IACZ;IACA;IACA,MAAMO,MAAK,GAAIA,CAAA,KAAM;MACnBP,QAAQ,EAAE;IACZ;IACA,MAAMQ,iBAAgB,GAAI;MACxBnB,IAAI,EAAE,CAAC;QAAEoB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC7DC,gBAAgB,EAAE,CAAC;QAAEH,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,cAAc;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAChFE,YAAY,EAAE,CAAC;QAAEJ,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MACzEG,YAAY,EAAE,CAAC;QAAEL,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MACzEI,mBAAmB,EAAE,CAAC;QAAEN,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MACjFK,SAAS,EAAE,CAAC;QAAEP,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MACtEM,kBAAkB,EAAE,CAAC;QAAER,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC/EO,kBAAkB,EAAE,CAAC;QAAET,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAO,CAAC;IAChF;IACA,MAAMQ,YAAW,GAAIpC,GAAG,CAAC,CAAC,CAAC;IAC3B,MAAMqC,eAAc,GAAIrC,GAAG,CAAC,IAAI;IAChC,MAAMsC,qBAAoB,GAAItC,GAAG,CAAC,KAAK;IACvC,MAAMuC,eAAc,GAAIA,CAAA,KAAM;MAC5BD,qBAAqB,CAACpB,KAAI,GAAI,KAAK;MACnCkB,YAAY,CAAClB,KAAI,GAAI,CAAC;IACxB;IACA,MAAMsB,GAAE,GAAIA,CAAA,KAAM;MAChBF,qBAAqB,CAACpB,KAAI,GAAI,IAAI;IACpC;IACA;IACA,MAAMuB,IAAG,GAAKC,IAAI,IAAK;MACrBN,YAAY,CAAClB,KAAI,GAAIwB,IAAG;MACxBJ,qBAAqB,CAACpB,KAAI,GAAI,IAAI;IACpC;IACA;IACA,MAAMyB,aAAY,GAAIA,CAAA,KAAM;MAC1BN,eAAe,CAACnB,KAAK,CAAC0B,QAAQ,CAACC,KAAI,IAAK;QACtC,IAAI,CAACA,KAAK,EAAE;UACV,OAAO,KAAK;QACd;QACA,IAAIT,YAAY,CAAClB,KAAK,CAAC4B,EAAE,EAAE;UACzB5C,aAAa,CAACkC,YAAY,CAAClB,KAAK,EAAE,MAAM;YACtCb,OAAO,CAAC,MAAM;YACdY,QAAQ,EAAC;YACTsB,eAAe,EAAC;UAClB,CAAC,CAAC;QACJ,OAAO;UACLpC,WAAW,CAACiC,YAAY,CAAClB,KAAK,EAAE,MAAM;YACpCb,OAAO,CAAC,MAAM;YACdY,QAAQ,EAAC;YACTsB,eAAe,EAAC;UAClB,CAAC,CAAC;QACJ;MACF,CAAC;IACH;IACA,OAAO;MACL7B,IAAI;MACJC,KAAK;MACLE,WAAW;MACXW,MAAM;MACNJ,aAAa;MACbE,UAAU;MACVgB,qBAAqB;MACrBE,GAAG;MACHJ,YAAY;MACZC,eAAe;MACfI,IAAI;MACJF,eAAe;MACfI,aAAa;MACblB,iBAAiB;MACjBhB,SAAS;MACTG;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}