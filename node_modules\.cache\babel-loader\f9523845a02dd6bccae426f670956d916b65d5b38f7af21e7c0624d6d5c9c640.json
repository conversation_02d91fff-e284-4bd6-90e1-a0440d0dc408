{"ast": null, "code": "import { reactive, ref } from \"vue\";\nimport { authCodeLogin, getMsgAuthCode, passwordLogin, getWorkWeChatConfig, getDingTalkConfig } from \"@/api/login\";\nimport { error, info, success } from \"@/util/tipsUtils\";\nimport { setToken } from \"@/util/tokenUtils\";\nexport default {\n  name: \"LoginIndex\",\n  emits: [\"success\", \"callback\"],\n  props: {\n    show: Boolean,\n    callback: {\n      type: Function,\n      default: () => {}\n    },\n    success: {\n      type: Function,\n      default: () => {}\n    },\n    showClose: {\n      type: Boolean,\n      default: true,\n      required: false\n    }\n  },\n  setup(props, context) {\n    console.log(\"props:\", {\n      ...props\n    });\n    console.log(\"context.attrs:\", {\n      ...context.attrs\n    });\n    const menuActiveIndex = ref(\"2\");\n    const menuType = ref(\"password\");\n    const showPassword = ref(false);\n    const passwordType = ref(\"password\");\n    const authCodeAppendLabel = \"获取验证码\";\n    const authCodeAppendValue = ref(\"获取验证码\");\n    const loginQrCodeUrl = ref(\"https://img.alicdn.com/imgextra/O1CN01xstIAQ28A7zAahq9C_!!6000000007891-2-xcode.png\");\n    const passwordForm = reactive({\n      username: \"\",\n      password: \"\"\n    });\n    const authCodeForm = reactive({\n      mobile: \"\",\n      authCode: \"\"\n    });\n    // 关闭组件回调\n    const close = () => {\n      context.emit(\"callback\");\n    };\n    // 选择登录方式\n    const oAuth2Type = ref(\"\");\n    const selectMenu = index => {\n      menuActiveIndex.value = index;\n      switch (menuActiveIndex.value) {\n        // 验证码\n        case \"1\":\n          menuType.value = \"authCode\";\n          break;\n        // 密码\n        case \"2\":\n          menuType.value = \"password\";\n          break;\n        // 扫码\n        case \"3\":\n          if (menuType.value === \"qrCode\") {\n            menuType.value = \"authCode\";\n            menuActiveIndex.value = \"1\";\n            oAuth2Type.value = \"\";\n          } else {\n            menuType.value = \"qrCode\";\n            // 获取二维码\n            // getLoginQrCode(res => {\n            //   console.log(res);\n            //   // 二维码地址\n            //   loginQrCodeUrl.value = res.data;\n            // });\n          }\n\n          break;\n      }\n    };\n    const selectOauth2 = type => {\n      oAuth2Type.value = type;\n      menuType.value = \"qrCode\";\n      const schema = window.location.protocol;\n      const host = window.location.host;\n      let redirectUri = schema + \"//\" + host + \"/ding-talk\";\n      switch (type) {\n        case \"ding-talk\":\n          getDingTalkConfig(res => {\n            const url = \"https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=\" + res.appId + \"&response_type=code&scope=snsapi_login&state=STATE\";\n            window.DDLogin({\n              id: \"ding-talk-body\",\n              goto: encodeURIComponent(url + \"&redirect_uri=\" + redirectUri),\n              style: \"border:none;background-color:#FFFFFF;\",\n              width: \"300\",\n              height: \"400\"\n            });\n            const handleMessage = function (event) {\n              const origin = event.origin;\n              console.log(\"origin\", event.origin);\n              //判断是否来自ddLogin扫码事件。\n              if (origin === \"https://login.dingtalk.com\") {\n                const loginTmpCode = event.data;\n                //获取到loginTmpCode后就可以在这里构造跳转链接进行跳转了\n                window.location.href = url + \"&redirect_uri=\" + encodeURIComponent(redirectUri) + \"&loginTmpCode=\" + loginTmpCode;\n              }\n            };\n            if (typeof window.addEventListener != \"undefined\") {\n              window.addEventListener(\"message\", handleMessage, false);\n            } else if (typeof window.attachEvent != \"undefined\") {\n              window.attachEvent(\"onmessage\", handleMessage);\n            }\n          });\n          break;\n        case \"work-we-chat\":\n          getWorkWeChatConfig(res => {\n            window.WwLogin({\n              \"id\": \"work-we-chat-body\",\n              \"appid\": res.appId,\n              \"agentid\": res.agentId,\n              \"redirect_uri\": encodeURIComponent(schema + \"//\" + host + \"/work-we-chat\"),\n              \"state\": res.state,\n              \"href\": \"\"\n            });\n          });\n          break;\n      }\n    };\n    // 显示密码\n    const showPasswordChange = () => {\n      showPassword.value = !showPassword.value;\n      if (showPassword.value) {\n        passwordType.value = \"text\";\n      } else {\n        passwordType.value = \"password\";\n      }\n    };\n    // 获取验证码\n    const getAuthCode = value => {\n      if (value !== authCodeAppendLabel) {\n        return;\n      }\n      if (!authCodeForm.mobile) {\n        error(\"请输入手机号码\");\n        return;\n      }\n      // 获取验证码\n      getMsgAuthCode(authCodeForm.mobile, () => {\n        let times = 60;\n        const timer = setInterval(() => {\n          times--;\n          if (times === 0) {\n            authCodeAppendValue.value = authCodeAppendLabel;\n            clearInterval(timer);\n          } else {\n            authCodeAppendValue.value = times + \"\";\n          }\n        }, 1000);\n        info(\"暂不发送短信，统一验证码为：123456\");\n      });\n    };\n    const loginLoading = ref(false);\n    // 登录方法\n    const login = () => {\n      loginLoading.value = true;\n      if (menuType.value === \"password\") {\n        passwordLogin(passwordForm, res => {\n          success(\"登录成功\");\n          const accessToken = {\n            expiresIn: res.expires_in,\n            value: res.access_token\n          };\n          const refreshToken = res.refresh_token;\n          const data = {\n            accessToken: accessToken,\n            refreshToken: refreshToken\n          };\n          // 保存登录信息\n          setToken(data);\n          // 成功后回调\n          context.emit(\"success\");\n          loginLoading.value = false;\n          close();\n        }).catch(() => {\n          loginLoading.value = false;\n        });\n      } else if (menuType.value === \"authCode\") {\n        authCodeLogin(authCodeForm, res => {\n          success(\"登录成功\");\n          const accessToken = {\n            expiresIn: res.expires_in,\n            value: res.access_token\n          };\n          const refreshToken = res.refresh_token;\n          const data = {\n            accessToken: accessToken,\n            refreshToken: refreshToken\n          };\n          // 保存登录信息\n          setToken(data);\n          // 登录成功回调\n          context.emit(\"success\");\n          loginLoading.value = false;\n          // 关闭窗口回调\n          close();\n        }).catch(() => {\n          loginLoading.value = false;\n        });\n      }\n    };\n    return {\n      // 变量\n      menuActiveIndex,\n      menuType,\n      showPassword,\n      passwordType,\n      authCodeAppendValue,\n      loginQrCodeUrl,\n      passwordForm,\n      authCodeForm,\n      oAuth2Type,\n      loginLoading,\n      // 方法\n      close,\n      selectMenu,\n      showPasswordChange,\n      getAuthCode,\n      login,\n      selectOauth2\n    };\n  }\n};", "map": {"version": 3, "names": ["reactive", "ref", "authCodeLogin", "getMsgAuthCode", "passwordLogin", "getWorkWeChatConfig", "getDingTalkConfig", "error", "info", "success", "setToken", "name", "emits", "props", "show", "Boolean", "callback", "type", "Function", "default", "showClose", "required", "setup", "context", "console", "log", "attrs", "menuActiveIndex", "menuType", "showPassword", "passwordType", "authCodeAppendLabel", "authCodeAppendValue", "loginQrCodeUrl", "passwordForm", "username", "password", "authCodeForm", "mobile", "authCode", "close", "emit", "oAuth2Type", "selectMenu", "index", "value", "selectOauth2", "schema", "window", "location", "protocol", "host", "redirectUri", "res", "url", "appId", "DDLogin", "id", "goto", "encodeURIComponent", "style", "width", "height", "handleMessage", "event", "origin", "loginTmpCode", "data", "href", "addEventListener", "attachEvent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "agentId", "state", "showPasswordChange", "getAuthCode", "times", "timer", "setInterval", "clearInterval", "loginLoading", "login", "accessToken", "expiresIn", "expires_in", "access_token", "refreshToken", "refresh_token", "catch"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/login/Login.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    custom-class=\"login\"\n    :model-value=\"show\"\n    :show-close=\"showClose\"\n    :before-close=\"close\"\n    :modal=\"false\"\n    :lock-scroll=\"false\"\n    :close-on-press-escape=\"false\"\n    :close-on-click-modal=\"false\">\n    <div class=\"dialog-content\">\n      <div class=\"dialog-left\">\n        <div class=\"logo\">\n          <!--          <img src=\"../../assets/logo.svg\"/>-->\n          <div style=\"font-size: 80px;color: #000000; font-weight: 600;\">知否</div>\n        </div>\n        <div class=\"title\">\n          <div class=\"title-main\" style=\"font-size: 48px;font-weight: 400;\">管理后台</div>\n          <div class=\"sub-title\" style=\"display: none;\">\n            <p>登录本系统即表示您已查看了</p>\n            <a target=\"_blank\" href=\"/agreement/service\">服务协议</a>\n            以及\n            <a target=\"_blank\" href=\"/agreement/privacy\">隐私政策</a>\n            ，并同意遵守条款和条件。\n          </div>\n        </div>\n      </div>\n      <div class=\"dialog-right\">\n        <div\n          class=\"corner-icon-view view-type-qr-code\"\n          :class=\"{ 'view-type-pc': menuType === 'qrCode' }\"\n          style=\"display: none;\">\n          <i\n            @click=\"selectMenu('3')\"\n            class=\"iconfont icon-qr-code\"\n            :class=\"{ 'icon-pc': menuType === 'qrCode' }\"/>\n        </div>\n        <div class=\"login-content\">\n          <div class=\"login-password\">\n            <div v-if=\"menuType !== 'qrCode'\">\n              <el-menu\n                :default-active=\"menuActiveIndex\"\n                class=\"el-menu\"\n                mode=\"horizontal\"\n                :ellipsis=\"false\"\n                @select=\"selectMenu\">\n                <el-menu-item index=\"2\">账号登录</el-menu-item>\n                <el-menu-item index=\"1\">手机登录</el-menu-item>\n              </el-menu>\n              <div v-if=\"menuType === 'authCode'\">\n                <el-form :model=\"authCodeForm\" ref=\"login-form\" class=\"login-form\">\n                  <el-input\n                    v-model=\"authCodeForm.mobile\"\n                    placeholder=\"请输入手机号码\"\n                    class=\"input-text username input-with-select\"/>\n                  <div class=\"input-text input-wrap-password\">\n                    <el-input\n                      v-model=\"authCodeForm.authCode\"\n                      placeholder=\"请输入验证码\">\n                      <template #append>\n                        <el-button class=\"code-btn\" @click=\"getAuthCode(authCodeAppendValue)\">{{authCodeAppendValue}}</el-button>\n                      </template>\n                    </el-input>\n                  </div>\n                  <div class=\"forgot-password-links\">\n                    <a target=\"_blank\"/>\n                  </div>\n                </el-form>\n              </div>\n              <div v-if=\"menuType === 'password'\">\n                <el-form :model=\"passwordForm\" ref=\"login-form\" class=\"login-form\">\n                  <el-input\n                    v-model=\"passwordForm.username\"\n                    class=\"input-text username\"\n                    placeholder=\"手机号码/邮箱\"/>\n                  <div class=\"input-wrap-password\">\n                    <el-input\n                      v-model=\"passwordForm.password\"\n                      :type=\"passwordType\"\n                      class=\"input-text\"\n                      placeholder=\"登录密码\"\n                      maxlength=\"40\"/>\n                    <div class=\"password-look-btn\" @click=\"showPasswordChange\">\n                      <i\n                        class=\"iconfont\"\n                        :class=\"{\n                          'icon-eye-close': !showPassword,\n                          'icon-eye-open': showPassword\n                        }\"/>\n                    </div>\n                  </div>\n                  <div class=\"forgot-password-links\">\n                    <div>\n                    </div>\n                    <div>\n                      <!--                      <a target=\"_blank\" href=\"/password/reset\">忘记密码?</a>-->\n                    </div>\n                  </div>\n                </el-form>\n              </div>\n              <div class=\"login-btn\">\n                <el-button :loading=\"loginLoading\" @click=\"login\">登录</el-button>\n              </div>\n              <div class=\"agreement-login\">\n                <div class=\"agreement\">\n                  <!--       协议提示       -->\n                </div>\n              </div>\n            </div>\n            <div v-else class=\"login-qr-code\">\n              <div v-show=\"oAuth2Type !== ''\" class=\"qr-code-back\"><a @click=\"selectMenu('3')\" class=\"el-icon-back\"> 返回</a></div>\n              <div id=\"ding-talk-body\" v-show=\"oAuth2Type === 'ding-talk'\" style=\"margin: 0 auto;width: 300px;height: 400px\"></div>\n              <div id=\"work-we-chat-body\" v-show=\"oAuth2Type === 'work-we-chat'\" style=\"margin: 0 auto;width: 300px;height: 400px\"></div>\n              <div class=\"custom-qr-code\" v-show=\"oAuth2Type === ''\">\n                <div class=\"login-tip\">\n                  <div>\n                    打开手机<strong>App</strong>\n                    <br>\n                    在「我的」页面右上角打开扫一扫\n                  </div>\n                </div>\n                <div class=\"qr-code-img\">\n                  <div><img src=\"\" alt=\"敬请期待\" height=\"165\" width=\"165\"></div>\n                </div>\n              </div>\n            </div>\n            <div class=\"sns-login-block\" v-if=\"menuType !== 'qrCode'\">\n              <div class=\"sns-login\">\n                <div class=\"sns-login-title\">其他登录方式</div>\n                <div class=\"oauth2-login\">\n                  <el-tooltip effect=\"dark\" content=\"钉钉登录\" placement=\"top\">\n                    <a @click=\"selectOauth2('ding-talk')\" class=\"oauth2-login-icon\">\n                      <el-icon :size=\"36\"><ChromeFilled /></el-icon>\n                    </a>\n                  </el-tooltip>\n                  <el-tooltip effect=\"dark\" content=\"企业微信登录\" placement=\"top\">\n                    <a @click=\"selectOauth2('work-we-chat')\" class=\"oauth2-login-icon\">\n                      <el-icon :size=\"36\"><ChatDotRound /></el-icon>\n                    </a>\n                  </el-tooltip>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport {reactive, ref} from \"vue\";\nimport {authCodeLogin, getMsgAuthCode, passwordLogin, getWorkWeChatConfig, getDingTalkConfig} from \"@/api/login\";\nimport {error, info, success} from \"@/util/tipsUtils\";\nimport {setToken} from \"@/util/tokenUtils\";\n\nexport default {\n  name: \"LoginIndex\",\n  emits: [\"success\", \"callback\"],\n  props: {\n    show: Boolean,\n    callback: {\n      type: Function,\n      default: () => {}\n    },\n    success: {\n      type: Function,\n      default: () => {}\n    },\n    showClose: {\n      type: Boolean,\n      default: true,\n      required: false\n    }\n  },\n  setup(props, context) {\n    console.log(\"props:\", {\n      ...props\n    });\n    console.log(\"context.attrs:\", {\n      ...context.attrs\n    });\n    const menuActiveIndex = ref(\"2\");\n    const menuType = ref(\"password\");\n    const showPassword = ref(false);\n    const passwordType = ref(\"password\");\n    const authCodeAppendLabel = \"获取验证码\";\n    const authCodeAppendValue = ref(\"获取验证码\");\n    const loginQrCodeUrl = ref(\"https://img.alicdn.com/imgextra/O1CN01xstIAQ28A7zAahq9C_!!6000000007891-2-xcode.png\");\n    const passwordForm = reactive({\n      username: \"\",\n      password: \"\"\n    });\n    const authCodeForm = reactive({\n      mobile: \"\",\n      authCode: \"\"\n    });\n    // 关闭组件回调\n    const close = () => {\n      context.emit(\"callback\");\n    };\n    // 选择登录方式\n    const oAuth2Type = ref(\"\");\n    const selectMenu = index => {\n      menuActiveIndex.value = index;\n      switch (menuActiveIndex.value) {\n        // 验证码\n      case \"1\":\n        menuType.value = \"authCode\";\n        break;\n        // 密码\n      case \"2\":\n        menuType.value = \"password\";\n        break;\n        // 扫码\n      case \"3\":\n        if (menuType.value === \"qrCode\") {\n          menuType.value = \"authCode\";\n          menuActiveIndex.value = \"1\";\n          oAuth2Type.value = \"\"\n        } else {\n          menuType.value = \"qrCode\";\n          // 获取二维码\n          // getLoginQrCode(res => {\n          //   console.log(res);\n          //   // 二维码地址\n          //   loginQrCodeUrl.value = res.data;\n          // });\n        }\n        break;\n      }\n    };\n    const selectOauth2 = (type) => {\n      oAuth2Type.value = type\n      menuType.value = \"qrCode\";\n      const schema = window.location.protocol\n      const host = window.location.host\n      let redirectUri = schema + \"//\" + host + \"/ding-talk\"\n      switch (type) {\n      case \"ding-talk\":\n        getDingTalkConfig((res) => {\n          const url = \"https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=\" + res.appId + \"&response_type=code&scope=snsapi_login&state=STATE\"\n          window.DDLogin({\n            id: \"ding-talk-body\",\n            goto: encodeURIComponent(url + \"&redirect_uri=\"+ redirectUri),\n            style: \"border:none;background-color:#FFFFFF;\",\n            width: \"300\",\n            height: \"400\"\n          });\n          const handleMessage = function (event) {\n            const origin = event.origin;\n            console.log(\"origin\", event.origin);\n            //判断是否来自ddLogin扫码事件。\n            if (origin === \"https://login.dingtalk.com\") {\n              const loginTmpCode = event.data;\n              //获取到loginTmpCode后就可以在这里构造跳转链接进行跳转了\n              window.location.href = url + \"&redirect_uri=\"+ encodeURIComponent(redirectUri) + \"&loginTmpCode=\" + loginTmpCode\n            }\n          };\n          if (typeof window.addEventListener != \"undefined\") {\n            window.addEventListener(\"message\", handleMessage, false);\n          } else if (typeof window.attachEvent != \"undefined\") {\n            window.attachEvent(\"onmessage\", handleMessage);\n          }\n        })\n        break;\n      case \"work-we-chat\":\n        getWorkWeChatConfig((res) => {\n          window.WwLogin({\n            \"id\": \"work-we-chat-body\",\n            \"appid\": res.appId,\n            \"agentid\": res.agentId,\n            \"redirect_uri\": encodeURIComponent(schema + \"//\" + host + \"/work-we-chat\"),\n            \"state\": res.state,\n            \"href\": \"\"\n          })\n        })\n        break;\n      }\n    }\n    // 显示密码\n    const showPasswordChange = () => {\n      showPassword.value = !showPassword.value;\n      if (showPassword.value) {\n        passwordType.value = \"text\";\n      } else {\n        passwordType.value = \"password\";\n      }\n    };\n    // 获取验证码\n    const getAuthCode = value => {\n      if (value !== authCodeAppendLabel) {\n        return;\n      }\n      if (!authCodeForm.mobile) {\n        error(\"请输入手机号码\");\n        return;\n      }\n      // 获取验证码\n      getMsgAuthCode(authCodeForm.mobile, () => {\n        let times = 60;\n        const timer = setInterval(() => {\n          times--;\n          if (times === 0) {\n            authCodeAppendValue.value = authCodeAppendLabel;\n            clearInterval(timer);\n          } else {\n            authCodeAppendValue.value = times + \"\";\n          }\n        }, 1000);\n        info(\"暂不发送短信，统一验证码为：123456\");\n      });\n    };\n    const loginLoading = ref(false)\n    // 登录方法\n    const login = () => {\n      loginLoading.value = true;\n      if (menuType.value === \"password\") {\n        passwordLogin(passwordForm, res => {\n          success(\"登录成功\");\n          const accessToken = { expiresIn: res.expires_in, value: res.access_token };\n          const refreshToken = res.refresh_token;\n          const data = { accessToken: accessToken, refreshToken: refreshToken };\n          // 保存登录信息\n          setToken(data)\n          // 成功后回调\n          context.emit(\"success\");\n          loginLoading.value = false;\n          close();\n        }).catch(() => {\n          loginLoading.value = false;\n        });\n      } else if (menuType.value === \"authCode\") {\n        authCodeLogin(authCodeForm, (res) => {\n          success(\"登录成功\");\n          const accessToken = { expiresIn: res.expires_in, value: res.access_token };\n          const refreshToken = res.refresh_token;\n          const data = { accessToken: accessToken, refreshToken: refreshToken };\n          // 保存登录信息\n          setToken(data)\n          // 登录成功回调\n          context.emit(\"success\");\n          loginLoading.value = false;\n          // 关闭窗口回调\n          close();\n        }).catch(() => {\n          loginLoading.value = false;\n        });\n      }\n    };\n    return {\n      // 变量\n      menuActiveIndex,\n      menuType,\n      showPassword,\n      passwordType,\n      authCodeAppendValue,\n      loginQrCodeUrl,\n      passwordForm,\n      authCodeForm,\n      oAuth2Type,\n      loginLoading,\n      // 方法\n      close,\n      selectMenu,\n      showPasswordChange,\n      getAuthCode,\n      login,\n      selectOauth2\n    };\n  }\n};\n</script>\n\n<style lang=\"scss\">\n.login {\n  width: 720px !important;\n  max-width: 720px;\n  height: 500px;\n  background-color: #fff;\n  //background-color: rgba(255,255,255,0.5);\n  background-size: 720px 500px!important;\n  background-repeat: no-repeat;\n  background-position: top;\n  margin: 0 auto;\n  border-radius: 4px!important;\n  box-shadow: none!important;\n  margin-top: 20vh!important;\n  display: flex;\n  .el-dialog__header {\n    padding: 0;\n    margin: 0;\n    .el-dialog__headerbtn {\n      left: 10px;\n      font-size: 20px;\n      top: 10px;\n      z-index: 999999;\n    }\n  }\n  .dialog-content {\n    display: flex;\n    .dialog-left {\n      width: 360px;\n      background-size: contain;\n      border-right: 1px solid #f5f5f5;\n      .logo {\n        margin: 30% auto 0;\n        width: 100%;\n        text-align: center;\n        img {\n          width: 240px;\n        }\n      }\n      .title {\n        font-size: 24px;\n        text-align: center;\n        color: #000000;\n        padding: 0 60px;\n      }\n      .title-main {\n        font-size: 32px;\n        font-weight: 700;\n        padding: 20% 0 0;\n      }\n      .sub-title {\n        font-size: 14px;\n        color: #000000;\n        padding: 10px 0;\n        line-height: 32px;\n      }\n      .dialog-left-img {\n        img {\n          width: 100%;\n        }\n      }\n    }\n    .dialog-right {\n      width: 50%;\n    }\n  }\n  .corner-icon-view {\n    position: relative;\n    width: 100%;\n    margin: 0 auto;\n    height: 48px;\n  }\n  .corner-icon-view.view-type-qr-code:before {\n    content: \"\";\n    display: inline-block;\n    background: url(\"./../../assets/login/scan-qrcode-tips.png\");\n    background-size: contain;\n    width: 132px;\n    height: 28px;\n    position: absolute;\n    top: 7px;\n    right: 57px;\n    z-index: 1;\n  }\n  .corner-icon-view.view-type-pc:before {\n    background: none;\n  }\n  .iconfont {\n    font-style: normal;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    display: block;\n    cursor: pointer;\n    position: absolute;\n    top: 0;\n    right: 0;\n    font-size: 40px;\n    color: #ff9000;\n    width: 60px;\n    height: 60px;\n    text-indent: -99999px;\n    background-size: contain;\n  }\n  .icon-qr-code {\n    background-image: url(\"./../../assets/login/qr-code-icon.png\");\n  }\n  .icon-pc {\n    background-image: url(\"./../../assets/login/pc-icon.png\");\n  }\n  .el-dialog__body {\n    padding: 0;\n    display: flex;\n  }\n  .login-content {\n    margin: 0 auto;\n    padding: 30px 30px 0;\n    width: 300px;\n    .el-menu {\n      background: rgba(255, 255, 255, 0);\n      border: none;\n      li {\n        width: 50%;\n        height: 42px;\n        line-height: 42px;\n        text-align: center;\n        font-size: 14px;\n        font-weight: 500;\n        color: #999;\n        border-bottom: 1px solid #eee!important;\n      }\n      li:hover,\n      li:focus {\n        background: rgba(255, 255, 255, 0);\n        color: #999;\n      }\n      li.is-active {\n        color: $--color-primary!important;\n        border-color: $--color-primary!important;\n      }\n    }\n\n    .username {\n      margin-top: 34px;\n      input {\n        margin-bottom: 10px;\n      }\n      .el-input__wrapper {\n        box-shadow: none;\n        padding: 0;\n      }\n    }\n    .input-text {\n      input {\n        height: 42px;\n        outline: none;\n        width: 100%;\n        background: none;\n        border: none;\n        border-bottom: 1px solid #EEEEEE;\n        font-size: 14px;\n        padding-left: 0;\n        color: #000;\n        border-radius: 0;\n      }\n      .el-input-group__append {\n        height: auto;\n      }\n    }\n    .input-wrap-password {\n      position: relative;\n      font-size: 14px;\n      .code-btn {\n        font-size: 12px;\n        &:hover {\n          color: $--color-primary;\n        }\n      }\n      .el-input__wrapper {\n        box-shadow: none;\n        padding: 0;\n      }\n      .el-input-group__append {\n        box-shadow: none;\n        margin: 0;\n      }\n    }\n    .password-look-btn {\n      position: absolute;\n      right: 10px;\n      top: 10px;\n      .iconfont {\n        font-style: normal;\n        -webkit-font-smoothing: antialiased;\n        -moz-osx-font-smoothing: grayscale;\n        font-size: 20px;\n        display: block;\n        background-size: contain;\n        width: 24px;\n        height: 24px;\n      }\n      .icon-eye-close {\n        background-image: url(\"./../../assets/login/eye-close.png\");\n      }\n      .icon-eye-open {\n        background-image: url(\"./../../assets/login/eye-open.png\");\n      }\n    }\n\n    .forgot-password-links {\n      text-align: right;\n      zoom: 1;\n      margin: 12px 0;\n      line-height: 18px;\n      display: flex;\n      justify-content: space-between;\n      min-height: 20px;\n      a {\n        display: inline-block;\n        cursor: pointer;\n        font-size: 12px;\n        color: #999999;\n      }\n      a:hover {\n        color: $--color-primary;\n      }\n    }\n    .login-btn {\n      width: 100%;\n      button {\n        outline: none;\n        color: #fff;\n        width: 100%;\n        cursor: pointer;\n        height: 48px;\n        line-height: 48px;\n        text-align: center;\n        border: none;\n        border-radius: 4px;\n        font-size: 16px;\n        background-image: linear-gradient(90deg, #8d9f91, $--color-primary);\n        padding: 0;\n      }\n    }\n    .agreement-login {\n      zoom: 1;\n      text-align: center;\n      font-size: 12px;\n      margin: 12px 0;\n      color: #666;\n      .agreement {\n        margin-bottom: 8px;\n        font-size: 12px;\n        line-height: 18px;\n        a {\n          text-decoration: none;\n          display: inline-block;\n          font-size: 12px;\n          margin-left: 0;\n          color: $--color-primary;\n        }\n      }\n    }\n\n    .sns-login-block {\n      zoom: 1;\n      text-align: center;\n      position: relative;\n      height: 122px;\n      margin: 50px 0 0;\n      .sns-login {\n        margin: 10px 0;\n        float: none;\n        .sns-login-title {\n          font-size: 12px;\n          line-height: 18px;\n          color: #999999;\n        }\n        .sns-login-title:before,\n        .sns-login-title:after {\n          content: \"\";\n          display: inline-block;\n          width: 90px;\n          height: 1px;\n          margin: 0 6px;\n          vertical-align: middle;\n          background-color: #cccccc;\n        }\n\n        .oauth2-login {\n          float: none;\n          /*display: flex;*/\n          justify-content: space-between;\n          margin-top: 20px;\n          a {\n            margin-left: 0;\n          }\n          .oauth2-login-icon {\n            background-size: contain;\n            background-position: 0 0;\n            display: inline-block;\n            background-repeat: no-repeat;\n            width: 36px;\n            height: 36px;\n            margin: 0 20px;\n          }\n          .icon-taobao {\n            background-image: url(\"./../../assets/login/taobao.png\");\n          }\n          .icon-alipay {\n            background-image: url(\"./../../assets/login/alipay.png\");\n          }\n          .icon-weixin {\n            background-image: url(\"./../../assets/login/weixin.png\");\n          }\n          .icon-qq {\n            background-image: url(\"./../../assets/login/qq.png\");\n          }\n          .icon-weibo {\n            background-image: url(\"./../../assets/login/weibo.png\");\n          }\n          .icon-dingtalk {\n            background-image: url(\"./../../assets/login/dingtalk.png\");\n            padding: 0 0 2px 0;\n          }\n          .icon-workwechat {\n            background-image: url(\"./../../assets/login/workwechat.png\");\n          }\n        }\n      }\n      a:hover {\n        color: $--color-primary;\n      }\n      a {\n        text-decoration: none;\n        display: inline-block;\n        margin-left: 10px;\n        cursor: pointer;\n      }\n      .registerLink,\n      .forgotLoginIdLink {\n        /*position: absolute;*/\n        bottom: 0;\n        font-size: 14px;\n        line-height: 20px;\n        color: #999;\n      }\n      .registerLink {\n        // left: -30px;\n        margin: 20px auto 0;\n      }\n      .forgotLoginIdLink {\n        right: -30px;\n      }\n    }\n  }\n  .el-input-group__prepend {\n    border: none;\n    width: 86px !important;\n  }\n  .el-input-group__prepend,\n  .el-input-group__append {\n    border-radius: 0 !important;\n    width: 66px;\n    background: rgba(255, 255, 255, 0);\n    height: 42px;\n    border-right: none;\n    border-top: none;\n    border-left: none;\n    margin-bottom: 10px;\n    padding: 0;\n    .el-select {\n      height: 42px;\n      margin: 0 0 10px;\n      input {\n        border: none;\n        margin: 0;\n      }\n    }\n    .el-input__suffix-inner {\n      height: 20px;\n      border-right: 1px solid #eee;\n    }\n    .el-input--suffix {\n      height: 42px;\n      border-bottom: 1px solid #ccc;\n    }\n    .el-select:hover,\n    .el-select:focus {\n      color: #000;\n      border-bottom: 1px solid #ccc;\n    }\n  }\n  .el-input-group__append {\n    text-align: center;\n  }\n  .login-qr-code {\n    text-align: center;\n    zoom: 1;\n    .qr-code-back {\n      text-align: left;\n      cursor: pointer;\n      a {\n        &:hover {\n          color: $--color-primary;\n        }\n      }\n    }\n    .custom-qr-code{\n      margin-top: 30px;\n      margin-bottom: 40px;\n    }\n    .login-tip {\n      color: #999;\n      font-size: 14px;\n      line-height: 22px;\n      strong {\n        color: $--color-primary;\n      }\n    }\n    .qr-code-img {\n      position: relative;\n      margin: 20px auto;\n      font-size: 14px;\n      -webkit-box-shadow: 0 0 8px #ddd;\n      opacity: 1;\n      width: 165px;\n      height: 165px;\n      box-shadow: none;\n    }\n  }\n  input:-webkit-autofill {\n    -webkit-box-shadow: 0 0 0 1000px white inset !important;\n  }\n\n}\n</style>\n"], "mappings": "AAuJA,SAAQA,QAAQ,EAAEC,GAAG,QAAO,KAAK;AACjC,SAAQC,aAAa,EAAEC,cAAc,EAAEC,aAAa,EAAEC,mBAAmB,EAAEC,iBAAiB,QAAO,aAAa;AAChH,SAAQC,KAAK,EAAEC,IAAI,EAAEC,OAAO,QAAO,kBAAkB;AACrD,SAAQC,QAAQ,QAAO,mBAAmB;AAE1C,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,KAAK,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;EAC9BC,KAAK,EAAE;IACLC,IAAI,EAAEC,OAAO;IACbC,QAAQ,EAAE;MACRC,IAAI,EAAEC,QAAQ;MACdC,OAAO,EAAEA,CAAA,KAAM,CAAC;IAClB,CAAC;IACDV,OAAO,EAAE;MACPQ,IAAI,EAAEC,QAAQ;MACdC,OAAO,EAAEA,CAAA,KAAM,CAAC;IAClB,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAEF,OAAO;MACbI,OAAO,EAAE,IAAI;MACbE,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,KAAKA,CAACT,KAAK,EAAEU,OAAO,EAAE;IACpBC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE;MACpB,GAAGZ;IACL,CAAC,CAAC;IACFW,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;MAC5B,GAAGF,OAAO,CAACG;IACb,CAAC,CAAC;IACF,MAAMC,eAAc,GAAI1B,GAAG,CAAC,GAAG,CAAC;IAChC,MAAM2B,QAAO,GAAI3B,GAAG,CAAC,UAAU,CAAC;IAChC,MAAM4B,YAAW,GAAI5B,GAAG,CAAC,KAAK,CAAC;IAC/B,MAAM6B,YAAW,GAAI7B,GAAG,CAAC,UAAU,CAAC;IACpC,MAAM8B,mBAAkB,GAAI,OAAO;IACnC,MAAMC,mBAAkB,GAAI/B,GAAG,CAAC,OAAO,CAAC;IACxC,MAAMgC,cAAa,GAAIhC,GAAG,CAAC,qFAAqF,CAAC;IACjH,MAAMiC,YAAW,GAAIlC,QAAQ,CAAC;MAC5BmC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF,MAAMC,YAAW,GAAIrC,QAAQ,CAAC;MAC5BsC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF;IACA,MAAMC,KAAI,GAAIA,CAAA,KAAM;MAClBjB,OAAO,CAACkB,IAAI,CAAC,UAAU,CAAC;IAC1B,CAAC;IACD;IACA,MAAMC,UAAS,GAAIzC,GAAG,CAAC,EAAE,CAAC;IAC1B,MAAM0C,UAAS,GAAIC,KAAI,IAAK;MAC1BjB,eAAe,CAACkB,KAAI,GAAID,KAAK;MAC7B,QAAQjB,eAAe,CAACkB,KAAK;QAC3B;QACF,KAAK,GAAG;UACNjB,QAAQ,CAACiB,KAAI,GAAI,UAAU;UAC3B;QACA;QACF,KAAK,GAAG;UACNjB,QAAQ,CAACiB,KAAI,GAAI,UAAU;UAC3B;QACA;QACF,KAAK,GAAG;UACN,IAAIjB,QAAQ,CAACiB,KAAI,KAAM,QAAQ,EAAE;YAC/BjB,QAAQ,CAACiB,KAAI,GAAI,UAAU;YAC3BlB,eAAe,CAACkB,KAAI,GAAI,GAAG;YAC3BH,UAAU,CAACG,KAAI,GAAI,EAAC;UACtB,OAAO;YACLjB,QAAQ,CAACiB,KAAI,GAAI,QAAQ;YACzB;YACA;YACA;YACA;YACA;YACA;UACF;;UACA;MAAK;IAET,CAAC;IACD,MAAMC,YAAW,GAAK7B,IAAI,IAAK;MAC7ByB,UAAU,CAACG,KAAI,GAAI5B,IAAG;MACtBW,QAAQ,CAACiB,KAAI,GAAI,QAAQ;MACzB,MAAME,MAAK,GAAIC,MAAM,CAACC,QAAQ,CAACC,QAAO;MACtC,MAAMC,IAAG,GAAIH,MAAM,CAACC,QAAQ,CAACE,IAAG;MAChC,IAAIC,WAAU,GAAIL,MAAK,GAAI,IAAG,GAAII,IAAG,GAAI,YAAW;MACpD,QAAQlC,IAAI;QACZ,KAAK,WAAW;UACdX,iBAAiB,CAAE+C,GAAG,IAAK;YACzB,MAAMC,GAAE,GAAI,+DAA8D,GAAID,GAAG,CAACE,KAAI,GAAI,oDAAmD;YAC7IP,MAAM,CAACQ,OAAO,CAAC;cACbC,EAAE,EAAE,gBAAgB;cACpBC,IAAI,EAAEC,kBAAkB,CAACL,GAAE,GAAI,gBAAgB,GAAEF,WAAW,CAAC;cAC7DQ,KAAK,EAAE,uCAAuC;cAC9CC,KAAK,EAAE,KAAK;cACZC,MAAM,EAAE;YACV,CAAC,CAAC;YACF,MAAMC,aAAY,GAAI,SAAAA,CAAUC,KAAK,EAAE;cACrC,MAAMC,MAAK,GAAID,KAAK,CAACC,MAAM;cAC3BzC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEuC,KAAK,CAACC,MAAM,CAAC;cACnC;cACA,IAAIA,MAAK,KAAM,4BAA4B,EAAE;gBAC3C,MAAMC,YAAW,GAAIF,KAAK,CAACG,IAAI;gBAC/B;gBACAnB,MAAM,CAACC,QAAQ,CAACmB,IAAG,GAAId,GAAE,GAAI,gBAAgB,GAAEK,kBAAkB,CAACP,WAAW,IAAI,gBAAe,GAAIc,YAAW;cACjH;YACF,CAAC;YACD,IAAI,OAAOlB,MAAM,CAACqB,gBAAe,IAAK,WAAW,EAAE;cACjDrB,MAAM,CAACqB,gBAAgB,CAAC,SAAS,EAAEN,aAAa,EAAE,KAAK,CAAC;YAC1D,OAAO,IAAI,OAAOf,MAAM,CAACsB,WAAU,IAAK,WAAW,EAAE;cACnDtB,MAAM,CAACsB,WAAW,CAAC,WAAW,EAAEP,aAAa,CAAC;YAChD;UACF,CAAC;UACD;QACF,KAAK,cAAc;UACjB1D,mBAAmB,CAAEgD,GAAG,IAAK;YAC3BL,MAAM,CAACuB,OAAO,CAAC;cACb,IAAI,EAAE,mBAAmB;cACzB,OAAO,EAAElB,GAAG,CAACE,KAAK;cAClB,SAAS,EAAEF,GAAG,CAACmB,OAAO;cACtB,cAAc,EAAEb,kBAAkB,CAACZ,MAAK,GAAI,IAAG,GAAII,IAAG,GAAI,eAAe,CAAC;cAC1E,OAAO,EAAEE,GAAG,CAACoB,KAAK;cAClB,MAAM,EAAE;YACV,CAAC;UACH,CAAC;UACD;MAAK;IAET;IACA;IACA,MAAMC,kBAAiB,GAAIA,CAAA,KAAM;MAC/B7C,YAAY,CAACgB,KAAI,GAAI,CAAChB,YAAY,CAACgB,KAAK;MACxC,IAAIhB,YAAY,CAACgB,KAAK,EAAE;QACtBf,YAAY,CAACe,KAAI,GAAI,MAAM;MAC7B,OAAO;QACLf,YAAY,CAACe,KAAI,GAAI,UAAU;MACjC;IACF,CAAC;IACD;IACA,MAAM8B,WAAU,GAAI9B,KAAI,IAAK;MAC3B,IAAIA,KAAI,KAAMd,mBAAmB,EAAE;QACjC;MACF;MACA,IAAI,CAACM,YAAY,CAACC,MAAM,EAAE;QACxB/B,KAAK,CAAC,SAAS,CAAC;QAChB;MACF;MACA;MACAJ,cAAc,CAACkC,YAAY,CAACC,MAAM,EAAE,MAAM;QACxC,IAAIsC,KAAI,GAAI,EAAE;QACd,MAAMC,KAAI,GAAIC,WAAW,CAAC,MAAM;UAC9BF,KAAK,EAAE;UACP,IAAIA,KAAI,KAAM,CAAC,EAAE;YACf5C,mBAAmB,CAACa,KAAI,GAAId,mBAAmB;YAC/CgD,aAAa,CAACF,KAAK,CAAC;UACtB,OAAO;YACL7C,mBAAmB,CAACa,KAAI,GAAI+B,KAAI,GAAI,EAAE;UACxC;QACF,CAAC,EAAE,IAAI,CAAC;QACRpE,IAAI,CAAC,sBAAsB,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC;IACD,MAAMwE,YAAW,GAAI/E,GAAG,CAAC,KAAK;IAC9B;IACA,MAAMgF,KAAI,GAAIA,CAAA,KAAM;MAClBD,YAAY,CAACnC,KAAI,GAAI,IAAI;MACzB,IAAIjB,QAAQ,CAACiB,KAAI,KAAM,UAAU,EAAE;QACjCzC,aAAa,CAAC8B,YAAY,EAAEmB,GAAE,IAAK;UACjC5C,OAAO,CAAC,MAAM,CAAC;UACf,MAAMyE,WAAU,GAAI;YAAEC,SAAS,EAAE9B,GAAG,CAAC+B,UAAU;YAAEvC,KAAK,EAAEQ,GAAG,CAACgC;UAAa,CAAC;UAC1E,MAAMC,YAAW,GAAIjC,GAAG,CAACkC,aAAa;UACtC,MAAMpB,IAAG,GAAI;YAAEe,WAAW,EAAEA,WAAW;YAAEI,YAAY,EAAEA;UAAa,CAAC;UACrE;UACA5E,QAAQ,CAACyD,IAAI;UACb;UACA5C,OAAO,CAACkB,IAAI,CAAC,SAAS,CAAC;UACvBuC,YAAY,CAACnC,KAAI,GAAI,KAAK;UAC1BL,KAAK,EAAE;QACT,CAAC,CAAC,CAACgD,KAAK,CAAC,MAAM;UACbR,YAAY,CAACnC,KAAI,GAAI,KAAK;QAC5B,CAAC,CAAC;MACJ,OAAO,IAAIjB,QAAQ,CAACiB,KAAI,KAAM,UAAU,EAAE;QACxC3C,aAAa,CAACmC,YAAY,EAAGgB,GAAG,IAAK;UACnC5C,OAAO,CAAC,MAAM,CAAC;UACf,MAAMyE,WAAU,GAAI;YAAEC,SAAS,EAAE9B,GAAG,CAAC+B,UAAU;YAAEvC,KAAK,EAAEQ,GAAG,CAACgC;UAAa,CAAC;UAC1E,MAAMC,YAAW,GAAIjC,GAAG,CAACkC,aAAa;UACtC,MAAMpB,IAAG,GAAI;YAAEe,WAAW,EAAEA,WAAW;YAAEI,YAAY,EAAEA;UAAa,CAAC;UACrE;UACA5E,QAAQ,CAACyD,IAAI;UACb;UACA5C,OAAO,CAACkB,IAAI,CAAC,SAAS,CAAC;UACvBuC,YAAY,CAACnC,KAAI,GAAI,KAAK;UAC1B;UACAL,KAAK,EAAE;QACT,CAAC,CAAC,CAACgD,KAAK,CAAC,MAAM;UACbR,YAAY,CAACnC,KAAI,GAAI,KAAK;QAC5B,CAAC,CAAC;MACJ;IACF,CAAC;IACD,OAAO;MACL;MACAlB,eAAe;MACfC,QAAQ;MACRC,YAAY;MACZC,YAAY;MACZE,mBAAmB;MACnBC,cAAc;MACdC,YAAY;MACZG,YAAY;MACZK,UAAU;MACVsC,YAAY;MACZ;MACAxC,KAAK;MACLG,UAAU;MACV+B,kBAAkB;MAClBC,WAAW;MACXM,KAAK;MACLnC;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}