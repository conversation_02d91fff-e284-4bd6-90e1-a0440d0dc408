{"ast": null, "code": "import request from \"./remoteUtils\";\nimport { error } from \"./tipsUtils\";\nexport function get(url, params, success, notShowError) {\n  return request({\n    url: url,\n    method: \"get\",\n    params: params\n  }).then(res => {\n    if (res && res.code === 0) {\n      success && success(res.data);\n    } else {\n      !notShowError && error(res.msg);\n    }\n  });\n}\nexport function post(url, data, success) {\n  return request({\n    url: url,\n    method: \"post\",\n    data\n  }).then(res => {\n    if (res && res.code === 0) {\n      success && success(res.data);\n    } else {\n      error(res.msg);\n    }\n  });\n}\nexport function put(url, data, success) {\n  return request({\n    url: url,\n    method: \"put\",\n    data\n  }).then(res => {\n    if (res && res.code === 0) {\n      success && success(res.data);\n    } else {\n      error(res.msg);\n    }\n  });\n}\nexport function del(url, data, success) {\n  return request({\n    url: url,\n    method: \"delete\",\n    data\n  }).then(res => {\n    if (!res || res && res.code === 0) {\n      success && success(res.data);\n    } else {\n      error(res.msg);\n    }\n  });\n}", "map": {"version": 3, "names": ["request", "error", "get", "url", "params", "success", "notShowError", "method", "then", "res", "code", "data", "msg", "post", "put", "del"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/util/requestUtils.js"], "sourcesContent": ["import request from \"./remoteUtils\";\nimport { error } from \"./tipsUtils\";\n\nexport function get(url, params, success, notShowError) {\n  return request({\n    url: url,\n    method: \"get\",\n    params: params\n  }).then(res => {\n    if (res && res.code === 0) {\n      success && success(res.data);\n    } else {\n      !notShowError && error(res.msg);\n    }\n  });\n}\n\nexport function post(url, data, success) {\n  return request({\n    url: url,\n    method: \"post\",\n    data\n  }).then(res => {\n    if (res && res.code === 0) {\n      success && success(res.data);\n    } else {\n      error(res.msg);\n    }\n  });\n}\n\nexport function put(url, data, success) {\n  return request({\n    url: url,\n    method: \"put\",\n    data\n  }).then(res => {\n    if (res && res.code === 0) {\n      success && success(res.data);\n    } else {\n      error(res.msg);\n    }\n  });\n}\n\nexport function del(url, data, success) {\n  return request({\n    url: url,\n    method: \"delete\",\n    data\n  }).then(res => {\n    if (!res || res && res.code === 0) {\n      success && success(res.data);\n    } else {\n      error(res.msg);\n    }\n  });\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;AACnC,SAASC,KAAK,QAAQ,aAAa;AAEnC,OAAO,SAASC,GAAGA,CAACC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAEC,YAAY,EAAE;EACtD,OAAON,OAAO,CAAC;IACbG,GAAG,EAAEA,GAAG;IACRI,MAAM,EAAE,KAAK;IACbH,MAAM,EAAEA;EACV,CAAC,CAAC,CAACI,IAAI,CAACC,GAAG,IAAI;IACb,IAAIA,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAK,CAAC,EAAE;MACzBL,OAAO,IAAIA,OAAO,CAACI,GAAG,CAACE,IAAI,CAAC;IAC9B,CAAC,MAAM;MACL,CAACL,YAAY,IAAIL,KAAK,CAACQ,GAAG,CAACG,GAAG,CAAC;IACjC;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASC,IAAIA,CAACV,GAAG,EAAEQ,IAAI,EAAEN,OAAO,EAAE;EACvC,OAAOL,OAAO,CAAC;IACbG,GAAG,EAAEA,GAAG;IACRI,MAAM,EAAE,MAAM;IACdI;EACF,CAAC,CAAC,CAACH,IAAI,CAACC,GAAG,IAAI;IACb,IAAIA,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAK,CAAC,EAAE;MACzBL,OAAO,IAAIA,OAAO,CAACI,GAAG,CAACE,IAAI,CAAC;IAC9B,CAAC,MAAM;MACLV,KAAK,CAACQ,GAAG,CAACG,GAAG,CAAC;IAChB;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASE,GAAGA,CAACX,GAAG,EAAEQ,IAAI,EAAEN,OAAO,EAAE;EACtC,OAAOL,OAAO,CAAC;IACbG,GAAG,EAAEA,GAAG;IACRI,MAAM,EAAE,KAAK;IACbI;EACF,CAAC,CAAC,CAACH,IAAI,CAACC,GAAG,IAAI;IACb,IAAIA,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAK,CAAC,EAAE;MACzBL,OAAO,IAAIA,OAAO,CAACI,GAAG,CAACE,IAAI,CAAC;IAC9B,CAAC,MAAM;MACLV,KAAK,CAACQ,GAAG,CAACG,GAAG,CAAC;IAChB;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASG,GAAGA,CAACZ,GAAG,EAAEQ,IAAI,EAAEN,OAAO,EAAE;EACtC,OAAOL,OAAO,CAAC;IACbG,GAAG,EAAEA,GAAG;IACRI,MAAM,EAAE,QAAQ;IAChBI;EACF,CAAC,CAAC,CAACH,IAAI,CAACC,GAAG,IAAI;IACb,IAAI,CAACA,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAK,CAAC,EAAE;MACjCL,OAAO,IAAIA,OAAO,CAACI,GAAG,CAACE,IAAI,CAAC;IAC9B,CAAC,MAAM;MACLV,KAAK,CAACQ,GAAG,CAACG,GAAG,CAAC;IAChB;EACF,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}