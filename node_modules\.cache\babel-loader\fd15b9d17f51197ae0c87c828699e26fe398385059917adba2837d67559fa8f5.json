{"ast": null, "code": "import { get, post, put, del } from \"../../util/requestUtils\";\nexport function getMemberList(param, success) {\n  return get(\"/member/list\", param, success);\n}\nexport function getMemberUnauditedList(param, success) {\n  return get(\"/member/unaudited/list\", param, success);\n}\nexport function sealMember(param, success) {\n  return get(\"/member/seal\", param, success);\n}\nexport function unsealMember(param, success) {\n  return get(\"/member/unseal\", param, success);\n}\nexport function addMemberLevel(data, success) {\n  return post(\"/member/level\", data, success);\n}\nexport function editMemberLevel(data, success) {\n  return put(\"/member/level\", data, success);\n}\nexport function getMemberLevel(id, success) {\n  return get(\"/member/level\", {\n    id: id\n  }, success);\n}\nexport function listMemberLevel() {\n  return \"member/level/list\";\n}\nexport function removeMemberLevel(id, success) {\n  const data = {\n    id: id\n  };\n  return del(\"/member/level\", data, success);\n}\nexport function getListByIds(params, success) {\n  return get(\"/member/public-api/by-ids\", params, success);\n}", "map": {"version": 3, "names": ["get", "post", "put", "del", "getMemberList", "param", "success", "getMemberUnauditedList", "sealMember", "unsealMember", "addMemberLevel", "data", "editMemberLevel", "getMemberLevel", "id", "listMemberLevel", "removeMemberLevel", "getListByIds", "params"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/api/member/index.js"], "sourcesContent": ["import { get, post, put, del } from \"../../util/requestUtils\"\n\nexport function getMemberList(param, success) {\n  return get(\"/member/list\", param, success)\n}\n\nexport function getMemberUnauditedList(param, success) {\n  return get(\"/member/unaudited/list\", param, success)\n}\n\nexport function sealMember(param, success) {\n  return get(\"/member/seal\", param, success)\n}\n\nexport function unsealMember(param, success) {\n  return get(\"/member/unseal\", param, success)\n}\n\nexport function addMemberLevel(data, success) {\n  return post(\"/member/level\", data, success)\n}\n\nexport function editMemberLevel(data, success) {\n  return put(\"/member/level\", data, success)\n}\n\nexport function getMemberLevel(id, success) {\n  return get(\"/member/level\", { id: id }, success)\n}\n\nexport function listMemberLevel() {\n  return \"member/level/list\"\n}\n\nexport function removeMemberLevel(id, success) {\n  const data = { id: id }\n  return del(\"/member/level\", data, success)\n}\n\nexport function getListByIds(params, success) {\n  return get(\"/member/public-api/by-ids\", params, success)\n}\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAQ,yBAAyB;AAE7D,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC5C,OAAON,GAAG,CAAC,cAAc,EAAEK,KAAK,EAAEC,OAAO,CAAC;AAC5C;AAEA,OAAO,SAASC,sBAAsBA,CAACF,KAAK,EAAEC,OAAO,EAAE;EACrD,OAAON,GAAG,CAAC,wBAAwB,EAAEK,KAAK,EAAEC,OAAO,CAAC;AACtD;AAEA,OAAO,SAASE,UAAUA,CAACH,KAAK,EAAEC,OAAO,EAAE;EACzC,OAAON,GAAG,CAAC,cAAc,EAAEK,KAAK,EAAEC,OAAO,CAAC;AAC5C;AAEA,OAAO,SAASG,YAAYA,CAACJ,KAAK,EAAEC,OAAO,EAAE;EAC3C,OAAON,GAAG,CAAC,gBAAgB,EAAEK,KAAK,EAAEC,OAAO,CAAC;AAC9C;AAEA,OAAO,SAASI,cAAcA,CAACC,IAAI,EAAEL,OAAO,EAAE;EAC5C,OAAOL,IAAI,CAAC,eAAe,EAAEU,IAAI,EAAEL,OAAO,CAAC;AAC7C;AAEA,OAAO,SAASM,eAAeA,CAACD,IAAI,EAAEL,OAAO,EAAE;EAC7C,OAAOJ,GAAG,CAAC,eAAe,EAAES,IAAI,EAAEL,OAAO,CAAC;AAC5C;AAEA,OAAO,SAASO,cAAcA,CAACC,EAAE,EAAER,OAAO,EAAE;EAC1C,OAAON,GAAG,CAAC,eAAe,EAAE;IAAEc,EAAE,EAAEA;EAAG,CAAC,EAAER,OAAO,CAAC;AAClD;AAEA,OAAO,SAASS,eAAeA,CAAA,EAAG;EAChC,OAAO,mBAAmB;AAC5B;AAEA,OAAO,SAASC,iBAAiBA,CAACF,EAAE,EAAER,OAAO,EAAE;EAC7C,MAAMK,IAAI,GAAG;IAAEG,EAAE,EAAEA;EAAG,CAAC;EACvB,OAAOX,GAAG,CAAC,eAAe,EAAEQ,IAAI,EAAEL,OAAO,CAAC;AAC5C;AAEA,OAAO,SAASW,YAAYA,CAACC,MAAM,EAAEZ,OAAO,EAAE;EAC5C,OAAON,GAAG,CAAC,2BAA2B,EAAEkB,MAAM,EAAEZ,OAAO,CAAC;AAC1D"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}