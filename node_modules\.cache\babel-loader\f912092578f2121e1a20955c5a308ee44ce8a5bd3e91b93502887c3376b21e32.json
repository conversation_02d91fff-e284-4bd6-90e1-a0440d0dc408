{"ast": null, "code": "import { ref } from \"vue\";\nimport Page from \"@/components/Page\";\nimport { validCertificate, suspendedCertificate, cancelledCertificate, expiredCertificate, deleteCertificate, revokedCertificate, findCertificateList } from \"@/api/certificate\";\nimport { confirm, success } from \"@/util/tipsUtils\";\nimport CertificatePreview from \"@/views/certificate/preview/index.vue\";\nexport default {\n  name: \"CertificateIndex\",\n  components: {\n    CertificatePreview,\n    Page\n  },\n  setup() {\n    const dataLoading = ref(true);\n    const certificateList = ref([]);\n    const params = ref({\n      current: 1,\n      size: 20,\n      neqStatusList: [\"deleted\"]\n    });\n    const loadList = () => {\n      findCertificateList(params.value, res => {\n        console.log(res);\n        if (res) {\n          total.value = res.total;\n          certificateList.value = res.list;\n        }\n        dataLoading.value = false;\n      }).catch(() => {\n        dataLoading.value = false;\n      });\n    };\n    loadList();\n    const total = ref(0);\n    const currentChange = c => {\n      params.value.current = c;\n      loadList();\n    };\n    const sizeChange = s => {\n      params.value.size = s;\n      loadList();\n    };\n    const search = () => {\n      loadList();\n    };\n    const remove = id => {\n      confirm(\"确认删除该证书模版？\", \"提示\", () => {\n        deleteCertificate(id, () => {\n          success(\"删除成功\");\n          loadList();\n        });\n      });\n    };\n    const valid = id => {\n      confirm(\"确认有效该证书模版？\", \"提示\", () => {\n        validCertificate({\n          id: id\n        }, () => {\n          success(\"有效成功\");\n          loadList();\n        });\n      });\n    };\n    const suspended = id => {\n      confirm(\"确认暂停该证书模版？\", \"提示\", () => {\n        suspendedCertificate({\n          id: id\n        }, () => {\n          success(\"暂停成功\");\n          loadList();\n        });\n      });\n    };\n    const revoked = id => {\n      confirm(\"确认撤销该证书模版？\", \"提示\", () => {\n        revokedCertificate({\n          id: id\n        }, () => {\n          success(\"撤销成功\");\n          loadList();\n        });\n      });\n    };\n    const cancelled = id => {\n      confirm(\"确认注销该证书模版？\", \"提示\", () => {\n        cancelledCertificate({\n          id: id\n        }, () => {\n          success(\"注销成功\");\n          loadList();\n        });\n      });\n    };\n    const expired = id => {\n      confirm(\"确认失效该证书模版？\", \"提示\", () => {\n        expiredCertificate({\n          id: id\n        }, () => {\n          success(\"失效成功\");\n          loadList();\n        });\n      });\n    };\n    const previewCertificate = ref({});\n    const showPreviewViewFlag = ref(false);\n    const showPreview = item => {\n      showPreviewViewFlag.value = true;\n      previewCertificate.value = item;\n    };\n    const hidePreview = () => {\n      showPreviewViewFlag.value = false;\n    };\n    return {\n      previewCertificate,\n      showPreviewViewFlag,\n      showPreview,\n      hidePreview,\n      dataLoading,\n      search,\n      params,\n      total,\n      currentChange,\n      sizeChange,\n      certificateList,\n      valid,\n      suspended,\n      revoked,\n      cancelled,\n      expired,\n      remove\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "Page", "validCertificate", "suspendedCertificate", "cancelledCertificate", "expiredCertificate", "deleteCertificate", "revokedCertificate", "findCertificateList", "confirm", "success", "CertificatePreview", "name", "components", "setup", "dataLoading", "certificateList", "params", "current", "size", "neqStatusList", "loadList", "value", "res", "console", "log", "total", "list", "catch", "currentChange", "c", "sizeChange", "s", "search", "remove", "id", "valid", "suspended", "revoked", "cancelled", "expired", "previewCertificate", "showPreviewViewFlag", "showPreview", "item", "hidePreview"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/certificate/index.vue"], "sourcesContent": ["<template>\n  <div class=\"cert-wrap\">\n    <div class=\"cert-header\">\n      <el-form :inline=\"true\" :model=\"params\" class=\"form-inline\">\n        <el-form-item label=\"证书名称\">\n          <el-input size=\"small\" @keydown.enter=\"search\" class=\"search-input\" v-model=\"params.name\" placeholder=\"请输入证书名称\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"证书编号\">\n          <el-input size=\"small\" @keydown.enter=\"search\" class=\"search-input\" v-model=\"params.code\" placeholder=\"请输入证书编号\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"状态\" class=\"select\">\n          <el-select size=\"small\" v-model=\"params.status\" @change=\"search\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"有效\" value=\"valid\"></el-option>\n            <el-option label=\"暂停\" value=\"suspended\"></el-option>\n            <el-option label=\"撤销\" value=\"revoked\"></el-option>\n            <el-option label=\"注销\" value=\"cancelled\"></el-option>\n            <el-option label=\"失效\" value=\"expired\"></el-option>\n            <el-option label=\"删除\" value=\"deleted\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" @click=\"search()\">\n            <span style=\"vertical-align: middle\">搜索</span>\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"cert-main\" v-loading=\"dataLoading\">\n      <el-table :data=\"certificateList\">\n        <el-table-column label=\"序号\" type=\"index\"></el-table-column>\n        <el-table-column label=\"证书编码\" prop=\"code\"></el-table-column>\n        <el-table-column label=\"会员名称\" prop=\"member.name\"></el-table-column>\n        <el-table-column label=\"关联课程\" prop=\"lesson.name\"></el-table-column>\n        <el-table-column label=\"证书名称\" prop=\"name\"></el-table-column>\n<!--        <el-table-column label=\"证书描述\" prop=\"description\"></el-table-column>-->\n        <el-table-column label=\"颁发机构\" prop=\"awardingOrganization\"></el-table-column>\n        <el-table-column label=\"颁发日期\" prop=\"awardDate\"></el-table-column>\n<!--        <el-table-column label=\"颁发人员\" prop=\"awarderName\"></el-table-column>-->\n<!--        <el-table-column label=\"颁发条件\" prop=\"awardConditions\"></el-table-column>-->\n<!--        <el-table-column label=\"到期策略\" prop=\"validityPolicy\"></el-table-column>-->\n        <el-table-column label=\"状态\" prop=\"statusName\"></el-table-column>\n        <el-table-column label=\"操作\">\n          <template #default=\"scope\">\n            <div class=\"opt-btn-wrap\">\n              <div class=\"opt-btn-item\">\n                <el-button size=\"small\" @click=\"showPreview(scope.row)\">预览</el-button>\n              </div>\n              <div class=\"opt-btn-item\" v-if=\"scope.row.status !== 'valid'\">\n                <el-button size=\"small\" type=\"success\" @click=\"valid(scope.row.id)\">有效</el-button>\n              </div>\n              <div class=\"opt-btn-item\" v-if=\"scope.row.status !== 'suspended'\">\n                <el-button size=\"small\" type=\"info\" @click=\"suspended(scope.row.id)\">暂停</el-button>\n              </div>\n              <div class=\"opt-btn-item\" v-if=\"scope.row.status !== 'revoked'\">\n                <el-button size=\"small\" type=\"info\" @click=\"revoked(scope.row.id)\">撤销</el-button>\n              </div>\n              <div class=\"opt-btn-item\" v-if=\"scope.row.status !== 'cancelled'\">\n                <el-button size=\"small\" type=\"warning\" @click=\"cancelled(scope.row.id)\">注销</el-button>\n              </div>\n              <div class=\"opt-btn-item\" v-if=\"scope.row.status !== 'expired'\">\n                <el-button size=\"small\" type=\"warning\" @click=\"expired(scope.row.id)\">失效</el-button>\n              </div>\n              <div class=\"opt-btn-item\" v-if=\"scope.row.status !== 'deleted'\">\n                <el-button size=\"small\" type=\"danger\" @click=\"remove(scope.row.id)\">删除</el-button>\n              </div>\n            </div>\n          </template>\n        </el-table-column>\n      </el-table>\n      <page :total=\"total\" :size-change=\"sizeChange\" :current-change=\"currentChange\" :page-size=\"params.size\"/>\n    </div>\n    <el-dialog style=\"min-width: 840px\" title=\"证书预览\" v-model=\"showPreviewViewFlag\" :before-close=\"hidePreview\">\n      <div>\n        <certificate-preview :download=\"false\" :certificate=\"previewCertificate\" />\n      </div>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button size=\"small\" @click=\"hidePreview\">取 消</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {ref} from \"vue\"\nimport Page from \"@/components/Page\";\nimport {\n  validCertificate,\n  suspendedCertificate,\n  cancelledCertificate,\n  expiredCertificate,\n  deleteCertificate,\n  revokedCertificate,\n  findCertificateList\n} from \"@/api/certificate\";\nimport {confirm, success} from \"@/util/tipsUtils\";\nimport CertificatePreview from \"@/views/certificate/preview/index.vue\";\nexport default {\n  name: \"CertificateIndex\",\n  components: {CertificatePreview, Page},\n  setup() {\n    const dataLoading = ref(true)\n    const certificateList = ref([])\n    const params = ref({\n      current: 1,\n      size: 20,\n      neqStatusList: [\"deleted\"]\n    })\n    const loadList = () => {\n      findCertificateList(params.value, res => {\n        console.log(res)\n        if (res) {\n          total.value = res.total;\n          certificateList.value = res.list;\n        }\n        dataLoading.value = false\n      }).catch(() => {\n        dataLoading.value = false\n      })\n    }\n    loadList()\n    const total = ref(0)\n    const currentChange = (c) => {\n      params.value.current = c;\n      loadList();\n    }\n    const sizeChange = (s) => {\n      params.value.size = s;\n      loadList();\n    }\n    const search = () => {\n      loadList();\n    }\n    const remove = (id) => {\n      confirm(\"确认删除该证书模版？\", \"提示\", () => {\n        deleteCertificate(id, () => {\n          success(\"删除成功\");\n          loadList();\n        })\n      })\n    }\n\n    const valid = (id) => {\n      confirm(\"确认有效该证书模版？\", \"提示\", () => {\n        validCertificate({id: id}, () => {\n          success(\"有效成功\");\n          loadList();\n        })\n      })\n    }\n    const suspended = (id) => {\n      confirm(\"确认暂停该证书模版？\", \"提示\", () => {\n        suspendedCertificate({id: id}, () => {\n          success(\"暂停成功\");\n          loadList();\n        })\n      })\n    }\n    const revoked = (id) => {\n      confirm(\"确认撤销该证书模版？\", \"提示\", () => {\n        revokedCertificate({id: id}, () => {\n          success(\"撤销成功\");\n          loadList();\n        })\n      })\n    }\n    const cancelled = (id) => {\n      confirm(\"确认注销该证书模版？\", \"提示\", () => {\n        cancelledCertificate({id: id}, () => {\n          success(\"注销成功\");\n          loadList();\n        })\n      })\n    }\n    const expired = (id) => {\n      confirm(\"确认失效该证书模版？\", \"提示\", () => {\n        expiredCertificate({id: id}, () => {\n          success(\"失效成功\");\n          loadList();\n        })\n      })\n    }\n\n    const previewCertificate = ref({})\n    const showPreviewViewFlag = ref(false);\n    const showPreview = (item) => {\n      showPreviewViewFlag.value = true;\n      previewCertificate.value = item\n    }\n    const hidePreview = () => {\n      showPreviewViewFlag.value = false;\n    }\n\n    return {\n      previewCertificate,\n      showPreviewViewFlag,\n      showPreview,\n      hidePreview,\n      dataLoading,\n      search,\n      params,\n      total,\n      currentChange,\n      sizeChange,\n      certificateList,\n      valid,\n      suspended,\n      revoked,\n      cancelled,\n      expired,\n      remove\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n  .cert-wrap {\n    margin: 20px;\n    font-size: 12px;\n    .cert-main {\n      ::v-deep .el-table {\n        font-size: 12px;\n        .el-table__empty-block {\n          line-height: 400px;\n          .el-table__empty-text {\n            line-height: 400px;\n          }\n        }\n        th, td {\n          padding: 6px 0;\n        }\n      }\n    }\n  }\n  .opt-btn-wrap {\n    //display: flex;\n  }\n  .opt-btn-item {\n    width: 50%;\n    display: inline-block;\n    margin: 2px 0;\n  }\n</style>\n"], "mappings": "AAsFA,SAAQA,GAAG,QAAO,KAAI;AACtB,OAAOC,IAAG,MAAO,mBAAmB;AACpC,SACEC,gBAAgB,EAChBC,oBAAoB,EACpBC,oBAAoB,EACpBC,kBAAkB,EAClBC,iBAAiB,EACjBC,kBAAkB,EAClBC,mBAAkB,QACb,mBAAmB;AAC1B,SAAQC,OAAO,EAAEC,OAAO,QAAO,kBAAkB;AACjD,OAAOC,kBAAiB,MAAO,uCAAuC;AACtE,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,UAAU,EAAE;IAACF,kBAAkB;IAAEV;EAAI,CAAC;EACtCa,KAAKA,CAAA,EAAG;IACN,MAAMC,WAAU,GAAIf,GAAG,CAAC,IAAI;IAC5B,MAAMgB,eAAc,GAAIhB,GAAG,CAAC,EAAE;IAC9B,MAAMiB,MAAK,GAAIjB,GAAG,CAAC;MACjBkB,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,aAAa,EAAE,CAAC,SAAS;IAC3B,CAAC;IACD,MAAMC,QAAO,GAAIA,CAAA,KAAM;MACrBb,mBAAmB,CAACS,MAAM,CAACK,KAAK,EAAEC,GAAE,IAAK;QACvCC,OAAO,CAACC,GAAG,CAACF,GAAG;QACf,IAAIA,GAAG,EAAE;UACPG,KAAK,CAACJ,KAAI,GAAIC,GAAG,CAACG,KAAK;UACvBV,eAAe,CAACM,KAAI,GAAIC,GAAG,CAACI,IAAI;QAClC;QACAZ,WAAW,CAACO,KAAI,GAAI,KAAI;MAC1B,CAAC,CAAC,CAACM,KAAK,CAAC,MAAM;QACbb,WAAW,CAACO,KAAI,GAAI,KAAI;MAC1B,CAAC;IACH;IACAD,QAAQ,EAAC;IACT,MAAMK,KAAI,GAAI1B,GAAG,CAAC,CAAC;IACnB,MAAM6B,aAAY,GAAKC,CAAC,IAAK;MAC3Bb,MAAM,CAACK,KAAK,CAACJ,OAAM,GAAIY,CAAC;MACxBT,QAAQ,EAAE;IACZ;IACA,MAAMU,UAAS,GAAKC,CAAC,IAAK;MACxBf,MAAM,CAACK,KAAK,CAACH,IAAG,GAAIa,CAAC;MACrBX,QAAQ,EAAE;IACZ;IACA,MAAMY,MAAK,GAAIA,CAAA,KAAM;MACnBZ,QAAQ,EAAE;IACZ;IACA,MAAMa,MAAK,GAAKC,EAAE,IAAK;MACrB1B,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM;QAChCH,iBAAiB,CAAC6B,EAAE,EAAE,MAAM;UAC1BzB,OAAO,CAAC,MAAM,CAAC;UACfW,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC;IACH;IAEA,MAAMe,KAAI,GAAKD,EAAE,IAAK;MACpB1B,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM;QAChCP,gBAAgB,CAAC;UAACiC,EAAE,EAAEA;QAAE,CAAC,EAAE,MAAM;UAC/BzB,OAAO,CAAC,MAAM,CAAC;UACfW,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC;IACH;IACA,MAAMgB,SAAQ,GAAKF,EAAE,IAAK;MACxB1B,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM;QAChCN,oBAAoB,CAAC;UAACgC,EAAE,EAAEA;QAAE,CAAC,EAAE,MAAM;UACnCzB,OAAO,CAAC,MAAM,CAAC;UACfW,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC;IACH;IACA,MAAMiB,OAAM,GAAKH,EAAE,IAAK;MACtB1B,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM;QAChCF,kBAAkB,CAAC;UAAC4B,EAAE,EAAEA;QAAE,CAAC,EAAE,MAAM;UACjCzB,OAAO,CAAC,MAAM,CAAC;UACfW,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC;IACH;IACA,MAAMkB,SAAQ,GAAKJ,EAAE,IAAK;MACxB1B,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM;QAChCL,oBAAoB,CAAC;UAAC+B,EAAE,EAAEA;QAAE,CAAC,EAAE,MAAM;UACnCzB,OAAO,CAAC,MAAM,CAAC;UACfW,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC;IACH;IACA,MAAMmB,OAAM,GAAKL,EAAE,IAAK;MACtB1B,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM;QAChCJ,kBAAkB,CAAC;UAAC8B,EAAE,EAAEA;QAAE,CAAC,EAAE,MAAM;UACjCzB,OAAO,CAAC,MAAM,CAAC;UACfW,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC;IACH;IAEA,MAAMoB,kBAAiB,GAAIzC,GAAG,CAAC,CAAC,CAAC;IACjC,MAAM0C,mBAAkB,GAAI1C,GAAG,CAAC,KAAK,CAAC;IACtC,MAAM2C,WAAU,GAAKC,IAAI,IAAK;MAC5BF,mBAAmB,CAACpB,KAAI,GAAI,IAAI;MAChCmB,kBAAkB,CAACnB,KAAI,GAAIsB,IAAG;IAChC;IACA,MAAMC,WAAU,GAAIA,CAAA,KAAM;MACxBH,mBAAmB,CAACpB,KAAI,GAAI,KAAK;IACnC;IAEA,OAAO;MACLmB,kBAAkB;MAClBC,mBAAmB;MACnBC,WAAW;MACXE,WAAW;MACX9B,WAAW;MACXkB,MAAM;MACNhB,MAAM;MACNS,KAAK;MACLG,aAAa;MACbE,UAAU;MACVf,eAAe;MACfoB,KAAK;MACLC,SAAS;MACTC,OAAO;MACPC,SAAS;MACTC,OAAO;MACPN;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}