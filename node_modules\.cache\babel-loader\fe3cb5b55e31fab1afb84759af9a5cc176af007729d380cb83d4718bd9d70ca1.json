{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_tree = _resolveComponent(\"el-tree\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createVNode(_component_el_input, {\n    size: \"mini\",\n    placeholder: \"输入关键字进行过滤\",\n    modelValue: $setup.filterText,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.filterText = $event)\n  }, null, 8, [\"modelValue\"]), $setup.treeFlag ? (_openBlock(), _createBlock(_component_el_tree, {\n    key: 0,\n    size: \"mini\",\n    ref: \"treeRef\",\n    \"current-node-key\": $setup.nodeKey,\n    \"node-key\": \"id\",\n    \"filter-node-method\": $setup.filterNode,\n    \"highlight-current\": true,\n    data: $setup.treeData,\n    props: $setup.defaultProps,\n    \"expand-on-click-node\": false,\n    onNodeClick: $setup.handleNodeClick,\n    class: \"el-tree\"\n  }, null, 8, [\"current-node-key\", \"filter-node-method\", \"data\", \"props\", \"onNodeClick\"])) : _createCommentVNode(\"\", true)]);\n}", "map": {"version": 3, "names": ["_createElementBlock", "_createVNode", "_component_el_input", "size", "placeholder", "$setup", "filterText", "$event", "treeFlag", "_createBlock", "_component_el_tree", "ref", "nodeKey", "filterNode", "data", "treeData", "props", "defaultProps", "onNodeClick", "handleNodeClick", "class"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/live/category/tree.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-input size=\"mini\" placeholder=\"输入关键字进行过滤\" v-model=\"filterText\"></el-input>\n    <el-tree size=\"mini\" ref=\"treeRef\" v-if=\"treeFlag\" :current-node-key=\"nodeKey\" node-key=\"id\" :filter-node-method=\"filterNode\" :highlight-current=\"true\" :data=\"treeData\" :props=\"defaultProps\" :expand-on-click-node=\"false\" @node-click=\"handleNodeClick\" class=\"el-tree\"></el-tree>\n  </div>\n</template>\n\n<script>\n// 目录API\nimport { findCategoryList } from \"../../../api/live/category\"\nimport {ref, watch, nextTick} from \"vue\";\nexport default {\n  name: \"categoryTree\",\n  props: {\n    currentNodeKey: Number\n  },\n  setup(props, context) {\n    const filterText = ref(\"\");\n    const defaultProps = {\n      children: \"children\",\n      label: \"name\"\n    }\n    const treeData = ref([])\n    let treeRef = ref(null);\n    watch([filterText], (nv) => {\n      treeRef.value.filter(nv);\n    })\n    const loadCategoryList = () => {\n      findCategoryList(0, true, res => {\n        // 获取部门列表中的根节点（父节点id为0的）（获取的根节点包含孩子）\n        function getRootNodes(nodeList) {\n          if (!nodeList || nodeList.length <= 0) {\n            return [];\n          }\n          // 递归获取节点的孩子节点\n          const getChildren = function(parent) {\n            const children = [];\n            for (let i = 0; i < nodeList.length; i++) {\n              const item = nodeList[i];\n              if (item.pid === parent.id) {\n                children.push(item);\n              }\n            }\n            parent.children = children\n            if (children.length === 0) {\n              return;\n            }\n            for (let i = 0; i < children.length; i++) {\n              getChildren(children[i]);\n            }\n          }\n          const result = [];\n          for (let i = 0; i < nodeList.length; i++) {\n            const item = nodeList[i];\n            if (item.pid === 0 || item.pid === null) {\n              result.push(item);\n              getChildren(item);\n            }\n          }\n          return result;\n        }\n        treeData.value = getRootNodes(res);\n        console.log(treeData.value)\n      })\n    }\n    loadCategoryList()\n    let nodeKey = ref(props.currentNodeKey)\n    const treeFlag = ref(true)\n    watch(() => props.currentNodeKey, (nv) => {\n      nodeKey.value = nv\n      treeFlag.value =false\n      nextTick(() => {\n        treeFlag.value =true\n      })\n      loadCategoryList()\n    })\n    const filterNode = function(value, data, node) {\n      console.log(node)\n      if (!value) {\n        return true;\n      }\n      return data.name.indexOf(value) !== -1;\n    }\n    const handleNodeClick = (data) => {\n      context.emit(\"node-click\", data, this);\n    }\n    return {\n      treeFlag,\n      nodeKey,\n      filterText,\n      defaultProps,\n      treeData,\n      treeRef,\n      filterNode,\n      handleNodeClick\n    }\n  }\n}\n</script>\n<style scoped>\n  .el-tree {\n    margin-top: 10px;\n    min-height: 102px;\n  }\n</style>\n"], "mappings": ";;;;uBACEA,mBAAA,CAGM,cAFJC,YAAA,CAA8EC,mBAAA;IAApEC,IAAI,EAAC,MAAM;IAACC,WAAW,EAAC,WAAW;gBAAUC,MAAA,CAAAC,UAAU;+D<PERSON><PERSON>,MAAA,CAAAC,UAAU,GAAAC,MAAA;+BACxBF,MAAA,CAAAG,QAAQ,I,cAAjDC,YAAA,CAAqRC,kBAAA;;IAA5QP,IAAI,EAAC,MAAM;IAACQ,GAAG,EAAC,SAAS;IAAkB,kBAAgB,EAAEN,MAAA,CAAAO,OAAO;IAAE,UAAQ,EAAC,IAAI;IAAE,oBAAkB,EAAEP,MAAA,CAAAQ,UAAU;IAAG,mBAAiB,EAAE,IAAI;IAAGC,IAAI,EAAET,MAAA,CAAAU,QAAQ;IAAGC,KAAK,EAAEX,MAAA,CAAAY,YAAY;IAAG,sBAAoB,EAAE,KAAK;IAAGC,WAAU,EAAEb,MAAA,CAAAc,eAAe;IAAEC,KAAK,EAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}