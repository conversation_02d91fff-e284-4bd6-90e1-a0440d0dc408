{"ast": null, "code": "import { get, post, put, del } from \"@/util/requestUtils\";\nexport function findList(params, success) {\n  return get(\"/learn/lesson/list\", params, success);\n}\nexport function saveBaseInfo(params, success) {\n  return post(\"/learn/lesson\", params, success);\n}\nexport function updateBaseInfo(params, success) {\n  return put(\"/learn/lesson\", params, success);\n}\nexport function updateLearnExamPaper(params, success) {\n  return put(\"/learn/lesson/exampaper\", params, success);\n}\nexport function updateLearnCertificate(params, success) {\n  return put(\"/learn/lesson/certificate\", params, success);\n}\nexport function getBaseInfo(id, success) {\n  return get(\"/learn/lesson\", {\n    id: id\n  }, success);\n}\nexport function removeLesson(data, success) {\n  return del(\"/learn/lesson\", data, success);\n}\nexport function publishLesson(params, success) {\n  return put(\"/learn/lesson/publish\", params, success);\n}\nexport function unPublishLesson(params, success) {\n  return put(\"/learn/lesson/un-publish\", params, success);\n}\nexport function saveLessonChapter(params, success) {\n  return post(\"/learn/lesson/chapter\", params, success);\n}\nexport function updateLessonChapter(params, success) {\n  return put(\"/learn/lesson/chapter\", params, success);\n}\nexport function deleteLessonChapter(params, success) {\n  return del(\"/learn/lesson/chapter\", params, success);\n}\nexport function getLessonChapterList(params, success) {\n  return get(\"/learn/lesson/chapter/list\", params, success);\n}\nexport function updateSortOrder(data, success) {\n  return put(\"/learn/lesson/chapter/sort-order\", data, success);\n}\nexport function saveLessonChapterSection(params, success) {\n  return post(\"/learn/lesson/chapter-section\", params, success);\n}\nexport function updateLessonChapterSection(params, success) {\n  return put(\"/learn/lesson/chapter-section\", params, success);\n}\nexport function deleteLessonChapterSection(params, success) {\n  return del(\"/learn/lesson/chapter-section\", params, success);\n}\nexport function getSignUpList(params, success) {\n  return get(\"/learn/sign-up/list\", params, success);\n}\nexport function getLessonListByIds(params, success) {\n  return get(\"/learn/public-api/lesson/list/by-ids\", params, success);\n}\nexport function saveHomework(data, success) {\n  return post(\"/learn/lesson/homework\", data, success);\n}\nexport function updateHomework(data, success) {\n  return put(\"/learn/lesson/homework\", data, success);\n}\nexport function getHomework(params, success) {\n  return get(\"/learn/lesson/homework\", params, success);\n}\nexport function getLessonSignReport(params, success) {\n  return get(\"/learn/report/lesson/sign\", params, success);\n}\nexport function getLessonStudyReport(params, success) {\n  return get(\"/learn/report/lesson/study\", params, success);\n}\nexport function getMemberStudyReport(params, success) {\n  return get(\"/learn/report/member/study\", params, success);\n}\nexport function getLearnExamPaper(params, success) {\n  return get(\"/exam/auth-api/paper\", params, success);\n}\nexport function batchSignUp(data, success) {\n  return post(\"/learn/auth-api/sign-up/batch\", data, success);\n}\nexport function getCompanyStudyReport(params, success) {\n  return get(\"/learn/report/company/member/signup\", params, success);\n}", "map": {"version": 3, "names": ["get", "post", "put", "del", "findList", "params", "success", "saveBaseInfo", "updateBaseInfo", "updateLearnExamPaper", "updateLearnCertificate", "getBaseInfo", "id", "<PERSON><PERSON><PERSON><PERSON>", "data", "<PERSON><PERSON><PERSON><PERSON>", "unPublish<PERSON><PERSON><PERSON>", "saveLessonChapter", "updateLessonChapter", "delete<PERSON>esson<PERSON>hapter", "getLessonChapterList", "updateSortOrder", "saveLessonChapterSection", "updateLessonChapterSection", "deleteLessonChapterSection", "getSignUpList", "getLessonListByIds", "saveHomework", "updateHomework", "getHomework", "getLessonSignReport", "getLessonStudyReport", "getMemberStudyReport", "getLearnExamPaper", "batchSignUp", "getCompanyStudyReport"], "sources": ["D:/sourcecodeAndDocument/learning-platform/admin/src/api/learn/lesson.js"], "sourcesContent": ["import { get, post, put, del } from \"@/util/requestUtils\"\n\nexport function findList(params, success) {\n  return get(\"/learn/lesson/list\", params, success)\n}\n\nexport function saveBaseInfo(params, success) {\n  return post(\"/learn/lesson\", params, success)\n}\n\nexport function updateBaseInfo(params, success) {\n  return put(\"/learn/lesson\", params, success)\n}\n\nexport function updateLearnExamPaper(params, success) {\n  return put(\"/learn/lesson/exampaper\", params, success)\n}\n\nexport function updateLearnCertificate(params, success) {\n  return put(\"/learn/lesson/certificate\", params, success)\n}\n\nexport function getBaseInfo(id, success) {\n  return get(\"/learn/lesson\", { id: id }, success)\n}\n\nexport function removeLesson(data, success) {\n  return del(\"/learn/lesson\", data, success)\n}\n\nexport function publishLesson(params, success) {\n  return put(\"/learn/lesson/publish\", params, success)\n}\n\nexport function unPublishLesson(params, success) {\n  return put(\"/learn/lesson/un-publish\", params, success)\n}\n\nexport function saveLessonChapter(params, success) {\n  return post(\"/learn/lesson/chapter\", params, success)\n}\n\nexport function updateLessonChapter(params, success) {\n  return put(\"/learn/lesson/chapter\", params, success)\n}\n\nexport function deleteLessonChapter(params, success) {\n  return del(\"/learn/lesson/chapter\", params, success)\n}\n\nexport function getLessonChapterList(params, success) {\n  return get(\"/learn/lesson/chapter/list\", params, success)\n}\n\nexport function updateSortOrder(data, success) {\n  return put(\"/learn/lesson/chapter/sort-order\", data, success)\n}\n\nexport function saveLessonChapterSection(params, success) {\n  return post(\"/learn/lesson/chapter-section\", params, success)\n}\n\nexport function updateLessonChapterSection(params, success) {\n  return put(\"/learn/lesson/chapter-section\", params, success)\n}\n\nexport function deleteLessonChapterSection(params, success) {\n  return del(\"/learn/lesson/chapter-section\", params, success)\n}\n\nexport function getSignUpList(params, success) {\n  return get(\"/learn/sign-up/list\", params, success)\n}\n\nexport function getLessonListByIds(params, success) {\n  return get(\"/learn/public-api/lesson/list/by-ids\", params, success)\n}\n\nexport function saveHomework(data, success) {\n  return post(\"/learn/lesson/homework\", data, success)\n}\n\nexport function updateHomework(data, success) {\n  return put(\"/learn/lesson/homework\", data, success)\n}\n\nexport function getHomework(params, success) {\n  return get(\"/learn/lesson/homework\", params, success)\n}\n\nexport function getLessonSignReport(params, success) {\n  return get(\"/learn/report/lesson/sign\", params, success)\n}\n\nexport function getLessonStudyReport(params, success) {\n  return get(\"/learn/report/lesson/study\", params, success)\n}\n\nexport function getMemberStudyReport(params, success) {\n  return get(\"/learn/report/member/study\", params, success)\n}\n\nexport function getLearnExamPaper(params, success) {\n  return get(\"/exam/auth-api/paper\", params, success)\n}\n\nexport function batchSignUp(data, success) {\n  return post(\"/learn/auth-api/sign-up/batch\", data, success)\n}\n\nexport function getCompanyStudyReport(params, success) {\n  return get(\"/learn/report/company/member/signup\", params, success)\n}"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAQ,qBAAqB;AAEzD,OAAO,SAASC,QAAQA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACxC,OAAON,GAAG,CAAC,oBAAoB,EAAEK,MAAM,EAAEC,OAAO,CAAC;AACnD;AAEA,OAAO,SAASC,YAAYA,CAACF,MAAM,EAAEC,OAAO,EAAE;EAC5C,OAAOL,IAAI,CAAC,eAAe,EAAEI,MAAM,EAAEC,OAAO,CAAC;AAC/C;AAEA,OAAO,SAASE,cAAcA,CAACH,MAAM,EAAEC,OAAO,EAAE;EAC9C,OAAOJ,GAAG,CAAC,eAAe,EAAEG,MAAM,EAAEC,OAAO,CAAC;AAC9C;AAEA,OAAO,SAASG,oBAAoBA,CAACJ,MAAM,EAAEC,OAAO,EAAE;EACpD,OAAOJ,GAAG,CAAC,yBAAyB,EAAEG,MAAM,EAAEC,OAAO,CAAC;AACxD;AAEA,OAAO,SAASI,sBAAsBA,CAACL,MAAM,EAAEC,OAAO,EAAE;EACtD,OAAOJ,GAAG,CAAC,2BAA2B,EAAEG,MAAM,EAAEC,OAAO,CAAC;AAC1D;AAEA,OAAO,SAASK,WAAWA,CAACC,EAAE,EAAEN,OAAO,EAAE;EACvC,OAAON,GAAG,CAAC,eAAe,EAAE;IAAEY,EAAE,EAAEA;EAAG,CAAC,EAAEN,OAAO,CAAC;AAClD;AAEA,OAAO,SAASO,YAAYA,CAACC,IAAI,EAAER,OAAO,EAAE;EAC1C,OAAOH,GAAG,CAAC,eAAe,EAAEW,IAAI,EAAER,OAAO,CAAC;AAC5C;AAEA,OAAO,SAASS,aAAaA,CAACV,MAAM,EAAEC,OAAO,EAAE;EAC7C,OAAOJ,GAAG,CAAC,uBAAuB,EAAEG,MAAM,EAAEC,OAAO,CAAC;AACtD;AAEA,OAAO,SAASU,eAAeA,CAACX,MAAM,EAAEC,OAAO,EAAE;EAC/C,OAAOJ,GAAG,CAAC,0BAA0B,EAAEG,MAAM,EAAEC,OAAO,CAAC;AACzD;AAEA,OAAO,SAASW,iBAAiBA,CAACZ,MAAM,EAAEC,OAAO,EAAE;EACjD,OAAOL,IAAI,CAAC,uBAAuB,EAAEI,MAAM,EAAEC,OAAO,CAAC;AACvD;AAEA,OAAO,SAASY,mBAAmBA,CAACb,MAAM,EAAEC,OAAO,EAAE;EACnD,OAAOJ,GAAG,CAAC,uBAAuB,EAAEG,MAAM,EAAEC,OAAO,CAAC;AACtD;AAEA,OAAO,SAASa,mBAAmBA,CAACd,MAAM,EAAEC,OAAO,EAAE;EACnD,OAAOH,GAAG,CAAC,uBAAuB,EAAEE,MAAM,EAAEC,OAAO,CAAC;AACtD;AAEA,OAAO,SAASc,oBAAoBA,CAACf,MAAM,EAAEC,OAAO,EAAE;EACpD,OAAON,GAAG,CAAC,4BAA4B,EAAEK,MAAM,EAAEC,OAAO,CAAC;AAC3D;AAEA,OAAO,SAASe,eAAeA,CAACP,IAAI,EAAER,OAAO,EAAE;EAC7C,OAAOJ,GAAG,CAAC,kCAAkC,EAAEY,IAAI,EAAER,OAAO,CAAC;AAC/D;AAEA,OAAO,SAASgB,wBAAwBA,CAACjB,MAAM,EAAEC,OAAO,EAAE;EACxD,OAAOL,IAAI,CAAC,+BAA+B,EAAEI,MAAM,EAAEC,OAAO,CAAC;AAC/D;AAEA,OAAO,SAASiB,0BAA0BA,CAAClB,MAAM,EAAEC,OAAO,EAAE;EAC1D,OAAOJ,GAAG,CAAC,+BAA+B,EAAEG,MAAM,EAAEC,OAAO,CAAC;AAC9D;AAEA,OAAO,SAASkB,0BAA0BA,CAACnB,MAAM,EAAEC,OAAO,EAAE;EAC1D,OAAOH,GAAG,CAAC,+BAA+B,EAAEE,MAAM,EAAEC,OAAO,CAAC;AAC9D;AAEA,OAAO,SAASmB,aAAaA,CAACpB,MAAM,EAAEC,OAAO,EAAE;EAC7C,OAAON,GAAG,CAAC,qBAAqB,EAAEK,MAAM,EAAEC,OAAO,CAAC;AACpD;AAEA,OAAO,SAASoB,kBAAkBA,CAACrB,MAAM,EAAEC,OAAO,EAAE;EAClD,OAAON,GAAG,CAAC,sCAAsC,EAAEK,MAAM,EAAEC,OAAO,CAAC;AACrE;AAEA,OAAO,SAASqB,YAAYA,CAACb,IAAI,EAAER,OAAO,EAAE;EAC1C,OAAOL,IAAI,CAAC,wBAAwB,EAAEa,IAAI,EAAER,OAAO,CAAC;AACtD;AAEA,OAAO,SAASsB,cAAcA,CAACd,IAAI,EAAER,OAAO,EAAE;EAC5C,OAAOJ,GAAG,CAAC,wBAAwB,EAAEY,IAAI,EAAER,OAAO,CAAC;AACrD;AAEA,OAAO,SAASuB,WAAWA,CAACxB,MAAM,EAAEC,OAAO,EAAE;EAC3C,OAAON,GAAG,CAAC,wBAAwB,EAAEK,MAAM,EAAEC,OAAO,CAAC;AACvD;AAEA,OAAO,SAASwB,mBAAmBA,CAACzB,MAAM,EAAEC,OAAO,EAAE;EACnD,OAAON,GAAG,CAAC,2BAA2B,EAAEK,MAAM,EAAEC,OAAO,CAAC;AAC1D;AAEA,OAAO,SAASyB,oBAAoBA,CAAC1B,MAAM,EAAEC,OAAO,EAAE;EACpD,OAAON,GAAG,CAAC,4BAA4B,EAAEK,MAAM,EAAEC,OAAO,CAAC;AAC3D;AAEA,OAAO,SAAS0B,oBAAoBA,CAAC3B,MAAM,EAAEC,OAAO,EAAE;EACpD,OAAON,GAAG,CAAC,4BAA4B,EAAEK,MAAM,EAAEC,OAAO,CAAC;AAC3D;AAEA,OAAO,SAAS2B,iBAAiBA,CAAC5B,MAAM,EAAEC,OAAO,EAAE;EACjD,OAAON,GAAG,CAAC,sBAAsB,EAAEK,MAAM,EAAEC,OAAO,CAAC;AACrD;AAEA,OAAO,SAAS4B,WAAWA,CAACpB,IAAI,EAAER,OAAO,EAAE;EACzC,OAAOL,IAAI,CAAC,+BAA+B,EAAEa,IAAI,EAAER,OAAO,CAAC;AAC7D;AAEA,OAAO,SAAS6B,qBAAqBA,CAAC9B,MAAM,EAAEC,OAAO,EAAE;EACrD,OAAON,GAAG,CAAC,qCAAqC,EAAEK,MAAM,EAAEC,OAAO,CAAC;AACpE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}