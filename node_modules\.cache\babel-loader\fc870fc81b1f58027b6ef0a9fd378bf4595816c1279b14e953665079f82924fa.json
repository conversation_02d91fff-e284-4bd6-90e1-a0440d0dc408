{"ast": null, "code": "import { ref } from \"vue\";\nimport Page from \"../../../components/Page\";\nimport { getAnnouncementList, removeAnnouncement, saveAnnouncement, updateAnnouncement } from \"../../../api/message\";\nimport { confirm } from \"@/util/tipsUtils\";\nimport WangEditor from \"@/components/WangEditor/index.vue\";\nexport default {\n  name: \"MessageAnnouncementIndex\",\n  components: {\n    WangEditor,\n    Page\n  },\n  setup() {\n    const statusMap = {\n      \"unpublished\": \"未发布\",\n      \"published\": \"已发布\",\n      \"deleted\": \"已删除\"\n    };\n    const total = ref(0);\n    const announcementList = ref([]);\n    const param = ref({\n      current: 1,\n      size: 20,\n      keyword: \"\"\n    });\n    const dataLoading = ref(true);\n    const loadList = () => {\n      dataLoading.value = true;\n      getAnnouncementList(param.value, res => {\n        announcementList.value = res.list;\n        total.value = res.total;\n        dataLoading.value = false;\n      });\n    };\n    loadList();\n    // 页码改变\n    const currentChange = currentPage => {\n      param.value.current = currentPage;\n      loadList();\n    };\n    // 页面显示数量改变\n    const sizeChange = size => {\n      param.value.size = size;\n      loadList();\n    };\n    const search = () => {\n      loadList();\n    };\n    const announcement = ref({\n      id: \"\",\n      title: \"\",\n      content: \"\"\n    });\n    const announcementRules = {\n      title: [{\n        required: true,\n        message: \"请输入标题\",\n        trigger: \"blur\"\n      }],\n      content: [{\n        required: true,\n        message: \"请输入内容\",\n        trigger: \"blur\"\n      }]\n    };\n    const announcementRef = ref(null);\n    const showDialog = ref(false);\n    const hide = () => {\n      showDialog.value = false;\n      announcement.value = {\n        id: \"\",\n        title: \"\",\n        content: \"\"\n      };\n    };\n    const show = id => {\n      showDialog.value = true;\n      if (id > 0) {\n        announcement.value.id = id;\n      } else {\n        announcement.value.id = \"\";\n      }\n    };\n    const edit = item => {\n      announcement.value.title = item.title;\n      announcement.value.content = item.content;\n      show(item.id);\n    };\n    const del = item => {\n      confirm(\"确认删除该条数据？\", \"提示\", () => {\n        removeAnnouncement({\n          id: item.id\n        }, () => {\n          loadList();\n        });\n      });\n    };\n    const submit = () => {\n      if (announcement.value.id) {\n        updateAnnouncement(announcement.value, () => {\n          loadList();\n          hide();\n        });\n      } else {\n        saveAnnouncement(announcement.value, () => {\n          loadList();\n          hide();\n        });\n      }\n      console.log(announcement.value);\n    };\n    const publish = item => {\n      if (item.status === \"published\") {\n        item.status = \"unpublished\";\n      } else {\n        item.status = \"published\";\n      }\n      updateAnnouncement(item, () => {\n        loadList();\n      });\n    };\n    return {\n      param,\n      total,\n      announcementList,\n      currentChange,\n      sizeChange,\n      search,\n      showDialog,\n      hide,\n      show,\n      announcement,\n      announcementRules,\n      announcementRef,\n      edit,\n      del,\n      submit,\n      dataLoading,\n      publish,\n      statusMap\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "Page", "getAnnouncementList", "removeAnnouncement", "saveAnnouncement", "updateAnnouncement", "confirm", "WangEditor", "name", "components", "setup", "statusMap", "total", "announcementList", "param", "current", "size", "keyword", "dataLoading", "loadList", "value", "res", "list", "currentChange", "currentPage", "sizeChange", "search", "announcement", "id", "title", "content", "announcementRules", "required", "message", "trigger", "announcementRef", "showDialog", "hide", "show", "edit", "item", "del", "submit", "console", "log", "publish", "status"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/message/announcement/index.vue"], "sourcesContent": ["<template>\n  <div class=\"sensitive-word-container\">\n    <div class=\"head\">\n      <el-input size=\"small\" v-model=\"param.keyword\" clearable placeholder=\"输入标题搜索\" class=\"custom-input\" @keyup.enter=\"search\">\n        <template #append>\n          <el-button size=\"small\" class=\"custom-btn\" icon=\"el-icon-search\" @click=\"search\">搜索</el-button>\n        </template>\n      </el-input>\n      <el-button style=\"margin-left: 10px;\" @click=\"show(-1)\" size=\"small\" type=\"primary\">新增</el-button>\n    </div>\n    <el-table v-loading=\"dataLoading\" :data=\"announcementList\" size=\"small\" style=\"width: 100%;\">\n      <el-table-column prop=\"title\" label=\"标题\"/>\n      <el-table-column width=\"90\" label=\"发布状态\">\n        <template #default=\"scope\">\n          {{statusMap[scope.row.status]}}\n        </template>\n      </el-table-column>\n      <el-table-column width=\"140\" prop=\"createTime\" label=\"创建时间\"/>\n      <el-table-column width=\"140\" prop=\"updateTime\" label=\"修改时间\"/>\n      <el-table-column label=\"操作\" align=\"center\">\n        <template #default=\"scope\">\n          <el-button class=\"right-btn\" @click=\"publish(scope.row)\" v-if=\"scope.row.status !== 'deleted'\" size=\"small\">{{scope.row.status === 'published' ? '取消发布' : '发布'}}</el-button>\n          <el-button class=\"right-btn\" @click=\"edit(scope.row)\" v-if=\"scope.row.status !== 'deleted'\" size=\"small\">编辑</el-button>\n          <el-button class=\"right-btn\" @click=\"del(scope.row)\" v-if=\"scope.row.status !== 'deleted'\" size=\"small\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <!--分页组件-->\n    <page :total=\"total\" @size-change=\"sizeChange\" @current-change=\"currentChange\" :page-size=\"param.size\"/>\n    <el-dialog title=\"编辑\" v-model=\"showDialog\" :before-close=\"hide\">\n      <el-form :model=\"announcement\" :rules=\"announcementRules\" ref=\"announcementRef\">\n        <el-form-item label=\"标题：\" label-width=\"80px\" prop=\"title\">\n          <el-input size=\"small\" v-model=\"announcement.title\" placeholder=\"请输入标题\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"内容：\" label-width=\"80px\" prop=\"content\">\n          <wang-editor v-model=\"announcement.content\"></wang-editor>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button size=\"small\" @click=\"hide\">取 消</el-button>\n          <el-button size=\"small\" type=\"primary\" @click=\"submit\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import {ref} from \"vue\"\n  import Page from \"../../../components/Page\"\n  import {\n    getAnnouncementList,\n    removeAnnouncement,\n    saveAnnouncement,\n    updateAnnouncement\n  } from \"../../../api/message\";\n  import {confirm} from \"@/util/tipsUtils\"\n  import WangEditor from \"@/components/WangEditor/index.vue\";\n  export default {\n    name: \"MessageAnnouncementIndex\",\n    components: {\n      WangEditor,\n      Page\n    },\n    setup() {\n      const statusMap = {\n        \"unpublished\": \"未发布\",\n        \"published\": \"已发布\",\n        \"deleted\": \"已删除\"\n      }\n      const total = ref(0)\n      const announcementList = ref([])\n      const param = ref({\n        current: 1,\n        size: 20,\n        keyword: \"\"\n      })\n      const dataLoading = ref(true)\n      const loadList = () => {\n        dataLoading.value = true\n        getAnnouncementList(param.value, res => {\n          announcementList.value = res.list\n          total.value = res.total\n          dataLoading.value = false\n        })\n      }\n      loadList();\n      // 页码改变\n      const currentChange = (currentPage) => {\n        param.value.current = currentPage;\n        loadList()\n      }\n      // 页面显示数量改变\n      const sizeChange = (size) => {\n        param.value.size = size;\n        loadList()\n      }\n      const search = () => {\n        loadList()\n      }\n      const announcement = ref({\n        id: \"\",\n        title: \"\",\n        content: \"\"\n      })\n      const announcementRules = {\n        title: [{ required: true, message: \"请输入标题\", trigger: \"blur\" }],\n        content: [{ required: true, message: \"请输入内容\", trigger: \"blur\" }]\n      }\n      const announcementRef = ref(null)\n      const showDialog = ref(false)\n      const hide = () => {\n        showDialog.value = false;\n        announcement.value = {\n          id: \"\",\n          title: \"\",\n          content: \"\"\n        }\n      }\n      const show = (id) => {\n        showDialog.value = true\n        if (id > 0) {\n          announcement.value.id = id\n        } else {\n          announcement.value.id = \"\"\n        }\n      }\n      const edit = (item) => {\n        announcement.value.title = item.title\n        announcement.value.content = item.content\n        show(item.id)\n      }\n      const del = (item) => {\n        confirm(\"确认删除该条数据？\", \"提示\", () => {\n          removeAnnouncement({id: item.id}, () => {\n            loadList()\n          })\n        })\n      }\n      const submit = () => {\n        if (announcement.value.id) {\n          updateAnnouncement(announcement.value, () => {\n            loadList()\n            hide()\n          })\n        } else {\n          saveAnnouncement(announcement.value, () => {\n            loadList()\n            hide()\n          })\n        }\n        console.log(announcement.value)\n      }\n      const publish = (item) => {\n        if(item.status === \"published\") {\n          item.status = \"unpublished\"\n        } else {\n          item.status = \"published\"\n        }\n        updateAnnouncement(item, () => {\n          loadList()\n        })\n      }\n      return {\n        param,\n        total,\n        announcementList,\n        currentChange,\n        sizeChange,\n        search,\n        showDialog,\n        hide,\n        show,\n        announcement,\n        announcementRules,\n        announcementRef,\n        edit,\n        del,\n        submit,\n        dataLoading,\n        publish,\n        statusMap\n      }\n    }\n  }\n</script>\n\n<style scoped lang=\"scss\">\n  .sensitive-word-container {\n    margin: 20px;\n    .head {\n      margin-bottom: 10px;\n      .custom-input {\n        width: 50%;\n        min-width: 300px;\n      }\n      .custom-btn {\n        color: #606266;\n        &:hover {\n          color: $--color-primary;\n        }\n      }\n    }\n  }\n  .box-card {\n    max-width: 500px;\n  }\n  .fl-table {\n    border-radius: 5px;\n    font-size: 12px;\n    font-weight: normal;\n    border: none;\n    border-collapse: collapse;\n    width: 100%;\n    background-color: white;\n  }\n  .fl-table td {\n    border: 1px solid #f8f8f8;\n    font-size: 12px;\n    padding: 12px;\n  }\n  .fl-table tr td:nth-child(1) {\n    background: #F8F8F8;\n    width: 30%;\n    min-width: 100px;\n  }\n</style>\n"], "mappings": "AAiDE,SAAQA,GAAG,QAAO,KAAI;AACtB,OAAOC,IAAG,MAAO,0BAAyB;AAC1C,SACEC,mBAAmB,EACnBC,kBAAkB,EAClBC,gBAAgB,EAChBC,kBAAiB,QACZ,sBAAsB;AAC7B,SAAQC,OAAO,QAAO,kBAAiB;AACvC,OAAOC,UAAS,MAAO,mCAAmC;AAC1D,eAAe;EACbC,IAAI,EAAE,0BAA0B;EAChCC,UAAU,EAAE;IACVF,UAAU;IACVN;EACF,CAAC;EACDS,KAAKA,CAAA,EAAG;IACN,MAAMC,SAAQ,GAAI;MAChB,aAAa,EAAE,KAAK;MACpB,WAAW,EAAE,KAAK;MAClB,SAAS,EAAE;IACb;IACA,MAAMC,KAAI,GAAIZ,GAAG,CAAC,CAAC;IACnB,MAAMa,gBAAe,GAAIb,GAAG,CAAC,EAAE;IAC/B,MAAMc,KAAI,GAAId,GAAG,CAAC;MAChBe,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC;IACD,MAAMC,WAAU,GAAIlB,GAAG,CAAC,IAAI;IAC5B,MAAMmB,QAAO,GAAIA,CAAA,KAAM;MACrBD,WAAW,CAACE,KAAI,GAAI,IAAG;MACvBlB,mBAAmB,CAACY,KAAK,CAACM,KAAK,EAAEC,GAAE,IAAK;QACtCR,gBAAgB,CAACO,KAAI,GAAIC,GAAG,CAACC,IAAG;QAChCV,KAAK,CAACQ,KAAI,GAAIC,GAAG,CAACT,KAAI;QACtBM,WAAW,CAACE,KAAI,GAAI,KAAI;MAC1B,CAAC;IACH;IACAD,QAAQ,EAAE;IACV;IACA,MAAMI,aAAY,GAAKC,WAAW,IAAK;MACrCV,KAAK,CAACM,KAAK,CAACL,OAAM,GAAIS,WAAW;MACjCL,QAAQ,EAAC;IACX;IACA;IACA,MAAMM,UAAS,GAAKT,IAAI,IAAK;MAC3BF,KAAK,CAACM,KAAK,CAACJ,IAAG,GAAIA,IAAI;MACvBG,QAAQ,EAAC;IACX;IACA,MAAMO,MAAK,GAAIA,CAAA,KAAM;MACnBP,QAAQ,EAAC;IACX;IACA,MAAMQ,YAAW,GAAI3B,GAAG,CAAC;MACvB4B,EAAE,EAAE,EAAE;MACNC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE;IACX,CAAC;IACD,MAAMC,iBAAgB,GAAI;MACxBF,KAAK,EAAE,CAAC;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9DJ,OAAO,EAAE,CAAC;QAAEE,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC;IACjE;IACA,MAAMC,eAAc,GAAInC,GAAG,CAAC,IAAI;IAChC,MAAMoC,UAAS,GAAIpC,GAAG,CAAC,KAAK;IAC5B,MAAMqC,IAAG,GAAIA,CAAA,KAAM;MACjBD,UAAU,CAAChB,KAAI,GAAI,KAAK;MACxBO,YAAY,CAACP,KAAI,GAAI;QACnBQ,EAAE,EAAE,EAAE;QACNC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE;MACX;IACF;IACA,MAAMQ,IAAG,GAAKV,EAAE,IAAK;MACnBQ,UAAU,CAAChB,KAAI,GAAI,IAAG;MACtB,IAAIQ,EAAC,GAAI,CAAC,EAAE;QACVD,YAAY,CAACP,KAAK,CAACQ,EAAC,GAAIA,EAAC;MAC3B,OAAO;QACLD,YAAY,CAACP,KAAK,CAACQ,EAAC,GAAI,EAAC;MAC3B;IACF;IACA,MAAMW,IAAG,GAAKC,IAAI,IAAK;MACrBb,YAAY,CAACP,KAAK,CAACS,KAAI,GAAIW,IAAI,CAACX,KAAI;MACpCF,YAAY,CAACP,KAAK,CAACU,OAAM,GAAIU,IAAI,CAACV,OAAM;MACxCQ,IAAI,CAACE,IAAI,CAACZ,EAAE;IACd;IACA,MAAMa,GAAE,GAAKD,IAAI,IAAK;MACpBlC,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,MAAM;QAC/BH,kBAAkB,CAAC;UAACyB,EAAE,EAAEY,IAAI,CAACZ;QAAE,CAAC,EAAE,MAAM;UACtCT,QAAQ,EAAC;QACX,CAAC;MACH,CAAC;IACH;IACA,MAAMuB,MAAK,GAAIA,CAAA,KAAM;MACnB,IAAIf,YAAY,CAACP,KAAK,CAACQ,EAAE,EAAE;QACzBvB,kBAAkB,CAACsB,YAAY,CAACP,KAAK,EAAE,MAAM;UAC3CD,QAAQ,EAAC;UACTkB,IAAI,EAAC;QACP,CAAC;MACH,OAAO;QACLjC,gBAAgB,CAACuB,YAAY,CAACP,KAAK,EAAE,MAAM;UACzCD,QAAQ,EAAC;UACTkB,IAAI,EAAC;QACP,CAAC;MACH;MACAM,OAAO,CAACC,GAAG,CAACjB,YAAY,CAACP,KAAK;IAChC;IACA,MAAMyB,OAAM,GAAKL,IAAI,IAAK;MACxB,IAAGA,IAAI,CAACM,MAAK,KAAM,WAAW,EAAE;QAC9BN,IAAI,CAACM,MAAK,GAAI,aAAY;MAC5B,OAAO;QACLN,IAAI,CAACM,MAAK,GAAI,WAAU;MAC1B;MACAzC,kBAAkB,CAACmC,IAAI,EAAE,MAAM;QAC7BrB,QAAQ,EAAC;MACX,CAAC;IACH;IACA,OAAO;MACLL,KAAK;MACLF,KAAK;MACLC,gBAAgB;MAChBU,aAAa;MACbE,UAAU;MACVC,MAAM;MACNU,UAAU;MACVC,IAAI;MACJC,IAAI;MACJX,YAAY;MACZI,iBAAiB;MACjBI,eAAe;MACfI,IAAI;MACJE,GAAG;MACHC,MAAM;MACNxB,WAAW;MACX2B,OAAO;MACPlC;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}