{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref } from \"vue\";\nimport router from \"@/router\";\nimport { findCategoryList, toTree } from \"@/api/exam/category\";\nimport { findList, getExamChapterList } from \"@/api/exam\";\nimport Page from \"@/components/Page\";\nimport { info } from \"@/util/tipsUtils\";\nimport { getRecordList, getPaper } from \"@/api/exam/paper\";\nimport PaperDetail from \"@/views/exam/answer/detail\";\nexport default {\n  name: \"ExamAnswerListIndex\",\n  components: {\n    PaperDetail,\n    Page\n  },\n  setup() {\n    const list = ref([]);\n    const total = ref(0);\n    const dataLoading = ref(true);\n    const selectCidList = ref([]);\n    const categoryOptions = ref([]);\n    const examIdList = ref([]);\n    const searchParam = ref({\n      keyword: \"\",\n      cid: \"\",\n      isShow: \"\",\n      size: 20,\n      current: 1\n    });\n    const statusMap = {\n      unpublished: \"未发布\",\n      published: \"已发布\",\n      deleted: \"已删除\"\n    };\n    // 加载分类\n    const loadCategory = () => {\n      findCategoryList(0, true, res => {\n        if (res) {\n          categoryOptions.value = toTree(res);\n        }\n      });\n    };\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true;\n      findList(searchParam.value, res => {\n        dataLoading.value = false;\n        if (!res) {\n          return;\n        }\n        for (const listElement of res.list) {\n          listElement.chapterList = [];\n          getExamChapterList({\n            examId: listElement.id\n          }, r => {\n            if (r && r.list) {\n              listElement.chapterList = r.list;\n            }\n          });\n        }\n        list.value = res.list;\n        total.value = res.total;\n      }).catch(() => {\n        dataLoading.value = false;\n      });\n    };\n    loadList();\n    loadCategory();\n    // 搜索\n    const search = () => {\n      if (selectCidList.value && selectCidList.value.length > 0) {\n        searchParam.value.cid = selectCidList.value[selectCidList.value.length - 1];\n      }\n      loadList();\n    };\n    // 选择列表项\n    const selectItem = val => {\n      examIdList.value = [];\n      if (val && val.length > 0) {\n        for (const valElement of val) {\n          examIdList.value.push(valElement.id);\n        }\n      }\n    };\n    // 编辑\n    const edit = id => {\n      router.push({\n        path: \"/exam/edit\",\n        query: {\n          id: id\n        }\n      });\n    };\n    const currentChange = currentPage => {\n      searchParam.value.current = currentPage;\n      loadList();\n    };\n    const sizeChange = s => {\n      searchParam.value.size = s;\n      loadList();\n    };\n    const expandChange = (row, expandedRows) => {\n      // 展开\n      if (expandedRows.length > 0) {\n        console.log(row, expandedRows);\n      }\n    };\n    // 查看评论\n    const selectTopic = ref({});\n    const recordListDrawer = ref(false);\n    const drawerClose = done => {\n      recordListDrawer.value = false;\n      done();\n    };\n    const paperRecordLoading = ref(false);\n    const paperRecordList = ref([]);\n    const paperRecordTotal = ref(0);\n    const paperRecordParam = ref({\n      current: 1,\n      size: 20\n    });\n    const loadRecordList = () => {\n      paperRecordLoading.value = true;\n      getRecordList(paperRecordParam.value, res => {\n        paperRecordList.value = res.list;\n        paperRecordTotal.value = res.total;\n        paperRecordLoading.value = false;\n      });\n    };\n    const paperRecordCurrentChange = currentPage => {\n      paperRecordParam.value.current = currentPage;\n      loadRecordList();\n    };\n    const paperRecordSizeChange = s => {\n      paperRecordParam.value.size = s;\n      loadRecordList();\n    };\n    const showRecordListDrawer = item => {\n      recordListDrawer.value = true;\n      selectTopic.value = item;\n      getPaper(item.paperId, res => {\n        selectTopic.value.paper = res;\n      });\n      paperRecordParam.value.current = 1;\n      paperRecordParam.value.examChapterSectionId = item.id;\n      loadRecordList();\n      console.log(selectTopic.value);\n    };\n    const paperTypeMap = {\n      \"normal\": \"静态试卷\",\n      \"random\": \"随机试卷\",\n      \"mock\": \"模拟试卷\"\n    };\n    const paperStatusMap = {\n      \"draft\": \"草稿\",\n      \"submitted\": \"待批改\",\n      \"passed\": \"已通过\",\n      \"failed\": \"未通过\",\n      \"deleted\": \"已删除\"\n    };\n    const colors = [\"#99A9BF\", \"#F7BA2A\", \"#FF9900\"];\n    const detailDrawer = ref(false);\n    const detailItem = ref({});\n    const showDetail = item => {\n      detailDrawer.value = true;\n      console.log(item);\n      detailItem.value = item;\n    };\n    const hideDetail = done => {\n      detailDrawer.value = false;\n      done();\n    };\n    return {\n      list,\n      total,\n      searchParam,\n      selectCidList,\n      categoryOptions,\n      examIdList,\n      search,\n      selectItem,\n      edit,\n      currentChange,\n      sizeChange,\n      expandChange,\n      dataLoading,\n      statusMap,\n      showRecordListDrawer,\n      selectTopic,\n      recordListDrawer,\n      drawerClose,\n      info,\n      paperTypeMap,\n      paperStatusMap,\n      colors,\n      paperRecordLoading,\n      paperRecordList,\n      paperRecordTotal,\n      paperRecordParam,\n      paperRecordCurrentChange,\n      paperRecordSizeChange,\n      showDetail,\n      hideDetail,\n      detailItem,\n      detailDrawer\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "router", "findCategoryList", "toTree", "findList", "getExamChapterList", "Page", "info", "getRecordList", "getPaper", "PaperDetail", "name", "components", "setup", "list", "total", "dataLoading", "selectCidList", "categoryOptions", "examIdList", "searchParam", "keyword", "cid", "isShow", "size", "current", "statusMap", "unpublished", "published", "deleted", "loadCategory", "res", "value", "loadList", "listElement", "chapterList", "examId", "id", "r", "catch", "search", "length", "selectItem", "val", "valElement", "push", "edit", "path", "query", "currentChange", "currentPage", "sizeChange", "s", "expandChange", "row", "expandedRows", "console", "log", "selectTopic", "recordListDrawer", "drawerClose", "done", "paperRecordLoading", "paperRecordList", "paperRecordTotal", "paperRecordParam", "loadRecordList", "paperRecordCurrentChange", "paperRecordSizeChange", "showRecordListDrawer", "item", "paperId", "paper", "examChapterSectionId", "paperTypeMap", "paperStatusMap", "colors", "detailDrawer", "detailItem", "showDetail", "hideDetail"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/exam/answer/list/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"header\">\n      <el-form :inline=\"true\" :model=\"searchParam\" class=\"demo-form-inline\">\n        <el-form-item label=\"\">\n          <el-input size=\"small\" class=\"search-input\" v-model=\"searchParam.keyword\" placeholder=\"请输入关键字\"></el-input>\n          <el-button size=\"small\" class=\"search-btn\" type=\"primary\" @click=\"search\">搜索</el-button>\n        </el-form-item>\n        <el-form-item label=\"状态\" class=\"status\">\n          <el-select size=\"small\" v-model=\"searchParam.isShow\" @change=\"search\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"未发布\" value=\"unpublished\"></el-option>\n            <el-option label=\"已发布\" value=\"published\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"分类\">\n          <el-cascader size=\"small\" v-model=\"selectCidList\" :options=\"categoryOptions\" :props=\"{ checkStrictly: true }\" @change=\"search\" clearable></el-cascader>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"content\">\n      <el-table v-loading=\"dataLoading\" class=\"custom-table\" ref=\"multipleTable\" :data=\"list\" style=\"width: 100%\" @expand-change=\"expandChange\">\n        <el-table-column type=\"expand\">\n          <template #default=\"scope\">\n            <el-card style=\"margin-top: 20px;\">\n              <template #header>\n                <div class=\"clearfix\">\n                  <span>章节</span>\n                </div>\n              </template>\n              <div>\n                <el-table :default-expand-all=\"true\" class=\"custom-table\" :data=\"scope.row.chapterList\" :show-header=\"false\" style=\"width: 100%;\">\n                  <el-table-column type=\"expand\">\n                    <template #default=\"props\">\n                      <el-table class=\"custom-table\" :data=\"props.row.chapterSectionList\" :show-header=\"false\" style=\"width: 100%;\">\n                        <el-table-column prop=\"title\" label=\"标题\"></el-table-column>\n                        <el-table-column label=\"操作\" width=\"100\">\n                          <template #default=\"s\">\n                            <el-button type=\"text\" @click=\"showRecordListDrawer(s.row)\">答题记录</el-button>\n                          </template>\n                        </el-table-column>\n                      </el-table>\n                    </template>\n                  </el-table-column>\n                  <el-table-column prop=\"title\" label=\"标题\"></el-table-column>\n                </el-table>\n              </div>\n            </el-card>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"name\" label=\"考试标题\"></el-table-column>\n        <el-table-column label=\"报名人数\">\n          <template #default=\"scope\">\n            {{scope.row.signUpNum || 0}}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"状态\">\n          <template #default=\"scope\">\n            {{statusMap[scope.row.status]}}\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n    <el-drawer custom-class=\"custom-drawer\" v-model=\"recordListDrawer\" direction=\"rtl\" :before-close=\"drawerClose\" destroy-on-close>\n      <template #title>\n        <div class=\"work-item-box\">\n          <div class=\"item-content\">\n            <div class=\"content-main\">\n              <div class=\"main-title\">\n                <div class=\"title-box two-line\">\n                  <span class=\"title-text\">{{selectTopic.name || selectTopic.title || selectTopic.content}}</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"content-info\">\n              <div class=\"answer-box\">\n                <el-row v-if=\"selectTopic.paper\">\n                  <el-col :span=\"8\">\n                    <div class=\"answer-item\">\n                      <div class=\"answer-info-label\">试卷标题：</div>\n                      <div class=\"answer-info-value\">\n                        {{selectTopic.paper.title}}\n                      </div>\n                    </div>\n                    <div class=\"answer-item\">\n                      <div class=\"answer-info-label\">试卷类型：</div>\n                      <div class=\"answer-info-value\">\n                        {{paperTypeMap[selectTopic.paper.type]}}\n                      </div>\n                    </div>\n                    <div class=\"answer-item\">\n                      <div class=\"answer-info-label\">试卷难度：</div>\n                      <div class=\"answer-info-value\">\n                        <el-rate :disabled=\"true\" v-model=\"selectTopic.paper.difficulty\" :colors=\"colors\"></el-rate>\n                      </div>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"answer-item\" v-if=\"selectTopic.paper && selectTopic.paper.questionList\">\n                      <div class=\"answer-info-label\">题目数量：</div>\n                      <div class=\"answer-info-value\">{{selectTopic.paper.questionList.length || 0}}</div>\n                    </div>\n                    <div class=\"answer-item\">\n                      <div class=\"answer-info-label\">试卷总分：</div>\n                      <div class=\"answer-info-value\">{{selectTopic.paper.score || 0}}</div>\n                    </div>\n                    <div class=\"answer-item\">\n                      <div class=\"answer-info-label\">合格分数：</div>\n                      <div class=\"answer-info-value\">{{selectTopic.paper.passScore || 0}}</div>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                  </el-col>\n                </el-row>\n              </div>\n            </div>\n          </div>\n        </div>\n      </template>\n      <div class=\"topic-comment-list-wrapper\">\n        <el-table v-loading=\"paperRecordLoading\" :data=\"paperRecordList\" style=\"width: 100%\">\n          <el-table-column label=\"姓名\">\n            <template #default=\"scope\">\n              {{scope.row.member && scope.row.member.name}}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"开始时间\" prop=\"startTime\"></el-table-column>\n          <el-table-column label=\"提交时间\" prop=\"endTime\"></el-table-column>\n          <el-table-column label=\"得分\">\n            <template #default=\"scope\">\n              {{scope.row.score || 0}}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"得分\">\n            <template #default=\"scope\">\n              <el-button type=\"text\" @click=\"showDetail(scope.row)\">答题详情</el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        <page :total=\"paperRecordTotal\" :current-change=\"paperRecordCurrentChange\" :size-change=\"paperRecordSizeChange\" :page-size=\"paperRecordParam.size\"></page>\n      </div>\n      <el-drawer v-if=\"detailDrawer\" :append-to-body=\"true\" v-model=\"detailDrawer\" direction=\"rtl\" :before-close=\"hideDetail\" destroy-on-close custom-class=\"detail-drawer\">\n        <template #title>\n          <div>\n            {{detailItem.member && detailItem.member.name}} <span style=\"color: #999999;font-size: 12px;\">(报名id：{{detailItem.signUpId}})</span>\n          </div>\n        </template>\n        <paper-detail v-if=\"detailDrawer\" :exam-chapter-section-id=\"detailItem.examChapterSectionId\" :exam-id=\"detailItem.examId\" :sign-up-id=\"detailItem.signUpId\"/>\n      </el-drawer>\n    </el-drawer>\n    <page :total=\"total\" :current-change=\"currentChange\" :size-change=\"sizeChange\" :page-size=\"searchParam.size\"></page>\n  </div>\n</template>\n\n<script>\nimport {ref} from \"vue\"\nimport router from \"@/router\"\nimport {findCategoryList, toTree} from \"@/api/exam/category\"\nimport {findList, getExamChapterList} from \"@/api/exam\"\nimport Page from \"@/components/Page\"\nimport {info} from \"@/util/tipsUtils\";\nimport {getRecordList, getPaper} from \"@/api/exam/paper\";\nimport PaperDetail from \"@/views/exam/answer/detail\";\n\nexport default {\n  name: \"ExamAnswerListIndex\",\n  components: {\n    PaperDetail,\n    Page\n  },\n  setup() {\n    const list = ref([])\n    const total = ref(0)\n    const dataLoading = ref(true)\n    const selectCidList = ref([])\n    const categoryOptions = ref([])\n    const examIdList = ref([])\n    const searchParam = ref({\n      keyword: \"\",\n      cid: \"\",\n      isShow: \"\",\n      size: 20,\n      current: 1\n    })\n    const statusMap = {\n      unpublished: \"未发布\",\n      published: \"已发布\",\n      deleted: \"已删除\"\n    }\n    // 加载分类\n    const loadCategory = () => {\n      findCategoryList(0, true, (res) => {if (res) { categoryOptions.value = toTree(res);}})\n    }\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true\n      findList(searchParam.value, (res) => {\n        dataLoading.value = false\n        if (!res) {return;}\n        for (const listElement of res.list) {\n          listElement.chapterList = [];\n          getExamChapterList({examId: listElement.id}, (r) => {\n            if (r && r.list) {\n              listElement.chapterList = r.list\n            }\n          })\n        }\n        list.value = res.list;\n        total.value = res.total;\n      }).catch(() => {\n        dataLoading.value = false\n      })\n    }\n    loadList();\n    loadCategory();\n    // 搜索\n    const search = () => {\n      if (selectCidList.value && selectCidList.value.length > 0) {\n        searchParam.value.cid = selectCidList.value[selectCidList.value.length - 1];\n      }\n      loadList();\n    }\n    // 选择列表项\n    const selectItem = (val) => {\n      examIdList.value = [];\n      if (val && val.length > 0) {\n        for (const valElement of val) {\n          examIdList.value.push(valElement.id);\n        }\n      }\n    }\n    // 编辑\n    const edit = (id) => {\n      router.push({path: \"/exam/edit\", query: { id : id }})\n    }\n    const currentChange = (currentPage) => {\n      searchParam.value.current = currentPage;\n      loadList();\n    }\n    const sizeChange = (s) => {\n      searchParam.value.size = s;\n      loadList();\n    }\n    const expandChange = (row, expandedRows) => {\n      // 展开\n      if(expandedRows.length>0) {\n        console.log(row, expandedRows)\n      }\n    }\n    // 查看评论\n    const selectTopic = ref({})\n    const recordListDrawer = ref(false)\n    const drawerClose = (done) => {\n      recordListDrawer.value = false\n      done()\n    }\n    const paperRecordLoading = ref(false)\n    const paperRecordList = ref([])\n    const paperRecordTotal = ref(0)\n    const paperRecordParam = ref({\n      current: 1,\n      size: 20\n    })\n    const loadRecordList = () => {\n      paperRecordLoading.value = true\n      getRecordList(paperRecordParam.value, res => {\n        paperRecordList.value = res.list\n        paperRecordTotal.value = res.total\n        paperRecordLoading.value = false\n      })\n    }\n    const paperRecordCurrentChange = (currentPage) => {\n      paperRecordParam.value.current = currentPage;\n      loadRecordList();\n    }\n    const paperRecordSizeChange = (s) => {\n      paperRecordParam.value.size = s;\n      loadRecordList();\n    }\n    const showRecordListDrawer = (item) => {\n      recordListDrawer.value = true\n      selectTopic.value = item\n      getPaper(item.paperId, res => {\n        selectTopic.value.paper = res;\n      })\n      paperRecordParam.value.current = 1\n      paperRecordParam.value.examChapterSectionId = item.id\n      loadRecordList()\n      console.log(selectTopic.value)\n    }\n    const paperTypeMap = {\n      \"normal\": \"静态试卷\",\n      \"random\": \"随机试卷\",\n      \"mock\": \"模拟试卷\",\n    }\n    const paperStatusMap = {\n      \"draft\": \"草稿\",\n      \"submitted\": \"待批改\",\n      \"passed\": \"已通过\",\n      \"failed\": \"未通过\",\n      \"deleted\": \"已删除\"\n    }\n    const colors = [\"#99A9BF\", \"#F7BA2A\", \"#FF9900\"]\n    const detailDrawer = ref(false)\n    const detailItem = ref({})\n    const showDetail = (item) => {\n      detailDrawer.value = true\n      console.log(item)\n      detailItem.value = item\n    }\n    const hideDetail = (done) => {\n      detailDrawer.value = false\n      done()\n    }\n    return {\n      list,\n      total,\n      searchParam,\n      selectCidList,\n      categoryOptions,\n      examIdList,\n      search,\n      selectItem,\n      edit,\n      currentChange,\n      sizeChange,\n      expandChange,\n      dataLoading,\n      statusMap,\n      showRecordListDrawer,\n      selectTopic,\n      recordListDrawer,\n      drawerClose,\n      info,\n      paperTypeMap,\n      paperStatusMap,\n      colors,\n      paperRecordLoading,\n      paperRecordList,\n      paperRecordTotal,\n      paperRecordParam,\n      paperRecordCurrentChange,\n      paperRecordSizeChange,\n      showDetail,\n      hideDetail,\n      detailItem,\n      detailDrawer\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.app-container {\n  margin: 20px;\n  .content {\n    .content-item-warp {\n      position: relative;\n      display: flex;\n      .image {\n        width: 168px;\n        min-width: 168px;\n        height: 108px;\n        margin-right: 24px;\n        position: relative;\n        overflow: hidden;\n        border-radius: 4px;\n        border: 1px solid #e8e8e8;\n        cursor: default;\n        img {\n          width: 100%;\n          height: 100%;\n          transition: all .5s ease-out .1s;\n          -o-object-fit: cover;\n          object-fit: cover;\n          -o-object-position: center;\n          object-position: center;\n          &:hover {\n            transform: matrix(1.04,0,0,1.04,0,0);\n            -webkit-backface-visibility: hidden;\n            backface-visibility: hidden;\n          }\n        }\n      }\n      .article-card-bone {\n        width: 100%;\n        display: flex;\n        flex-direction: column;\n        min-width: 0;\n        .title-wrap {\n          display: flex;\n          justify-content: space-between;\n          margin-top: 0;\n          .title {\n            font-size: 16px;\n            overflow: hidden;\n            white-space: nowrap;\n            text-overflow: ellipsis;\n            line-height: 24px;\n            font-weight: 600;\n            display: block;\n            color: #222;\n            cursor: text;\n          }\n          .create-time {\n            color: #999;\n            line-height: 24px;\n            margin-left: 12px;\n            flex-shrink: 0;\n          }\n        }\n        .content {\n          word-break: break-word;\n          overflow-wrap: break-word;\n          margin: 8px 0 4px 0;\n          font-size: 12px;\n        }\n        .count-wrapper {\n          margin-top: 24px;\n          display: flex;\n          justify-content: space-between;\n          .count {\n            line-height: 20px;\n            position: relative;\n            li {\n              display: inline-block;\n              margin-right: 24px;\n              &:after {\n                content: \"\\ff65\";\n                font-size: 20px;\n                margin: 0 8px;\n                line-height: 0;\n                position: absolute;\n                top: 10px;\n                color: #666;\n              }\n              &:last-child:after {\n                content: \"\"\n              }\n            }\n          }\n          .article-action-list {\n            display: flex;\n            line-height: 20px;\n            flex: 1 0 auto;\n            justify-content: flex-end;\n            .icon-label {\n              cursor: pointer;\n              font-size: 14px;\n              line-height: 20px;\n              display: flex;\n              color: #222;\n              font-weight: 400;\n              margin-left: 24px;\n              &:first-child {\n                margin-left: 0;\n              }\n              &:hover {\n                color: $--color-primary;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  .el-table th.is-leaf, .el-table td {\n    border: 0!important;\n  }\n  .image {\n    height: 60px;\n    display: inline-block;\n  }\n  .search-input {\n    width: 242px;\n  }\n  .el-table-column--selection .cell{\n    padding-left: 14px;\n    padding-right: 14px;\n  }\n  ::v-deep .el-table tbody tr:hover > td {\n    background-color: transparent;\n  }\n  ::v-deep .custom-drawer {\n    width: calc(100% - 210px) !important;\n    .el-drawer__header {\n      align-items: end;\n    }\n    &:focus {\n      outline: none;\n    }\n    .el-drawer__close-btn {\n      &:focus {\n        outline: none;\n      }\n      &:hover {\n        color: $--color-primary;\n      }\n    }\n    .work-item-box {\n      margin: 0;\n      border: 0;\n      font: inherit;\n      vertical-align: baseline;\n      display: flex;\n      align-items: center;\n      width: 100%;\n      border-bottom: none;\n      .item-cover {\n        position: relative;\n        width: 80px;\n        height: 80px;\n        margin-right: 16px;\n        border-radius: 4px;\n        border: 1px solid #e8e8e8;\n        cursor: pointer;\n        background-repeat: no-repeat;\n        background-size: cover;\n        background-position: 50%;\n      }\n      .item-content {\n        overflow: hidden;\n        flex: 1;\n        display: flex;\n        flex-direction: column;\n        justify-content: space-between;\n        height: auto;\n        .content-main {\n          .main-title {\n            .title-box {\n              flex: 1 0 0;\n              display: -webkit-box;\n              overflow: hidden;\n              text-overflow: ellipsis;\n              -webkit-line-clamp: 2;\n              -webkit-box-orient: vertical;\n              white-space: normal;\n              word-break: break-word;\n              word-wrap: break-word;\n              .title-text {\n                line-height: 24px;\n                font-size: 16px;\n                color: #222;\n                cursor: pointer;\n                &:hover {\n                  color: $--color-primary;\n                }\n              }\n            }\n          }\n        }\n        .content-info {\n          font-size: 12px;\n          line-height: 16px;\n          color: #999;\n          .info-item {\n            margin-right: 8px;\n          }\n          .answer-box {\n            margin-top: 10px;\n            line-height: 28px;\n            .answer-item {\n              .answer-info-label {\n                display: inline-block;\n              }\n              .answer-info-value {\n                display: inline-block;\n                ::v-deep .el-rate {\n                  line-height: 16px;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    .topic-comment-list-wrapper {\n      margin: 0 20px;\n    }\n  }\n}\n::v-deep .el-table__inner-wrapper::before {\n  content: normal;\n}\n</style>\n<style lang=\"scss\">\n  .custom-table table tr:last-child {\n    td {\n      border: 0!important;\n    }\n  }\n  .el-table::before {\n    height: 0!important;\n  }\n  .detail-drawer {\n    width: calc(100% - 210px) !important;\n  }\n</style>\n"], "mappings": ";AA2JA,SAAQA,GAAG,QAAO,KAAI;AACtB,OAAOC,MAAK,MAAO,UAAS;AAC5B,SAAQC,gBAAgB,EAAEC,MAAM,QAAO,qBAAoB;AAC3D,SAAQC,QAAQ,EAAEC,kBAAkB,QAAO,YAAW;AACtD,OAAOC,IAAG,MAAO,mBAAkB;AACnC,SAAQC,IAAI,QAAO,kBAAkB;AACrC,SAAQC,aAAa,EAAEC,QAAQ,QAAO,kBAAkB;AACxD,OAAOC,WAAU,MAAO,4BAA4B;AAEpD,eAAe;EACbC,IAAI,EAAE,qBAAqB;EAC3BC,UAAU,EAAE;IACVF,WAAW;IACXJ;EACF,CAAC;EACDO,KAAKA,CAAA,EAAG;IACN,MAAMC,IAAG,GAAId,GAAG,CAAC,EAAE;IACnB,MAAMe,KAAI,GAAIf,GAAG,CAAC,CAAC;IACnB,MAAMgB,WAAU,GAAIhB,GAAG,CAAC,IAAI;IAC5B,MAAMiB,aAAY,GAAIjB,GAAG,CAAC,EAAE;IAC5B,MAAMkB,eAAc,GAAIlB,GAAG,CAAC,EAAE;IAC9B,MAAMmB,UAAS,GAAInB,GAAG,CAAC,EAAE;IACzB,MAAMoB,WAAU,GAAIpB,GAAG,CAAC;MACtBqB,OAAO,EAAE,EAAE;MACXC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC;IACD,MAAMC,SAAQ,GAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE;IACX;IACA;IACA,MAAMC,YAAW,GAAIA,CAAA,KAAM;MACzB5B,gBAAgB,CAAC,CAAC,EAAE,IAAI,EAAG6B,GAAG,IAAK;QAAC,IAAIA,GAAG,EAAE;UAAEb,eAAe,CAACc,KAAI,GAAI7B,MAAM,CAAC4B,GAAG,CAAC;QAAC;MAAC,CAAC;IACvF;IACA;IACA,MAAME,QAAO,GAAIA,CAAA,KAAM;MACrBjB,WAAW,CAACgB,KAAI,GAAI,IAAG;MACvB5B,QAAQ,CAACgB,WAAW,CAACY,KAAK,EAAGD,GAAG,IAAK;QACnCf,WAAW,CAACgB,KAAI,GAAI,KAAI;QACxB,IAAI,CAACD,GAAG,EAAE;UAAC;QAAO;QAClB,KAAK,MAAMG,WAAU,IAAKH,GAAG,CAACjB,IAAI,EAAE;UAClCoB,WAAW,CAACC,WAAU,GAAI,EAAE;UAC5B9B,kBAAkB,CAAC;YAAC+B,MAAM,EAAEF,WAAW,CAACG;UAAE,CAAC,EAAGC,CAAC,IAAK;YAClD,IAAIA,CAAA,IAAKA,CAAC,CAACxB,IAAI,EAAE;cACfoB,WAAW,CAACC,WAAU,GAAIG,CAAC,CAACxB,IAAG;YACjC;UACF,CAAC;QACH;QACAA,IAAI,CAACkB,KAAI,GAAID,GAAG,CAACjB,IAAI;QACrBC,KAAK,CAACiB,KAAI,GAAID,GAAG,CAAChB,KAAK;MACzB,CAAC,CAAC,CAACwB,KAAK,CAAC,MAAM;QACbvB,WAAW,CAACgB,KAAI,GAAI,KAAI;MAC1B,CAAC;IACH;IACAC,QAAQ,EAAE;IACVH,YAAY,EAAE;IACd;IACA,MAAMU,MAAK,GAAIA,CAAA,KAAM;MACnB,IAAIvB,aAAa,CAACe,KAAI,IAAKf,aAAa,CAACe,KAAK,CAACS,MAAK,GAAI,CAAC,EAAE;QACzDrB,WAAW,CAACY,KAAK,CAACV,GAAE,GAAIL,aAAa,CAACe,KAAK,CAACf,aAAa,CAACe,KAAK,CAACS,MAAK,GAAI,CAAC,CAAC;MAC7E;MACAR,QAAQ,EAAE;IACZ;IACA;IACA,MAAMS,UAAS,GAAKC,GAAG,IAAK;MAC1BxB,UAAU,CAACa,KAAI,GAAI,EAAE;MACrB,IAAIW,GAAE,IAAKA,GAAG,CAACF,MAAK,GAAI,CAAC,EAAE;QACzB,KAAK,MAAMG,UAAS,IAAKD,GAAG,EAAE;UAC5BxB,UAAU,CAACa,KAAK,CAACa,IAAI,CAACD,UAAU,CAACP,EAAE,CAAC;QACtC;MACF;IACF;IACA;IACA,MAAMS,IAAG,GAAKT,EAAE,IAAK;MACnBpC,MAAM,CAAC4C,IAAI,CAAC;QAACE,IAAI,EAAE,YAAY;QAAEC,KAAK,EAAE;UAAEX,EAAC,EAAIA;QAAG;MAAC,CAAC;IACtD;IACA,MAAMY,aAAY,GAAKC,WAAW,IAAK;MACrC9B,WAAW,CAACY,KAAK,CAACP,OAAM,GAAIyB,WAAW;MACvCjB,QAAQ,EAAE;IACZ;IACA,MAAMkB,UAAS,GAAKC,CAAC,IAAK;MACxBhC,WAAW,CAACY,KAAK,CAACR,IAAG,GAAI4B,CAAC;MAC1BnB,QAAQ,EAAE;IACZ;IACA,MAAMoB,YAAW,GAAIA,CAACC,GAAG,EAAEC,YAAY,KAAK;MAC1C;MACA,IAAGA,YAAY,CAACd,MAAM,GAAC,CAAC,EAAE;QACxBe,OAAO,CAACC,GAAG,CAACH,GAAG,EAAEC,YAAY;MAC/B;IACF;IACA;IACA,MAAMG,WAAU,GAAI1D,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAM2D,gBAAe,GAAI3D,GAAG,CAAC,KAAK;IAClC,MAAM4D,WAAU,GAAKC,IAAI,IAAK;MAC5BF,gBAAgB,CAAC3B,KAAI,GAAI,KAAI;MAC7B6B,IAAI,EAAC;IACP;IACA,MAAMC,kBAAiB,GAAI9D,GAAG,CAAC,KAAK;IACpC,MAAM+D,eAAc,GAAI/D,GAAG,CAAC,EAAE;IAC9B,MAAMgE,gBAAe,GAAIhE,GAAG,CAAC,CAAC;IAC9B,MAAMiE,gBAAe,GAAIjE,GAAG,CAAC;MAC3ByB,OAAO,EAAE,CAAC;MACVD,IAAI,EAAE;IACR,CAAC;IACD,MAAM0C,cAAa,GAAIA,CAAA,KAAM;MAC3BJ,kBAAkB,CAAC9B,KAAI,GAAI,IAAG;MAC9BxB,aAAa,CAACyD,gBAAgB,CAACjC,KAAK,EAAED,GAAE,IAAK;QAC3CgC,eAAe,CAAC/B,KAAI,GAAID,GAAG,CAACjB,IAAG;QAC/BkD,gBAAgB,CAAChC,KAAI,GAAID,GAAG,CAAChB,KAAI;QACjC+C,kBAAkB,CAAC9B,KAAI,GAAI,KAAI;MACjC,CAAC;IACH;IACA,MAAMmC,wBAAuB,GAAKjB,WAAW,IAAK;MAChDe,gBAAgB,CAACjC,KAAK,CAACP,OAAM,GAAIyB,WAAW;MAC5CgB,cAAc,EAAE;IAClB;IACA,MAAME,qBAAoB,GAAKhB,CAAC,IAAK;MACnCa,gBAAgB,CAACjC,KAAK,CAACR,IAAG,GAAI4B,CAAC;MAC/Bc,cAAc,EAAE;IAClB;IACA,MAAMG,oBAAmB,GAAKC,IAAI,IAAK;MACrCX,gBAAgB,CAAC3B,KAAI,GAAI,IAAG;MAC5B0B,WAAW,CAAC1B,KAAI,GAAIsC,IAAG;MACvB7D,QAAQ,CAAC6D,IAAI,CAACC,OAAO,EAAExC,GAAE,IAAK;QAC5B2B,WAAW,CAAC1B,KAAK,CAACwC,KAAI,GAAIzC,GAAG;MAC/B,CAAC;MACDkC,gBAAgB,CAACjC,KAAK,CAACP,OAAM,GAAI;MACjCwC,gBAAgB,CAACjC,KAAK,CAACyC,oBAAmB,GAAIH,IAAI,CAACjC,EAAC;MACpD6B,cAAc,EAAC;MACfV,OAAO,CAACC,GAAG,CAACC,WAAW,CAAC1B,KAAK;IAC/B;IACA,MAAM0C,YAAW,GAAI;MACnB,QAAQ,EAAE,MAAM;MAChB,QAAQ,EAAE,MAAM;MAChB,MAAM,EAAE;IACV;IACA,MAAMC,cAAa,GAAI;MACrB,OAAO,EAAE,IAAI;MACb,WAAW,EAAE,KAAK;MAClB,QAAQ,EAAE,KAAK;MACf,QAAQ,EAAE,KAAK;MACf,SAAS,EAAE;IACb;IACA,MAAMC,MAAK,GAAI,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;IAC/C,MAAMC,YAAW,GAAI7E,GAAG,CAAC,KAAK;IAC9B,MAAM8E,UAAS,GAAI9E,GAAG,CAAC,CAAC,CAAC;IACzB,MAAM+E,UAAS,GAAKT,IAAI,IAAK;MAC3BO,YAAY,CAAC7C,KAAI,GAAI,IAAG;MACxBwB,OAAO,CAACC,GAAG,CAACa,IAAI;MAChBQ,UAAU,CAAC9C,KAAI,GAAIsC,IAAG;IACxB;IACA,MAAMU,UAAS,GAAKnB,IAAI,IAAK;MAC3BgB,YAAY,CAAC7C,KAAI,GAAI,KAAI;MACzB6B,IAAI,EAAC;IACP;IACA,OAAO;MACL/C,IAAI;MACJC,KAAK;MACLK,WAAW;MACXH,aAAa;MACbC,eAAe;MACfC,UAAU;MACVqB,MAAM;MACNE,UAAU;MACVI,IAAI;MACJG,aAAa;MACbE,UAAU;MACVE,YAAY;MACZrC,WAAW;MACXU,SAAS;MACT2C,oBAAoB;MACpBX,WAAW;MACXC,gBAAgB;MAChBC,WAAW;MACXrD,IAAI;MACJmE,YAAY;MACZC,cAAc;MACdC,MAAM;MACNd,kBAAkB;MAClBC,eAAe;MACfC,gBAAgB;MAChBC,gBAAgB;MAChBE,wBAAwB;MACxBC,qBAAqB;MACrBW,UAAU;MACVC,UAAU;MACVF,UAAU;MACVD;IACF;EACF;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}