{"ast": null, "code": "import { get, post, put, del } from \"@/util/requestUtils\";\nexport function getAnnouncementList(params, success) {\n  return get(\"/message/announcement/list\", params, success);\n}\nexport function saveAnnouncement(params, success) {\n  return post(\"/message/announcement\", params, success);\n}\nexport function updateAnnouncement(params, success) {\n  return put(\"/message/announcement\", params, success);\n}\nexport function getAnnouncement(id, success) {\n  return get(\"/message/announcement\", {\n    id: id\n  }, success);\n}\nexport function removeAnnouncement(data, success) {\n  return del(\"/message/announcement\", data, success);\n}", "map": {"version": 3, "names": ["get", "post", "put", "del", "getAnnouncementList", "params", "success", "saveAnnouncement", "updateAnnouncement", "getAnnouncement", "id", "removeAnnouncement", "data"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/api/message/index.js"], "sourcesContent": ["import { get, post, put, del } from \"@/util/requestUtils\"\n\nexport function getAnnouncementList(params, success) {\n  return get(\"/message/announcement/list\", params, success)\n}\n\nexport function saveAnnouncement(params, success) {\n  return post(\"/message/announcement\", params, success)\n}\n\nexport function updateAnnouncement(params, success) {\n  return put(\"/message/announcement\", params, success)\n}\n\nexport function getAnnouncement(id, success) {\n  return get(\"/message/announcement\", {id: id}, success)\n}\n\nexport function removeAnnouncement(data, success) {\n  return del(\"/message/announcement\", data, success)\n}\n\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAQ,qBAAqB;AAEzD,OAAO,SAASC,mBAAmBA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACnD,OAAON,GAAG,CAAC,4BAA4B,EAAEK,MAAM,EAAEC,OAAO,CAAC;AAC3D;AAEA,OAAO,SAASC,gBAAgBA,CAACF,MAAM,EAAEC,OAAO,EAAE;EAChD,OAAOL,IAAI,CAAC,uBAAuB,EAAEI,MAAM,EAAEC,OAAO,CAAC;AACvD;AAEA,OAAO,SAASE,kBAAkBA,CAACH,MAAM,EAAEC,OAAO,EAAE;EAClD,OAAOJ,GAAG,CAAC,uBAAuB,EAAEG,MAAM,EAAEC,OAAO,CAAC;AACtD;AAEA,OAAO,SAASG,eAAeA,CAACC,EAAE,EAAEJ,OAAO,EAAE;EAC3C,OAAON,GAAG,CAAC,uBAAuB,EAAE;IAACU,EAAE,EAAEA;EAAE,CAAC,EAAEJ,OAAO,CAAC;AACxD;AAEA,OAAO,SAASK,kBAAkBA,CAACC,IAAI,EAAEN,OAAO,EAAE;EAChD,OAAOH,GAAG,CAAC,uBAAuB,EAAES,IAAI,EAAEN,OAAO,CAAC;AACpD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}