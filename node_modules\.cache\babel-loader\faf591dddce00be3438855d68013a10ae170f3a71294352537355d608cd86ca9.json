{"ast": null, "code": "const storage = {\n  set(key, value) {\n    localStorage.setItem(key, value);\n  },\n  get(key) {\n    return localStorage.getItem(key);\n  },\n  remove(key) {\n    localStorage.removeItem(key);\n  },\n  set<PERSON><PERSON>(key, value) {\n    this.set(key, JSON.stringify(value));\n  },\n  get<PERSON><PERSON>(key) {\n    const value = this.get(key);\n    if (value) {\n      return JSON.parse(value);\n    }\n    return undefined;\n  }\n};\nexport default storage;", "map": {"version": 3, "names": ["storage", "set", "key", "value", "localStorage", "setItem", "get", "getItem", "remove", "removeItem", "<PERSON><PERSON><PERSON>", "JSON", "stringify", "get<PERSON>son", "parse", "undefined"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/util/storageUtils.js"], "sourcesContent": ["const storage = {\n  set(key, value) {\n    localStorage.setItem(key, value);\n  },\n  get(key) {\n    return localStorage.getItem(key);\n  },\n  remove(key) {\n    localStorage.removeItem(key);\n  },\n  set<PERSON><PERSON>(key, value) {\n    this.set(key, JSON.stringify(value));\n  },\n  get<PERSON><PERSON>(key) {\n    const value = this.get(key);\n    if (value) {\n      return JSON.parse(value);\n    }\n    return undefined;\n  }\n}\nexport default storage;\n"], "mappings": "AAAA,MAAMA,OAAO,GAAG;EACdC,GAAGA,CAACC,GAAG,EAAEC,KAAK,EAAE;IACdC,YAAY,CAACC,OAAO,CAACH,GAAG,EAAEC,KAAK,CAAC;EAClC,CAAC;EACDG,GAAGA,CAACJ,GAAG,EAAE;IACP,OAAOE,YAAY,CAACG,OAAO,CAACL,GAAG,CAAC;EAClC,CAAC;EACDM,MAAMA,CAACN,GAAG,EAAE;IACVE,YAAY,CAACK,UAAU,CAACP,GAAG,CAAC;EAC9B,CAAC;EACDQ,OAAOA,CAACR,GAAG,EAAEC,KAAK,EAAE;IAClB,IAAI,CAACF,GAAG,CAACC,GAAG,EAAES,IAAI,CAACC,SAAS,CAACT,KAAK,CAAC,CAAC;EACtC,CAAC;EACDU,OAAOA,CAACX,GAAG,EAAE;IACX,MAAMC,KAAK,GAAG,IAAI,CAACG,GAAG,CAACJ,GAAG,CAAC;IAC3B,IAAIC,KAAK,EAAE;MACT,OAAOQ,IAAI,CAACG,KAAK,CAACX,KAAK,CAAC;IAC1B;IACA,OAAOY,SAAS;EAClB;AACF,CAAC;AACD,eAAef,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}