{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, resolveDirective as _resolveDirective, withDirectives as _withDirectives, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-9cf9abe0\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"app-container\"\n};\nconst _hoisted_2 = {\n  class: \"header\"\n};\nconst _hoisted_3 = {\n  class: \"content\"\n};\nconst _hoisted_4 = {\n  class: \"content-list\"\n};\nconst _hoisted_5 = {\n  class: \"content-item-warp\"\n};\nconst _hoisted_6 = {\n  class: \"image\"\n};\nconst _hoisted_7 = [\"src\"];\nconst _hoisted_8 = {\n  class: \"article-card-bone\"\n};\nconst _hoisted_9 = {\n  class: \"title-wrap\"\n};\nconst _hoisted_10 = {\n  class: \"title\"\n};\nconst _hoisted_11 = {\n  class: \"label create-time\"\n};\nconst _hoisted_12 = {\n  class: \"abstruct\"\n};\nconst _hoisted_13 = {\n  key: 0,\n  class: \"status\"\n};\nconst _hoisted_14 = {\n  key: 1,\n  class: \"divider\"\n};\nconst _hoisted_15 = {\n  key: 2,\n  class: \"status\",\n  style: {\n    \"background\": \"#07c160\",\n    \"color\": \"#fff\"\n  }\n};\nconst _hoisted_16 = {\n  key: 3,\n  class: \"divider\"\n};\nconst _hoisted_17 = {\n  key: 4,\n  class: \"status\",\n  style: {\n    \"background\": \"green\",\n    \"color\": \"#fff\"\n  }\n};\nconst _hoisted_18 = {\n  class: \"count-wrapper\"\n};\nconst _hoisted_19 = {\n  class: \"count\"\n};\nconst _hoisted_20 = {\n  class: \"article-action-list\"\n};\nconst _hoisted_21 = [\"onClick\"];\nconst _hoisted_22 = [\"onClick\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_Plus = _resolveComponent(\"Plus\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_page = _resolveComponent(\"page\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_form, {\n    inline: true,\n    model: $setup.searchParam,\n    class: \"demo-form-inline\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      label: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"small\",\n        class: \"search-input\",\n        modelValue: $setup.searchParam.keyword,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.searchParam.keyword = $event),\n        placeholder: \"请输入关键字\"\n      }, null, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_button, {\n        size: \"small\",\n        class: \"search-btn\",\n        type: \"primary\",\n        onClick: $setup.search\n      }, {\n        default: _withCtx(() => [_createTextVNode(\"搜索\")]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onClick\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        size: \"small\",\n        type: \"primary\",\n        onClick: _cache[1] || (_cache[1] = $event => $setup.edit())\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_Plus)]),\n          _: 1 /* STABLE */\n        }), _createTextVNode(\" 新增 \")]),\n        _: 1 /* STABLE */\n      })]),\n\n      _: 1 /* STABLE */\n    })]),\n\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\"])]), _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [!$setup.list || !$setup.list.length ? (_openBlock(), _createBlock(_component_el_empty, {\n    key: 0\n  })) : _createCommentVNode(\"v-if\", true), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.list, item => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"content-item\",\n      key: item.id + ''\n    }, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"a\", _hoisted_6, [_createElementVNode(\"img\", {\n      src: item.image\n    }, null, 8 /* PROPS */, _hoisted_7)]), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"a\", _hoisted_10, _toDisplayString(item.userName), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_11, _toDisplayString(item.createTime), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_12, [item.status ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, _toDisplayString($setup.statusMap[item.status]), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), item.status && item.jobTitle ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14)) : _createCommentVNode(\"v-if\", true), item.jobTitle ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, _toDisplayString(item.jobTitle), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), item.mobile ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16)) : _createCommentVNode(\"v-if\", true), item.mobile ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, _toDisplayString(item.mobile), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"ul\", _hoisted_19, [_createElementVNode(\"li\", null, \"直播 \" + _toDisplayString(item.lessonNum || 0), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"span\", {\n      class: \"icon-label\",\n      onClick: $event => $setup.edit(item.id)\n    }, \"修改\", 8 /* PROPS */, _hoisted_21), _createElementVNode(\"span\", {\n      class: \"icon-label\",\n      onClick: $event => $setup.remove(item)\n    }, \"删除\", 8 /* PROPS */, _hoisted_22)])])])])]);\n  }), 128 /* KEYED_FRAGMENT */))])])), [[_directive_loading, $setup.dataLoading]]), _createVNode(_component_page, {\n    total: $setup.total,\n    \"current-change\": $setup.currentChange,\n    \"size-change\": $setup.sizeChange,\n    \"page-size\": $setup.searchParam.size\n  }, null, 8 /* PROPS */, [\"total\", \"current-change\", \"size-change\", \"page-size\"])]);\n}", "map": {"version": 3, "names": ["class", "style", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_form", "inline", "model", "$setup", "searchParam", "_component_el_form_item", "label", "_component_el_input", "size", "keyword", "$event", "placeholder", "_component_el_button", "type", "onClick", "search", "_cache", "edit", "_component_el_icon", "_component_Plus", "_hoisted_3", "_hoisted_4", "list", "length", "_createBlock", "_component_el_empty", "key", "_Fragment", "_renderList", "item", "id", "_hoisted_5", "_hoisted_6", "src", "image", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_toDisplayString", "userName", "_hoisted_11", "createTime", "_hoisted_12", "status", "_hoisted_13", "statusMap", "jobTitle", "_hoisted_14", "_hoisted_15", "mobile", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "lessonNum", "_hoisted_20", "_hoisted_21", "remove", "_hoisted_22", "dataLoading", "_component_page", "total", "currentChange", "sizeChange"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/live/lecturer/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"header\">\n      <el-form :inline=\"true\" :model=\"searchParam\" class=\"demo-form-inline\">\n        <el-form-item label=\"\">\n          <el-input size=\"small\" class=\"search-input\" v-model=\"searchParam.keyword\" placeholder=\"请输入关键字\"></el-input>\n          <el-button size=\"small\" class=\"search-btn\" type=\"primary\" @click=\"search\">搜索</el-button>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" type=\"primary\" @click=\"edit()\">\n            <el-icon><Plus /></el-icon>\n            新增\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"content\" v-loading=\"dataLoading\">\n      <div class=\"content-list\">\n        <el-empty v-if=\"!list || !list.length\"/>\n        <div class=\"content-item\" v-for=\"item in list\" :key=\"item.id + ''\">\n          <div class=\"content-item-warp\">\n            <a class=\"image\">\n              <img :src=\"item.image\">\n            </a>\n            <div class=\"article-card-bone\">\n              <div class=\"title-wrap\">\n                <a class=\"title\">{{item.userName}}</a>\n                <span class=\"label create-time\">{{item.createTime}}</span>\n              </div>\n              <div class=\"abstruct\">\n                <div class=\"status\" v-if=\"item.status\">{{statusMap[item.status]}}</div>\n                <div class=\"divider\" v-if=\"item.status && item.jobTitle\"></div>\n                <div class=\"status\" style=\"background: #07c160;color: #fff;\" v-if=\"item.jobTitle\">{{item.jobTitle}}</div>\n                <div class=\"divider\" v-if=\"item.mobile\"></div>\n                <div class=\"status\" style=\"background: green;color: #fff;\" v-if=\"item.mobile\">{{item.mobile}}</div>\n              </div>\n              <div class=\"count-wrapper\">\n                <ul class=\"count\">\n                  <li>直播 {{item.lessonNum || 0}}</li>\n                </ul>\n                <div class=\"article-action-list\">\n                  <span class=\"icon-label\" @click=\"edit(item.id)\">修改</span>\n                  <span class=\"icon-label\" @click=\"remove(item)\">删除</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <page :total=\"total\" :current-change=\"currentChange\" :size-change=\"sizeChange\" :page-size=\"searchParam.size\"></page>\n  </div>\n</template>\n\n<script>\n  import {ref} from \"vue\"\n  import router from \"../../../router\"\n  import {deleteLecturer, findList} from \"@/api/lecturer\"\n  import Page from \"../../../components/Page\"\n  import {confirm, success} from \"@/util/tipsUtils\";\n\n  export default {\n    name: \"LecturerIndex\",\n  components: {\n    Page,\n  },\n  setup() {\n    const statusMap = {\n      \"published\": \"已发布\",\n      \"delete\": \"已删除\"\n    }\n    const list = ref([])\n    const total = ref(0)\n    const dataLoading = ref(true)\n    const searchParam = ref({\n      keyword: \"\",\n      status: \"\",\n      size: 20,\n      current: 1\n    })\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true\n      findList(searchParam.value, (res) => {\n        dataLoading.value = false\n        if (!res) {return;}\n        list.value = res.list;\n        total.value = res.total;\n      })\n    }\n    loadList();\n    // 搜索\n    const search = () => {\n      loadList();\n    }\n    // 编辑\n    const edit = (id) => {\n      router.push({path: \"/live/lecturer/edit\", query: { id : id }})\n    }\n    // 编辑\n    const remove = (item) => {\n      confirm(\"确认删除讲师 \" + item.name + \" 吗？\", \"提示\", () => {\n        deleteLecturer(item.id, () => {\n          success(\"删除成功\")\n          loadList()\n        })\n      }, () => {\n      })\n    }\n    const currentChange = (currentPage) => {\n      searchParam.value.current = currentPage;\n      loadList();\n    }\n    const sizeChange = (s) => {\n      searchParam.value.size = s;\n      loadList();\n    }\n    return {\n      list,\n      total,\n      searchParam,\n      search,\n      edit,\n      currentChange,\n      sizeChange,\n      remove,\n      statusMap,\n      dataLoading,\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n  .app-container {\n    margin: 20px;\n    .content-list {\n      margin: 0;\n      padding: 0;\n      border: 0;\n      font: inherit;\n      vertical-align: baseline;\n      .content-item {\n        padding: 24px 12px;\n        line-height: 1;\n        font-size: 14px;\n        color: #666;\n        border-bottom: 1px solid #e8e8e8;\n        position: relative;\n        background: #ffffff;\n        &:last-child {\n          border-bottom: 0;\n        }\n        .content-item-warp {\n          position: relative;\n          display: flex;\n          .image {\n            width: 108px;\n            min-width: 108px;\n            height: 108px;\n            margin-right: 24px;\n            position: relative;\n            overflow: hidden;\n            border-radius: 4px;\n            border: 1px solid #e8e8e8;\n            cursor: default;\n            img {\n              width: 100%;\n              height: 100%;\n              transition: all .5s ease-out .1s;\n              -o-object-fit: cover;\n              object-fit: cover;\n              -o-object-position: center;\n              object-position: center;\n              &:hover {\n                transform: matrix(1.04,0,0,1.04,0,0);\n                -webkit-backface-visibility: hidden;\n                backface-visibility: hidden;\n              }\n            }\n          }\n          .article-card-bone {\n            width: 100%;\n            display: flex;\n            flex-direction: column;\n            min-width: 0;\n            .title-wrap {\n              display: flex;\n              justify-content: space-between;\n              margin-top: 0;\n              .title {\n                font-size: 16px;\n                overflow: hidden;\n                white-space: nowrap;\n                text-overflow: ellipsis;\n                line-height: 24px;\n                font-weight: 600;\n                display: block;\n                color: #222;\n                cursor: text;\n              }\n              .create-time {\n                color: #999;\n                line-height: 24px;\n                margin-left: 12px;\n                flex-shrink: 0;\n              }\n            }\n            .abstruct {\n              line-height: 20px;\n              margin-top: 20px;\n              height: 20px;\n              display: flex;\n              align-items: flex-end;\n              .status {\n                color: #999;\n                border: none;\n                background-color: #f5f5f5;\n                padding: 0 8px;\n                line-height: 20px;\n                font-size: 12px;\n                border-radius: 2px;\n                white-space: nowrap;\n                display: inline-block;\n                box-sizing: border-box;\n                transition: all .3s;\n                margin-right: 8px;\n              }\n              .article-card .byte-tag-simple {\n                margin-right: 8px;\n              }\n              .divider {\n                width: 1px;\n                height: 12px;\n                margin: 4px 10px 4px 4px;\n                background: #bfbfbf;\n              }\n              .icon {\n                margin-right: 8px;\n                svg {\n                  vertical-align: bottom;\n                  &:focus {\n                    outline: none;\n                  }\n                }\n              }\n            }\n            .count-wrapper {\n              margin-top: 24px;\n              display: flex;\n              justify-content: space-between;\n              .count {\n                line-height: 20px;\n                position: relative;\n                li {\n                  display: inline-block;\n                  margin-right: 24px;\n                  &:after {\n                    content: \"\\ff65\";\n                    font-size: 20px;\n                    margin: 0 8px;\n                    line-height: 0;\n                    position: absolute;\n                    top: 10px;\n                    color: #666;\n                  }\n                  &:last-child:after {\n                    content: \"\"\n                  }\n                }\n              }\n              .article-action-list {\n                display: flex;\n                line-height: 20px;\n                flex: 1 0 auto;\n                justify-content: flex-end;\n                .icon-label {\n                  cursor: pointer;\n                  font-size: 14px;\n                  line-height: 20px;\n                  display: flex;\n                  color: #222;\n                  font-weight: 400;\n                  margin-left: 24px;\n                  &:first-child {\n                    margin-left: 0;\n                  }\n                  &:hover {\n                    color: $--color-primary;\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    .search-input {\n      width: 242px;\n    }\n  }\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAQ;;EAcdA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAc;;EAGhBA,KAAK,EAAC;AAAmB;;EACzBA,KAAK,EAAC;AAAO;;;EAGXA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAY;;EAClBA,KAAK,EAAC;AAAO;;EACVA,KAAK,EAAC;AAAmB;;EAE5BA,KAAK,EAAC;AAAU;;;EACdA,KAAK,EAAC;;;;EACNA,KAAK,EAAC;;;;EACNA,KAAK,EAAC,QAAQ;EAACC,KAAwC,EAAxC;IAAA;IAAA;EAAA;;;;EACfD,KAAK,EAAC;;;;EACNA,KAAK,EAAC,QAAQ;EAACC,KAAsC,EAAtC;IAAA;IAAA;EAAA;;;EAEjBD,KAAK,EAAC;AAAe;;EACpBA,KAAK,EAAC;AAAO;;EAGZA,KAAK,EAAC;AAAqB;;;;;;;;;;;;;uBAvC9CE,mBAAA,CAkDM,OAlDNC,UAkDM,GAjDJC,mBAAA,CAaM,OAbNC,UAaM,GAZJC,YAAA,CAWUC,kBAAA;IAXAC,MAAM,EAAE,IAAI;IAAGC,KAAK,EAAEC,MAAA,CAAAC,WAAW;IAAEX,KAAK,EAAC;;sBACjD,MAGe,CAHfM,YAAA,CAGeM,uBAAA;MAHDC,KAAK,EAAC;IAAE;wBACpB,MAA0G,CAA1GP,YAAA,CAA0GQ,mBAAA;QAAhGC,IAAI,EAAC,OAAO;QAACf,KAAK,EAAC,cAAc;oBAAUU,MAAA,CAAAC,WAAW,CAACK,OAAO;mEAAnBN,MAAA,CAAAC,WAAW,CAACK,OAAO,GAAAC,MAAA;QAAEC,WAAW,EAAC;+CACtFZ,YAAA,CAAwFa,oBAAA;QAA7EJ,IAAI,EAAC,OAAO;QAACf,KAAK,EAAC,YAAY;QAACoB,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAEX,MAAA,CAAAY;;0BAAQ,MAAE,C,iBAAF,IAAE,E;;;;QAE9EhB,YAAA,CAKeM,uBAAA;wBAJb,MAGY,CAHZN,YAAA,CAGYa,oBAAA;QAHDJ,IAAI,EAAC,OAAO;QAACK,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAAE,MAAA,QAAAA,MAAA,MAAAN,MAAA,IAAEP,MAAA,CAAAc,IAAI;;0BACjD,MAA2B,CAA3BlB,YAAA,CAA2BmB,kBAAA;4BAAlB,MAAQ,CAARnB,YAAA,CAAQoB,eAAA,E;;6BAAU,MAE7B,E;;;;;;;;iEAINxB,mBAAA,CAiCM,OAjCNyB,UAiCM,GAhCJvB,mBAAA,CA+BM,OA/BNwB,UA+BM,G,CA9BalB,MAAA,CAAAmB,IAAI,KAAKnB,MAAA,CAAAmB,IAAI,CAACC,MAAM,I,cAArCC,YAAA,CAAwCC,mBAAA;IAAAC,GAAA;EAAA,M,sDACxC/B,mBAAA,CA4BMgC,SAAA,QAAAC,WAAA,CA5BmCzB,MAAA,CAAAmB,IAAI,EAAZO,IAAI;yBAArClC,mBAAA,CA4BM;MA5BDF,KAAK,EAAC,cAAc;MAAuBiC,GAAG,EAAEG,IAAI,CAACC,EAAE;QAC1DjC,mBAAA,CA0BM,OA1BNkC,UA0BM,GAzBJlC,mBAAA,CAEI,KAFJmC,UAEI,GADFnC,mBAAA,CAAuB;MAAjBoC,GAAG,EAAEJ,IAAI,CAACK;2CAElBrC,mBAAA,CAqBM,OArBNsC,UAqBM,GApBJtC,mBAAA,CAGM,OAHNuC,UAGM,GAFJvC,mBAAA,CAAsC,KAAtCwC,WAAsC,EAAAC,gBAAA,CAAnBT,IAAI,CAACU,QAAQ,kBAChC1C,mBAAA,CAA0D,QAA1D2C,WAA0D,EAAAF,gBAAA,CAAxBT,IAAI,CAACY,UAAU,iB,GAEnD5C,mBAAA,CAMM,OANN6C,WAMM,GALsBb,IAAI,CAACc,MAAM,I,cAArChD,mBAAA,CAAuE,OAAvEiD,WAAuE,EAAAN,gBAAA,CAA9BnC,MAAA,CAAA0C,SAAS,CAAChB,IAAI,CAACc,MAAM,qB,mCACnCd,IAAI,CAACc,MAAM,IAAId,IAAI,CAACiB,QAAQ,I,cAAvDnD,mBAAA,CAA+D,OAA/DoD,WAA+D,K,mCACIlB,IAAI,CAACiB,QAAQ,I,cAAhFnD,mBAAA,CAAyG,OAAzGqD,WAAyG,EAAAV,gBAAA,CAArBT,IAAI,CAACiB,QAAQ,oB,mCACtEjB,IAAI,CAACoB,MAAM,I,cAAtCtD,mBAAA,CAA8C,OAA9CuD,WAA8C,K,mCACmBrB,IAAI,CAACoB,MAAM,I,cAA5EtD,mBAAA,CAAmG,OAAnGwD,WAAmG,EAAAb,gBAAA,CAAnBT,IAAI,CAACoB,MAAM,oB,qCAE7FpD,mBAAA,CAQM,OARNuD,WAQM,GAPJvD,mBAAA,CAEK,MAFLwD,WAEK,GADHxD,mBAAA,CAAmC,YAA/B,KAAG,GAAAyC,gBAAA,CAAET,IAAI,CAACyB,SAAS,sB,GAEzBzD,mBAAA,CAGM,OAHN0D,WAGM,GAFJ1D,mBAAA,CAAyD;MAAnDJ,KAAK,EAAC,YAAY;MAAEqB,OAAK,EAAAJ,MAAA,IAAEP,MAAA,CAAAc,IAAI,CAACY,IAAI,CAACC,EAAE;OAAG,IAAE,iBAAA0B,WAAA,GAClD3D,mBAAA,CAAwD;MAAlDJ,KAAK,EAAC,YAAY;MAAEqB,OAAK,EAAAJ,MAAA,IAAEP,MAAA,CAAAsD,MAAM,CAAC5B,IAAI;OAAG,IAAE,iBAAA6B,WAAA,E;6DA1B/BvD,MAAA,CAAAwD,WAAW,E,GAkC3C5D,YAAA,CAAoH6D,eAAA;IAA7GC,KAAK,EAAE1D,MAAA,CAAA0D,KAAK;IAAG,gBAAc,EAAE1D,MAAA,CAAA2D,aAAa;IAAG,aAAW,EAAE3D,MAAA,CAAA4D,UAAU;IAAG,WAAS,EAAE5D,MAAA,CAAAC,WAAW,CAACI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}