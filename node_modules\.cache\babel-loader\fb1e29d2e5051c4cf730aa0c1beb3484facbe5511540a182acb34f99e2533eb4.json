{"ast": null, "code": "import { get, put, post, del } from \"../../util/requestUtils\";\nexport function findList(params, success) {\n  return get(\"/point/point/list\", params, success);\n}\nexport function getPoint(id, success) {\n  return get(\"/point/public-api/point\", {\n    id: id\n  }, success);\n}\nexport function updatePoint(data, success) {\n  return put(\"/point/point\", data, success);\n}\nexport function savePoint(data, success) {\n  return post(\"/point/point\", data, success);\n}\nexport function deletePoint(id, success) {\n  return del(\"/point/point\", {\n    id: id\n  }, success);\n}\nexport function findPointChannelList(params, success) {\n  return get(\"/point/channel/all\", params, success);\n}\nexport function findPointChannelRelationList(params, success) {\n  return get(\"/point/point/channel/relation/list\", params, success);\n}\nexport function updatePointChannel(data, success) {\n  return put(\"/point/point/channel/relation\", data, success);\n}", "map": {"version": 3, "names": ["get", "put", "post", "del", "findList", "params", "success", "getPoint", "id", "updatePoint", "data", "savePoint", "deletePoint", "findPointChannelList", "findPointChannelRelationList", "updatePointChannel"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/api/point/index.js"], "sourcesContent": ["import { get, put, post, del } from \"../../util/requestUtils\"\n\nexport function findList(params, success) {\n  return get(\"/point/point/list\", params, success)\n}\n\nexport function getPoint(id, success) {\n  return get(\"/point/public-api/point\", {id: id}, success)\n}\n\nexport function updatePoint(data, success) {\n  return put(\"/point/point\", data, success)\n}\n\nexport function savePoint(data, success) {\n  return post(\"/point/point\", data, success)\n}\n\nexport function deletePoint(id, success) {\n  return del(\"/point/point\", {id: id}, success)\n}\n\nexport function findPointChannelList(params, success) {\n  return get(\"/point/channel/all\", params, success)\n}\n\nexport function findPointChannelRelationList(params, success) {\n  return get(\"/point/point/channel/relation/list\", params, success)\n}\n\nexport function updatePointChannel(data, success) {\n  return put(\"/point/point/channel/relation\", data, success)\n}\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,QAAQ,yBAAyB;AAE7D,OAAO,SAASC,QAAQA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACxC,OAAON,GAAG,CAAC,mBAAmB,EAAEK,MAAM,EAAEC,OAAO,CAAC;AAClD;AAEA,OAAO,SAASC,QAAQA,CAACC,EAAE,EAAEF,OAAO,EAAE;EACpC,OAAON,GAAG,CAAC,yBAAyB,EAAE;IAACQ,EAAE,EAAEA;EAAE,CAAC,EAAEF,OAAO,CAAC;AAC1D;AAEA,OAAO,SAASG,WAAWA,CAACC,IAAI,EAAEJ,OAAO,EAAE;EACzC,OAAOL,GAAG,CAAC,cAAc,EAAES,IAAI,EAAEJ,OAAO,CAAC;AAC3C;AAEA,OAAO,SAASK,SAASA,CAACD,IAAI,EAAEJ,OAAO,EAAE;EACvC,OAAOJ,IAAI,CAAC,cAAc,EAAEQ,IAAI,EAAEJ,OAAO,CAAC;AAC5C;AAEA,OAAO,SAASM,WAAWA,CAACJ,EAAE,EAAEF,OAAO,EAAE;EACvC,OAAOH,GAAG,CAAC,cAAc,EAAE;IAACK,EAAE,EAAEA;EAAE,CAAC,EAAEF,OAAO,CAAC;AAC/C;AAEA,OAAO,SAASO,oBAAoBA,CAACR,MAAM,EAAEC,OAAO,EAAE;EACpD,OAAON,GAAG,CAAC,oBAAoB,EAAEK,MAAM,EAAEC,OAAO,CAAC;AACnD;AAEA,OAAO,SAASQ,4BAA4BA,CAACT,MAAM,EAAEC,OAAO,EAAE;EAC5D,OAAON,GAAG,CAAC,oCAAoC,EAAEK,MAAM,EAAEC,OAAO,CAAC;AACnE;AAEA,OAAO,SAASS,kBAAkBA,CAACL,IAAI,EAAEJ,OAAO,EAAE;EAChD,OAAOL,GAAG,CAAC,+BAA+B,EAAES,IAAI,EAAEJ,OAAO,CAAC;AAC5D"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}