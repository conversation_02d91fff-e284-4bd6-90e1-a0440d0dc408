{"ast": null, "code": "import { ref } from \"vue\";\nimport Page from \"@/components/Page\";\nimport { gotoCertificateTemplateEdit } from \"@/router/goto\";\nimport { findCertificateTemplateList, deleteCertificateTemplate } from \"@/api/certificate\";\nimport { confirm, success } from \"@/util/tipsUtils\";\nexport default {\n  name: \"LearnReportSignUpIndex\",\n  components: {\n    Page\n  },\n  setup() {\n    const templateList = ref([]);\n    const params = ref({\n      current: 1,\n      size: 20,\n      neqStatusList: [\"deleted\"]\n    });\n    const loadList = () => {\n      findCertificateTemplateList(params.value, res => {\n        console.log(res);\n        if (res) {\n          total.value = res.total;\n          templateList.value = res.list;\n        }\n      });\n    };\n    loadList();\n    const total = ref(0);\n    const currentChange = c => {\n      params.value.current = c;\n      loadList();\n    };\n    const sizeChange = s => {\n      params.value.size = s;\n      loadList();\n    };\n    const search = () => {\n      loadList();\n    };\n    const remove = id => {\n      confirm(\"确认删除该证书模版？\", \"提示\", () => {\n        deleteCertificateTemplate(id, () => {\n          success(\"删除成功\");\n          loadList();\n        });\n      });\n    };\n    const active = id => {\n      confirm(\"确认启用该证书模版？\", \"提示\", () => {\n        deleteCertificateTemplate(id, () => {\n          success(\"启用成功\");\n          loadList();\n        });\n      });\n    };\n    const inactive = id => {\n      confirm(\"确认禁用该证书模版？\", \"提示\", () => {\n        deleteCertificateTemplate(id, () => {\n          success(\"禁用成功\");\n          loadList();\n        });\n      });\n    };\n    return {\n      remove,\n      gotoCertificateTemplateEdit,\n      search,\n      params,\n      total,\n      currentChange,\n      sizeChange,\n      templateList,\n      inactive,\n      active\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "Page", "gotoCertificateTemplateEdit", "findCertificateTemplateList", "deleteCertificateTemplate", "confirm", "success", "name", "components", "setup", "templateList", "params", "current", "size", "neqStatusList", "loadList", "value", "res", "console", "log", "total", "list", "currentChange", "c", "sizeChange", "s", "search", "remove", "id", "active", "inactive"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/certificate/template/index.vue"], "sourcesContent": ["<template>\n  <div class=\"cert-template-wrap\">\n    <div class=\"cert-template-header\">\n      <el-form :inline=\"true\" :model=\"params\" class=\"form-inline\">\n        <el-form-item label=\"证书名称\">\n          <el-input size=\"small\" @keydown.enter=\"search\" class=\"search-input\" v-model=\"params.keyword\" placeholder=\"请输入关键字\">\n            <template #suffix>\n              <i @click=\"search\" class=\"el-input__icon el-icon-search search-btn\"></i>\n            </template>\n          </el-input>\n        </el-form-item>\n        <el-form-item label=\"状态\" class=\"select\">\n          <el-select size=\"small\" v-model=\"params.status\" @change=\"search\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"启用\" value=\"active\"></el-option>\n            <el-option label=\"禁用\" value=\"inactive\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" type=\"primary\" @click=\"gotoCertificateTemplateEdit()\">\n            <el-icon style=\"vertical-align: middle\">\n              <Plus />\n            </el-icon>\n            <span style=\"vertical-align: middle\">新增</span>\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"cert-template-main\">\n      <el-table :data=\"templateList\">\n        <el-table-column label=\"序号\" type=\"index\">\n        </el-table-column>\n        <el-table-column label=\"背景图\" prop=\"desgin\">\n          <template #default=\"scope\">\n            <img class=\"desgin\" :src=\"scope.row.design\" />\n          </template>\n        </el-table-column>\n        <el-table-column label=\"证书名称\" prop=\"name\"></el-table-column>\n        <el-table-column label=\"证书描述\" prop=\"description\"></el-table-column>\n        <el-table-column label=\"颁发机构\" prop=\"awardingOrganization\"></el-table-column>\n        <el-table-column label=\"颁发人员\" prop=\"awarderName\"></el-table-column>\n        <el-table-column label=\"颁发条件\" prop=\"awardConditions\"></el-table-column>\n        <el-table-column label=\"到期策略\" prop=\"validityPolicy\"></el-table-column>\n        <el-table-column label=\"状态\" prop=\"statusName\"></el-table-column>\n        <el-table-column label=\"操作\">\n          <template #default=\"scope\">\n            <div class=\"opt-btn-wrap\">\n              <div class=\"opt-btn-item\">\n                <el-button size=\"small\">预览</el-button>\n              </div>\n              <div class=\"opt-btn-item\">\n                <el-button size=\"small\" @click=\"gotoCertificateTemplateEdit(scope.row.id)\">编辑</el-button>\n              </div>\n              <div class=\"opt-btn-item\" v-if=\"scope.row.status === 'inactive'\">\n                <el-button size=\"small\" type=\"primary\" @click=\"active(scope.row.id)\">启用</el-button>\n              </div>\n              <div class=\"opt-btn-item\" v-if=\"scope.row.status === 'active'\">\n                <el-button size=\"small\" type=\"warning\" @click=\"inactive(scope.row.id)\">禁用</el-button>\n              </div>\n  <!--            <div class=\"opt-btn-item\">-->\n  <!--              <el-button size=\"small\" type=\"primary\">关联</el-button>-->\n  <!--            </div>-->\n              <div class=\"opt-btn-item\">\n                <el-button size=\"small\" type=\"danger\" @click=\"remove(scope.row.id)\">删除</el-button>\n              </div>\n            </div>\n          </template>\n        </el-table-column>\n      </el-table>\n      <page :total=\"total\" :size-change=\"sizeChange\" :current-change=\"currentChange\" :page-size=\"params.size\"/>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {ref} from \"vue\"\nimport Page from \"@/components/Page\";\nimport {gotoCertificateTemplateEdit} from \"@/router/goto\";\nimport {findCertificateTemplateList, deleteCertificateTemplate} from \"@/api/certificate\";\nimport {confirm, success} from \"@/util/tipsUtils\";\nexport default {\n  name: \"LearnReportSignUpIndex\",\n  components: {Page},\n  setup() {\n    const templateList = ref([])\n    const params = ref({\n      current: 1,\n      size: 20,\n      neqStatusList: [\"deleted\"]\n    })\n    const loadList = () => {\n      findCertificateTemplateList(params.value, res => {\n        console.log(res)\n        if (res) {\n          total.value = res.total;\n          templateList.value = res.list;\n        }\n      })\n    }\n    loadList()\n    const total = ref(0)\n    const currentChange = (c) => {\n      params.value.current = c;\n      loadList();\n    }\n    const sizeChange = (s) => {\n      params.value.size = s;\n      loadList();\n    }\n    const search = () => {\n      loadList();\n    }\n    const remove = (id) => {\n      confirm(\"确认删除该证书模版？\", \"提示\", () => {\n        deleteCertificateTemplate(id, () => {\n          success(\"删除成功\");\n          loadList();\n        })\n      })\n    }\n    const active = (id) => {\n      confirm(\"确认启用该证书模版？\", \"提示\", () => {\n        deleteCertificateTemplate(id, () => {\n          success(\"启用成功\");\n          loadList();\n        })\n      })\n    }\n    const inactive = (id) => {\n      confirm(\"确认禁用该证书模版？\", \"提示\", () => {\n        deleteCertificateTemplate(id, () => {\n          success(\"禁用成功\");\n          loadList();\n        })\n      })\n    }\n    return {\n      remove,\n      gotoCertificateTemplateEdit,\n      search,\n      params,\n      total,\n      currentChange,\n      sizeChange,\n      templateList,\n      inactive,\n      active\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n  .cert-template-wrap {\n    margin: 20px;\n    font-size: 12px;\n    .cert-template-main {\n      ::v-deep .el-table {\n        font-size: 12px;\n        .el-table__empty-block {\n          line-height: 400px;\n          .el-table__empty-text {\n            line-height: 400px;\n          }\n        }\n        th, td {\n          padding: 6px 0;\n        }\n      }\n    }\n    .opt-btn-wrap {\n      //display: flex;\n    }\n    .opt-btn-item {\n      width: 50%;\n      display: inline-block;\n      margin: 2px;\n    }\n  }\n  .desgin {\n    width: 116px;\n    height: 76px;\n  }\n</style>\n"], "mappings": "AA2EA,SAAQA,GAAG,QAAO,KAAI;AACtB,OAAOC,IAAG,MAAO,mBAAmB;AACpC,SAAQC,2BAA2B,QAAO,eAAe;AACzD,SAAQC,2BAA2B,EAAEC,yBAAyB,QAAO,mBAAmB;AACxF,SAAQC,OAAO,EAAEC,OAAO,QAAO,kBAAkB;AACjD,eAAe;EACbC,IAAI,EAAE,wBAAwB;EAC9BC,UAAU,EAAE;IAACP;EAAI,CAAC;EAClBQ,KAAKA,CAAA,EAAG;IACN,MAAMC,YAAW,GAAIV,GAAG,CAAC,EAAE;IAC3B,MAAMW,MAAK,GAAIX,GAAG,CAAC;MACjBY,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,aAAa,EAAE,CAAC,SAAS;IAC3B,CAAC;IACD,MAAMC,QAAO,GAAIA,CAAA,KAAM;MACrBZ,2BAA2B,CAACQ,MAAM,CAACK,KAAK,EAAEC,GAAE,IAAK;QAC/CC,OAAO,CAACC,GAAG,CAACF,GAAG;QACf,IAAIA,GAAG,EAAE;UACPG,KAAK,CAACJ,KAAI,GAAIC,GAAG,CAACG,KAAK;UACvBV,YAAY,CAACM,KAAI,GAAIC,GAAG,CAACI,IAAI;QAC/B;MACF,CAAC;IACH;IACAN,QAAQ,EAAC;IACT,MAAMK,KAAI,GAAIpB,GAAG,CAAC,CAAC;IACnB,MAAMsB,aAAY,GAAKC,CAAC,IAAK;MAC3BZ,MAAM,CAACK,KAAK,CAACJ,OAAM,GAAIW,CAAC;MACxBR,QAAQ,EAAE;IACZ;IACA,MAAMS,UAAS,GAAKC,CAAC,IAAK;MACxBd,MAAM,CAACK,KAAK,CAACH,IAAG,GAAIY,CAAC;MACrBV,QAAQ,EAAE;IACZ;IACA,MAAMW,MAAK,GAAIA,CAAA,KAAM;MACnBX,QAAQ,EAAE;IACZ;IACA,MAAMY,MAAK,GAAKC,EAAE,IAAK;MACrBvB,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM;QAChCD,yBAAyB,CAACwB,EAAE,EAAE,MAAM;UAClCtB,OAAO,CAAC,MAAM,CAAC;UACfS,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC;IACH;IACA,MAAMc,MAAK,GAAKD,EAAE,IAAK;MACrBvB,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM;QAChCD,yBAAyB,CAACwB,EAAE,EAAE,MAAM;UAClCtB,OAAO,CAAC,MAAM,CAAC;UACfS,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC;IACH;IACA,MAAMe,QAAO,GAAKF,EAAE,IAAK;MACvBvB,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM;QAChCD,yBAAyB,CAACwB,EAAE,EAAE,MAAM;UAClCtB,OAAO,CAAC,MAAM,CAAC;UACfS,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC;IACH;IACA,OAAO;MACLY,MAAM;MACNzB,2BAA2B;MAC3BwB,MAAM;MACNf,MAAM;MACNS,KAAK;MACLE,aAAa;MACbE,UAAU;MACVd,YAAY;MACZoB,QAAQ;MACRD;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}