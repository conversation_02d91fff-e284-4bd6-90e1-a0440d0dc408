{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n// 目录API\nimport { findCategoryList } from \"@/api/exam/paper/category\";\nimport { ref, watch, nextTick } from \"vue\";\nexport default {\n  name: \"categoryTree\",\n  props: {\n    currentNodeKey: Number\n  },\n  setup(props, context) {\n    const filterText = ref(\"\");\n    const defaultProps = {\n      children: \"children\",\n      label: \"name\"\n    };\n    const treeData = ref([]);\n    let treeRef = ref(null);\n    watch([filterText], nv => {\n      treeRef.value.filter(nv);\n    });\n    const loadCategoryList = () => {\n      findCategoryList(0, true, res => {\n        // 获取部门列表中的根节点（父节点id为0的）（获取的根节点包含孩子）\n        function getRootNodes(nodeList) {\n          if (!nodeList || nodeList.length <= 0) {\n            return [];\n          }\n          // 递归获取节点的孩子节点\n          const getChildren = function (parent) {\n            const children = [];\n            for (let i = 0; i < nodeList.length; i++) {\n              const item = nodeList[i];\n              if (item.pid === parent.id) {\n                children.push(item);\n              }\n            }\n            parent.children = children;\n            if (children.length === 0) {\n              return;\n            }\n            for (let i = 0; i < children.length; i++) {\n              getChildren(children[i]);\n            }\n          };\n          const result = [];\n          for (let i = 0; i < nodeList.length; i++) {\n            const item = nodeList[i];\n            if (item.pid === 0 || item.pid === null) {\n              result.push(item);\n              getChildren(item);\n            }\n          }\n          return result;\n        }\n        treeData.value = getRootNodes(res);\n        console.log(treeData.value);\n      });\n    };\n    loadCategoryList();\n    let nodeKey = ref(props.currentNodeKey);\n    const treeFlag = ref(true);\n    watch(() => props.currentNodeKey, nv => {\n      nodeKey.value = nv;\n      treeFlag.value = false;\n      nextTick(() => {\n        treeFlag.value = true;\n      });\n      loadCategoryList();\n    });\n    const filterNode = function (value, data, node) {\n      console.log(node);\n      if (!value) {\n        return true;\n      }\n      return data.name.indexOf(value) !== -1;\n    };\n    const handleNodeClick = data => {\n      context.emit(\"node-click\", data, this);\n    };\n    return {\n      treeFlag,\n      nodeKey,\n      filterText,\n      defaultProps,\n      treeData,\n      treeRef,\n      filterNode,\n      handleNodeClick\n    };\n  }\n};", "map": {"version": 3, "names": ["findCategoryList", "ref", "watch", "nextTick", "name", "props", "currentNodeKey", "Number", "setup", "context", "filterText", "defaultProps", "children", "label", "treeData", "treeRef", "nv", "value", "filter", "loadCategoryList", "res", "getRootNodes", "nodeList", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parent", "i", "item", "pid", "id", "push", "result", "console", "log", "nodeKey", "treeFlag", "filterNode", "data", "node", "indexOf", "handleNodeClick", "emit"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/exam/paper/category/tree.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-input size=\"mini\" placeholder=\"输入关键字进行过滤\" v-model=\"filterText\"></el-input>\n    <el-tree size=\"mini\" ref=\"treeRef\" v-if=\"treeFlag\" :current-node-key=\"nodeKey\" node-key=\"id\" :filter-node-method=\"filterNode\" :highlight-current=\"true\" :data=\"treeData\" :props=\"defaultProps\" :expand-on-click-node=\"false\" @node-click=\"handleNodeClick\" class=\"el-tree\"></el-tree>\n  </div>\n</template>\n\n<script>\n// 目录API\nimport { findCategoryList } from \"@/api/exam/paper/category\"\nimport {ref, watch, nextTick} from \"vue\";\nexport default {\n  name: \"categoryTree\",\n  props: {\n    currentNodeKey: Number\n  },\n  setup(props, context) {\n    const filterText = ref(\"\");\n    const defaultProps = {\n      children: \"children\",\n      label: \"name\"\n    }\n    const treeData = ref([])\n    let treeRef = ref(null);\n    watch([filterText], (nv) => {\n      treeRef.value.filter(nv);\n    })\n    const loadCategoryList = () => {\n      findCategoryList(0, true, res => {\n        // 获取部门列表中的根节点（父节点id为0的）（获取的根节点包含孩子）\n        function getRootNodes(nodeList) {\n          if (!nodeList || nodeList.length <= 0) {\n            return [];\n          }\n          // 递归获取节点的孩子节点\n          const getChildren = function(parent) {\n            const children = [];\n            for (let i = 0; i < nodeList.length; i++) {\n              const item = nodeList[i];\n              if (item.pid === parent.id) {\n                children.push(item);\n              }\n            }\n            parent.children = children\n            if (children.length === 0) {\n              return;\n            }\n            for (let i = 0; i < children.length; i++) {\n              getChildren(children[i]);\n            }\n          }\n          const result = [];\n          for (let i = 0; i < nodeList.length; i++) {\n            const item = nodeList[i];\n            if (item.pid === 0 || item.pid === null) {\n              result.push(item);\n              getChildren(item);\n            }\n          }\n          return result;\n        }\n        treeData.value = getRootNodes(res);\n        console.log(treeData.value)\n      })\n    }\n    loadCategoryList()\n    let nodeKey = ref(props.currentNodeKey)\n    const treeFlag = ref(true)\n    watch(() => props.currentNodeKey, (nv) => {\n      nodeKey.value = nv\n      treeFlag.value =false\n      nextTick(() => {\n        treeFlag.value =true\n      })\n      loadCategoryList()\n    })\n    const filterNode = function(value, data, node) {\n      console.log(node)\n      if (!value) {\n        return true;\n      }\n      return data.name.indexOf(value) !== -1;\n    }\n    const handleNodeClick = (data) => {\n      context.emit(\"node-click\", data, this);\n    }\n    return {\n      treeFlag,\n      nodeKey,\n      filterText,\n      defaultProps,\n      treeData,\n      treeRef,\n      filterNode,\n      handleNodeClick\n    }\n  }\n}\n</script>\n<style scoped>\n  .el-tree {\n    margin-top: 10px;\n    min-height: 102px;\n  }\n</style>\n"], "mappings": ";AAQA;AACA,SAASA,gBAAe,QAAS,2BAA0B;AAC3D,SAAQC,GAAG,EAAEC,KAAK,EAAEC,QAAQ,QAAO,KAAK;AACxC,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,KAAK,EAAE;IACLC,cAAc,EAAEC;EAClB,CAAC;EACDC,KAAKA,CAACH,KAAK,EAAEI,OAAO,EAAE;IACpB,MAAMC,UAAS,GAAIT,GAAG,CAAC,EAAE,CAAC;IAC1B,MAAMU,YAAW,GAAI;MACnBC,QAAQ,EAAE,UAAU;MACpBC,KAAK,EAAE;IACT;IACA,MAAMC,QAAO,GAAIb,GAAG,CAAC,EAAE;IACvB,IAAIc,OAAM,GAAId,GAAG,CAAC,IAAI,CAAC;IACvBC,KAAK,CAAC,CAACQ,UAAU,CAAC,EAAGM,EAAE,IAAK;MAC1BD,OAAO,CAACE,KAAK,CAACC,MAAM,CAACF,EAAE,CAAC;IAC1B,CAAC;IACD,MAAMG,gBAAe,GAAIA,CAAA,KAAM;MAC7BnB,gBAAgB,CAAC,CAAC,EAAE,IAAI,EAAEoB,GAAE,IAAK;QAC/B;QACA,SAASC,YAAYA,CAACC,QAAQ,EAAE;UAC9B,IAAI,CAACA,QAAO,IAAKA,QAAQ,CAACC,MAAK,IAAK,CAAC,EAAE;YACrC,OAAO,EAAE;UACX;UACA;UACA,MAAMC,WAAU,GAAI,SAAAA,CAASC,MAAM,EAAE;YACnC,MAAMb,QAAO,GAAI,EAAE;YACnB,KAAK,IAAIc,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIJ,QAAQ,CAACC,MAAM,EAAEG,CAAC,EAAE,EAAE;cACxC,MAAMC,IAAG,GAAIL,QAAQ,CAACI,CAAC,CAAC;cACxB,IAAIC,IAAI,CAACC,GAAE,KAAMH,MAAM,CAACI,EAAE,EAAE;gBAC1BjB,QAAQ,CAACkB,IAAI,CAACH,IAAI,CAAC;cACrB;YACF;YACAF,MAAM,CAACb,QAAO,GAAIA,QAAO;YACzB,IAAIA,QAAQ,CAACW,MAAK,KAAM,CAAC,EAAE;cACzB;YACF;YACA,KAAK,IAAIG,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAId,QAAQ,CAACW,MAAM,EAAEG,CAAC,EAAE,EAAE;cACxCF,WAAW,CAACZ,QAAQ,CAACc,CAAC,CAAC,CAAC;YAC1B;UACF;UACA,MAAMK,MAAK,GAAI,EAAE;UACjB,KAAK,IAAIL,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIJ,QAAQ,CAACC,MAAM,EAAEG,CAAC,EAAE,EAAE;YACxC,MAAMC,IAAG,GAAIL,QAAQ,CAACI,CAAC,CAAC;YACxB,IAAIC,IAAI,CAACC,GAAE,KAAM,KAAKD,IAAI,CAACC,GAAE,KAAM,IAAI,EAAE;cACvCG,MAAM,CAACD,IAAI,CAACH,IAAI,CAAC;cACjBH,WAAW,CAACG,IAAI,CAAC;YACnB;UACF;UACA,OAAOI,MAAM;QACf;QACAjB,QAAQ,CAACG,KAAI,GAAII,YAAY,CAACD,GAAG,CAAC;QAClCY,OAAO,CAACC,GAAG,CAACnB,QAAQ,CAACG,KAAK;MAC5B,CAAC;IACH;IACAE,gBAAgB,EAAC;IACjB,IAAIe,OAAM,GAAIjC,GAAG,CAACI,KAAK,CAACC,cAAc;IACtC,MAAM6B,QAAO,GAAIlC,GAAG,CAAC,IAAI;IACzBC,KAAK,CAAC,MAAMG,KAAK,CAACC,cAAc,EAAGU,EAAE,IAAK;MACxCkB,OAAO,CAACjB,KAAI,GAAID,EAAC;MACjBmB,QAAQ,CAAClB,KAAI,GAAG,KAAI;MACpBd,QAAQ,CAAC,MAAM;QACbgC,QAAQ,CAAClB,KAAI,GAAG,IAAG;MACrB,CAAC;MACDE,gBAAgB,EAAC;IACnB,CAAC;IACD,MAAMiB,UAAS,GAAI,SAAAA,CAASnB,KAAK,EAAEoB,IAAI,EAAEC,IAAI,EAAE;MAC7CN,OAAO,CAACC,GAAG,CAACK,IAAI;MAChB,IAAI,CAACrB,KAAK,EAAE;QACV,OAAO,IAAI;MACb;MACA,OAAOoB,IAAI,CAACjC,IAAI,CAACmC,OAAO,CAACtB,KAAK,MAAM,CAAC,CAAC;IACxC;IACA,MAAMuB,eAAc,GAAKH,IAAI,IAAK;MAChC5B,OAAO,CAACgC,IAAI,CAAC,YAAY,EAAEJ,IAAI,EAAE,IAAI,CAAC;IACxC;IACA,OAAO;MACLF,QAAQ;MACRD,OAAO;MACPxB,UAAU;MACVC,YAAY;MACZG,QAAQ;MACRC,OAAO;MACPqB,UAAU;MACVI;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}