{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { computed, ref } from \"vue\";\nimport MemberList from \"@/views/member/list/index.vue\";\nimport { confirm, error, success } from \"@/util/tipsUtils\";\nimport { batchSignUp } from \"@/api/learn/lesson\";\nexport default {\n  name: \"SignupRecordIndex\",\n  components: {\n    MemberList\n  },\n  props: {\n    topic: {\n      type: Object,\n      required: true\n    },\n    showDrawer: {\n      type: Boolean,\n      required: true\n    },\n    drawerClose: {\n      type: Function,\n      required: true\n    }\n  },\n  setup(props, context) {\n    const dialogModel = computed({\n      get() {\n        return props.showDrawer;\n      },\n      set(val) {\n        context.emit('update:showDrawer', val);\n      }\n    });\n    // 查看报名记录\n    const signUpLoading = ref(false);\n    const signUpList = ref([]);\n    const signUpTotal = ref(0);\n    const signUpParam = ref({\n      current: 1,\n      size: 20,\n      lessonId: 0\n    });\n    // const loadSignUpList = () => {\n    //   signUpLoading.value = true\n    //   getSignUpList(signUpParam.value, res => {\n    //     signUpList.value = res.list\n    //     signUpTotal.value = res.total\n    //     signUpLoading.value = false\n    //   })\n    // }\n    // const signUpCurrentChange = (currentPage) => {\n    //   signUpParam.value.current = currentPage;\n    //   loadSignUpList();\n    // }\n    // const signUpSizeChange = (s) => {\n    //   signUpParam.value.size = s;\n    //   loadSignUpList();\n    // }\n    // signUpParam.value.current = 1\n    // signUpParam.value.lessonId = ref(props.topic.id)\n    // loadSignUpList()\n    // const signUpStatusMap = {\n    //   \"signed_up\": \"已报名\",\n    //   \"cancel_sign_up\": \"取消报名\",\n    //   \"completed\": \"已完成\"\n    // }\n\n    const cancelSignUpSelectMember = () => {\n      this.dialogModel = false;\n    };\n    const signUpSelectMember = memberList => {\n      console.log(\"memberList, \", memberList);\n      if (!memberList || !memberList.length) {\n        error(\"请选择会员\");\n        return;\n      }\n      confirm(\"确认为所选的会员报名？\", \"批量报名\", () => {\n        const memberIdList = [];\n        for (const m of memberList) {\n          memberIdList.push(m.id);\n        }\n        batchSignUp({\n          lessonIdList: [props.topic.id],\n          memberIdList: memberIdList\n        }, resp => {\n          console.log(\"报名结果：\", resp);\n          success(\"报名成功\");\n          cancelSignUpSelectMember();\n        });\n      });\n    };\n    return {\n      cancelSignUpSelectMember,\n      signUpSelectMember,\n      signUpParam,\n      signUpTotal,\n      signUpList,\n      signUpLoading,\n      // signUpCurrentChange,\n      // signUpSizeChange,\n      // signUpStatusMap,\n      dialogModel\n    };\n  }\n};", "map": {"version": 3, "names": ["computed", "ref", "MemberList", "confirm", "error", "success", "batchSignUp", "name", "components", "props", "topic", "type", "Object", "required", "showDrawer", "Boolean", "drawerClose", "Function", "setup", "context", "dialogModel", "get", "set", "val", "emit", "signUpLoading", "signUpList", "signUpTotal", "signUpParam", "current", "size", "lessonId", "cancelSignUpSelectMember", "signUpSelectMember", "memberList", "console", "log", "length", "memberIdList", "m", "push", "id", "lessonIdList", "resp"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/learn/signup/batch/index.vue"], "sourcesContent": ["<template>\n  <el-drawer class=\"sign-up-drawer\" v-model=\"dialogModel\" direction=\"rtl\" :before-close=\"drawerClose\" destroy-on-close>\n    <template #title>\n      <div class=\"work-item-box\">\n        <div class=\"item-content\">\n          <div class=\"content-main\">\n            <div class=\"main-title\">\n              <div class=\"title-box two-line\">\n                <span class=\"title-text\">批量报名</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </template>\n    <div class=\"topic-list-wrapper\">\n      <member-list :is-component=\"true\" :cancel-callback=\"cancelSignUpSelectMember\" :select-callback=\"signUpSelectMember\" />\n    </div>\n  </el-drawer>\n</template>\n\n<script>\nimport {computed, ref} from \"vue\";\nimport MemberList from \"@/views/member/list/index.vue\";\nimport {confirm, error, success} from \"@/util/tipsUtils\";\nimport {batchSignUp} from \"@/api/learn/lesson\";\n\nexport default {\n  name: \"SignupRecordIndex\",\n  components: {\n    MemberList\n  },\n  props: {\n    topic: {\n      type: Object,\n      required: true\n    },\n    showDrawer: {\n      type: Boolean,\n      required: true\n    },\n    drawerClose: {\n      type: Function,\n      required: true\n    }\n  },\n  setup(props, context) {\n    const dialogModel = computed({\n      get() {\n        return props.showDrawer;\n      },\n      set(val) {\n        context.emit('update:showDrawer', val);\n      },\n    });\n    // 查看报名记录\n    const signUpLoading = ref(false)\n    const signUpList = ref([])\n    const signUpTotal = ref(0)\n    const signUpParam = ref({\n      current: 1,\n      size: 20,\n      lessonId: 0\n    })\n    // const loadSignUpList = () => {\n    //   signUpLoading.value = true\n    //   getSignUpList(signUpParam.value, res => {\n    //     signUpList.value = res.list\n    //     signUpTotal.value = res.total\n    //     signUpLoading.value = false\n    //   })\n    // }\n    // const signUpCurrentChange = (currentPage) => {\n    //   signUpParam.value.current = currentPage;\n    //   loadSignUpList();\n    // }\n    // const signUpSizeChange = (s) => {\n    //   signUpParam.value.size = s;\n    //   loadSignUpList();\n    // }\n    // signUpParam.value.current = 1\n    // signUpParam.value.lessonId = ref(props.topic.id)\n    // loadSignUpList()\n    // const signUpStatusMap = {\n    //   \"signed_up\": \"已报名\",\n    //   \"cancel_sign_up\": \"取消报名\",\n    //   \"completed\": \"已完成\"\n    // }\n\n    const cancelSignUpSelectMember = () => {\n      this.dialogModel = false\n    }\n    const signUpSelectMember = (memberList) => {\n      console.log(\"memberList, \", memberList)\n      if(!memberList || !memberList.length) {\n        error(\"请选择会员\");\n        return;\n      }\n\n      confirm(\"确认为所选的会员报名？\", \"批量报名\", () => {\n        const memberIdList = []\n        for (const m of memberList) {\n          memberIdList.push(m.id)\n        }\n\n        batchSignUp({lessonIdList: [props.topic.id], memberIdList: memberIdList}, resp => {\n          console.log(\"报名结果：\", resp);\n          success(\"报名成功\")\n          cancelSignUpSelectMember()\n        })\n      })\n    }\n\n    return {\n      cancelSignUpSelectMember,\n      signUpSelectMember,\n      signUpParam,\n      signUpTotal,\n      signUpList,\n      signUpLoading,\n      // signUpCurrentChange,\n      // signUpSizeChange,\n      // signUpStatusMap,\n      dialogModel\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n\n</style>\n"], "mappings": ";AAsBA,SAAQA,QAAQ,EAAEC,GAAG,QAAO,KAAK;AACjC,OAAOC,UAAS,MAAO,+BAA+B;AACtD,SAAQC,OAAO,EAAEC,KAAK,EAAEC,OAAO,QAAO,kBAAkB;AACxD,SAAQC,WAAW,QAAO,oBAAoB;AAE9C,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,UAAU,EAAE;IACVN;EACF,CAAC;EACDO,KAAK,EAAE;IACLC,KAAK,EAAE;MACLC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,UAAU,EAAE;MACVH,IAAI,EAAEI,OAAO;MACbF,QAAQ,EAAE;IACZ,CAAC;IACDG,WAAW,EAAE;MACXL,IAAI,EAAEM,QAAQ;MACdJ,QAAQ,EAAE;IACZ;EACF,CAAC;EACDK,KAAKA,CAACT,KAAK,EAAEU,OAAO,EAAE;IACpB,MAAMC,WAAU,GAAIpB,QAAQ,CAAC;MAC3BqB,GAAGA,CAAA,EAAG;QACJ,OAAOZ,KAAK,CAACK,UAAU;MACzB,CAAC;MACDQ,GAAGA,CAACC,GAAG,EAAE;QACPJ,OAAO,CAACK,IAAI,CAAC,mBAAmB,EAAED,GAAG,CAAC;MACxC;IACF,CAAC,CAAC;IACF;IACA,MAAME,aAAY,GAAIxB,GAAG,CAAC,KAAK;IAC/B,MAAMyB,UAAS,GAAIzB,GAAG,CAAC,EAAE;IACzB,MAAM0B,WAAU,GAAI1B,GAAG,CAAC,CAAC;IACzB,MAAM2B,WAAU,GAAI3B,GAAG,CAAC;MACtB4B,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE;IACZ,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,MAAMC,wBAAuB,GAAIA,CAAA,KAAM;MACrC,IAAI,CAACZ,WAAU,GAAI,KAAI;IACzB;IACA,MAAMa,kBAAiB,GAAKC,UAAU,IAAK;MACzCC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,UAAU;MACtC,IAAG,CAACA,UAAS,IAAK,CAACA,UAAU,CAACG,MAAM,EAAE;QACpCjC,KAAK,CAAC,OAAO,CAAC;QACd;MACF;MAEAD,OAAO,CAAC,aAAa,EAAE,MAAM,EAAE,MAAM;QACnC,MAAMmC,YAAW,GAAI,EAAC;QACtB,KAAK,MAAMC,CAAA,IAAKL,UAAU,EAAE;UAC1BI,YAAY,CAACE,IAAI,CAACD,CAAC,CAACE,EAAE;QACxB;QAEAnC,WAAW,CAAC;UAACoC,YAAY,EAAE,CAACjC,KAAK,CAACC,KAAK,CAAC+B,EAAE,CAAC;UAAEH,YAAY,EAAEA;QAAY,CAAC,EAAEK,IAAG,IAAK;UAChFR,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEO,IAAI,CAAC;UAC1BtC,OAAO,CAAC,MAAM;UACd2B,wBAAwB,EAAC;QAC3B,CAAC;MACH,CAAC;IACH;IAEA,OAAO;MACLA,wBAAwB;MACxBC,kBAAkB;MAClBL,WAAW;MACXD,WAAW;MACXD,UAAU;MACVD,aAAa;MACb;MACA;MACA;MACAL;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}