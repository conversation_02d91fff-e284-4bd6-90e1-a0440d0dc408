{"ast": null, "code": "import { createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock, normalizeStyle as _normalizeStyle, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, resolveDirective as _resolveDirective, withDirectives as _withDirectives, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-2511622a\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"personal-container\"\n};\nconst _hoisted_2 = {\n  class: \"status-menu\"\n};\nconst _hoisted_3 = {\n  key: 0\n};\nconst _hoisted_4 = {\n  class: \"comment-load-more-wrapper\"\n};\nconst _hoisted_5 = [\"onClick\"];\nconst _hoisted_6 = {\n  class: \"article-item-content has-img\"\n};\nconst _hoisted_7 = {\n  class: \"article-item-content-title\"\n};\nconst _hoisted_8 = {\n  class: \"article-item-content-footer\"\n};\nconst _hoisted_9 = {\n  key: 0,\n  class: \"load-more-no-more-tips\"\n};\nconst _hoisted_10 = {\n  class: \"topic-comment-list-wrapper\"\n};\nconst _hoisted_11 = {\n  key: 1\n};\nconst _hoisted_12 = {\n  class: \"comment-load-more-wrapper\"\n};\nconst _hoisted_13 = {\n  key: 0,\n  class: \"load-more-no-more-tips\"\n};\nconst _hoisted_14 = {\n  key: 0,\n  class: \"comment-sub-list-wrapper\"\n};\nconst _hoisted_15 = {\n  class: \"comment-item-wrapper-select\"\n};\nconst _hoisted_16 = {\n  class: \"comment-count\"\n};\nconst _hoisted_17 = {\n  key: 1,\n  class: \"reply-list\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_menu_item = _resolveComponent(\"el-menu-item\");\n  const _component_el_menu = _resolveComponent(\"el-menu\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_topic_comment = _resolveComponent(\"topic-comment\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_comment_item = _resolveComponent(\"comment-item\");\n  const _component_empty = _resolveComponent(\"empty\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: \"content-container\",\n    style: _normalizeStyle('height: ' + $setup.clientHeight + 'px')\n  }, [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_menu, {\n    \"default-active\": $setup.defaultMenuActive,\n    class: \"el-menu-demo\",\n    mode: \"horizontal\",\n    onSelect: $setup.handleSelectMenu\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_menu_item, {\n      index: \"\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"全部\")]),\n      _: 1\n    }), _createVNode(_component_el_menu_item, {\n      index: \"lesson\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"课程\")]),\n      _: 1\n    }), _createVNode(_component_el_menu_item, {\n      index: \"channel\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"直播\")]),\n      _: 1\n    }), _createVNode(_component_el_menu_item, {\n      index: \"news\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"新闻\")]),\n      _: 1\n    }), _createVNode(_component_el_menu_item, {\n      index: \"article\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"文章\")]),\n      _: 1\n    }), _createVNode(_component_el_menu_item, {\n      index: \"question\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"问题\")]),\n      _: 1\n    }), _createVNode(_component_el_menu_item, {\n      index: \"answer\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"答案\")]),\n      _: 1\n    }), _createVNode(_component_el_menu_item, {\n      index: \"dynamic\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"动态\")]),\n      _: 1\n    })]),\n    _: 1\n  }, 8, [\"default-active\", \"onSelect\"])]), $setup.defaultMenuActive !== '' ? _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_3, [!$setup.topicList || !$setup.topicList.length ? (_openBlock(), _createBlock(_component_el_empty, {\n    key: 0\n  })) : (_openBlock(), _createBlock(_component_el_row, {\n    key: 1,\n    class: \"row\",\n    style: _normalizeStyle('height: ' + ($setup.clientHeight - 50) + 'px')\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_col, {\n      span: 8\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", {\n        class: \"comment-list-wrapper\",\n        onScroll: _cache[1] || (_cache[1] = (...args) => $setup.topicListScrollEvent && $setup.topicListScrollEvent(...args))\n      }, [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_input, {\n        size: \"small\",\n        placeholder: \"搜索标题\",\n        clearable: \"\",\n        modelValue: $setup.topicListParam.keyword,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.topicListParam.keyword = $event),\n        style: {\n          \"width\": \"96%\",\n          \"margin\": \"10px 2%\"\n        }\n      }, {\n        append: _withCtx(() => [_createVNode(_component_el_button, {\n          onClick: $setup.searchTopic\n        }, {\n          default: _withCtx(() => [_createTextVNode(\"搜索\")]),\n          _: 1\n        }, 8, [\"onClick\"])]),\n        _: 1\n      }, 8, [\"modelValue\"]), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.topicList, item => {\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: item.id,\n          class: _normalizeClass([\"comment-item-wrapper\", {\n            'select': $setup.selectTopic.id === item.id\n          }])\n        }, [_createElementVNode(\"div\", {\n          class: \"article-item\",\n          onClick: $event => $setup.selectTopicHandle(item)\n        }, [item.image ? (_openBlock(), _createElementBlock(\"div\", {\n          key: 0,\n          class: \"article-item-cover\",\n          style: _normalizeStyle('background-image: url(\"' + item.image + '\");')\n        }, null, 4)) : _createCommentVNode(\"\", true), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, _toDisplayString(item.name || item.title || item.content), 1), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"span\", null, \"评论 \" + _toDisplayString(item.commentNum || 0), 1)])])], 8, _hoisted_5)], 2);\n      }), 128)), $setup.noMoreTopicList ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, \"我是有底线的\")) : _createCommentVNode(\"\", true)])], 32)]),\n      _: 1\n    }), _createVNode(_component_el_col, {\n      span: 16\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_topic_comment, {\n        \"topic-type\": $setup.defaultMenuActive,\n        \"topic-id\": $setup.selectTopic.id\n      }, null, 8, [\"topic-type\", \"topic-id\"])])]),\n      _: 1\n    })]),\n    _: 1\n  }, 8, [\"style\"]))])), [[_directive_loading, $setup.dataLoading]]) : _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_11, [!$setup.commentList || !$setup.commentList.length ? (_openBlock(), _createBlock(_component_el_empty, {\n    key: 0,\n    tips: \"暂无评论\"\n  })) : (_openBlock(), _createBlock(_component_el_row, {\n    key: 1,\n    class: \"row\",\n    style: _normalizeStyle('height: ' + ($setup.clientHeight - 50) + 'px')\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_col, {\n      span: 12\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", {\n        class: \"comment-list-wrapper\",\n        onScroll: _cache[2] || (_cache[2] = (...args) => $setup.commentListScrollEvent && $setup.commentListScrollEvent(...args))\n      }, [_createElementVNode(\"div\", _hoisted_12, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.commentList, item => {\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: item.id,\n          class: _normalizeClass([\"comment-item-wrapper\", {\n            'select': $setup.selectComment.id === item.id\n          }])\n        }, [_createVNode(_component_comment_item, {\n          \"reply-num\": item.replyList && item.replyList.length || 0,\n          item: item,\n          member: $setup.member,\n          \"comment-id\": $setup.selectComment.id,\n          \"submit-callback\": $setup.submitCallback,\n          onClick: $event => $setup.selectCommentHandle(item)\n        }, null, 8, [\"reply-num\", \"item\", \"member\", \"comment-id\", \"submit-callback\", \"onClick\"])], 2);\n      }), 128)), $setup.noMoreComment ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, \"我是有底线的\")) : _createCommentVNode(\"\", true)])], 32)]),\n      _: 1\n    }), _createVNode(_component_el_col, {\n      span: 12\n    }, {\n      default: _withCtx(() => [$setup.selectComment && $setup.selectComment.id ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_comment_item, {\n        \"reply-num\": $setup.selectComment && $setup.selectComment.replyList && $setup.selectComment.replyList.length || 0,\n        member: $setup.member,\n        item: $setup.selectComment,\n        \"submit-callback\": $setup.submitCallback,\n        \"delete-callback\": $setup.deleteCallback,\n        \"comment-id\": $setup.selectComment.id\n      }, null, 8, [\"reply-num\", \"member\", \"item\", \"submit-callback\", \"delete-callback\", \"comment-id\"])]), _createElementVNode(\"div\", _hoisted_16, \"全部 \" + _toDisplayString($setup.selectComment && $setup.selectComment.replyList && $setup.selectComment.replyList.length || 0) + \" 条回复\", 1), !$setup.selectComment.replyList || !$setup.selectComment.replyList.length ? (_openBlock(), _createBlock(_component_empty, {\n        key: 0,\n        tips: \"暂无回复\"\n      })) : (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.selectComment.replyList, item => {\n        return _openBlock(), _createBlock(_component_comment_item, {\n          key: item.id,\n          \"reply-num\": -1,\n          member: $setup.member,\n          item: item,\n          \"submit-callback\": $setup.submitCallback,\n          \"comment-id\": $setup.selectComment.id\n        }, null, 8, [\"member\", \"item\", \"submit-callback\", \"comment-id\"]);\n      }), 128))]))])) : _createCommentVNode(\"\", true)]),\n      _: 1\n    })]),\n    _: 1\n  }, 8, [\"style\"]))])), [[_directive_loading, $setup.dataLoading]])])], 4);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "style", "_normalizeStyle", "$setup", "clientHeight", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_createVNode", "_component_el_menu", "defaultMenuActive", "mode", "onSelect", "handleSelectMenu", "_component_el_menu_item", "index", "_hoisted_3", "topicList", "length", "_createBlock", "_component_el_empty", "key", "_component_el_row", "_component_el_col", "span", "onScroll", "_cache", "args", "topicListScrollEvent", "_hoisted_4", "_component_el_input", "size", "placeholder", "clearable", "topicListParam", "keyword", "$event", "append", "_withCtx", "_component_el_button", "onClick", "searchTopic", "_Fragment", "_renderList", "item", "id", "_normalizeClass", "selectTopic", "selectTopicHandle", "image", "_hoisted_6", "_hoisted_7", "_toDisplayString", "name", "title", "content", "_hoisted_8", "commentNum", "noMoreTopicList", "_hoisted_9", "_hoisted_10", "_component_topic_comment", "dataLoading", "_hoisted_11", "commentList", "tips", "commentListScrollEvent", "_hoisted_12", "selectComment", "_component_comment_item", "replyList", "member", "submitCallback", "selectCommentHandle", "noMoreComment", "_hoisted_13", "_hoisted_14", "_hoisted_15", "deleteCallback", "_hoisted_16", "_component_empty", "_hoisted_17"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/comment/list/index.vue"], "sourcesContent": ["<template>\n  <div class=\"content-container\" :style=\"'height: ' + clientHeight + 'px'\">\n    <div class=\"personal-container\">\n      <div class=\"status-menu\">\n        <el-menu :default-active=\"defaultMenuActive\" class=\"el-menu-demo\" mode=\"horizontal\" @select=\"handleSelectMenu\">\n          <el-menu-item index=\"\">全部</el-menu-item>\n          <el-menu-item index=\"lesson\">课程</el-menu-item>\n          <el-menu-item index=\"channel\">直播</el-menu-item>\n          <el-menu-item index=\"news\">新闻</el-menu-item>\n          <el-menu-item index=\"article\">文章</el-menu-item>\n          <el-menu-item index=\"question\">问题</el-menu-item>\n          <el-menu-item index=\"answer\">答案</el-menu-item>\n          <el-menu-item index=\"dynamic\">动态</el-menu-item>\n        </el-menu>\n      </div>\n      <div v-if=\"defaultMenuActive !== ''\" v-loading=\"dataLoading\">\n        <el-empty v-if=\"!topicList || !topicList.length\"/>\n        <el-row class=\"row\" :style=\"'height: ' + (clientHeight - 50) + 'px'\" v-else>\n          <el-col :span=\"8\">\n            <div class=\"comment-list-wrapper\" @scroll=\"topicListScrollEvent\">\n              <div class=\"comment-load-more-wrapper\">\n                <el-input size=\"small\" placeholder=\"搜索标题\" clearable v-model=\"topicListParam.keyword\" style=\"width: 96%;margin: 10px 2%;\">\n                  <template #append>\n                    <el-button @click=\"searchTopic\">搜索</el-button>\n                  </template>\n                </el-input>\n                <template v-for=\"item in topicList\" :key=\"item.id\">\n                  <div class=\"comment-item-wrapper\" :class=\"{'select' : selectTopic.id === item.id}\">\n                    <div class=\"article-item\" @click=\"selectTopicHandle(item)\">\n                      <div class=\"article-item-cover\" v-if=\"item.image\" :style=\"'background-image: url(&quot;' + item.image + '&quot;);'\"></div>\n                      <div class=\"article-item-content has-img\">\n                        <div class=\"article-item-content-title\">{{item.name || item.title || item.content}}</div>\n                        <div class=\"article-item-content-footer\"><span>评论 {{item.commentNum || 0}}</span></div>\n                      </div>\n                    </div>\n                  </div>\n                </template>\n                <div v-if=\"noMoreTopicList\" class=\"load-more-no-more-tips\">我是有底线的</div>\n              </div>\n            </div>\n          </el-col>\n          <el-col :span=\"16\">\n            <div class=\"topic-comment-list-wrapper\">\n              <topic-comment :topic-type=\"defaultMenuActive\" :topic-id=\"selectTopic.id\"/>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n      <div v-else v-loading=\"dataLoading\">\n        <el-empty v-if=\"!commentList || !commentList.length\" tips=\"暂无评论\"/>\n        <el-row class=\"row\" :style=\"'height: ' + (clientHeight - 50) + 'px'\" v-else>\n          <el-col :span=\"12\">\n            <div class=\"comment-list-wrapper\" @scroll=\"commentListScrollEvent\">\n              <div class=\"comment-load-more-wrapper\">\n                <template v-for=\"item in commentList\" :key=\"item.id\">\n                  <div class=\"comment-item-wrapper\" :class=\"{'select' : selectComment.id === item.id}\">\n                    <comment-item :reply-num=\"item.replyList && item.replyList.length || 0\" :item=\"item\" :member=\"member\" :comment-id=\"selectComment.id\" :submit-callback=\"submitCallback\" @click=\"selectCommentHandle(item)\"/>\n                  </div>\n                </template>\n                <div v-if=\"noMoreComment\" class=\"load-more-no-more-tips\">我是有底线的</div>\n              </div>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"comment-sub-list-wrapper\" v-if=\"selectComment && selectComment.id\">\n              <div class=\"comment-item-wrapper-select\">\n                <comment-item :reply-num=\"selectComment && selectComment.replyList && selectComment.replyList.length || 0\" :member=\"member\" :item=\"selectComment\" :submit-callback=\"submitCallback\" :delete-callback=\"deleteCallback\" :comment-id=\"selectComment.id\"/>\n              </div>\n              <div class=\"comment-count\">全部&nbsp;{{selectComment && selectComment.replyList && selectComment.replyList.length || 0}}&nbsp;条回复</div>\n              <empty v-if=\"!selectComment.replyList || !selectComment.replyList.length\" tips=\"暂无回复\"/>\n              <div class=\"reply-list\" v-else>\n                <template v-for=\"item in selectComment.replyList\" :key=\"item.id\">\n                  <comment-item :reply-num=\"-1\" :member=\"member\" :item=\"item\" :submit-callback=\"submitCallback\" :comment-id=\"selectComment.id\"/>\n                </template>\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\n  import {ref} from \"vue\"\n  import commentItem from \"../commentItem\"\n  import topicComment from \"../list\"\n  import router from \"../../../router\"\n  import {getCommentList, findTopicList} from \"../../../api/comment\";\n  import {useRoute} from \"vue-router\";\n  import {getTopicList} from \"@/api/topic\";\n\n  export default {\n    name: \"memberComment\",\n    components: {\n      commentItem,\n      topicComment\n    },\n    setup() {\n      const route = useRoute()\n      const dataLoading = ref(true)\n      const member = ref({})\n      const param = ref({\n        topicId: \"\",\n        topicType: \"\",\n        current: 1,\n        size: 10\n      })\n      const commentList = ref([])\n      const selectComment = ref({})\n      const noMoreComment = ref(false)\n      const loadCommentList = () => {\n        dataLoading.value = true\n        getCommentList(param.value, res => {\n          dataLoading.value = false\n          if (!res || !res.list || !res.list.length) {\n            if (res.total > param.value.size) {\n              noMoreComment.value = true\n            }\n            param.value.current = param.value.current - 1\n            if (param.value.current < 1) {\n              param.value.current = 1\n            }\n            return\n          }\n          if (res.list.length < param.value.size) {\n            noMoreComment.value = true\n          }\n          // for (const listElement of res.list) {\n          //   commentList.value.push(listElement)\n          // }\n          const topicIdMap = {}\n          for (const e of res.list) {\n            if (!topicIdMap[e.topicType]) {\n              topicIdMap[e.topicType] = []\n            }\n            topicIdMap[e.topicType].push(e.topicId)\n          }\n          for (const me in topicIdMap) {\n            console.log(me)\n            getTopicList(me, topicIdMap[me], response => {\n              for (const r of response) {\n                for (const v of res.list) {\n                  if (v.topicId === r.id && me === v.topicType) {\n                    r.name = r.name || r.title || r.content\n                    v.topic = r;\n                    commentList.value.push(v)\n                  }\n                }\n              }\n            })\n          }\n          if (!selectComment.value || !selectComment.value.id) {\n            if (res.list && res.list.length) {\n              selectComment.value = res.list[0]\n            }\n          } else {\n            for (const re of res.list) {\n              if (selectComment.value.id === re.id) {\n                selectComment.value = re;\n                break\n              }\n            }\n          }\n        })\n      }\n      const selectCommentHandle = (item) => {\n        selectComment.value = item\n      }\n      const topicListParam = ref({\n        current: 1,\n        size: 10,\n        keyword: \"\",\n        topicType: \"\"\n      })\n      const topicList = ref([])\n      const selectTopic = ref({id: 0})\n      const noMoreTopicList = ref(false)\n      const loadTopicList = () => {\n        dataLoading.value = true\n        findTopicList(topicListParam.value, res => {\n          dataLoading.value = false\n          if (!res || !res.list || !res.list.length) {\n            if (res.total > param.value.size) {\n              noMoreTopicList.value = true\n            }\n            topicListParam.value.current = topicListParam.value.current - 1;\n            if (topicListParam.value.current < 1) {\n              topicListParam.value.current = 1\n            }\n            return\n          }\n          if (res.list.length < param.value.size) {\n            noMoreTopicList.value = true\n          }\n          if (!selectTopic.value || !selectTopic.value.id) {\n            if (res.list && res.list.length) {\n              selectTopic.value = res.list[0]\n            }\n          } else {\n            for (const re of res.list) {\n              if (selectTopic.value.id === re.id) {\n                selectTopic.value = re;\n                break\n              }\n            }\n          }\n          for (const listElement of res.list) {\n            topicList.value.push(listElement)\n          }\n        })\n      }\n      const selectTopicHandle = (item) => {\n        console.log(item)\n        selectTopic.value = item\n      }\n      const topicListScrollEvent = (e) => {\n        let scrollTop = e.srcElement.scrollTop\n        let clientHeight = e.srcElement.offsetHeight\n        let scrollHeight = e.srcElement.scrollHeight\n        // 滚动到底部，逻辑代码\n        if (scrollTop + clientHeight >= scrollHeight) {\n          if (!noMoreTopicList.value) {\n            topicListParam.value.current = topicListParam.value.current + 1\n            loadTopicList()\n          }\n        }\n      }\n      const defaultMenuActive = ref(\"\")\n      const handleSelectMenu = (val) => {\n        commentList.value = []\n        selectTopic.value = null\n        topicListParam.value.keyword = \"\"\n        if (\"\" !== val) {\n          topicListParam.value.current = 1\n          topicListParam.value.topicType = val\n          topicList.value = []\n          loadTopicList()\n        } else {\n          param.value.current = 1\n          param.value.topicType = val\n          noMoreTopicList.value = false\n          loadCommentList()\n          topicList.value = []\n          router.push(\"/comment/list\")\n        }\n        defaultMenuActive.value = val\n      }\n      const submitCallback = () => {\n        loadCommentList()\n      }\n      const deleteCallback = () => {\n        loadCommentList()\n      }\n      const commentListScrollEvent = (e) => {\n        let scrollTop = e.srcElement.scrollTop\n        let clientHeight = e.srcElement.offsetHeight\n        let scrollHeight = e.srcElement.scrollHeight\n        // 滚动到底部，逻辑代码\n        if (scrollTop + clientHeight >= scrollHeight) {\n          if (!noMoreComment.value) {\n            param.value.current = param.value.current + 1\n            loadCommentList()\n          }\n        }\n      }\n      let clientHeight = document.documentElement.clientHeight - 90;\n      if (clientHeight < 500) {\n        clientHeight = 500;\n      }\n      const type = route.query.type\n      const topicId = route.query.topicId\n      if (type && topicId) {\n        const topicName = route.query.topicName\n        if (topicName) {\n          topicListParam.value.keyword = topicName\n        }\n        if (type) {\n          selectTopic.value = {id: parseInt(topicId)}\n          topicListParam.value.topicType = type\n          loadTopicList()\n        }\n        defaultMenuActive.value = type\n      } else {\n        loadCommentList()\n      }\n      const searchTopic = () => {\n        topicList.value = []\n        noMoreTopicList.value = false\n        selectTopic.value = {}\n        topicListParam.value.type = type\n        loadTopicList()\n      }\n      return {\n        param,\n        clientHeight,\n        member,\n        commentList,\n        selectComment,\n        selectCommentHandle,\n        defaultMenuActive,\n        handleSelectMenu,\n        submitCallback,\n        deleteCallback,\n        commentListScrollEvent,\n        topicListParam,\n        noMoreComment,\n        topicList,\n        noMoreTopicList,\n        selectTopic,\n        selectTopicHandle,\n        topicListScrollEvent,\n        searchTopic,\n        dataLoading\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .personal-container {\n    background-color: #FFFFFF;\n    margin: 20px;\n    display: flex;\n    flex-direction: column;\n    flex: 1;\n  }\n  .row {\n    .el-col {\n      height: 100%;\n    }\n    .topic-comment-list-wrapper {\n      margin-left: 30px;\n      overflow-x: hidden;\n      overflow-y: auto;\n      height: 100%;\n      &::-webkit-scrollbar { width: 0 !important }\n      -ms-overflow-style: none;\n      overflow: -moz-scrollbars-none;\n    }\n  }\n  .el-menu-item {\n    height: 80px;\n    .el-icon-close {\n      display: none;\n      position: absolute;\n      right: 5px;\n      top: 50%;\n      transform: translateY(-50%);\n      color: #999;\n    }\n  }\n  .el-menu-item:focus, .el-menu-item:hover {\n    height: 80px;\n    em {\n      display: none;\n    }\n    .el-icon-close {\n      display: block;\n    }\n  }\n  .el-menu-item:focus{\n    em {\n      display: block;\n    }\n    .el-icon-close {\n      display: none;\n    }\n  }\n  .status-menu {\n    padding-bottom: 10px;\n    .el-menu-demo {\n      border: 0;\n      margin-left: 30px;\n      .el-menu-item {\n        height: 40px;\n        padding: 0;\n        margin-left: 30px;\n        font-size: 16px;\n        color: #333333;\n        line-height: 40px;\n        &:first-child {\n          margin-left: 0;\n        }\n        &:hover {\n          color: $--color-primary;\n        }\n      }\n      .el-menu-item.is-active {\n        color: $--color-primary;\n        font-weight: 500;\n      }\n    }\n  }\n  .comment-list-wrapper {\n    overflow-x: hidden;\n    overflow-y: auto;\n    height: 100%;\n    &::-webkit-scrollbar { width: 0 !important }\n    -ms-overflow-style: none;\n    overflow: -moz-scrollbars-none;\n    .comment-load-more-wrapper {\n      border-right: 1px solid #e8e8e8;\n      .load-more-no-more-tips {\n        color: #cccccc;\n        text-align: center;\n        font-size: 14px;\n        padding: 20px 0;\n      }\n      .comment-item-wrapper {\n        cursor: pointer;\n        position: relative;\n        border-left: 2px solid #ffffff;\n        &:after {\n          content: \" \";\n          height: 1px;\n          background-color: #e8e8e8;\n          position: absolute;\n          bottom: 0;\n          right: 30px;\n          left: 30px;\n        }\n        &:last-child:after {\n          height: 0;\n        }\n        &:hover {\n          border-left: 2px solid transparent;\n          background-color: #e8e8e8;\n          border-left-color: $--color-primary;\n        }\n        .article-item {\n          display: flex;\n          padding: 20px 30px;\n          .article-item-cover {\n            display: inline-block;\n            width: 78px;\n            height: 78px;\n            background-size: cover;\n            background-position: 50%;\n            background-repeat: no-repeat;\n            border-radius: 2px;\n          }\n          .article-item-content {\n            flex: 1;\n            width: calc(100% - 84px);\n            display: flex;\n            flex-direction: column;\n            .article-item-content-title {\n              font-size: 15px;\n              line-height: 24px;\n              color: var(--black01);\n              height: 48px;\n              max-height: 48px;\n              -webkit-box-orient: vertical;\n              text-overflow: ellipsis;\n              -webkit-line-clamp: 2;\n              display: -webkit-box;\n              overflow: hidden;\n              word-break: break-all;\n            }\n            .article-item-content-footer {\n              margin-top: 8px;\n              line-height: 1.43;\n              color: #999999;\n            }\n          }\n          .article-item-content.has-img {\n            margin-left: 12px;\n          }\n        }\n      }\n      .select {\n        border-left: 2px solid transparent;\n        background-color: #e8e8e8;\n        border-left-color: $--color-primary;\n      }\n    }\n  }\n  .comment-sub-list-wrapper {\n    overflow: hidden;\n    overflow-y: auto;\n    height: 100%;\n    &::-webkit-scrollbar { width: 0 !important }\n    -ms-overflow-style: none;\n    overflow: -moz-scrollbars-none;\n    .comment-item-wrapper-select {\n      position: relative;\n    }\n    .comment-count {\n      padding-left: 20px;\n      padding-right: 20px;\n      margin: 10px 0;\n    }\n    .reply-list {\n      position: relative;\n      .comment-item {\n        padding: 10px 20px;\n      }\n    }\n  }\n</style>\n<style>\n  body {\n    background-color: #f5f5f5!important;\n    height: 100%;\n  }\n</style>\n"], "mappings": ";;;EAESA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAa;;;;;EAiBXA,KAAK,EAAC;AAA2B;;;EAUzBA,KAAK,EAAC;AAA8B;;EAClCA,KAAK,EAAC;AAA4B;;EAClCA,KAAK,EAAC;AAA6B;;;EAKpBA,KAAK,EAAC;;;EAKjCA,KAAK,EAAC;AAA4B;;;;;EAWhCA,KAAK,EAAC;AAA2B;;;EAMVA,KAAK,EAAC;;;;EAK/BA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA6B;;EAGnCA,KAAK,EAAC;AAAe;;;EAErBA,KAAK,EAAC;;;;;;;;;;;;;;uBArEvBC,mBAAA,CA+EM;IA/EDD,KAAK,EAAC,mBAAmB;IAAEE,KAAK,EAAAC,eAAA,cAAeC,MAAA,CAAAC,YAAY;MAC9DC,mBAAA,CA6EM,OA7ENC,UA6EM,GA5EJD,mBAAA,CAWM,OAXNE,UAWM,GAVJC,YAAA,CASUC,kBAAA;IATA,gBAAc,EAAEN,MAAA,CAAAO,iBAAiB;IAAEX,KAAK,EAAC,cAAc;IAACY,IAAI,EAAC,YAAY;IAAEC,QAAM,EAAET,MAAA,CAAAU;;sBAC3F,MAAwC,CAAxCL,YAAA,CAAwCM,uBAAA;MAA1BC,KAAK,EAAC;IAAE;wBAAC,MAAE,C,iBAAF,IAAE,E;;QACzBP,YAAA,CAA8CM,uBAAA;MAAhCC,KAAK,EAAC;IAAQ;wBAAC,MAAE,C,iBAAF,IAAE,E;;QAC/BP,YAAA,CAA+CM,uBAAA;MAAjCC,KAAK,EAAC;IAAS;wBAAC,MAAE,C,iBAAF,IAAE,E;;QAChCP,YAAA,CAA4CM,uBAAA;MAA9BC,KAAK,EAAC;IAAM;wBAAC,MAAE,C,iBAAF,IAAE,E;;QAC7BP,YAAA,CAA+CM,uBAAA;MAAjCC,KAAK,EAAC;IAAS;wBAAC,MAAE,C,iBAAF,IAAE,E;;QAChCP,YAAA,CAAgDM,uBAAA;MAAlCC,KAAK,EAAC;IAAU;wBAAC,MAAE,C,iBAAF,IAAE,E;;QACjCP,YAAA,CAA8CM,uBAAA;MAAhCC,KAAK,EAAC;IAAQ;wBAAC,MAAE,C,iBAAF,IAAE,E;;QAC/BP,YAAA,CAA+CM,uBAAA;MAAjCC,KAAK,EAAC;IAAS;wBAAC,MAAE,C,iBAAF,IAAE,E;;;;2CAGzBZ,MAAA,CAAAO,iBAAiB,U,+BAA5BV,mBAAA,CAgCM,OAAAgB,UAAA,G,CA/Bab,MAAA,CAAAc,SAAS,KAAKd,MAAA,CAAAc,SAAS,CAACC,MAAM,I,cAA/CC,YAAA,CAAkDC,mBAAA;IAAAC,GAAA;EAAA,O,cAClDF,YAAA,CA6BSG,iBAAA;;IA7BDvB,KAAK,EAAC,KAAK;IAAEE,KAAK,EAAAC,eAAA,eAAgBC,MAAA,CAAAC,YAAY;;sBACpD,MAsBS,CAtBTI,YAAA,CAsBSe,iBAAA;MAtBAC,IAAI,EAAE;IAAC;wBACd,MAoBM,CApBNnB,mBAAA,CAoBM;QApBDN,KAAK,EAAC,sBAAsB;QAAE0B,QAAM,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAExB,MAAA,CAAAyB,oBAAA,IAAAzB,MAAA,CAAAyB,oBAAA,IAAAD,IAAA,CAAoB;UAC7DtB,mBAAA,CAkBM,OAlBNwB,UAkBM,GAjBJrB,YAAA,CAIWsB,mBAAA;QAJDC,IAAI,EAAC,OAAO;QAACC,WAAW,EAAC,MAAM;QAACC,SAAS,EAAT,EAAS;oBAAU9B,MAAA,CAAA+B,cAAc,CAACC,OAAO;mEAAtBhC,MAAA,CAAA+B,cAAc,CAACC,OAAO,GAAAC,MAAA;QAAEnC,KAAmC,EAAnC;UAAA;UAAA;QAAA;;QACxEoC,MAAM,EAAAC,QAAA,CACf,MAA8C,CAA9C9B,YAAA,CAA8C+B,oBAAA;UAAlCC,OAAK,EAAErC,MAAA,CAAAsC;QAAW;4BAAE,MAAE,C,iBAAF,IAAE,E;;;;gDAGtCzC,mBAAA,CAUW0C,SAAA,QAAAC,WAAA,CAVcxC,MAAA,CAAAc,SAAS,EAAjB2B,IAAI;6BACnB5C,mBAAA,CAQM;eATkC4C,IAAI,CAACC,EAAE;UAC1C9C,KAAK,EAAA+C,eAAA,EAAC,sBAAsB;YAAA,UAAqB3C,MAAA,CAAA4C,WAAW,CAACF,EAAE,KAAKD,IAAI,CAACC;UAAE;YAC9ExC,mBAAA,CAMM;UANDN,KAAK,EAAC,cAAc;UAAEyC,OAAK,EAAAJ,MAAA,IAAEjC,MAAA,CAAA6C,iBAAiB,CAACJ,IAAI;YAChBA,IAAI,CAACK,KAAK,I,cAAhDjD,mBAAA,CAA0H;;UAArHD,KAAK,EAAC,oBAAoB;UAAoBE,KAAK,EAAAC,eAAA,6BAA8B0C,IAAI,CAACK,KAAK;sDAChG5C,mBAAA,CAGM,OAHN6C,UAGM,GAFJ7C,mBAAA,CAAyF,OAAzF8C,UAAyF,EAAAC,gBAAA,CAA/CR,IAAI,CAACS,IAAI,IAAIT,IAAI,CAACU,KAAK,IAAIV,IAAI,CAACW,OAAO,OACjFlD,mBAAA,CAAuF,OAAvFmD,UAAuF,GAA9CnD,mBAAA,CAAwC,cAAlC,KAAG,GAAA+C,gBAAA,CAAER,IAAI,CAACa,UAAU,W;iBAKhEtD,MAAA,CAAAuD,eAAe,I,cAA1B1D,mBAAA,CAAuE,OAAvE2D,UAAuE,EAAZ,QAAM,K;;QAIvEnD,YAAA,CAISe,iBAAA;MAJAC,IAAI,EAAE;IAAE;wBACf,MAEM,CAFNnB,mBAAA,CAEM,OAFNuD,WAEM,GADJpD,YAAA,CAA2EqD,wBAAA;QAA3D,YAAU,EAAE1D,MAAA,CAAAO,iBAAiB;QAAG,UAAQ,EAAEP,MAAA,CAAA4C,WAAW,CAACF;;;;;8CA5B9B1C,MAAA,CAAA2D,WAAW,E,mCAiC3D9D,mBAAA,CA8BM,OAAA+D,WAAA,G,CA7Ba5D,MAAA,CAAA6D,WAAW,KAAK7D,MAAA,CAAA6D,WAAW,CAAC9C,MAAM,I,cAAnDC,YAAA,CAAkEC,mBAAA;;IAAb6C,IAAI,EAAC;uBAC1D9C,YAAA,CA2BSG,iBAAA;;IA3BDvB,KAAK,EAAC,KAAK;IAAEE,KAAK,EAAAC,eAAA,eAAgBC,MAAA,CAAAC,YAAY;;sBACpD,MAWS,CAXTI,YAAA,CAWSe,iBAAA;MAXAC,IAAI,EAAE;IAAE;wBACf,MASM,CATNnB,mBAAA,CASM;QATDN,KAAK,EAAC,sBAAsB;QAAE0B,QAAM,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAExB,MAAA,CAAA+D,sBAAA,IAAA/D,MAAA,CAAA+D,sBAAA,IAAAvC,IAAA,CAAsB;UAC/DtB,mBAAA,CAOM,OAPN8D,WAOM,I,kBANJnE,mBAAA,CAIW0C,SAAA,QAAAC,WAAA,CAJcxC,MAAA,CAAA6D,WAAW,EAAnBpB,IAAI;6BACnB5C,mBAAA,CAEM;eAHoC4C,IAAI,CAACC,EAAE;UAC5C9C,KAAK,EAAA+C,eAAA,EAAC,sBAAsB;YAAA,UAAqB3C,MAAA,CAAAiE,aAAa,CAACvB,EAAE,KAAKD,IAAI,CAACC;UAAE;YAChFrC,YAAA,CAA2M6D,uBAAA;UAA5L,WAAS,EAAEzB,IAAI,CAAC0B,SAAS,IAAI1B,IAAI,CAAC0B,SAAS,CAACpD,MAAM;UAAQ0B,IAAI,EAAEA,IAAI;UAAG2B,MAAM,EAAEpE,MAAA,CAAAoE,MAAM;UAAG,YAAU,EAAEpE,MAAA,CAAAiE,aAAa,CAACvB,EAAE;UAAG,iBAAe,EAAE1C,MAAA,CAAAqE,cAAc;UAAGhC,OAAK,EAAAJ,MAAA,IAAEjC,MAAA,CAAAsE,mBAAmB,CAAC7B,IAAI;;iBAGhMzC,MAAA,CAAAuE,aAAa,I,cAAxB1E,mBAAA,CAAqE,OAArE2E,WAAqE,EAAZ,QAAM,K;;QAIrEnE,YAAA,CAaSe,iBAAA;MAbAC,IAAI,EAAE;IAAE;wBACf,MAWM,CAXsCrB,MAAA,CAAAiE,aAAa,IAAIjE,MAAA,CAAAiE,aAAa,CAACvB,EAAE,I,cAA7E7C,mBAAA,CAWM,OAXN4E,WAWM,GAVJvE,mBAAA,CAEM,OAFNwE,WAEM,GADJrE,YAAA,CAAsP6D,uBAAA;QAAvO,WAAS,EAAElE,MAAA,CAAAiE,aAAa,IAAIjE,MAAA,CAAAiE,aAAa,CAACE,SAAS,IAAInE,MAAA,CAAAiE,aAAa,CAACE,SAAS,CAACpD,MAAM;QAAQqD,MAAM,EAAEpE,MAAA,CAAAoE,MAAM;QAAG3B,IAAI,EAAEzC,MAAA,CAAAiE,aAAa;QAAG,iBAAe,EAAEjE,MAAA,CAAAqE,cAAc;QAAG,iBAAe,EAAErE,MAAA,CAAA2E,cAAc;QAAG,YAAU,EAAE3E,MAAA,CAAAiE,aAAa,CAACvB;0GAEnPxC,mBAAA,CAAqI,OAArI0E,WAAqI,EAA1G,KAAQ,GAAA3B,gBAAA,CAAEjD,MAAA,CAAAiE,aAAa,IAAIjE,MAAA,CAAAiE,aAAa,CAACE,SAAS,IAAInE,MAAA,CAAAiE,aAAa,CAACE,SAAS,CAACpD,MAAM,SAAO,MAAS,M,CACjHf,MAAA,CAAAiE,aAAa,CAACE,SAAS,KAAKnE,MAAA,CAAAiE,aAAa,CAACE,SAAS,CAACpD,MAAM,I,cAAxEC,YAAA,CAAuF6D,gBAAA;;QAAbf,IAAI,EAAC;2BAC/EjE,mBAAA,CAIM,OAJNiF,WAIM,I,kBAHJjF,mBAAA,CAEW0C,SAAA,QAAAC,WAAA,CAFcxC,MAAA,CAAAiE,aAAa,CAACE,SAAS,EAA/B1B,IAAI;6BACnBzB,YAAA,CAA8HkD,uBAAA;eADxEzB,IAAI,CAACC,EAAE;UAC9C,WAAS,EAAE,EAAE;UAAG0B,MAAM,EAAEpE,MAAA,CAAAoE,MAAM;UAAG3B,IAAI,EAAEA,IAAI;UAAG,iBAAe,EAAEzC,MAAA,CAAAqE,cAAc;UAAG,YAAU,EAAErE,MAAA,CAAAiE,aAAa,CAACvB;;;;;;8CAxB9G1C,MAAA,CAAA2D,WAAW,E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}