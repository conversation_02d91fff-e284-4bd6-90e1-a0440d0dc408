{"ast": null, "code": "import LayoutHeader from \"./LayoutHeader.vue\";\nimport CustomHeader from \"./Header.vue\";\nimport CustomFooter from \"./Footer.vue\";\nimport CustomAside from \"./Aside.vue\";\nimport store from \"../store\";\nexport default {\n  name: \"LayoutFirst\",\n  components: {\n    LayoutHeader,\n    CustomHeader,\n    CustomFooter,\n    CustomAside\n  },\n  computed: {\n    opened() {\n      return !store.getters.getAsideStatus;\n    }\n  }\n};", "map": {"version": 3, "names": ["LayoutHeader", "CustomHeader", "CustomFooter", "CustomAside", "store", "name", "components", "computed", "opened", "getters", "getAsideStatus"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/components/LayoutNotAside.vue"], "sourcesContent": ["<template>\n  <el-container>\n    <el-header class=\"layout-header\" height=\"50\">\n      <layout-header/>\n    </el-header>\n    <el-main class=\"layout-main\">\n      <router-view v-slot=\"{ Component }\">\n        <transition>\n          <component :is=\"Component\"/>\n        </transition>\n      </router-view>\n    </el-main>\n  </el-container>\n</template>\n\n<script>\nimport LayoutHeader from \"./LayoutHeader.vue\";\nimport CustomHeader from \"./Header.vue\";\nimport CustomFooter from \"./Footer.vue\";\nimport CustomAside from \"./Aside.vue\";\nimport store from \"../store\";\n\nexport default {\n  name: \"LayoutFirst\",\n  components: {\n    LayoutHeader,\n    CustomHeader,\n    CustomFooter,\n    CustomAside\n  },\n  computed: {\n    opened() {\n      return !store.getters.getAsideStatus\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.el-header, .el-footer, .el-main {\n  padding: 0!important;\n}\n.aside {\n  position: fixed;\n  height: 100%;\n  background: #f0f0f0;\n}\n/*隐藏滚动条*/\n.aside::-webkit-scrollbar{\n  display:none;\n}\n.main {\n  min-height: 100%;\n  position: relative;\n  margin-left: 210px;\n  transition: width 0.28s;\n  width: calc(100% - 210px);\n}\n.fixed-header {\n  top: 0;\n  right: 0;\n  z-index: 9;\n  transition: width 0.28s;\n}\n.main.fixed-header {\n  transition: width 0.28s;\n  margin-left: 64px;\n  width: calc(100% - 64px);\n}\n.layout-header {\n  font-size: 12px;\n  position: fixed;\n  z-index: 99;\n  width: 100%;\n}\n.layout-main {\n  margin-top: 50px;\n}\n</style>\n"], "mappings": "AAgBA,OAAOA,YAAW,MAAO,oBAAoB;AAC7C,OAAOC,YAAW,MAAO,cAAc;AACvC,OAAOC,YAAW,MAAO,cAAc;AACvC,OAAOC,WAAU,MAAO,aAAa;AACrC,OAAOC,KAAI,MAAO,UAAU;AAE5B,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE;IACVN,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC;EACF,CAAC;EACDI,QAAQ,EAAE;IACRC,MAAMA,CAAA,EAAG;MACP,OAAO,CAACJ,KAAK,CAACK,OAAO,CAACC,cAAa;IACrC;EACF;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}