{"ast": null, "code": "import { ref } from \"vue\";\nimport { deleteDynamic, findList } from \"@/api/circle/dynamic\";\nimport Page from \"@/components/Page\";\nimport { info, confirm, success } from \"@/util/tipsUtils\";\nimport CommentDrawer from \"@/views/comment/commentDrawer\";\nexport default {\n  name: \"CircleDynamicIndex\",\n  components: {\n    CommentDrawer,\n    Page\n  },\n  setup() {\n    const statusMap = {\n      \"deleted\": \"已删除\",\n      \"published\": \"已发布\"\n    };\n    const list = ref([]);\n    const total = ref(0);\n    const dataLoading = ref(true);\n    const searchParam = ref({\n      keyword: \"\",\n      status: \"\",\n      size: 20,\n      current: 1\n    });\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true;\n      findList(searchParam.value, res => {\n        dataLoading.value = false;\n        if (!res) {\n          return;\n        }\n        for (const listElement of res.list) {\n          listElement.chapterList = [];\n        }\n        list.value = res.list;\n        total.value = res.total;\n      });\n    };\n    loadList();\n    // 搜索\n    const search = () => {\n      loadList();\n    };\n    // 删除\n    const remove = item => {\n      confirm(\"确认删除动态 \" + item.content + \" 吗？\", \"提示\", () => {\n        deleteDynamic(item.id, () => {\n          success(\"删除成功\");\n          loadList();\n        });\n      }, () => {});\n    };\n    const currentChange = currentPage => {\n      searchParam.value.current = currentPage;\n      loadList();\n    };\n    const sizeChange = s => {\n      searchParam.value.size = s;\n      loadList();\n    };\n    const selectTopic = ref({});\n    const drawer = ref(false);\n    const drawerClose = done => {\n      drawer.value = false;\n      done();\n    };\n    const commentView = item => {\n      drawer.value = true;\n      selectTopic.value = item;\n    };\n    return {\n      list,\n      total,\n      searchParam,\n      search,\n      currentChange,\n      sizeChange,\n      remove,\n      commentView,\n      selectTopic,\n      drawer,\n      drawerClose,\n      statusMap,\n      info,\n      dataLoading\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "deleteDynamic", "findList", "Page", "info", "confirm", "success", "CommentDrawer", "name", "components", "setup", "statusMap", "list", "total", "dataLoading", "searchParam", "keyword", "status", "size", "current", "loadList", "value", "res", "listElement", "chapterList", "search", "remove", "item", "content", "id", "currentChange", "currentPage", "sizeChange", "s", "selectTopic", "drawer", "drawerClose", "done", "commentView"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/circle/dynamic/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"header\">\n      <el-form :inline=\"true\" :model=\"searchParam\" class=\"demo-form-inline\">\n        <el-form-item label=\"\">\n          <el-input size=\"mini\" class=\"search-input\" v-model=\"searchParam.keyword\" placeholder=\"请输入关键字\"></el-input>\n          <el-button size=\"mini\" class=\"search-btn\" type=\"primary\" @click=\"search\">搜索</el-button>\n        </el-form-item>\n        <el-form-item label=\"状态\" class=\"status\">\n          <el-select size=\"mini\" v-model=\"searchParam.status\" @change=\"search\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"已发布\" value=\"published\"></el-option>\n            <el-option label=\"已删除\" value=\"deleted\"></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"content\" v-loading=\"dataLoading\">\n      <div class=\"content-list\">\n        <el-empty v-if=\"!list || !list.length\"/>\n        <div class=\"content-item\" v-for=\"item in list\" :key=\"item.id + ''\">\n          <div class=\"content-item-warp\">\n            <div class=\"article-card-bone\">\n              <div class=\"title-wrap\">\n                <a class=\"title\">{{item.content}}</a>\n                <span class=\"label create-time\">{{item.createTime}}</span>\n              </div>\n              <div class=\"content\" v-if=\"item.image && item.image.trim()\">\n                <a class=\"image\" v-for=\"img in item.image.split(',')\" :key=\"img\">\n                  <img :src=\"img\">\n                </a>\n              </div>\n              <div class=\"abstruct\">\n                <div class=\"status\">{{statusMap[item.status]}}</div>\n                <div class=\"divider\"></div>\n                <div class=\"status\">\n                  <img :src=\"item.member.avatar\" style=\"width: 20px;vertical-align: text-top;border-radius: 10px;\"/>\n                  {{item.member.name}}\n                </div>\n                <div class=\"divider\" v-if=\"item.circle\"></div>\n                <div class=\"status\" v-if=\"item.circle\">\n                  {{item.circle.name}}\n                </div>\n              </div>\n              <div class=\"count-wrapper\">\n                <ul class=\"count\">\n                  <li>阅读 {{item.watchNum || 0}}</li>\n                  <li>评论 {{item.commentNum || 0}}</li>\n                  <li>点赞 {{item.likeNum || 0}}</li>\n                  <li>收藏 {{item.favoriteNum || 0}}</li>\n                </ul>\n                <div class=\"article-action-list\">\n                  <span class=\"icon-label\" @click=\"commentView(item)\">查看评论</span>\n                  <span class=\"icon-label\" @click=\"info('敬请期待')\">违规举报</span>\n                  <span class=\"icon-label\" @click=\"remove(item)\">删除</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <comment-drawer :topic=\"selectTopic\" :show-drawer=\"drawer\" topic-type=\"dynamic\" :drawer-close=\"drawerClose\"/>\n    <page :total=\"total\" :current-change=\"currentChange\" :size-change=\"sizeChange\"></page>\n  </div>\n</template>\n\n<script>\n  import {ref} from \"vue\"\n  import {deleteDynamic, findList} from \"@/api/circle/dynamic\"\n  import Page from \"@/components/Page\"\n  import {info, confirm, success} from \"@/util/tipsUtils\";\n  import CommentDrawer from \"@/views/comment/commentDrawer\";\n\n  export default {\n    name: \"CircleDynamicIndex\",\n  components: {\n    CommentDrawer,\n    Page\n  },\n  setup() {\n    const statusMap = {\n      \"deleted\": \"已删除\",\n      \"published\": \"已发布\"\n    }\n    const list = ref([])\n    const total = ref(0)\n    const dataLoading = ref(true)\n    const searchParam = ref({\n      keyword: \"\",\n      status: \"\",\n      size: 20,\n      current: 1\n    })\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true\n      findList(searchParam.value, (res) => {\n        dataLoading.value = false\n        if (!res) {return;}\n        for (const listElement of res.list) {\n          listElement.chapterList = [];\n        }\n        list.value = res.list;\n        total.value = res.total;\n      })\n    }\n    loadList();\n    // 搜索\n    const search = () => {\n      loadList();\n    }\n    // 删除\n    const remove = (item) => {\n      confirm(\"确认删除动态 \" + item.content + \" 吗？\", \"提示\", () => {\n        deleteDynamic(item.id, () => {\n          success(\"删除成功\")\n          loadList()\n        })\n      }, () => {\n      })\n    }\n    const currentChange = (currentPage) => {\n      searchParam.value.current = currentPage;\n      loadList();\n    }\n    const sizeChange = (s) => {\n      searchParam.value.size = s;\n      loadList();\n    }\n    const selectTopic = ref({})\n    const drawer = ref(false)\n    const drawerClose = (done) => {\n      drawer.value = false\n      done()\n    }\n    const commentView = (item) => {\n      drawer.value = true\n      selectTopic.value = item\n    }\n    return {\n      list,\n      total,\n      searchParam,\n      search,\n      currentChange,\n      sizeChange,\n      remove,\n      commentView,\n      selectTopic,\n      drawer,\n      drawerClose,\n      statusMap,\n      info,\n      dataLoading\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n  .app-container {\n    margin: 20px;\n    .content-list {\n      margin: 0;\n      padding: 0;\n      border: 0;\n      font: inherit;\n      vertical-align: baseline;\n      .content-item {\n        padding: 24px 12px;\n        line-height: 1;\n        font-size: 14px;\n        color: #666;\n        border-bottom: 1px solid #e8e8e8;\n        position: relative;\n        background: #ffffff;\n        &:last-child {\n          border-bottom: 0;\n        }\n        .content-item-warp {\n          position: relative;\n          display: flex;\n          .image {\n            width: 168px;\n            min-width: 168px;\n            height: 108px;\n            margin-right: 24px;\n            position: relative;\n            overflow: hidden;\n            border-radius: 4px;\n            border: 1px solid #e8e8e8;\n            img {\n              width: 100%;\n              height: 100%;\n              transition: all .5s ease-out .1s;\n              -o-object-fit: cover;\n              object-fit: cover;\n              -o-object-position: center;\n              object-position: center;\n              &:hover {\n                transform: matrix(1.04,0,0,1.04,0,0);\n                -webkit-backface-visibility: hidden;\n                backface-visibility: hidden;\n              }\n            }\n          }\n          .article-card-bone {\n            width: 100%;\n            display: flex;\n            flex-direction: column;\n            min-width: 0;\n            .title-wrap {\n              display: flex;\n              justify-content: space-between;\n              margin-top: 0;\n              .title {\n                font-size: 16px;\n                overflow: hidden;\n                white-space: nowrap;\n                text-overflow: ellipsis;\n                line-height: 24px;\n                display: block;\n                color: #222;\n                cursor: text;\n              }\n              .create-time {\n                color: #999;\n                line-height: 24px;\n                margin-left: 12px;\n                flex-shrink: 0;\n              }\n            }\n            .content {\n              word-break: break-word;\n              overflow-wrap: break-word;\n              margin: 8px 0 4px 0;\n              font-size: 12px;\n            }\n            .abstruct {\n              line-height: 20px;\n              margin-top: 20px;\n              height: 20px;\n              display: flex;\n              align-items: flex-end;\n              .status {\n                color: #999;\n                border: none;\n                background-color: #f5f5f5;\n                padding: 0 8px;\n                line-height: 20px;\n                font-size: 12px;\n                border-radius: 2px;\n                white-space: nowrap;\n                display: inline-block;\n                box-sizing: border-box;\n                transition: all .3s;\n                margin-right: 8px;\n              }\n              .article-card .byte-tag-simple {\n                margin-right: 8px;\n              }\n              .divider {\n                width: 1px;\n                height: 12px;\n                margin: 4px 10px 4px 4px;\n                background: #bfbfbf;\n              }\n              .icon {\n                margin-right: 8px;\n                svg {\n                  vertical-align: bottom;\n                  &:focus {\n                    outline: none;\n                  }\n                }\n              }\n            }\n            .count-wrapper {\n              margin-top: 24px;\n              display: flex;\n              justify-content: space-between;\n              .count {\n                line-height: 20px;\n                position: relative;\n                li {\n                  display: inline-block;\n                  margin-right: 24px;\n                  &:after {\n                    content: \"\\ff65\";\n                    font-size: 20px;\n                    margin: 0 8px;\n                    line-height: 0;\n                    position: absolute;\n                    top: 10px;\n                    color: #666;\n                  }\n                  &:last-child:after {\n                    content: \"\"\n                  }\n                }\n              }\n              .article-action-list {\n                display: flex;\n                line-height: 20px;\n                flex: 1 0 auto;\n                justify-content: flex-end;\n                .icon-label {\n                  cursor: pointer;\n                  font-size: 14px;\n                  line-height: 20px;\n                  display: flex;\n                  color: #222;\n                  font-weight: 400;\n                  margin-left: 24px;\n                  &:first-child {\n                    margin-left: 0;\n                  }\n                  &:hover {\n                    color: $--color-primary;\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    .search-input {\n      width: 242px;\n    }\n  }\n</style>\n"], "mappings": "AAoEE,SAAQA,GAAG,QAAO,KAAI;AACtB,SAAQC,aAAa,EAAEC,QAAQ,QAAO,sBAAqB;AAC3D,OAAOC,IAAG,MAAO,mBAAkB;AACnC,SAAQC,IAAI,EAAEC,OAAO,EAAEC,OAAO,QAAO,kBAAkB;AACvD,OAAOC,aAAY,MAAO,+BAA+B;AAEzD,eAAe;EACbC,IAAI,EAAE,oBAAoB;EAC5BC,UAAU,EAAE;IACVF,aAAa;IACbJ;EACF,CAAC;EACDO,KAAKA,CAAA,EAAG;IACN,MAAMC,SAAQ,GAAI;MAChB,SAAS,EAAE,KAAK;MAChB,WAAW,EAAE;IACf;IACA,MAAMC,IAAG,GAAIZ,GAAG,CAAC,EAAE;IACnB,MAAMa,KAAI,GAAIb,GAAG,CAAC,CAAC;IACnB,MAAMc,WAAU,GAAId,GAAG,CAAC,IAAI;IAC5B,MAAMe,WAAU,GAAIf,GAAG,CAAC;MACtBgB,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC;IACD;IACA,MAAMC,QAAO,GAAIA,CAAA,KAAM;MACrBN,WAAW,CAACO,KAAI,GAAI,IAAG;MACvBnB,QAAQ,CAACa,WAAW,CAACM,KAAK,EAAGC,GAAG,IAAK;QACnCR,WAAW,CAACO,KAAI,GAAI,KAAI;QACxB,IAAI,CAACC,GAAG,EAAE;UAAC;QAAO;QAClB,KAAK,MAAMC,WAAU,IAAKD,GAAG,CAACV,IAAI,EAAE;UAClCW,WAAW,CAACC,WAAU,GAAI,EAAE;QAC9B;QACAZ,IAAI,CAACS,KAAI,GAAIC,GAAG,CAACV,IAAI;QACrBC,KAAK,CAACQ,KAAI,GAAIC,GAAG,CAACT,KAAK;MACzB,CAAC;IACH;IACAO,QAAQ,EAAE;IACV;IACA,MAAMK,MAAK,GAAIA,CAAA,KAAM;MACnBL,QAAQ,EAAE;IACZ;IACA;IACA,MAAMM,MAAK,GAAKC,IAAI,IAAK;MACvBtB,OAAO,CAAC,SAAQ,GAAIsB,IAAI,CAACC,OAAM,GAAI,KAAK,EAAE,IAAI,EAAE,MAAM;QACpD3B,aAAa,CAAC0B,IAAI,CAACE,EAAE,EAAE,MAAM;UAC3BvB,OAAO,CAAC,MAAM;UACdc,QAAQ,EAAC;QACX,CAAC;MACH,CAAC,EAAE,MAAM,CACT,CAAC;IACH;IACA,MAAMU,aAAY,GAAKC,WAAW,IAAK;MACrChB,WAAW,CAACM,KAAK,CAACF,OAAM,GAAIY,WAAW;MACvCX,QAAQ,EAAE;IACZ;IACA,MAAMY,UAAS,GAAKC,CAAC,IAAK;MACxBlB,WAAW,CAACM,KAAK,CAACH,IAAG,GAAIe,CAAC;MAC1Bb,QAAQ,EAAE;IACZ;IACA,MAAMc,WAAU,GAAIlC,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAMmC,MAAK,GAAInC,GAAG,CAAC,KAAK;IACxB,MAAMoC,WAAU,GAAKC,IAAI,IAAK;MAC5BF,MAAM,CAACd,KAAI,GAAI,KAAI;MACnBgB,IAAI,EAAC;IACP;IACA,MAAMC,WAAU,GAAKX,IAAI,IAAK;MAC5BQ,MAAM,CAACd,KAAI,GAAI,IAAG;MAClBa,WAAW,CAACb,KAAI,GAAIM,IAAG;IACzB;IACA,OAAO;MACLf,IAAI;MACJC,KAAK;MACLE,WAAW;MACXU,MAAM;MACNK,aAAa;MACbE,UAAU;MACVN,MAAM;MACNY,WAAW;MACXJ,WAAW;MACXC,MAAM;MACNC,WAAW;MACXzB,SAAS;MACTP,IAAI;MACJU;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}