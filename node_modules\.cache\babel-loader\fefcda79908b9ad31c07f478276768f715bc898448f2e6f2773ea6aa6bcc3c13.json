{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref } from \"vue\";\nimport { findCategoryList, toTree, getAllParent } from \"@/api/exam/question-lib/category\";\nimport { saveBaseInfo, updateBaseInfo, getBaseInfo } from \"@/api/exam/question-lib/question\";\nimport { useRoute } from \"vue-router\";\nimport { error, success } from \"@/util/tipsUtils\";\nimport router from \"@/router\";\nexport default {\n  name: \"ExamQuestionLibMultiChoice\",\n  setup() {\n    const route = useRoute();\n    const colors = [\"#99A9BF\", \"#F7BA2A\", \"#FF9900\"];\n    const question = ref({\n      id: \"\",\n      title: \"\",\n      note: \"\",\n      type: \"multi_choice\",\n      score: \"\",\n      difficulty: 2,\n      referenceAnswer: \"\",\n      referenceAnswerNote: \"\",\n      options: \"\",\n      cidList: [],\n      referenceAnswerList: []\n    });\n    const questionRules = {\n      title: [{\n        required: true,\n        message: \"请输入题干\",\n        trigger: \"blur\"\n      }],\n      score: [{\n        required: true,\n        message: \"请输入分数\",\n        trigger: \"blur\"\n      }],\n      cidList: [{\n        required: true,\n        message: \"请选择分类\",\n        trigger: \"change\"\n      }],\n      referenceAnswer: [{\n        required: true,\n        message: \"请选择参考答案\",\n        trigger: \"blur\"\n      }],\n      referenceAnswerNote: [{\n        required: true,\n        message: \"请输入答案解析\",\n        trigger: \"blur\"\n      }],\n      options: [{\n        required: true,\n        message: \"请添加选项\",\n        trigger: \"blur\"\n      }]\n    };\n    const serialNumber = [\"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\", \"H\", \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\", \"R\", \"S\", \"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\"];\n    const optionList = ref([]);\n    const categoryOptions = ref([]);\n    const selectCidList = ref([]);\n    // 获取分类\n    findCategoryList(0, true, res => {\n      if (res && res.length) {\n        categoryOptions.value = toTree(res);\n        categoryOptions.value.splice(0, 1);\n        if (route.query.id) {\n          // 获取试题信息\n          getBaseInfo(route.query.id, function (res) {\n            console.log(res);\n            question.value = res;\n            optionList.value = JSON.parse(res.options);\n            selectCidList.value = getAllParent(categoryOptions.value, res.cidList);\n            question.value.cidList = [];\n            for (const valElement of selectCidList.value) {\n              question.value.cidList.push(valElement[valElement.length - 1]);\n            }\n            if (res.referenceAnswer) {\n              question.value.referenceAnswerList = res.referenceAnswer.split(\",\");\n            }\n          });\n        }\n      }\n    });\n    // 选择分类\n    const changeCategory = val => {\n      question.value.cidList = [];\n      for (const valElement of val) {\n        question.value.cidList.push(valElement[valElement.length - 1]);\n      }\n    };\n    let optionIndex = -1;\n    const option = ref(\"\");\n    const showAddOptionInput = ref(false);\n    const addOption = () => {\n      showAddOptionInput.value = true;\n    };\n    const optionBlur = () => {\n      showAddOptionInput.value = false;\n      if (!option.value) {\n        return;\n      }\n      if (optionIndex > -1) {\n        optionList.value[optionIndex].value = option.value;\n      } else {\n        optionList.value.push({\n          value: option.value,\n          key: serialNumber[optionList.value.length]\n        });\n      }\n      question.value.options = JSON.stringify(optionList.value);\n      option.value = \"\";\n      optionIndex = -1;\n    };\n    const editOption = index => {\n      const o = optionList.value[index];\n      option.value = o.value;\n      optionIndex = index;\n      showAddOptionInput.value = true;\n    };\n    const deleteOption = index => {\n      if (optionList.value && optionList.value.length) {\n        const o = optionList.value[index];\n        if (question.value.referenceAnswerList && question.value.referenceAnswerList.length) {\n          for (let i = 0; i < question.value.referenceAnswerList.length; i++) {\n            const ra = question.value.referenceAnswerList[i];\n            if (ra === o.key) {\n              question.value.referenceAnswerList.splice(i, 1);\n              break;\n            }\n          }\n        }\n        optionList.value.splice(index, 1);\n        optionList.value.forEach((item, index) => {\n          item.key = serialNumber[index];\n        });\n        question.value.options = JSON.stringify(optionList.value);\n      } else {\n        question.value.options = \"\";\n      }\n    };\n    const questionRef = ref();\n    const submitBaseInfo = () => {\n      if (question.value.referenceAnswerList && question.value.referenceAnswerList.length) {\n        if (question.value.referenceAnswerList.length < 2) {\n          error(\"参考答案应大于2个\");\n          return;\n        }\n        question.value.referenceAnswer = question.value.referenceAnswerList.join(\",\");\n      }\n      questionRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (question.value.id) {\n          updateBaseInfo(question.value, function () {\n            success(\"编辑成功\");\n            router.push({\n              path: \"/exam/question-lib/list\"\n            });\n          });\n        } else {\n          saveBaseInfo(question.value, function () {\n            success(\"新增成功\");\n            router.push({\n              path: \"/exam/question-lib/list\"\n            });\n          });\n        }\n      });\n    };\n    return {\n      colors,\n      question,\n      questionRules,\n      categoryOptions,\n      selectCidList,\n      serialNumber,\n      option,\n      optionList,\n      showAddOptionInput,\n      questionRef,\n      changeCategory,\n      addOption,\n      optionBlur,\n      editOption,\n      deleteOption,\n      submitBaseInfo\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "findCategoryList", "toTree", "getAllParent", "saveBaseInfo", "updateBaseInfo", "getBaseInfo", "useRoute", "error", "success", "router", "name", "setup", "route", "colors", "question", "id", "title", "note", "type", "score", "difficulty", "referenceAnswer", "referenceAnswerNote", "options", "cidList", "referenceAnswerList", "questionRules", "required", "message", "trigger", "serialNumber", "optionList", "categoryOptions", "selectCidList", "res", "length", "value", "splice", "query", "console", "log", "JSON", "parse", "valElement", "push", "split", "changeCategory", "val", "optionIndex", "option", "showAddOptionInput", "addOption", "optionBlur", "key", "stringify", "editOption", "index", "o", "deleteOption", "i", "ra", "for<PERSON>ach", "item", "questionRef", "submitBaseInfo", "join", "validate", "valid", "path"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/exam/question-lib/multi-choice/index.vue"], "sourcesContent": ["<template>\n  <div class=\"question-box\">\n    <el-form :model=\"question\" :rules=\"questionRules\" ref=\"questionRef\" label-width=\"120px\">\n      <el-form-item label=\"分类：\" prop=\"cidList\">\n        <el-cascader size=\"mini\" style=\"width: 100%;\"\n                     v-model=\"selectCidList\"\n                     :props=\"{ multiple: true, checkStrictly: true }\"\n                     :options=\"categoryOptions\"\n                     @change=\"changeCategory\">\n        </el-cascader>\n      </el-form-item>\n      <el-form-item label=\"题干：\" prop=\"title\">\n        <el-input size=\"mini\" v-model=\"question.title\" placeholder=\"请输入题干\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"描述：\" prop=\"note\">\n        <el-input size=\"mini\" type=\"textarea\" :rows=\"5\" v-model=\"question.note\" placeholder=\"请输入题干描述\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"选项：\" prop=\"options\">\n        <el-card size=\"mini\" shadow=\"never\">\n          <template #header>\n            <div class=\"clearfix\">\n              <el-button size=\"mini\" style=\"padding: 10px;\" type=\"text\" @click=\"addOption\">添加选项</el-button>\n            </div>\n          </template>\n          <div v-if=\"!(optionList && optionList.length > 0) && !showAddOptionInput\">请添加选项</div>\n          <div v-else-if=\"optionList && optionList.length > 0\" v-for=\"(o, index) in optionList\" :key=\"o.key\" class=\"text item\">\n            <span>{{o.key + '. ' + o.value}}</span>\n            <i class=\"option-delete el-icon-edit\" @click=\"editOption(index)\"></i>\n            <i class=\"option-delete el-icon-delete\" @click=\"deleteOption(index)\"></i>\n          </div>\n          <el-input size=\"mini\" placeholder=\"请输入选项内容\" v-if=\"showAddOptionInput\" v-model=\"option\" @blur=\"optionBlur\" @keypress.enter=\"optionBlur\"/>\n        </el-card>\n      </el-form-item>\n      <el-form-item label=\"参考答案：\"  prop=\"referenceAnswer\">\n        <el-checkbox-group v-model=\"question.referenceAnswerList\">\n          <el-checkbox v-for=\"item in optionList\" :key=\"item.key\" :label=\"item.key\"></el-checkbox>\n        </el-checkbox-group>\n      </el-form-item>\n      <el-form-item label=\"答案解析：\" prop=\"referenceAnswerNote\">\n        <el-input size=\"mini\" type=\"textarea\" :rows=\"5\" v-model=\"question.referenceAnswerNote\" placeholder=\"请输入答案解析\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"分数：\" prop=\"score\">\n        <el-input size=\"mini\" v-model=\"question.score\" placeholder=\"请输入试题分数\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"难度：\" prop=\"difficulty\">\n        <el-rate style=\"line-height: 48px;\" v-model=\"question.difficulty\" :colors=\"colors\"></el-rate>\n      </el-form-item>\n    </el-form>\n    <el-button size=\"mini\" style=\"display:block;margin:50px auto;\" @click=\"submitBaseInfo\">提交</el-button>\n  </div>\n</template>\n<script>\nimport {ref} from \"vue\"\nimport {findCategoryList, toTree, getAllParent} from \"@/api/exam/question-lib/category\"\nimport {saveBaseInfo, updateBaseInfo, getBaseInfo} from \"@/api/exam/question-lib/question\"\nimport {useRoute} from \"vue-router\";\nimport {error, success} from \"@/util/tipsUtils\";\nimport router from \"@/router\";\n\nexport default {\n  name: \"ExamQuestionLibMultiChoice\",\n  setup() {\n    const route = useRoute()\n    const colors = [\"#99A9BF\", \"#F7BA2A\", \"#FF9900\"]\n    const question = ref({\n      id: \"\",\n      title: \"\",\n      note: \"\",\n      type: \"multi_choice\",\n      score: \"\",\n      difficulty: 2,\n      referenceAnswer: \"\",\n      referenceAnswerNote: \"\",\n      options: \"\",\n      cidList: [],\n      referenceAnswerList: []\n    })\n    const questionRules = {\n      title: [{ required: true, message: \"请输入题干\", trigger: \"blur\" }],\n      score: [{ required: true, message: \"请输入分数\", trigger: \"blur\" }],\n      cidList: [{ required: true, message: \"请选择分类\", trigger: \"change\" }],\n      referenceAnswer: [{ required: true, message: \"请选择参考答案\", trigger: \"blur\" }],\n      referenceAnswerNote: [{ required: true, message: \"请输入答案解析\", trigger: \"blur\" }],\n      options: [{ required: true, message: \"请添加选项\", trigger: \"blur\" }],\n    }\n    const serialNumber = [\"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\", \"H\", \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\", \"R\", \"S\", \"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\"]\n    const optionList = ref([])\n    const categoryOptions = ref([])\n    const selectCidList = ref([])\n    // 获取分类\n    findCategoryList(0, true, (res) => {\n      if (res && res.length) {\n        categoryOptions.value = toTree(res);\n        categoryOptions.value.splice(0, 1);\n        if (route.query.id) {\n          // 获取试题信息\n          getBaseInfo(route.query.id, function (res) {\n            console.log(res)\n            question.value = res;\n            optionList.value = JSON.parse(res.options);\n            selectCidList.value = getAllParent(categoryOptions.value, res.cidList);\n            question.value.cidList = []\n            for (const valElement of selectCidList.value) {\n              question.value.cidList.push(valElement[valElement.length - 1])\n            }\n            if (res.referenceAnswer) {\n              question.value.referenceAnswerList = res.referenceAnswer.split(\",\")\n            }\n          })\n        }\n      }\n    })\n    // 选择分类\n    const changeCategory = (val) => {\n      question.value.cidList = []\n      for (const valElement of val) {\n        question.value.cidList.push(valElement[valElement.length - 1])\n      }\n    }\n    let optionIndex = -1;\n    const option = ref(\"\")\n    const showAddOptionInput = ref(false)\n    const addOption = () => {\n      showAddOptionInput.value = true\n    }\n    const optionBlur = () => {\n      showAddOptionInput.value = false\n      if (!option.value) {\n        return\n      }\n      if (optionIndex > -1) {\n        optionList.value[optionIndex].value = option.value\n      } else {\n        optionList.value.push({value: option.value, key: serialNumber[optionList.value.length]})\n      }\n      question.value.options = JSON.stringify(optionList.value)\n      option.value = \"\"\n      optionIndex = -1;\n    }\n    const editOption = (index) => {\n      const o = optionList.value[index];\n      option.value = o.value;\n      optionIndex = index;\n      showAddOptionInput.value = true\n    }\n    const deleteOption = (index) => {\n      if (optionList.value && optionList.value.length) {\n        const o = optionList.value[index]\n        if (question.value.referenceAnswerList && question.value.referenceAnswerList.length) {\n          for (let i = 0; i < question.value.referenceAnswerList.length; i++) {\n            const ra = question.value.referenceAnswerList[i];\n            if(ra === o.key) {\n              question.value.referenceAnswerList.splice(i, 1);\n              break\n            }\n          }\n        }\n        optionList.value.splice(index, 1);\n        optionList.value.forEach((item, index) => {\n          item.key = serialNumber[index]\n        })\n        question.value.options = JSON.stringify(optionList.value)\n      } else {\n        question.value.options = \"\"\n      }\n    }\n    const questionRef = ref();\n    const submitBaseInfo = () => {\n      if (question.value.referenceAnswerList && question.value.referenceAnswerList.length) {\n        if (question.value.referenceAnswerList.length < 2) {\n          error(\"参考答案应大于2个\")\n          return\n        }\n        question.value.referenceAnswer = question.value.referenceAnswerList.join(\",\")\n      }\n      questionRef.value.validate((valid) => {\n        if (!valid) { return false }\n        if (question.value.id) {\n          updateBaseInfo(question.value, function () {\n            success(\"编辑成功\")\n            router.push({path: \"/exam/question-lib/list\"})\n          })\n        } else {\n          saveBaseInfo(question.value, function () {\n            success(\"新增成功\")\n            router.push({path: \"/exam/question-lib/list\"})\n          })\n        }\n      })\n    }\n    return {\n      colors,\n      question,\n      questionRules,\n      categoryOptions,\n      selectCidList,\n      serialNumber,\n      option,\n      optionList,\n      showAddOptionInput,\n      questionRef,\n      changeCategory,\n      addOption,\n      optionBlur,\n      editOption,\n      deleteOption,\n      submitBaseInfo\n    }\n  }\n}\n</script>\n<style scoped lang=\"scss\">\n.question-box {\n  margin: 20px;\n  .option-delete {\n    margin-left: 20px;\n    cursor: pointer;\n  }\n  .option-delete:hover {\n    color: $--color-primary;\n  }\n  ::v-deep .el-card__header{\n    padding: 0!important;\n  }\n}\n</style>\n"], "mappings": ";AAoDA,SAAQA,GAAG,QAAO,KAAI;AACtB,SAAQC,gBAAgB,EAAEC,MAAM,EAAEC,YAAY,QAAO,kCAAiC;AACtF,SAAQC,YAAY,EAAEC,cAAc,EAAEC,WAAW,QAAO,kCAAiC;AACzF,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,KAAK,EAAEC,OAAO,QAAO,kBAAkB;AAC/C,OAAOC,MAAK,MAAO,UAAU;AAE7B,eAAe;EACbC,IAAI,EAAE,4BAA4B;EAClCC,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIN,QAAQ,EAAC;IACvB,MAAMO,MAAK,GAAI,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;IAC/C,MAAMC,QAAO,GAAIf,GAAG,CAAC;MACnBgB,EAAE,EAAE,EAAE;MACNC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,EAAE;MACTC,UAAU,EAAE,CAAC;MACbC,eAAe,EAAE,EAAE;MACnBC,mBAAmB,EAAE,EAAE;MACvBC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACXC,mBAAmB,EAAE;IACvB,CAAC;IACD,MAAMC,aAAY,GAAI;MACpBV,KAAK,EAAE,CAAC;QAAEW,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9DV,KAAK,EAAE,CAAC;QAAEQ,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9DL,OAAO,EAAE,CAAC;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC,CAAC;MAClER,eAAe,EAAE,CAAC;QAAEM,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC1EP,mBAAmB,EAAE,CAAC;QAAEK,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9EN,OAAO,EAAE,CAAC;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC;IACjE;IACA,MAAMC,YAAW,GAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACtJ,MAAMC,UAAS,GAAIhC,GAAG,CAAC,EAAE;IACzB,MAAMiC,eAAc,GAAIjC,GAAG,CAAC,EAAE;IAC9B,MAAMkC,aAAY,GAAIlC,GAAG,CAAC,EAAE;IAC5B;IACAC,gBAAgB,CAAC,CAAC,EAAE,IAAI,EAAGkC,GAAG,IAAK;MACjC,IAAIA,GAAE,IAAKA,GAAG,CAACC,MAAM,EAAE;QACrBH,eAAe,CAACI,KAAI,GAAInC,MAAM,CAACiC,GAAG,CAAC;QACnCF,eAAe,CAACI,KAAK,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;QAClC,IAAIzB,KAAK,CAAC0B,KAAK,CAACvB,EAAE,EAAE;UAClB;UACAV,WAAW,CAACO,KAAK,CAAC0B,KAAK,CAACvB,EAAE,EAAE,UAAUmB,GAAG,EAAE;YACzCK,OAAO,CAACC,GAAG,CAACN,GAAG;YACfpB,QAAQ,CAACsB,KAAI,GAAIF,GAAG;YACpBH,UAAU,CAACK,KAAI,GAAIK,IAAI,CAACC,KAAK,CAACR,GAAG,CAACX,OAAO,CAAC;YAC1CU,aAAa,CAACG,KAAI,GAAIlC,YAAY,CAAC8B,eAAe,CAACI,KAAK,EAAEF,GAAG,CAACV,OAAO,CAAC;YACtEV,QAAQ,CAACsB,KAAK,CAACZ,OAAM,GAAI,EAAC;YAC1B,KAAK,MAAMmB,UAAS,IAAKV,aAAa,CAACG,KAAK,EAAE;cAC5CtB,QAAQ,CAACsB,KAAK,CAACZ,OAAO,CAACoB,IAAI,CAACD,UAAU,CAACA,UAAU,CAACR,MAAK,GAAI,CAAC,CAAC;YAC/D;YACA,IAAID,GAAG,CAACb,eAAe,EAAE;cACvBP,QAAQ,CAACsB,KAAK,CAACX,mBAAkB,GAAIS,GAAG,CAACb,eAAe,CAACwB,KAAK,CAAC,GAAG;YACpE;UACF,CAAC;QACH;MACF;IACF,CAAC;IACD;IACA,MAAMC,cAAa,GAAKC,GAAG,IAAK;MAC9BjC,QAAQ,CAACsB,KAAK,CAACZ,OAAM,GAAI,EAAC;MAC1B,KAAK,MAAMmB,UAAS,IAAKI,GAAG,EAAE;QAC5BjC,QAAQ,CAACsB,KAAK,CAACZ,OAAO,CAACoB,IAAI,CAACD,UAAU,CAACA,UAAU,CAACR,MAAK,GAAI,CAAC,CAAC;MAC/D;IACF;IACA,IAAIa,WAAU,GAAI,CAAC,CAAC;IACpB,MAAMC,MAAK,GAAIlD,GAAG,CAAC,EAAE;IACrB,MAAMmD,kBAAiB,GAAInD,GAAG,CAAC,KAAK;IACpC,MAAMoD,SAAQ,GAAIA,CAAA,KAAM;MACtBD,kBAAkB,CAACd,KAAI,GAAI,IAAG;IAChC;IACA,MAAMgB,UAAS,GAAIA,CAAA,KAAM;MACvBF,kBAAkB,CAACd,KAAI,GAAI,KAAI;MAC/B,IAAI,CAACa,MAAM,CAACb,KAAK,EAAE;QACjB;MACF;MACA,IAAIY,WAAU,GAAI,CAAC,CAAC,EAAE;QACpBjB,UAAU,CAACK,KAAK,CAACY,WAAW,CAAC,CAACZ,KAAI,GAAIa,MAAM,CAACb,KAAI;MACnD,OAAO;QACLL,UAAU,CAACK,KAAK,CAACQ,IAAI,CAAC;UAACR,KAAK,EAAEa,MAAM,CAACb,KAAK;UAAEiB,GAAG,EAAEvB,YAAY,CAACC,UAAU,CAACK,KAAK,CAACD,MAAM;QAAC,CAAC;MACzF;MACArB,QAAQ,CAACsB,KAAK,CAACb,OAAM,GAAIkB,IAAI,CAACa,SAAS,CAACvB,UAAU,CAACK,KAAK;MACxDa,MAAM,CAACb,KAAI,GAAI,EAAC;MAChBY,WAAU,GAAI,CAAC,CAAC;IAClB;IACA,MAAMO,UAAS,GAAKC,KAAK,IAAK;MAC5B,MAAMC,CAAA,GAAI1B,UAAU,CAACK,KAAK,CAACoB,KAAK,CAAC;MACjCP,MAAM,CAACb,KAAI,GAAIqB,CAAC,CAACrB,KAAK;MACtBY,WAAU,GAAIQ,KAAK;MACnBN,kBAAkB,CAACd,KAAI,GAAI,IAAG;IAChC;IACA,MAAMsB,YAAW,GAAKF,KAAK,IAAK;MAC9B,IAAIzB,UAAU,CAACK,KAAI,IAAKL,UAAU,CAACK,KAAK,CAACD,MAAM,EAAE;QAC/C,MAAMsB,CAAA,GAAI1B,UAAU,CAACK,KAAK,CAACoB,KAAK;QAChC,IAAI1C,QAAQ,CAACsB,KAAK,CAACX,mBAAkB,IAAKX,QAAQ,CAACsB,KAAK,CAACX,mBAAmB,CAACU,MAAM,EAAE;UACnF,KAAK,IAAIwB,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAI7C,QAAQ,CAACsB,KAAK,CAACX,mBAAmB,CAACU,MAAM,EAAEwB,CAAC,EAAE,EAAE;YAClE,MAAMC,EAAC,GAAI9C,QAAQ,CAACsB,KAAK,CAACX,mBAAmB,CAACkC,CAAC,CAAC;YAChD,IAAGC,EAAC,KAAMH,CAAC,CAACJ,GAAG,EAAE;cACfvC,QAAQ,CAACsB,KAAK,CAACX,mBAAmB,CAACY,MAAM,CAACsB,CAAC,EAAE,CAAC,CAAC;cAC/C;YACF;UACF;QACF;QACA5B,UAAU,CAACK,KAAK,CAACC,MAAM,CAACmB,KAAK,EAAE,CAAC,CAAC;QACjCzB,UAAU,CAACK,KAAK,CAACyB,OAAO,CAAC,CAACC,IAAI,EAAEN,KAAK,KAAK;UACxCM,IAAI,CAACT,GAAE,GAAIvB,YAAY,CAAC0B,KAAK;QAC/B,CAAC;QACD1C,QAAQ,CAACsB,KAAK,CAACb,OAAM,GAAIkB,IAAI,CAACa,SAAS,CAACvB,UAAU,CAACK,KAAK;MAC1D,OAAO;QACLtB,QAAQ,CAACsB,KAAK,CAACb,OAAM,GAAI,EAAC;MAC5B;IACF;IACA,MAAMwC,WAAU,GAAIhE,GAAG,EAAE;IACzB,MAAMiE,cAAa,GAAIA,CAAA,KAAM;MAC3B,IAAIlD,QAAQ,CAACsB,KAAK,CAACX,mBAAkB,IAAKX,QAAQ,CAACsB,KAAK,CAACX,mBAAmB,CAACU,MAAM,EAAE;QACnF,IAAIrB,QAAQ,CAACsB,KAAK,CAACX,mBAAmB,CAACU,MAAK,GAAI,CAAC,EAAE;UACjD5B,KAAK,CAAC,WAAW;UACjB;QACF;QACAO,QAAQ,CAACsB,KAAK,CAACf,eAAc,GAAIP,QAAQ,CAACsB,KAAK,CAACX,mBAAmB,CAACwC,IAAI,CAAC,GAAG;MAC9E;MACAF,WAAW,CAAC3B,KAAK,CAAC8B,QAAQ,CAAEC,KAAK,IAAK;QACpC,IAAI,CAACA,KAAK,EAAE;UAAE,OAAO,KAAI;QAAE;QAC3B,IAAIrD,QAAQ,CAACsB,KAAK,CAACrB,EAAE,EAAE;UACrBX,cAAc,CAACU,QAAQ,CAACsB,KAAK,EAAE,YAAY;YACzC5B,OAAO,CAAC,MAAM;YACdC,MAAM,CAACmC,IAAI,CAAC;cAACwB,IAAI,EAAE;YAAyB,CAAC;UAC/C,CAAC;QACH,OAAO;UACLjE,YAAY,CAACW,QAAQ,CAACsB,KAAK,EAAE,YAAY;YACvC5B,OAAO,CAAC,MAAM;YACdC,MAAM,CAACmC,IAAI,CAAC;cAACwB,IAAI,EAAE;YAAyB,CAAC;UAC/C,CAAC;QACH;MACF,CAAC;IACH;IACA,OAAO;MACLvD,MAAM;MACNC,QAAQ;MACRY,aAAa;MACbM,eAAe;MACfC,aAAa;MACbH,YAAY;MACZmB,MAAM;MACNlB,UAAU;MACVmB,kBAAkB;MAClBa,WAAW;MACXjB,cAAc;MACdK,SAAS;MACTC,UAAU;MACVG,UAAU;MACVG,YAAY;MACZM;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}