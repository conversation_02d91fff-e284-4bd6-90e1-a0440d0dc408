{"ast": null, "code": "import { resolveDynamicComponent as _resolveDynamicComponent, openBlock as _openBlock, createBlock as _createBlock, KeepAlive as _KeepAlive, Transition as _Transition, withCtx as _withCtx, createVNode as _createVNode, resolveComponent as _resolveComponent, createElement<PERSON>lock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  id: \"app\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_view = _resolveComponent(\"router-view\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_router_view, null, {\n    default: _withCtx(({\n      Component\n    }) => [_createVNode(_Transition, null, {\n      default: _withCtx(() => [(_openBlock(), _createBlock(_KeepAlive, null, [(_openBlock(), _createBlock(_resolveDynamicComponent(Component)))], 1024 /* DYNAMIC_SLOTS */))]),\n\n      _: 2 /* DYNAMIC */\n    }, 1024 /* DYNAMIC_SLOTS */)]),\n\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["id", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_router_view", "Component", "_Transition", "_createBlock", "_KeepAlive", "_resolveDynamicComponent"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <router-view v-slot=\"{ Component }\">\n      <transition>\n        <keep-alive>\n          <component :is=\"Component\"/>\n        </keep-alive>\n      </transition>\n    </router-view>\n  </div>\n</template>\n\n<script>\nimport {refreshToken} from \"./util/tokenUtils\";\nimport {onMounted} from \"vue\";\n\nexport default {\n  name: \"App\",\n  setup() {\n    onMounted(() => {\n      document.body.style.setProperty('--el-color-primary', '#07c160');\n    })\n    // 每次进页面都要是否刷新token\n    refreshToken()\n  }\n};\n</script>\n\n<style>\nul li{\n  list-style: none;\n}\na {\n  text-decoration: none;\n  cursor: pointer;\n}\n* {\n  margin: 0;\n  padding: 0;\n}\nbody,\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  margin: 0;\n  padding: 0;\n  border: 0;\n  min-height: 100%;\n  background-color: #f7f7f7;\n}\n</style>\n"], "mappings": ";;EACOA,EAAE,EAAC;AAAK;;;uBAAbC,mBAAA,CAQM,OARNC,UAQM,GAPJC,YAAA,CAMcC,sBAAA;sBALZ,CAIa;MALQC;IAAS,OAC9BF,YAAA,CAIaG,WAAA;wBAHX,MAEa,E,cAFbC,YAAA,CAEaC,UAAA,U,cADXD,YAAA,CAA4BE,wBAAA,CAAZJ,SAAS,I"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}