{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport * as lessonApi from \"@/api/learn/lesson\";\nimport * as newsApi from \"@/api/content/news\";\nimport * as articleApi from \"@/api/content/article\";\nimport * as askApi from \"@/api/ask/question\";\nimport * as dynamicApi from \"@/api/circle/dynamic\";\nimport * as liveApi from \"@/api/live/channel\";\nimport * as responseApi from \"@/api/resource\";\nimport * as circleApi from \"@/api/circle\";\nimport * as memberApi from \"@/api/member\";\nimport router from \"@/router\";\nexport function getTopicList(topicType, topicIdList, success) {\n  switch (topicType) {\n    case \"lesson\":\n      return lessonApi.getLessonListByIds({\n        idList: topicIdList\n      }, success);\n    case \"news\":\n      return newsApi.getListByIds({\n        idList: topicIdList\n      }, success);\n    case \"article\":\n      return articleApi.getListByIds({\n        idList: topicIdList\n      }, success);\n    case \"question\":\n      return askApi.getQuestionListByIds({\n        idList: topicIdList\n      }, success);\n    case \"answer\":\n      return askApi.getAnswerListByIds({\n        idList: topicIdList\n      }, success);\n    case \"dynamic\":\n      return dynamicApi.getListByIds({\n        idList: topicIdList\n      }, success);\n    case \"channel\":\n      return liveApi.getChannelListByIds({\n        idList: topicIdList\n      }, success);\n    case \"resource\":\n      return responseApi.getListByIds({\n        idList: topicIdList\n      }, success);\n    case \"circle\":\n      return circleApi.getListByIds({\n        idList: topicIdList\n      }, success);\n    case \"learn_topic\":\n      success && success([]);\n      break;\n    case \"learn_map\":\n      success && success([]);\n      break;\n    case \"exam\":\n      break;\n    case \"member\":\n      return memberApi.getListByIds({\n        ids: topicIdList\n      }, success);\n  }\n}\nexport function gotoTopic(item) {\n  if (item.type === \"comment\" || item.type === \"reply_comment\") {\n    //return\n  } else {\n    switch (item.topicType) {\n      case \"lesson\":\n        router.push({\n          path: \"/learn/detail\",\n          query: {\n            id: item.topicId\n          }\n        });\n        break;\n      case \"news\":\n        router.push({\n          path: \"/news/detail\",\n          query: {\n            id: item.topicId\n          }\n        });\n        break;\n      case \"channel\":\n        router.push({\n          path: \"/live/detail\",\n          query: {\n            id: item.topicId\n          }\n        });\n        break;\n      case \"article\":\n        router.push({\n          path: \"/article/detail\",\n          query: {\n            id: item.topicId\n          }\n        });\n        break;\n      case \"resource\":\n        router.push({\n          path: \"/resource/detail\",\n          query: {\n            id: item.topicId\n          }\n        });\n        break;\n      case \"question\":\n        router.push({\n          path: \"/ask/question\",\n          query: {\n            id: item.topicId\n          }\n        });\n        break;\n      case \"answer\":\n        router.push({\n          path: \"/ask/question\",\n          query: {\n            id: item.question ? item.question.id : item.topic.parentTopic.id\n          }\n        });\n        break;\n      case \"dynamic\":\n        router.push({\n          path: \"/circle/detail\",\n          query: {\n            id: item.topicId\n          }\n        });\n        break;\n    }\n  }\n}", "map": {"version": 3, "names": ["lessonApi", "newsApi", "articleApi", "askApi", "dynamicApi", "liveApi", "responseApi", "circleApi", "memberApi", "router", "getTopicList", "topicType", "topicIdList", "success", "getLessonListByIds", "idList", "getListByIds", "getQuestionListByIds", "getAnswerListByIds", "getChannelListByIds", "ids", "gotoTopic", "item", "type", "push", "path", "query", "id", "topicId", "question", "topic", "parentTopic"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/api/topic/index.js"], "sourcesContent": ["import * as lessonApi from \"@/api/learn/lesson\";\nimport * as newsApi from \"@/api/content/news\";\nimport * as articleApi from \"@/api/content/article\";\nimport * as askApi from \"@/api/ask/question\";\nimport * as dynamicApi from \"@/api/circle/dynamic\";\nimport * as liveApi from \"@/api/live/channel\";\nimport * as responseApi from \"@/api/resource\";\nimport * as circleApi from \"@/api/circle\";\nimport * as memberApi from \"@/api/member\"\nimport router from \"@/router\";\n\nexport function getTopicList(topicType, topicIdList, success) {\n  switch (topicType) {\n  case \"lesson\":\n    return lessonApi.getLessonListByIds({idList: topicIdList}, success)\n  case \"news\":\n    return newsApi.getListByIds({idList: topicIdList}, success);\n  case \"article\":\n    return articleApi.getListByIds({idList: topicIdList}, success);\n  case \"question\":\n    return askApi.getQuestionListByIds({idList: topicIdList}, success);\n  case \"answer\":\n    return askApi.getAnswerListByIds({idList: topicIdList}, success);\n  case \"dynamic\":\n    return dynamicApi.getListByIds({idList: topicIdList}, success);\n  case \"channel\":\n    return liveApi.getChannelListByIds({idList: topicIdList}, success);\n  case \"resource\":\n    return responseApi.getListByIds({idList: topicIdList}, success);\n  case \"circle\":\n    return circleApi.getListByIds({idList: topicIdList}, success);\n  case \"learn_topic\":\n    success && success([])\n    break;\n  case \"learn_map\":\n    success && success([])\n    break;\n  case \"exam\":\n    break;\n  case \"member\":\n    return memberApi.getListByIds({ids: topicIdList}, success);\n  }\n}\n\nexport function gotoTopic(item) {\n  if (item.type === \"comment\" || item.type === \"reply_comment\") {\n    //return\n  } else {\n    switch (item.topicType) {\n    case \"lesson\":\n      router.push({path: \"/learn/detail\", query: {id: item.topicId}})\n      break;\n    case \"news\":\n      router.push({path: \"/news/detail\", query: {id: item.topicId}})\n      break;\n    case \"channel\":\n      router.push({path: \"/live/detail\", query: {id: item.topicId}})\n      break;\n    case \"article\":\n      router.push({path: \"/article/detail\", query: {id: item.topicId}})\n      break;\n    case \"resource\":\n      router.push({path: \"/resource/detail\", query: {id: item.topicId}})\n      break;\n    case \"question\":\n      router.push({path: \"/ask/question\", query: {id: item.topicId}})\n      break;\n    case \"answer\":\n      router.push({path: \"/ask/question\", query: {id: item.question ? item.question.id : item.topic.parentTopic.id}})\n      break;\n    case \"dynamic\":\n      router.push({path: \"/circle/detail\", query: {id: item.topicId}})\n      break;\n    }\n  }\n}\n"], "mappings": ";AAAA,OAAO,KAAKA,SAAS,MAAM,oBAAoB;AAC/C,OAAO,KAAKC,OAAO,MAAM,oBAAoB;AAC7C,OAAO,KAAKC,UAAU,MAAM,uBAAuB;AACnD,OAAO,KAAKC,MAAM,MAAM,oBAAoB;AAC5C,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAO,KAAKC,OAAO,MAAM,oBAAoB;AAC7C,OAAO,KAAKC,WAAW,MAAM,gBAAgB;AAC7C,OAAO,KAAKC,SAAS,MAAM,cAAc;AACzC,OAAO,KAAKC,SAAS,MAAM,cAAc;AACzC,OAAOC,MAAM,MAAM,UAAU;AAE7B,OAAO,SAASC,YAAYA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAC5D,QAAQF,SAAS;IACjB,KAAK,QAAQ;MACX,OAAOX,SAAS,CAACc,kBAAkB,CAAC;QAACC,MAAM,EAAEH;MAAW,CAAC,EAAEC,OAAO,CAAC;IACrE,KAAK,MAAM;MACT,OAAOZ,OAAO,CAACe,YAAY,CAAC;QAACD,MAAM,EAAEH;MAAW,CAAC,EAAEC,OAAO,CAAC;IAC7D,KAAK,SAAS;MACZ,OAAOX,UAAU,CAACc,YAAY,CAAC;QAACD,MAAM,EAAEH;MAAW,CAAC,EAAEC,OAAO,CAAC;IAChE,KAAK,UAAU;MACb,OAAOV,MAAM,CAACc,oBAAoB,CAAC;QAACF,MAAM,EAAEH;MAAW,CAAC,EAAEC,OAAO,CAAC;IACpE,KAAK,QAAQ;MACX,OAAOV,MAAM,CAACe,kBAAkB,CAAC;QAACH,MAAM,EAAEH;MAAW,CAAC,EAAEC,OAAO,CAAC;IAClE,KAAK,SAAS;MACZ,OAAOT,UAAU,CAACY,YAAY,CAAC;QAACD,MAAM,EAAEH;MAAW,CAAC,EAAEC,OAAO,CAAC;IAChE,KAAK,SAAS;MACZ,OAAOR,OAAO,CAACc,mBAAmB,CAAC;QAACJ,MAAM,EAAEH;MAAW,CAAC,EAAEC,OAAO,CAAC;IACpE,KAAK,UAAU;MACb,OAAOP,WAAW,CAACU,YAAY,CAAC;QAACD,MAAM,EAAEH;MAAW,CAAC,EAAEC,OAAO,CAAC;IACjE,KAAK,QAAQ;MACX,OAAON,SAAS,CAACS,YAAY,CAAC;QAACD,MAAM,EAAEH;MAAW,CAAC,EAAEC,OAAO,CAAC;IAC/D,KAAK,aAAa;MAChBA,OAAO,IAAIA,OAAO,CAAC,EAAE,CAAC;MACtB;IACF,KAAK,WAAW;MACdA,OAAO,IAAIA,OAAO,CAAC,EAAE,CAAC;MACtB;IACF,KAAK,MAAM;MACT;IACF,KAAK,QAAQ;MACX,OAAOL,SAAS,CAACQ,YAAY,CAAC;QAACI,GAAG,EAAER;MAAW,CAAC,EAAEC,OAAO,CAAC;EAAC;AAE/D;AAEA,OAAO,SAASQ,SAASA,CAACC,IAAI,EAAE;EAC9B,IAAIA,IAAI,CAACC,IAAI,KAAK,SAAS,IAAID,IAAI,CAACC,IAAI,KAAK,eAAe,EAAE;IAC5D;EAAA,CACD,MAAM;IACL,QAAQD,IAAI,CAACX,SAAS;MACtB,KAAK,QAAQ;QACXF,MAAM,CAACe,IAAI,CAAC;UAACC,IAAI,EAAE,eAAe;UAAEC,KAAK,EAAE;YAACC,EAAE,EAAEL,IAAI,CAACM;UAAO;QAAC,CAAC,CAAC;QAC/D;MACF,KAAK,MAAM;QACTnB,MAAM,CAACe,IAAI,CAAC;UAACC,IAAI,EAAE,cAAc;UAAEC,KAAK,EAAE;YAACC,EAAE,EAAEL,IAAI,CAACM;UAAO;QAAC,CAAC,CAAC;QAC9D;MACF,KAAK,SAAS;QACZnB,MAAM,CAACe,IAAI,CAAC;UAACC,IAAI,EAAE,cAAc;UAAEC,KAAK,EAAE;YAACC,EAAE,EAAEL,IAAI,CAACM;UAAO;QAAC,CAAC,CAAC;QAC9D;MACF,KAAK,SAAS;QACZnB,MAAM,CAACe,IAAI,CAAC;UAACC,IAAI,EAAE,iBAAiB;UAAEC,KAAK,EAAE;YAACC,EAAE,EAAEL,IAAI,CAACM;UAAO;QAAC,CAAC,CAAC;QACjE;MACF,KAAK,UAAU;QACbnB,MAAM,CAACe,IAAI,CAAC;UAACC,IAAI,EAAE,kBAAkB;UAAEC,KAAK,EAAE;YAACC,EAAE,EAAEL,IAAI,CAACM;UAAO;QAAC,CAAC,CAAC;QAClE;MACF,KAAK,UAAU;QACbnB,MAAM,CAACe,IAAI,CAAC;UAACC,IAAI,EAAE,eAAe;UAAEC,KAAK,EAAE;YAACC,EAAE,EAAEL,IAAI,CAACM;UAAO;QAAC,CAAC,CAAC;QAC/D;MACF,KAAK,QAAQ;QACXnB,MAAM,CAACe,IAAI,CAAC;UAACC,IAAI,EAAE,eAAe;UAAEC,KAAK,EAAE;YAACC,EAAE,EAAEL,IAAI,CAACO,QAAQ,GAAGP,IAAI,CAACO,QAAQ,CAACF,EAAE,GAAGL,IAAI,CAACQ,KAAK,CAACC,WAAW,CAACJ;UAAE;QAAC,CAAC,CAAC;QAC/G;MACF,KAAK,SAAS;QACZlB,MAAM,CAACe,IAAI,CAAC;UAACC,IAAI,EAAE,gBAAgB;UAAEC,KAAK,EAAE;YAACC,EAAE,EAAEL,IAAI,CAACM;UAAO;QAAC,CAAC,CAAC;QAChE;IAAM;EAEV;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}