{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, createBlock as _createBlock, normalizeStyle as _normalizeStyle, resolveDirective as _resolveDirective, withDirectives as _withDirectives, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-9adb5c22\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"exam-paper\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"question-menu-list\"\n};\nconst _hoisted_3 = [\"onClick\"];\nconst _hoisted_4 = {\n  class: \"paper-title\"\n};\nconst _hoisted_5 = {\n  class: \"paper-base\"\n};\nconst _hoisted_6 = {\n  key: 0,\n  class: \"paper-base-info\"\n};\nconst _hoisted_7 = {\n  class: \"paper-base-info\"\n};\nconst _hoisted_8 = {\n  class: \"paper-base-info\"\n};\nconst _hoisted_9 = {\n  class: \"paper-base-info\"\n};\nconst _hoisted_10 = {\n  class: \"paper-base-info\"\n};\nconst _hoisted_11 = {\n  class: \"paper-question-list\"\n};\nconst _hoisted_12 = {\n  class: \"title\"\n};\nconst _hoisted_13 = {\n  class: \"question-body\"\n};\nconst _hoisted_14 = {\n  key: 0\n};\nconst _hoisted_15 = {\n  key: 1\n};\nconst _hoisted_16 = {\n  style: {\n    \"width\": \"20px\",\n    \"padding\": \"0 10px\"\n  }\n};\nconst _hoisted_17 = {\n  key: 2\n};\nconst _hoisted_18 = {\n  class: \"answer-box\"\n};\nconst _hoisted_19 = {\n  class: \"answer-item\"\n};\nconst _hoisted_20 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"answer-info-label\"\n}, \"结果：\", -1 /* HOISTED */));\nconst _hoisted_21 = {\n  class: \"answer-info-value\"\n};\nconst _hoisted_22 = {\n  class: \"answer-item\"\n};\nconst _hoisted_23 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"answer-info-label\"\n}, \"得分：\", -1 /* HOISTED */));\nconst _hoisted_24 = {\n  class: \"answer-info-value\",\n  style: {\n    \"color\": \"green\",\n    \"font-size\": \"20px\",\n    \"font-weight\": \"500\"\n  }\n};\nconst _hoisted_25 = {\n  class: \"answer-item\"\n};\nconst _hoisted_26 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"answer-info-label\"\n}, \"分数：\", -1 /* HOISTED */));\nconst _hoisted_27 = {\n  class: \"answer-info-value\"\n};\nconst _hoisted_28 = {\n  class: \"answer-item\"\n};\nconst _hoisted_29 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"answer-info-label\"\n}, \"难度：\", -1 /* HOISTED */));\nconst _hoisted_30 = {\n  class: \"answer-info-value\"\n};\nconst _hoisted_31 = {\n  class: \"answer-item\"\n};\nconst _hoisted_32 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"answer-info-label\",\n  style: {\n    \"vertical-align\": \"top\"\n  }\n}, \"正确答案：\", -1 /* HOISTED */));\nconst _hoisted_33 = {\n  class: \"answer-info-value\"\n};\nconst _hoisted_34 = {\n  key: 0\n};\nconst _hoisted_35 = {\n  key: 1\n};\nconst _hoisted_36 = {\n  class: \"answer-item\"\n};\nconst _hoisted_37 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"answer-info-label\"\n}, \"解析：\", -1 /* HOISTED */));\nconst _hoisted_38 = {\n  class: \"answer-info-value\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_header = _resolveComponent(\"el-header\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  const _component_el_checkbox_group = _resolveComponent(\"el-checkbox-group\");\n  const _component_el_radio = _resolveComponent(\"el-radio\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_rate = _resolveComponent(\"el-rate\");\n  const _component_el_main = _resolveComponent(\"el-main\");\n  const _component_el_container = _resolveComponent(\"el-container\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_container, {\n    style: _normalizeStyle('height: ' + $setup.clientHeight + 'px')\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_header, {\n      class: \"exam-paper-header\",\n      height: \"auto\",\n      ref: \"headerRef\"\n    }, {\n      default: _withCtx(() => [$setup.paper.questionList && $setup.paper.questionList.length ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.paper.questionList.length, (i, index) => {\n        return _openBlock(), _createElementBlock(\"span\", {\n          class: \"question-menu\",\n          key: i,\n          onClick: $event => $setup.position(index),\n          ref_for: true,\n          ref: el => {\n            if (el) $setup.questionNavBtnList[index] = el;\n          }\n        }, _toDisplayString(i), 9 /* TEXT, PROPS */, _hoisted_3);\n      }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)]),\n      _: 1 /* STABLE */\n    }, 512 /* NEED_PATCH */), $setup.paper && $setup.paper.questionList && $setup.paper.questionList.length ? (_openBlock(), _createBlock(_component_el_main, {\n      key: 0,\n      ref: \"mainRef\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, _toDisplayString($setup.paper.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_5, [$setup.record && $setup.record.id ? (_openBlock(), _createElementBlock(\"span\", _hoisted_6, \"状态：\" + _toDisplayString($setup.record.status === 'submitted' ? \"待批改\" : $setup.record.status === 'passed' ? \"已通过\" : \"未通过\"), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"span\", _hoisted_7, \"试卷得分：\" + _toDisplayString($setup.record.score), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_8, \"合格分数：\" + _toDisplayString($setup.paper.passScore), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_9, \"试卷总分：\" + _toDisplayString($setup.paper.score), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_10, \"考试时长：\" + _toDisplayString($setup.paper.limitTime) + \" 分钟\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_11, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.paper.questionList, (item, index) => {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"paper-question\",\n          key: index,\n          ref_for: true,\n          ref: el => {\n            if (el) $setup.questionNavList[index] = el;\n          }\n        }, [_createElementVNode(\"div\", _hoisted_12, _toDisplayString(index + 1) + \". \" + _toDisplayString($setup.formatTitle(item)), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_13, [item.type === 'subjective' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_createVNode(_component_el_input, {\n          readonly: true,\n          type: \"textarea\",\n          onBlur: $event => $setup.answerChangeHandle(index, item),\n          rows: 10,\n          modelValue: $setup.answerMap[item.type + '_' + item.id],\n          \"onUpdate:modelValue\": $event => $setup.answerMap[item.type + '_' + item.id] = $event\n        }, null, 8 /* PROPS */, [\"onBlur\", \"modelValue\", \"onUpdate:modelValue\"])])) : _createCommentVNode(\"v-if\", true), item.type === 'fill_blank' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.blankCount, i => {\n          return _openBlock(), _createElementBlock(\"div\", {\n            key: i,\n            style: {\n              \"display\": \"flex\",\n              \"margin\": \"10px 0\"\n            }\n          }, [_createElementVNode(\"div\", _hoisted_16, _toDisplayString(i) + \".\", 1 /* TEXT */), _createVNode(_component_el_input, {\n            readonly: true,\n            onBlur: $event => $setup.answerChangeHandle(index, item),\n            size: \"mini\",\n            modelValue: $setup.answerMap[item.type + '_' + item.id + '_' + i],\n            \"onUpdate:modelValue\": $event => $setup.answerMap[item.type + '_' + item.id + '_' + i] = $event\n          }, null, 8 /* PROPS */, [\"onBlur\", \"modelValue\", \"onUpdate:modelValue\"])]);\n        }), 128 /* KEYED_FRAGMENT */))])) : item.options ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [item.type === 'multi_choice' ? (_openBlock(), _createBlock(_component_el_checkbox_group, {\n          key: 0,\n          modelValue: $setup.answerMap[item.type + '_' + item.id],\n          \"onUpdate:modelValue\": $event => $setup.answerMap[item.type + '_' + item.id] = $event,\n          onChange: $event => $setup.answerChangeHandle(index, item)\n        }, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(JSON.parse(item.options), o => {\n            return _openBlock(), _createBlock(_component_el_checkbox, {\n              disabled: true,\n              label: o.key,\n              key: o.key\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString(o.key) + \". \" + _toDisplayString(o.value), 1 /* TEXT */)]),\n\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"]);\n          }), 128 /* KEYED_FRAGMENT */))]),\n\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\", \"onChange\"])) : (_openBlock(true), _createElementBlock(_Fragment, {\n          key: 1\n        }, _renderList(JSON.parse(item.options), o => {\n          return _openBlock(), _createElementBlock(\"div\", {\n            key: o.key\n          }, [_createVNode(_component_el_radio, {\n            disabled: true,\n            onChange: $event => $setup.answerChangeHandle(index, item),\n            modelValue: $setup.answerMap[item.type + '_' + item.id],\n            \"onUpdate:modelValue\": $event => $setup.answerMap[item.type + '_' + item.id] = $event,\n            label: o.key\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString(o.key) + \". \" + _toDisplayString(o.value), 1 /* TEXT */)]),\n\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onChange\", \"modelValue\", \"onUpdate:modelValue\", \"label\"])]);\n        }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_hoisted_20, _createElementVNode(\"div\", _hoisted_21, [item.result ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 0,\n          style: {\n            \"padding\": \"3px 10px\"\n          },\n          size: \"mini\",\n          type: \"success\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(\"对\")]),\n          _: 1 /* STABLE */\n        })) : (_openBlock(), _createBlock(_component_el_button, {\n          key: 1,\n          style: {\n            \"padding\": \"3px 10px\"\n          },\n          size: \"mini\",\n          type: \"danger\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(\"错\")]),\n          _: 1 /* STABLE */\n        }))])]), _createElementVNode(\"div\", _hoisted_22, [_hoisted_23, _createElementVNode(\"div\", _hoisted_24, _toDisplayString(item.scored || 0), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_25, [_hoisted_26, _createElementVNode(\"div\", _hoisted_27, _toDisplayString(item.score), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_28, [_hoisted_29, _createElementVNode(\"div\", _hoisted_30, [_createVNode(_component_el_rate, {\n          disabled: true,\n          modelValue: item.difficulty,\n          \"onUpdate:modelValue\": $event => item.difficulty = $event,\n          colors: $setup.colors\n        }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\", \"colors\"])])]), _createElementVNode(\"div\", _hoisted_31, [_hoisted_32, _createElementVNode(\"div\", _hoisted_33, [item.type === 'fill_blank' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_34, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.referenceAnswer.split('[_]'), (blank, i) => {\n          return _openBlock(), _createElementBlock(\"div\", {\n            key: i\n          }, \" 填空 \" + _toDisplayString(i + 1) + \". \" + _toDisplayString(blank), 1 /* TEXT */);\n        }), 128 /* KEYED_FRAGMENT */))])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_35, _toDisplayString(item.referenceAnswer), 1 /* TEXT */))])]), _createElementVNode(\"div\", _hoisted_36, [_hoisted_37, _createElementVNode(\"div\", _hoisted_38, _toDisplayString(item.referenceAnswerNote), 1 /* TEXT */)])])])]);\n      }), 128 /* KEYED_FRAGMENT */))])]),\n\n      _: 1 /* STABLE */\n    }, 512 /* NEED_PATCH */)) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"style\"])])), [[_directive_loading, $setup.paperLoading]]);\n}", "map": {"version": 3, "names": ["class", "style", "_createElementVNode", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_container", "_normalizeStyle", "$setup", "clientHeight", "_component_el_header", "height", "ref", "paper", "questionList", "length", "_hoisted_2", "_Fragment", "_renderList", "i", "index", "key", "onClick", "$event", "position", "el", "questionNavBtnList", "_hoisted_3", "_createBlock", "_component_el_main", "_hoisted_4", "_toDisplayString", "title", "_hoisted_5", "record", "id", "_hoisted_6", "status", "_hoisted_7", "score", "_hoisted_8", "passScore", "_hoisted_9", "_hoisted_10", "limitTime", "_hoisted_11", "item", "questionNavList", "_hoisted_12", "formatTitle", "_hoisted_13", "type", "_hoisted_14", "_component_el_input", "readonly", "onBlur", "answerChangeHandle", "rows", "answerMap", "_hoisted_15", "blankCount", "_hoisted_16", "size", "options", "_hoisted_17", "_component_el_checkbox_group", "onChange", "JSON", "parse", "o", "_component_el_checkbox", "disabled", "label", "value", "_component_el_radio", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "result", "_component_el_button", "_hoisted_22", "_hoisted_23", "_hoisted_24", "scored", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_component_el_rate", "difficulty", "colors", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "referenceAnswer", "split", "blank", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "referenceAnswerNote", "paperLoading"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/exam/answer/detail/index.vue"], "sourcesContent": ["<template>\n  <div class=\"exam-paper\" v-loading=\"paperLoading\">\n    <el-container :style=\"'height: ' + clientHeight + 'px'\">\n      <el-header class=\"exam-paper-header\" height=\"auto\" ref=\"headerRef\">\n        <div class=\"question-menu-list\" v-if=\"paper.questionList && paper.questionList.length\">\n          <span class=\"question-menu\" v-for=\"(i, index) in paper.questionList.length\" :key=\"i\" @click=\"position(index)\" :ref=\"el => { if (el) questionNavBtnList[index] = el }\">{{i}}</span>\n        </div>\n      </el-header>\n      <el-main ref=\"mainRef\" v-if=\"paper && paper.questionList && paper.questionList.length\">\n        <div class=\"paper-title\">\n          {{paper.title}}\n        </div>\n        <div class=\"paper-base\">\n          <span class=\"paper-base-info\" v-if=\"record && record.id\">状态：{{record.status === 'submitted' ? \"待批改\" : record.status === 'passed' ? \"已通过\" : \"未通过\"}}</span>\n          <span class=\"paper-base-info\">试卷得分：{{record.score}}</span>\n          <span class=\"paper-base-info\">合格分数：{{paper.passScore}}</span>\n          <span class=\"paper-base-info\">试卷总分：{{paper.score}}</span>\n          <span class=\"paper-base-info\">考试时长：{{paper.limitTime}} 分钟</span>\n        </div>\n        <div class=\"paper-question-list\">\n          <div class=\"paper-question\" v-for=\"(item, index) in paper.questionList\" :key=\"index\" :ref=\"el => { if (el) questionNavList[index] = el }\">\n            <div class=\"title\">\n              {{index + 1}}. {{formatTitle(item)}}\n            </div>\n            <div class=\"question-body\">\n              <div v-if=\"item.type === 'subjective'\">\n                <el-input :readonly=\"true\" type=\"textarea\" @blur=\"answerChangeHandle(index, item)\" :rows=\"10\" v-model=\"answerMap[item.type + '_' + item.id]\"/>\n              </div>\n              <div v-if=\"item.type === 'fill_blank'\">\n                <div v-for=\"i in item.blankCount\" :key=\"i\" style=\"display: flex;margin: 10px 0;\">\n                  <div style=\"width: 20px;padding: 0 10px;\">{{i}}.</div>\n                  <el-input :readonly=\"true\" @blur=\"answerChangeHandle(index, item)\" size=\"mini\" v-model=\"answerMap[item.type + '_' + item.id + '_' + i]\"/>\n                </div>\n              </div>\n              <div v-else-if=\"item.options\">\n                <el-checkbox-group v-if=\"item.type === 'multi_choice'\" v-model=\"answerMap[item.type + '_' + item.id]\" @change=\"answerChangeHandle(index, item)\">\n                  <el-checkbox :disabled=\"true\" :label=\"o.key\" v-for=\"o in JSON.parse(item.options)\" :key=\"o.key\">{{o.key}}. {{o.value}}</el-checkbox>\n                </el-checkbox-group>\n                <div v-else v-for=\"o in JSON.parse(item.options)\" :key=\"o.key\">\n                  <el-radio :disabled=\"true\" @change=\"answerChangeHandle(index, item)\" v-model=\"answerMap[item.type + '_' + item.id]\" :label=\"o.key\">{{o.key}}. {{o.value}}</el-radio>\n                </div>\n              </div>\n              <div class=\"answer-box\">\n                <div class=\"answer-item\">\n                  <div class=\"answer-info-label\">结果：</div>\n                  <div class=\"answer-info-value\">\n                    <el-button style=\"padding: 3px 10px;\" v-if=\"item.result\" size=\"mini\" type=\"success\">对</el-button>\n                    <el-button style=\"padding: 3px 10px;\" v-else size=\"mini\" type=\"danger\">错</el-button>\n                  </div>\n                </div>\n                <div class=\"answer-item\">\n                  <div class=\"answer-info-label\">得分：</div>\n                  <div class=\"answer-info-value\" style=\"color: green;font-size: 20px;font-weight: 500;\">{{item.scored || 0}}</div>\n                </div>\n                <div class=\"answer-item\">\n                  <div class=\"answer-info-label\">分数：</div>\n                  <div class=\"answer-info-value\">{{item.score}}</div>\n                </div>\n                <div class=\"answer-item\">\n                  <div class=\"answer-info-label\">难度：</div>\n                  <div class=\"answer-info-value\">\n                    <el-rate :disabled=\"true\" v-model=\"item.difficulty\" :colors=\"colors\"></el-rate>\n                  </div>\n                </div>\n                <div class=\"answer-item\">\n                  <div class=\"answer-info-label\" style=\"vertical-align: top;\">正确答案：</div>\n                  <div class=\"answer-info-value\">\n                    <div v-if=\"item.type === 'fill_blank'\">\n                      <div v-for=\"(blank, i) in item.referenceAnswer.split('[_]')\" :key=\"i\">\n                        填空 {{i + 1}}. {{blank}}\n                      </div>\n                    </div>\n                    <div v-else>\n                      {{item.referenceAnswer}}\n                    </div>\n                  </div>\n                </div>\n                <div class=\"answer-item\">\n                  <div class=\"answer-info-label\">解析：</div>\n                  <div class=\"answer-info-value\">\n                    {{item.referenceAnswerNote}}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </el-main>\n    </el-container>\n  </div>\n</template>\n\n<script>\nimport {nextTick, ref} from \"vue\"\nimport {getPaper, getRecord} from \"@/api/exam/paper\"\nexport default {\n  name: \"PaperDetail\",\n  props: {\n    examId: {\n      type: Number\n    },\n    signUpId: {\n      type: Number\n    },\n    examChapterSectionId: {\n      type: Number\n    },\n  },\n  setup(props) {\n    const clientHeight = ref(document.documentElement.clientHeight)\n    window.onresize = () => {\n      return (() => {\n        clientHeight.value = document.documentElement.clientHeight;\n      })()\n    }\n    const questionNavList = ref([])\n    const questionNavBtnList = ref([])\n    const mainRef = ref(null)\n    const headerRef = ref(null)\n    const position = (i) => {\n      const anchor = questionNavList.value[i]\n      const scrollTop = anchor.offsetTop - headerRef.value.$el.offsetHeight\n      nextTick(() => {\n        mainRef.value.$el.scrollTop = scrollTop\n      })\n    }\n    const paper = ref({})\n    const paperLoading = ref(false)\n    const answerMap = ref({})\n    const answerChangeHandle = (index, item) => {\n      if (item.type === \"subjective\") {\n        if (answerMap.value[item.type + \"_\" + item.id]) {\n          questionNavBtnList.value[index].style.background = \"#415fff\";\n        } else {\n          questionNavBtnList.value[index].style.background = \"#cccccc\";\n        }\n      } else if (item.type === \"multi_choice\") {\n        if (answerMap.value[item.type + \"_\" + item.id].length >= 2) {\n          questionNavBtnList.value[index].style.background = \"#415fff\";\n        } else if (answerMap.value[item.type + \"_\" + item.id].length === 1) {\n          questionNavBtnList.value[index].style.background = \"#fdc90c\";\n        } else {\n          questionNavBtnList.value[index].style.background = \"#cccccc\";\n        }\n      } else if (item.type === \"fill_blank\") {\n        let hasEmpty = false;\n        let hasEmptyCount = 1;\n        for (let i = 0; i < item.blankCount; i++) {\n          if (!answerMap.value[item.type + \"_\" + item.id + \"_\" + (i + 1)]) {\n            hasEmpty = true;\n            hasEmptyCount++;\n          }\n        }\n        if (hasEmpty) {\n          if (hasEmptyCount === item.blankCount) {\n            questionNavBtnList.value[index].style.background = \"#fdc90c\";\n          } else {\n            questionNavBtnList.value[index].style.background = \"#cccccc\";\n          }\n        } else {\n          questionNavBtnList.value[index].style.background = \"#415fff\";\n        }\n      } else {\n        if (answerMap.value[item.type + \"_\" + item.id]) {\n          questionNavBtnList.value[index].style.background = \"#415fff\";\n        }\n      }\n    }\n    const record = ref({});\n    // 加载试卷\n    const loadPaper = () => {\n      paperLoading.value = true;\n      const p = {\n        examId: props.examId,\n        signUpId: props.signUpId,\n        examChapterSectionId: props.examChapterSectionId\n      }\n      getRecord(p, (res) => {\n        if (!(res && res.id)) {\n          record.value = res\n          getPaper(props.paperId, res => {\n            if (res.questionList && res.questionList.length) {\n              for (const question of res.questionList) {\n                if (question.type === \"multi_choice\") {\n                  answerMap.value[question.type + \"_\" + question.id] = []\n                } else if (question.type === \"fill_blank\") {\n                  let thisCount = 0;\n                  question.title.replace(/\\[_\\]/g, function () {\n                    thisCount++;\n                    answerMap.value[question.type + \"_\" + question.id + \"_\" + thisCount] = \"\"\n                    return \"[_]\"\n                  });\n                  question.blankCount = thisCount\n                } else {\n                  answerMap.value[question.type + \"_\" + question.id] = \"\"\n                }\n              }\n            }\n            paper.value = res\n            paperLoading.value = false;\n          })\n        } else {\n          record.value = res\n          answerMap.value = JSON.parse(res.answer)\n          console.log(answerMap.value)\n          paper.value = JSON.parse(res.paper)\n          paperLoading.value = false;\n          nextTick(() => {\n            // 题目导航颜色\n            let i = 0\n            for (const q of paper.value.questionList) {\n              answerChangeHandle(i, q);\n              i++;\n            }\n          })\n        }\n      })\n    }\n    loadPaper()\n    const formatTitle = (item) => {\n      if (item.type === \"fill_blank\") {\n        let title = item.title\n        for (let i = 0; i < item.blankCount; i++) {\n          title = title.replace(\"[_]\", \"[__\" + (i + 1) + \"__]\");\n        }\n        return title\n      } else {\n        return item.title\n      }\n    }\n    const colors = [\"#99A9BF\", \"#F7BA2A\", \"#FF9900\"]\n    return {\n      clientHeight,\n      questionNavList,\n      questionNavBtnList,\n      paper,\n      paperLoading,\n      answerMap,\n      position,\n      mainRef,\n      headerRef,\n      answerChangeHandle,\n      formatTitle,\n      colors,\n      record\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.exam-paper {\n  .exam-paper-header {\n    background: rgba(65, 95, 255, .1);\n    .question-menu-list {\n      .question-menu {\n        background: #cccccc;\n        padding: 3px 10px;\n        vertical-align: middle;\n        margin: 5px 10px 5px 0;\n        border-radius: 4px;\n        float: left;\n        cursor: pointer;\n        color: #ffffff;\n        &:not(.countdown):hover {\n          background: $--color-primary;\n        }\n      }\n      .countdown {\n        float: right;\n        background: none;\n        margin-right: 0;\n        cursor: text;\n        color: #222222;\n      }\n    }\n  }\n  .el-main {\n    background: #ffffff;\n    .paper-title {\n      font-size: 30px;\n      font-weight: 500;\n      text-align: center;\n    }\n    .paper-base {\n      text-align: center;\n      margin: 20px 0;\n      .paper-base-info {\n        padding: 0 20px;\n      }\n    }\n    .paper-question-list {\n      .paper-question {\n        padding: 20px 0;\n        line-height: 36px;\n        .question-body {\n          ::v-deep .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {\n            background-color: $--color-primary;\n          }\n          ::v-deep .el-radio__input.is-disabled.is-checked .el-radio__inner {\n            background-color: $--color-primary;\n          }\n          ::v-deep .el-checkbox-group {\n            .el-checkbox__label {\n              color: #333;\n            }\n          }\n          ::v-deep .el-radio__label {\n            color: #333;\n          }\n        }\n        .answer-box {\n          margin-top: 20px;\n          .answer-item {\n            .answer-info-label {\n              display: inline-block;\n            }\n            .answer-info-value {\n              display: inline-block;\n              ::v-deep .el-rate {\n                line-height: 16px;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAY;;;EAGZA,KAAK,EAAC;;;;EAKNA,KAAK,EAAC;AAAa;;EAGnBA,KAAK,EAAC;AAAY;;;EACfA,KAAK,EAAC;;;EACNA,KAAK,EAAC;AAAiB;;EACvBA,KAAK,EAAC;AAAiB;;EACvBA,KAAK,EAAC;AAAiB;;EACvBA,KAAK,EAAC;AAAiB;;EAE1BA,KAAK,EAAC;AAAqB;;EAEvBA,KAAK,EAAC;AAAO;;EAGbA,KAAK,EAAC;AAAe;;;;;;;;EAMfC,KAAoC,EAApC;IAAA;IAAA;EAAA;AAAoC;;;;;EAYxCD,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAa;iEACtBE,mBAAA,CAAwC;EAAnCF,KAAK,EAAC;AAAmB,GAAC,KAAG;;EAC7BA,KAAK,EAAC;AAAmB;;EAK3BA,KAAK,EAAC;AAAa;iEACtBE,mBAAA,CAAwC;EAAnCF,KAAK,EAAC;AAAmB,GAAC,KAAG;;EAC7BA,KAAK,EAAC,mBAAmB;EAACC,KAAsD,EAAtD;IAAA;IAAA;IAAA;EAAA;;;EAE5BD,KAAK,EAAC;AAAa;iEACtBE,mBAAA,CAAwC;EAAnCF,KAAK,EAAC;AAAmB,GAAC,KAAG;;EAC7BA,KAAK,EAAC;AAAmB;;EAE3BA,KAAK,EAAC;AAAa;iEACtBE,mBAAA,CAAwC;EAAnCF,KAAK,EAAC;AAAmB,GAAC,KAAG;;EAC7BA,KAAK,EAAC;AAAmB;;EAI3BA,KAAK,EAAC;AAAa;iEACtBE,mBAAA,CAAuE;EAAlEF,KAAK,EAAC,mBAAmB;EAACC,KAA4B,EAA5B;IAAA;EAAA;GAA6B,OAAK;;EAC5DD,KAAK,EAAC;AAAmB;;;;;;;;EAW3BA,KAAK,EAAC;AAAa;iEACtBE,mBAAA,CAAwC;EAAnCF,KAAK,EAAC;AAAmB,GAAC,KAAG;;EAC7BA,KAAK,EAAC;AAAmB;;;;;;;;;;;;wCA9E9CG,mBAAA,CAwFM,OAxFNC,UAwFM,GAvFJC,YAAA,CAsFeC,uBAAA;IAtFAL,KAAK,EAAAM,eAAA,cAAeC,MAAA,CAAAC,YAAY;;sBAC7C,MAIY,CAJZJ,YAAA,CAIYK,oBAAA;MAJDV,KAAK,EAAC,mBAAmB;MAACW,MAAM,EAAC,MAAM;MAACC,GAAG,EAAC;;wBACrD,MAEM,CAFgCJ,MAAA,CAAAK,KAAK,CAACC,YAAY,IAAIN,MAAA,CAAAK,KAAK,CAACC,YAAY,CAACC,MAAM,I,cAArFZ,mBAAA,CAEM,OAFNa,UAEM,I,kBADJb,mBAAA,CAAkLc,SAAA,QAAAC,WAAA,CAAjIV,MAAA,CAAAK,KAAK,CAACC,YAAY,CAACC,MAAM,GAAtCI,CAAC,EAAEC,KAAK;6BAA5CjB,mBAAA,CAAkL;UAA5KH,KAAK,EAAC,eAAe;UAAkDqB,GAAG,EAAEF,CAAC;UAAGG,OAAK,EAAAC,MAAA,IAAEf,MAAA,CAAAgB,QAAQ,CAACJ,KAAK;;UAAIR,GAAG,EAAEa,EAAE;YAAA,IAAUA,EAAE,EAAEjB,MAAA,CAAAkB,kBAAkB,CAACN,KAAK,IAAIK,EAAE;UAAA;4BAAMN,CAAC,wBAAAQ,UAAA;;;8BAGhJnB,MAAA,CAAAK,KAAK,IAAIL,MAAA,CAAAK,KAAK,CAACC,YAAY,IAAIN,MAAA,CAAAK,KAAK,CAACC,YAAY,CAACC,MAAM,I,cAArFa,YAAA,CA+EUC,kBAAA;;MA/EDjB,GAAG,EAAC;;wBACX,MAEM,CAFNV,mBAAA,CAEM,OAFN4B,UAEM,EAAAC,gBAAA,CADFvB,MAAA,CAAAK,KAAK,CAACmB,KAAK,kBAEf9B,mBAAA,CAMM,OANN+B,UAMM,GALgCzB,MAAA,CAAA0B,MAAM,IAAI1B,MAAA,CAAA0B,MAAM,CAACC,EAAE,I,cAAvDhC,mBAAA,CAAyJ,QAAzJiC,UAAyJ,EAAhG,KAAG,GAAAL,gBAAA,CAAEvB,MAAA,CAAA0B,MAAM,CAACG,MAAM,2BAA2B7B,MAAA,CAAA0B,MAAM,CAACG,MAAM,iD,mCACnHnC,mBAAA,CAA0D,QAA1DoC,UAA0D,EAA5B,OAAK,GAAAP,gBAAA,CAAEvB,MAAA,CAAA0B,MAAM,CAACK,KAAK,kBACjDrC,mBAAA,CAA6D,QAA7DsC,UAA6D,EAA/B,OAAK,GAAAT,gBAAA,CAAEvB,MAAA,CAAAK,KAAK,CAAC4B,SAAS,kBACpDvC,mBAAA,CAAyD,QAAzDwC,UAAyD,EAA3B,OAAK,GAAAX,gBAAA,CAAEvB,MAAA,CAAAK,KAAK,CAAC0B,KAAK,kBAChDrC,mBAAA,CAAgE,QAAhEyC,WAAgE,EAAlC,OAAK,GAAAZ,gBAAA,CAAEvB,MAAA,CAAAK,KAAK,CAAC+B,SAAS,IAAE,KAAG,gB,GAE3D1C,mBAAA,CAmEM,OAnEN2C,WAmEM,I,kBAlEJ1C,mBAAA,CAiEMc,SAAA,QAAAC,WAAA,CAjE8CV,MAAA,CAAAK,KAAK,CAACC,YAAY,GAAlCgC,IAAI,EAAE1B,KAAK;6BAA/CjB,mBAAA,CAiEM;UAjEDH,KAAK,EAAC,gBAAgB;UAA8CqB,GAAG,EAAED,KAAK;;UAAGR,GAAG,EAAEa,EAAE;YAAA,IAAUA,EAAE,EAAEjB,MAAA,CAAAuC,eAAe,CAAC3B,KAAK,IAAIK,EAAE;UAAA;YACpIvB,mBAAA,CAEM,OAFN8C,WAEM,EAAAjB,gBAAA,CADFX,KAAK,QAAM,IAAE,GAAAW,gBAAA,CAAEvB,MAAA,CAAAyC,WAAW,CAACH,IAAI,mBAEnC5C,mBAAA,CA4DM,OA5DNgD,WA4DM,GA3DOJ,IAAI,CAACK,IAAI,qB,cAApBhD,mBAAA,CAEM,OAAAiD,WAAA,GADJ/C,YAAA,CAA8IgD,mBAAA;UAAnIC,QAAQ,EAAE,IAAI;UAAEH,IAAI,EAAC,UAAU;UAAEI,MAAI,EAAAhC,MAAA,IAAEf,MAAA,CAAAgD,kBAAkB,CAACpC,KAAK,EAAE0B,IAAI;UAAIW,IAAI,EAAE,EAAE;sBAAWjD,MAAA,CAAAkD,SAAS,CAACZ,IAAI,CAACK,IAAI,SAASL,IAAI,CAACX,EAAE;2CAAnC3B,MAAA,CAAAkD,SAAS,CAACZ,IAAI,CAACK,IAAI,SAASL,IAAI,CAACX,EAAE,IAAAZ;yHAEjIuB,IAAI,CAACK,IAAI,qB,cAApBhD,mBAAA,CAKM,OAAAwD,WAAA,I,kBAJJxD,mBAAA,CAGMc,SAAA,QAAAC,WAAA,CAHW4B,IAAI,CAACc,UAAU,EAApBzC,CAAC;+BAAbhB,mBAAA,CAGM;YAH6BkB,GAAG,EAAEF,CAAC;YAAElB,KAAqC,EAArC;cAAA;cAAA;YAAA;cACzCC,mBAAA,CAAsD,OAAtD2D,WAAsD,EAAA9B,gBAAA,CAAVZ,CAAC,IAAE,GAAC,iBAChDd,YAAA,CAAyIgD,mBAAA;YAA9HC,QAAQ,EAAE,IAAI;YAAGC,MAAI,EAAAhC,MAAA,IAAEf,MAAA,CAAAgD,kBAAkB,CAACpC,KAAK,EAAE0B,IAAI;YAAGgB,IAAI,EAAC,MAAM;wBAAUtD,MAAA,CAAAkD,SAAS,CAACZ,IAAI,CAACK,IAAI,SAASL,IAAI,CAACX,EAAE,SAAShB,CAAC;6CAA7CX,MAAA,CAAAkD,SAAS,CAACZ,IAAI,CAACK,IAAI,SAASL,IAAI,CAACX,EAAE,SAAShB,CAAC,IAAAI;;4CAGzHuB,IAAI,CAACiB,OAAO,I,cAA5B5D,mBAAA,CAOM,OAAA6D,WAAA,GANqBlB,IAAI,CAACK,IAAI,uB,cAAlCvB,YAAA,CAEoBqC,4BAAA;;sBAF4CzD,MAAA,CAAAkD,SAAS,CAACZ,IAAI,CAACK,IAAI,SAASL,IAAI,CAACX,EAAE;2CAAnC3B,MAAA,CAAAkD,SAAS,CAACZ,IAAI,CAACK,IAAI,SAASL,IAAI,CAACX,EAAE,IAAAZ,MAAA;UAAI2C,QAAM,EAAA3C,MAAA,IAAEf,MAAA,CAAAgD,kBAAkB,CAACpC,KAAK,EAAE0B,IAAI;;4BAC9F,MAAqC,E,kBAAlF3C,mBAAA,CAAoIc,SAAA,QAAAC,WAAA,CAA3EiD,IAAI,CAACC,KAAK,CAACtB,IAAI,CAACiB,OAAO,GAA5BM,CAAC;iCAArDzC,YAAA,CAAoI0C,sBAAA;cAAtHC,QAAQ,EAAE,IAAI;cAAGC,KAAK,EAAEH,CAAC,CAAChD,GAAG;cAAyCA,GAAG,EAAEgD,CAAC,CAAChD;;gCAAK,MAAS,C,kCAAPgD,CAAC,CAAChD,GAAG,IAAE,IAAE,GAAAU,gBAAA,CAAEsC,CAAC,CAACI,KAAK,iB;;;;;;;qHAEtHtE,mBAAA,CAEMc,SAAA;UAAAI,GAAA;QAAA,GAAAH,WAAA,CAFkBiD,IAAI,CAACC,KAAK,CAACtB,IAAI,CAACiB,OAAO,GAA5BM,CAAC;+BAApBlE,mBAAA,CAEM;YAF6CkB,GAAG,EAAEgD,CAAC,CAAChD;cACxDhB,YAAA,CAAoKqE,mBAAA;YAAzJH,QAAQ,EAAE,IAAI;YAAGL,QAAM,EAAA3C,MAAA,IAAEf,MAAA,CAAAgD,kBAAkB,CAACpC,KAAK,EAAE0B,IAAI;wBAAYtC,MAAA,CAAAkD,SAAS,CAACZ,IAAI,CAACK,IAAI,SAASL,IAAI,CAACX,EAAE;6CAAnC3B,MAAA,CAAAkD,SAAS,CAACZ,IAAI,CAACK,IAAI,SAASL,IAAI,CAACX,EAAE,IAAAZ,MAAA;YAAIiD,KAAK,EAAEH,CAAC,CAAChD;;8BAAK,MAAS,C,kCAAPgD,CAAC,CAAChD,GAAG,IAAE,IAAE,GAAAU,gBAAA,CAAEsC,CAAC,CAACI,KAAK,iB;;;;+EAG3JvE,mBAAA,CAyCM,OAzCNyE,WAyCM,GAxCJzE,mBAAA,CAMM,OANN0E,WAMM,GALJC,WAAwC,EACxC3E,mBAAA,CAGM,OAHN4E,WAGM,GAFwChC,IAAI,CAACiC,MAAM,I,cAAvDnD,YAAA,CAAiGoD,oBAAA;;UAAtF/E,KAA0B,EAA1B;YAAA;UAAA,CAA0B;UAAoB6D,IAAI,EAAC,MAAM;UAACX,IAAI,EAAC;;4BAAU,MAAC,C,iBAAD,GAAC,E;;6BACrFvB,YAAA,CAAoFoD,oBAAA;;UAAzE/E,KAA0B,EAA1B;YAAA;UAAA,CAA0B;UAAQ6D,IAAI,EAAC,MAAM;UAACX,IAAI,EAAC;;4BAAS,MAAC,C,iBAAD,GAAC,E;;iBAG5EjD,mBAAA,CAGM,OAHN+E,WAGM,GAFJC,WAAwC,EACxChF,mBAAA,CAAgH,OAAhHiF,WAAgH,EAAApD,gBAAA,CAAxBe,IAAI,CAACsC,MAAM,sB,GAErGlF,mBAAA,CAGM,OAHNmF,WAGM,GAFJC,WAAwC,EACxCpF,mBAAA,CAAmD,OAAnDqF,WAAmD,EAAAxD,gBAAA,CAAlBe,IAAI,CAACP,KAAK,iB,GAE7CrC,mBAAA,CAKM,OALNsF,WAKM,GAJJC,WAAwC,EACxCvF,mBAAA,CAEM,OAFNwF,WAEM,GADJrF,YAAA,CAA+EsF,kBAAA;UAArEpB,QAAQ,EAAE,IAAI;sBAAWzB,IAAI,CAAC8C,UAAU;2CAAf9C,IAAI,CAAC8C,UAAU,GAAArE,MAAA;UAAGsE,MAAM,EAAErF,MAAA,CAAAqF;sFAGjE3F,mBAAA,CAYM,OAZN4F,WAYM,GAXJC,WAAuE,EACvE7F,mBAAA,CASM,OATN8F,WASM,GAROlD,IAAI,CAACK,IAAI,qB,cAApBhD,mBAAA,CAIM,OAAA8F,WAAA,I,kBAHJ9F,mBAAA,CAEMc,SAAA,QAAAC,WAAA,CAFoB4B,IAAI,CAACoD,eAAe,CAACC,KAAK,UAAvCC,KAAK,EAAEjF,CAAC;+BAArBhB,mBAAA,CAEM;YAFwDkB,GAAG,EAAEF;UAAC,GAAE,MACjE,GAAAY,gBAAA,CAAEZ,CAAC,QAAM,IAAE,GAAAY,gBAAA,CAAEqE,KAAK;2DAGzBjG,mBAAA,CAEM,OAAAkG,WAAA,EAAAtE,gBAAA,CADFe,IAAI,CAACoD,eAAe,kB,KAI5BhG,mBAAA,CAKM,OALNoG,WAKM,GAJJC,WAAwC,EACxCrG,mBAAA,CAEM,OAFNsG,WAEM,EAAAzE,gBAAA,CADFe,IAAI,CAAC2D,mBAAmB,iB;;;;;;yDA/ETjG,MAAA,CAAAkG,YAAY,E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}