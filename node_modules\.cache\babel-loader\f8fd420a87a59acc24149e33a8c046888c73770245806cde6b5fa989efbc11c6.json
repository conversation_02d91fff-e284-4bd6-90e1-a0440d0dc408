{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport router from \"@/router\";\nexport function gotoCertificateTemplateEdit(id) {\n  router.push({\n    path: \"/learn/certificate/template/edit\",\n    query: {\n      id: id\n    }\n  });\n}\nexport function gotoCertificateTemplate() {\n  router.push({\n    path: \"/learn/certificate/template\"\n  });\n}", "map": {"version": 3, "names": ["router", "gotoCertificateTemplateEdit", "id", "push", "path", "query", "gotoCertificateTemplate"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/router/goto.js"], "sourcesContent": ["import router from \"@/router\";\n\nexport function gotoCertificateTemplateEdit(id) {\n  router.push({path: \"/learn/certificate/template/edit\", query: { id : id }})\n}\n\nexport function gotoCertificateTemplate() {\n  router.push({path: \"/learn/certificate/template\"})\n}"], "mappings": ";AAAA,OAAOA,MAAM,MAAM,UAAU;AAE7B,OAAO,SAASC,2BAA2BA,CAACC,EAAE,EAAE;EAC9CF,MAAM,CAACG,IAAI,CAAC;IAACC,IAAI,EAAE,kCAAkC;IAAEC,KAAK,EAAE;MAAEH,EAAE,EAAGA;IAAG;EAAC,CAAC,CAAC;AAC7E;AAEA,OAAO,SAASI,uBAAuBA,CAAA,EAAG;EACxCN,MAAM,CAACG,IAAI,CAAC;IAACC,IAAI,EAAE;EAA6B,CAAC,CAAC;AACpD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}