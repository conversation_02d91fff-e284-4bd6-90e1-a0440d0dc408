{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, with<PERSON><PERSON><PERSON> as _withKeys, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, resolveDirective as _resolveDirective, withDirectives as _withDirectives, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-22cecf1c\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"report\"\n};\nconst _hoisted_2 = {\n  class: \"header\"\n};\nconst _hoisted_3 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", {\n  style: {\n    \"vertical-align\": \"middle\"\n  }\n}, \"搜索\", -1 /* HOISTED */));\nconst _hoisted_4 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", {\n  style: {\n    \"vertical-align\": \"middle\"\n  }\n}, \"重置\", -1 /* HOISTED */));\nconst _hoisted_5 = {\n  class: \"report-main\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_cascader = _resolveComponent(\"el-cascader\");\n  const _component_Search = _resolveComponent(\"Search\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_page = _resolveComponent(\"page\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_form, {\n    inline: true,\n    model: $setup.params,\n    class: \"form-inline\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      label: \"课程名称\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"small\",\n        onKeydown: _withKeys($setup.search, [\"enter\"]),\n        class: \"search-input\",\n        modelValue: $setup.params.name,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.params.name = $event),\n        placeholder: \"请输入关键字\"\n      }, {\n        suffix: _withCtx(() => [_createElementVNode(\"i\", {\n          onClick: _cache[0] || (_cache[0] = (...args) => $setup.search && $setup.search(...args)),\n          class: \"el-input__icon el-icon-search search-btn\"\n        })]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onKeydown\", \"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"课程状态\",\n      class: \"select\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_select, {\n        size: \"small\",\n        modelValue: $setup.params.status,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.params.status = $event),\n        onChange: $setup.search\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_option, {\n          label: \"全部\",\n          value: \"\"\n        }), _createVNode(_component_el_option, {\n          label: \"未发布\",\n          value: \"unpublished\"\n        }), _createVNode(_component_el_option, {\n          label: \"已发布\",\n          value: \"published\"\n        })]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"课程分类\",\n      class: \"select\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_cascader, {\n        size: \"small\",\n        modelValue: $setup.selectCidList,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.selectCidList = $event),\n        options: $setup.categoryOptions,\n        props: {\n          checkStrictly: true\n        },\n        onChange: $setup.search,\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"options\", \"onChange\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        size: \"small\",\n        type: \"primary\",\n        onClick: _cache[4] || (_cache[4] = $event => $setup.search())\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, {\n          style: {\n            \"vertical-align\": \"middle\"\n          }\n        }, {\n          default: _withCtx(() => [_createVNode(_component_Search)]),\n          _: 1 /* STABLE */\n        }), _hoisted_3]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[5] || (_cache[5] = $event => $setup.resetParams())\n      }, {\n        default: _withCtx(() => [_hoisted_4]),\n        _: 1 /* STABLE */\n      })]),\n\n      _: 1 /* STABLE */\n    })]),\n\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\"])]), _createElementVNode(\"div\", _hoisted_5, [_withDirectives((_openBlock(), _createBlock(_component_el_table, {\n    data: $setup.dataList\n  }, {\n    default: _withCtx(() => [_ctx.member ? (_openBlock(), _createBlock(_component_el_table_column, {\n      key: 0,\n      label: \"名字\",\n      prop: \"member.name\"\n    })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_table_column, {\n      label: \"报名课程数\",\n      prop: \"name\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"报名次数\",\n      prop: \"name\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"平均报名次数\",\n      prop: \"name\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"已完成数量\",\n      prop: \"name\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"进行中数量\",\n      prop: \"name\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"已取消数量\",\n      prop: \"name\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"学习时长\",\n      prop: \"name\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"平均学习时长\",\n      prop: \"name\"\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\"])), [[_directive_loading, $setup.loading]]), _createVNode(_component_page, {\n    total: $setup.total,\n    \"size-change\": $setup.sizeChange,\n    \"current-change\": $setup.currentChange,\n    \"page-size\": $setup.params.size\n  }, null, 8 /* PROPS */, [\"total\", \"size-change\", \"current-change\", \"page-size\"])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementVNode", "style", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_createVNode", "_component_el_form", "inline", "model", "$setup", "params", "_component_el_form_item", "label", "_component_el_input", "size", "onKeydown", "_with<PERSON><PERSON><PERSON>", "search", "name", "$event", "placeholder", "suffix", "_withCtx", "onClick", "_cache", "args", "_component_el_select", "status", "onChange", "_component_el_option", "value", "_component_el_cascader", "selectCidList", "options", "categoryOptions", "props", "checkStrictly", "clearable", "_component_el_button", "type", "_component_el_icon", "_component_Search", "_hoisted_3", "resetParams", "_hoisted_4", "_hoisted_5", "_createBlock", "_component_el_table", "data", "dataList", "_ctx", "member", "_component_el_table_column", "prop", "loading", "_component_page", "total", "sizeChange", "currentChange"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/learn/report/memberstudy/index.vue"], "sourcesContent": ["<template>\n  <div class=\"report\">\n    <div class=\"header\">\n      <el-form :inline=\"true\" :model=\"params\" class=\"form-inline\">\n        <el-form-item label=\"课程名称\">\n          <el-input size=\"small\" @keydown.enter=\"search\" class=\"search-input\" v-model=\"params.name\" placeholder=\"请输入关键字\">\n            <template #suffix>\n              <i @click=\"search\" class=\"el-input__icon el-icon-search search-btn\"></i>\n            </template>\n          </el-input>\n        </el-form-item>\n        <el-form-item label=\"课程状态\" class=\"select\">\n          <el-select size=\"small\" v-model=\"params.status\" @change=\"search\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"未发布\" value=\"unpublished\"></el-option>\n            <el-option label=\"已发布\" value=\"published\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"课程分类\" class=\"select\">\n          <el-cascader size=\"small\" v-model=\"selectCidList\" :options=\"categoryOptions\" :props=\"{ checkStrictly: true }\" @change=\"search\" clearable></el-cascader>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" type=\"primary\" @click=\"search()\">\n            <el-icon style=\"vertical-align: middle\">\n              <Search />\n            </el-icon>\n            <span style=\"vertical-align: middle\">搜索</span>\n          </el-button>\n          <el-button size=\"small\" @click=\"resetParams()\">\n            <span style=\"vertical-align: middle\">重置</span>\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"report-main\">\n      <el-table :data=\"dataList\" v-loading=\"loading\">\n        <el-table-column label=\"名字\" prop=\"member.name\" v-if=\"member\"></el-table-column>\n        <el-table-column label=\"报名课程数\" prop=\"name\"></el-table-column>\n        <el-table-column label=\"报名次数\" prop=\"name\"></el-table-column>\n        <el-table-column label=\"平均报名次数\" prop=\"name\"></el-table-column>\n        <el-table-column label=\"已完成数量\" prop=\"name\"></el-table-column>\n        <el-table-column label=\"进行中数量\" prop=\"name\"></el-table-column>\n        <el-table-column label=\"已取消数量\" prop=\"name\"></el-table-column>\n        <el-table-column label=\"学习时长\" prop=\"name\"></el-table-column>\n        <el-table-column label=\"平均学习时长\" prop=\"name\"></el-table-column>\n      </el-table>\n      <page :total=\"total\" :size-change=\"sizeChange\" :current-change=\"currentChange\" :page-size=\"params.size\"/>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {ref} from \"vue\"\nimport Page from \"@/components/Page\";\nimport {Search} from \"@element-plus/icons-vue\";\nimport {findCategoryList, toTree} from \"@/api/learn/category\";\nimport {getMemberStudyReport} from \"@/api/learn/lesson\";\nexport default {\n  name: \"LearnReportIndex\",\n  components: {Search, Page},\n  setup() {\n    const loading = ref(true)\n    const total = ref(0)\n    const dataList = ref([])\n    const c = {\n      current: 1,\n      size: 20\n    }\n    const params = ref(c)\n    const selectCidList = ref([])\n    const categoryOptions = ref([])\n    // 加载分类\n    const loadCategory = () => {\n      findCategoryList(0, true, (res) => {if (res) { categoryOptions.value = toTree(res);}})\n    }\n    loadCategory();\n\n    const loadList = () => {\n      loading.value = true\n      getMemberStudyReport(params.value, res => {\n        dataList.value = res.list\n        total.value = res.total\n        loading.value = false\n      }).catch(() => {\n        loading.value = false\n      })\n    }\n    loadList()\n    const currentChange = (c) => {\n      params.value.current = c;\n      loadList();\n    }\n    const sizeChange = (s) => {\n      params.value.size = s;\n      loadList();\n    }\n    const search = () => {\n      if (selectCidList.value && selectCidList.value.length > 0) {\n        params.value.cid = selectCidList.value[selectCidList.value.length - 1];\n      }\n      params.value.current = 1\n      loadList()\n    }\n    const resetParams = () => {\n      params.value = c\n    }\n    return {\n      loading,\n      dataList,\n      selectCidList,\n      categoryOptions,\n      params,\n      total,\n      currentChange,\n      sizeChange,\n      search,\n      resetParams\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.report {\n  margin: 20px;\n  font-size: 12px;\n  .report-main {\n    ::v-deep .el-table {\n      font-size: 12px;\n      .el-table__empty-block {\n        line-height: 400px;\n        .el-table__empty-text {\n          line-height: 400px;\n        }\n      }\n      th, td {\n        padding: 6px 0;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAQ;;EACZA,KAAK,EAAC;AAAQ;gEAwBXC,mBAAA,CAA8C;EAAxCC,KAA8B,EAA9B;IAAA;EAAA;AAA8B,GAAC,IAAE;gEAGvCD,mBAAA,CAA8C;EAAxCC,KAA8B,EAA9B;IAAA;EAAA;AAA8B,GAAC,IAAE;;EAK1CF,KAAK,EAAC;AAAa;;;;;;;;;;;;;;;uBAjC1BG,mBAAA,CA+CM,OA/CNC,UA+CM,GA9CJH,mBAAA,CA+BM,OA/BNI,UA+BM,GA9BJC,YAAA,CA6BUC,kBAAA;IA7BAC,MAAM,EAAE,IAAI;IAAGC,KAAK,EAAEC,MAAA,CAAAC,MAAM;IAAEX,KAAK,EAAC;;sBAC5C,MAMe,CANfM,YAAA,CAMeM,uBAAA;MANDC,KAAK,EAAC;IAAM;wBACxB,MAIW,CAJXP,YAAA,CAIWQ,mBAAA;QAJDC,IAAI,EAAC,OAAO;QAAEC,SAAO,EAAAC,SAAA,CAAQP,MAAA,CAAAQ,MAAM;QAAElB,KAAK,EAAC,cAAc;oBAAUU,MAAA,CAAAC,MAAM,CAACQ,IAAI;mEAAXT,MAAA,CAAAC,MAAM,CAACQ,IAAI,GAAAC,MAAA;QAAEC,WAAW,EAAC;;QACzFC,MAAM,EAAAC,QAAA,CACf,MAAwE,CAAxEtB,mBAAA,CAAwE;UAApEuB,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,MAAA,CAAAQ,MAAA,IAAAR,MAAA,CAAAQ,MAAA,IAAAQ,IAAA,CAAM;UAAE1B,KAAK,EAAC;;;;;QAI/BM,YAAA,CAMeM,uBAAA;MANDC,KAAK,EAAC,MAAM;MAACb,KAAK,EAAC;;wBAC/B,MAIY,CAJZM,YAAA,CAIYqB,oBAAA;QAJDZ,IAAI,EAAC,OAAO;oBAAUL,MAAA,CAAAC,MAAM,CAACiB,MAAM;mEAAblB,MAAA,CAAAC,MAAM,CAACiB,MAAM,GAAAR,MAAA;QAAGS,QAAM,EAAEnB,MAAA,CAAAQ;;0BACvD,MAA2C,CAA3CZ,YAAA,CAA2CwB,oBAAA;UAAhCjB,KAAK,EAAC,IAAI;UAACkB,KAAK,EAAC;YAC5BzB,YAAA,CAAuDwB,oBAAA;UAA5CjB,KAAK,EAAC,KAAK;UAACkB,KAAK,EAAC;YAC7BzB,YAAA,CAAqDwB,oBAAA;UAA1CjB,KAAK,EAAC,KAAK;UAACkB,KAAK,EAAC;;;;;QAGjCzB,YAAA,CAEeM,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACb,KAAK,EAAC;;wBAC/B,MAAuJ,CAAvJM,YAAA,CAAuJ0B,sBAAA;QAA1IjB,IAAI,EAAC,OAAO;oBAAUL,MAAA,CAAAuB,aAAa;mEAAbvB,MAAA,CAAAuB,aAAa,GAAAb,MAAA;QAAGc,OAAO,EAAExB,MAAA,CAAAyB,eAAe;QAAGC,KAAK,EAAE;UAAAC,aAAA;QAAA,CAAuB;QAAGR,QAAM,EAAEnB,MAAA,CAAAQ,MAAM;QAAEoB,SAAS,EAAT;;;QAEjIhC,YAAA,CAUeM,uBAAA;wBATb,MAKY,CALZN,YAAA,CAKYiC,oBAAA;QALDxB,IAAI,EAAC,OAAO;QAACyB,IAAI,EAAC,SAAS;QAAEhB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAL,MAAA,IAAEV,MAAA,CAAAQ,MAAM;;0BACnD,MAEU,CAFVZ,YAAA,CAEUmC,kBAAA;UAFDvC,KAA8B,EAA9B;YAAA;UAAA;QAA8B;4BACrC,MAAU,CAAVI,YAAA,CAAUoC,iBAAA,E;;YAEZC,UAA8C,C;;UAEhDrC,YAAA,CAEYiC,oBAAA;QAFDxB,IAAI,EAAC,OAAO;QAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAL,MAAA,IAAEV,MAAA,CAAAkC,WAAW;;0BACzC,MAA8C,CAA9CC,UAA8C,C;;;;;;;;kCAKtD5C,mBAAA,CAaM,OAbN6C,UAaM,G,+BAZJC,YAAA,CAUWC,mBAAA;IAVAC,IAAI,EAAEvC,MAAA,CAAAwC;EAAQ;sBACvB,MAA+E,CAA1BC,IAAA,CAAAC,MAAM,I,cAA3DL,YAAA,CAA+EM,0BAAA;;MAA9DxC,KAAK,EAAC,IAAI;MAACyC,IAAI,EAAC;6CACjChD,YAAA,CAA6D+C,0BAAA;MAA5CxC,KAAK,EAAC,OAAO;MAACyC,IAAI,EAAC;QACpChD,YAAA,CAA4D+C,0BAAA;MAA3CxC,KAAK,EAAC,MAAM;MAACyC,IAAI,EAAC;QACnChD,YAAA,CAA8D+C,0BAAA;MAA7CxC,KAAK,EAAC,QAAQ;MAACyC,IAAI,EAAC;QACrChD,YAAA,CAA6D+C,0BAAA;MAA5CxC,KAAK,EAAC,OAAO;MAACyC,IAAI,EAAC;QACpChD,YAAA,CAA6D+C,0BAAA;MAA5CxC,KAAK,EAAC,OAAO;MAACyC,IAAI,EAAC;QACpChD,YAAA,CAA6D+C,0BAAA;MAA5CxC,KAAK,EAAC,OAAO;MAACyC,IAAI,EAAC;QACpChD,YAAA,CAA4D+C,0BAAA;MAA3CxC,KAAK,EAAC,MAAM;MAACyC,IAAI,EAAC;QACnChD,YAAA,CAA8D+C,0BAAA;MAA7CxC,KAAK,EAAC,QAAQ;MAACyC,IAAI,EAAC;;;sDATD5C,MAAA,CAAA6C,OAAO,E,GAW7CjD,YAAA,CAAyGkD,eAAA;IAAlGC,KAAK,EAAE/C,MAAA,CAAA+C,KAAK;IAAG,aAAW,EAAE/C,MAAA,CAAAgD,UAAU;IAAG,gBAAc,EAAEhD,MAAA,CAAAiD,aAAa;IAAG,WAAS,EAAEjD,MAAA,CAAAC,MAAM,CAACI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}