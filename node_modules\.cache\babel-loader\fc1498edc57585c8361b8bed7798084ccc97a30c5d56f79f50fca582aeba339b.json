{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport router from \"@/router\";\nimport WangEditor from \"@/components/WangEditor/index.vue\";\nimport Upload from \"@/components/Uplaod\";\nimport { ref } from \"vue\";\nimport { useRoute } from \"vue-router\";\nimport { VueDraggableNext } from \"vue-draggable-next\";\nimport { success, confirm, error } from \"@/util/tipsUtils\";\nimport { findCategoryList, toTree, getAllParent } from \"@/api/learn/category\";\nimport { saveBaseInfo, updateBaseInfo, getBaseInfo, publishLesson, unPublishLesson, saveLessonChapter, updateLessonChapter, deleteLessonChapter, getLessonChapterList, updateSortOrder, saveLessonChapterSection, updateLessonChapterSection, deleteLessonChapterSection, saveHomework, updateHomework, getHomework } from \"@/api/learn/lesson\";\nimport CertificateTemplateList from \"@/views/certificate/template/index.vue\";\nimport CertificatePreview from \"@/views/certificate/preview/index.vue\";\nexport default {\n  name: \"LearnLessonEdit\",\n  components: {\n    CertificatePreview,\n    CertificateTemplateList,\n    Upload,\n    WangEditor,\n    draggable: VueDraggableNext\n  },\n  data() {\n    return {\n      expandedFlag: false,\n      expandedRows: [] // 用于存储已展开的行\n    };\n  },\n\n  methods: {\n    // 自定义切换行展开\n    toggleRowExpansion(row) {\n      this.expandedFlag = true;\n      const index = this.expandedRows.indexOf(row);\n      if (index === -1) {\n        this.expandedRows.push(row);\n        this.$refs.table.toggleRowExpansion(row, true);\n      } else {\n        this.expandedRows.splice(index, 1);\n        this.$refs.table.toggleRowExpansion(row, false);\n      }\n      this.expandedFlag = false;\n    },\n    handleExpandChange(row, expanded) {\n      console.log(\"222222: \", this.expandedFlag, expanded);\n      if (!this.expandedFlag) {\n        if (expanded) {\n          const index = this.expandedRows.indexOf(row);\n          if (index === -1) {\n            this.expandedRows.push(row);\n          }\n        } else {\n          console.log(\"\");\n          const index = this.expandedRows.indexOf(row);\n          if (index !== -1) {\n            this.expandedRows.splice(index, 1);\n          }\n        }\n      }\n      this.expandedFlag = false;\n    }\n  },\n  setup() {\n    const loadWangEditorFlag = ref(false);\n    const route = useRoute();\n    let isUpdate = !!route.query.id;\n    let showStep = ref(\"\");\n    const steps = [{\n      key: \"base\",\n      name: \"基础信息\"\n    }, {\n      key: \"content\",\n      name: \"章节内容\"\n    }, {\n      key: \"homework\",\n      name: \"课后作业\"\n    }, {\n      key: \"certificate\",\n      name: \"关联证书\"\n    }, {\n      key: \"publish\",\n      name: \"发布状态\"\n    }];\n    const stepActive = ref(0);\n    const loadStepActiveArray = () => {\n      const stepActiveArray = [];\n      for (let i = 0; i < steps.length; i++) {\n        const step = steps[i];\n        stepActiveArray.push(step.key);\n        if (step.key === showStep.value) {\n          stepActive.value = i;\n          break;\n        }\n      }\n      if (isUpdate) {\n        stepActive.value = steps.length;\n      }\n      return stepActiveArray;\n    };\n    // 基本信息\n    const uploadData = ref({\n      url: process.env.VUE_APP_BASE_API + \"/oss/learn/lesson/image\",\n      files: []\n    });\n    function getCurrentHour() {\n      const now = new Date();\n      const year = now.getFullYear();\n      const month = String(now.getMonth() + 1).padStart(2, '0');\n      const date = String(now.getDate()).padStart(2, '0');\n      const hour = String(now.getHours()).padStart(2, '0');\n      return `${year}-${month}-${date} ${hour}:00:00`;\n    }\n    const infiniteDate = \"2037-12-31 23:59:59\";\n    const categoryOptions = ref([]);\n    const selectCidList = ref([]);\n    const lesson = ref({\n      id: \"\",\n      name: \"\",\n      startTime: getCurrentHour(),\n      endTime: infiniteDate,\n      price: 0,\n      originalPrice: 0,\n      image: \"\",\n      cidList: [],\n      phrase: \"\",\n      introduction: \"\",\n      timeType: \"infinite\"\n    });\n    const lessonRules = {\n      name: [{\n        required: true,\n        message: \"请输入标题\",\n        trigger: \"blur\"\n      }],\n      startTime: [{\n        required: true,\n        message: \"请选择时间\",\n        trigger: \"change\"\n      }],\n      endTime: [{\n        required: true,\n        message: \"请选择时间\",\n        trigger: \"change\"\n      }],\n      phrase: [{\n        required: true,\n        message: \"请输入简介\",\n        trigger: \"blur\"\n      }],\n      price: [{\n        required: true,\n        message: \"请输入价格\",\n        trigger: \"blur\"\n      }],\n      cidList: [{\n        required: true,\n        message: \"请选择分类\",\n        trigger: \"change\"\n      }],\n      introduction: [{\n        required: true,\n        message: \"请输入描述\",\n        trigger: \"blur\"\n      }],\n      image: [{\n        required: true,\n        message: \"请选择海报\",\n        trigger: \"change\"\n      }]\n    };\n    const baseLoading = ref(true);\n    // 加载基本信息\n    const loadBaseInfo = () => {\n      let id = route.query.id;\n      if (!id) {\n        loadWangEditorFlag.value = true;\n        return;\n      }\n      getBaseInfo(id, function (res) {\n        baseLoading.value = false;\n        lesson.value = res;\n        if (res && res.endTime) {\n          if (res.endTime === new Date(infiniteDate)) {\n            lesson.value.timeType = 'infinite';\n          } else {\n            lesson.value.timeType = 'customize';\n          }\n        }\n        selectCidList.value = getAllParent(categoryOptions.value, res.cidList);\n        lesson.value.cidList = [];\n        uploadData.value.files = [{\n          name: \"海报\",\n          url: lesson.value.image\n        }];\n        for (const valElement of selectCidList.value) {\n          lesson.value.cidList.push(valElement[valElement.length - 1]);\n        }\n        loadWangEditorFlag.value = true;\n\n        // 获取证书\n        certificateTemplate.value = lesson.value.certificate;\n      });\n    };\n    // 获取分类\n    const loadCategory = () => {\n      findCategoryList(0, true, res => {\n        if (res && res.length) {\n          categoryOptions.value = toTree(res);\n          loadBaseInfo();\n        }\n      });\n    };\n    // 选择分类\n    const changeCategory = val => {\n      lesson.value.cidList = [];\n      for (const valElement of val) {\n        lesson.value.cidList.push(valElement[valElement.length - 1]);\n      }\n    };\n    // 选择时间\n    const changeStartTime = val => {\n      lesson.value.startTime = val;\n    };\n    // 选择时间\n    const changeEndTime = val => {\n      lesson.value.endTime = val;\n    };\n    // 上传图片成功\n    const onUploadImageSuccess = res => {\n      lesson.value.image = res.data;\n    };\n    // 删除图片\n    const onUploadImageRemove = () => {\n      lesson.value.image = \"\";\n      uploadData.value.files = [];\n    };\n    // 提交基本信息\n    const lessonRef = ref(null);\n    const submitBaseInfo = () => {\n      lessonRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (isUpdate) {\n          if (typeof lesson.value.startTime == \"string\") {\n            lesson.value.startTime = new Date(lesson.value.startTime);\n          }\n          if (typeof lesson.value.endTime == \"string\") {\n            lesson.value.endTime = new Date(lesson.value.endTime);\n          }\n          updateBaseInfo(lesson.value, function (res) {\n            if (res && res.id) {\n              lesson.value = res;\n              success(\"编辑成功\");\n              showStep.value = \"content\";\n              loadStepActiveArray();\n              let path = route.fullPath;\n              router.push({\n                path,\n                query: {\n                  id: lesson.value.id,\n                  step: \"content\"\n                }\n              });\n            }\n          });\n        } else {\n          if (lesson.value.timeType === 'infinite') {\n            lesson.value.startTime = new Date(getCurrentHour());\n            lesson.value.endTime = new Date(infiniteDate);\n          }\n          saveBaseInfo(lesson.value, function (res) {\n            if (res && res.id) {\n              lesson.value = res;\n              success(\"新增成功\");\n              showStep.value = \"content\";\n              loadStepActiveArray();\n              let path = route.fullPath;\n              router.push({\n                path,\n                query: {\n                  id: lesson.value.id,\n                  step: \"content\"\n                }\n              });\n            }\n          });\n        }\n      });\n    };\n\n    // 内容\n    const contentList = ref([]);\n    const showChapterDialog = ref(false);\n    const lessonChapter = ref({\n      id: \"\",\n      lessonId: \"\",\n      title: \"\",\n      phrase: \"\"\n    });\n    const lessonChapterRules = {\n      title: [{\n        required: true,\n        message: \"请输入标题\",\n        trigger: \"blur\"\n      }]\n    };\n    const showChapterSectionDialog = ref(false);\n    const lessonChapterSection = ref({\n      id: \"\",\n      lessonChapterId: \"\",\n      type: \"link\",\n      title: \"\",\n      url: \"\",\n      phrase: \"\",\n      totalTime: \"\"\n    });\n    const lessonChapterSectionRules = ref({\n      title: [{\n        required: true,\n        message: \"请输入标题\",\n        trigger: \"blur\"\n      }],\n      url: [{\n        required: true,\n        message: \"请输入视频地址\",\n        trigger: \"blur\"\n      }],\n      type: [{\n        required: true,\n        message: \"请选择类型\",\n        trigger: \"change\"\n      }],\n      totalTime: [{\n        required: true,\n        message: \"请输入时长\",\n        trigger: \"blur\"\n      }]\n    });\n    const homework = ref({\n      lessonId: \"\",\n      content: \"\",\n      attachment: \"\"\n    });\n    const uploadHomeworkData = ref({\n      url: process.env.VUE_APP_BASE_API + \"/oss/learn/homework/file\",\n      files: []\n    });\n    const loadContent = () => {\n      let id = route.query.id;\n      if (!id) {\n        return;\n      }\n      getLessonChapterList({\n        lessonId: id\n      }, res => {\n        if (res && res.list) {\n          contentList.value = res.list;\n        }\n      });\n      getHomework({\n        lessonId: route.query.id\n      }, res => {\n        homework.value = res;\n        if (homework.value.url) {\n          uploadHomeworkData.value.files = [{\n            name: \"作业附件\",\n            url: homework.value.url\n          }];\n        }\n      });\n    };\n    const showChapter = chapter => {\n      showChapterDialog.value = true;\n      if (chapter && chapter.id) {\n        lessonChapter.value = chapter;\n      } else {\n        lessonChapter.value = {\n          lessonId: lesson.value.id,\n          id: \"\",\n          title: \"\",\n          phrase: \"\"\n        };\n      }\n    };\n    const hideChapter = () => {\n      showChapterDialog.value = false;\n      lessonChapter.value = {\n        id: \"\",\n        lessonId: \"\",\n        title: \"\",\n        phrase: \"\"\n      };\n    };\n    const uploadVideoData = ref({\n      url: process.env.VUE_APP_BASE_API + \"/oss/learn/lesson/video\",\n      files: []\n    });\n    let videoLoaded = false;\n    const showChapterSection = (lessonChapterId, chapterSection) => {\n      showChapterSectionDialog.value = true;\n      if (chapterSection && chapterSection.id) {\n        lessonChapterSection.value = chapterSection;\n        uploadVideoData.value.files = [{\n          name: lessonChapterSection.value.title + \".mp4\",\n          url: lessonChapterSection.value.url\n        }];\n      } else {\n        videoLoaded = false;\n        lessonChapterSection.value = {\n          lessonChapterId: lessonChapterId,\n          id: \"\",\n          title: \"\",\n          url: \"\",\n          phrase: \"\",\n          type: \"link\",\n          totalTime: \"\"\n        };\n      }\n    };\n    const hideChapterSection = () => {\n      videoLoaded = false;\n      showChapterSectionDialog.value = false;\n      lessonChapterSection.value = {\n        id: \"\",\n        lessonChapterId: \"\",\n        title: \"\",\n        url: \"\",\n        phrase: \"\",\n        type: \"link\",\n        totalTime: \"\"\n      };\n    };\n    const deleteChapter = id => {\n      confirm(\"确认删除吗？\", \"提示\", () => {\n        deleteLessonChapter({\n          id: id\n        }, () => {\n          success(\"删除成功\");\n          loadContent();\n        });\n      });\n    };\n    const deleteChapterSection = id => {\n      confirm(\"确认删除吗？\", \"提示\", () => {\n        deleteLessonChapterSection({\n          id: id\n        }, () => {\n          success(\"删除成功\");\n          loadContent();\n        });\n      });\n    };\n    const lessonChapterRef = ref(null);\n    const submitChapter = () => {\n      lessonChapterRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (lessonChapter.value.id) {\n          updateLessonChapter(lessonChapter.value, function () {\n            success(\"编辑成功\");\n            hideChapter();\n            loadContent();\n          });\n        } else {\n          saveLessonChapter(lessonChapter.value, function () {\n            success(\"新增成功\");\n            hideChapter();\n            loadContent();\n            stepActive.value = steps.length;\n            isUpdate = true;\n          });\n        }\n      });\n    };\n    const linkVideo = ref(null);\n    const urlBlur = () => {\n      if (lessonChapterSection.value.type === \"link\") {\n        linkVideo.value.addEventListener(\"loadedmetadata\", () => {\n          //时长为秒，小数，182.36\n          lessonChapterSection.value.totalTime = linkVideo.value.duration;\n          videoLoaded = true;\n        });\n      }\n    };\n    const lessonChapterSectionRef = ref(null);\n    const submitChapterSection = () => {\n      if (lessonChapterSection.value.type === \"link\") {\n        if (!lessonChapterSection.value.id && !videoLoaded) {\n          error(\"正在计算视频时长，请稍后再试\");\n        }\n      }\n      lessonChapterSectionRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (lessonChapterSection.value.id) {\n          updateLessonChapterSection(lessonChapterSection.value, function () {\n            success(\"编辑成功\");\n            hideChapterSection();\n            loadContent();\n          });\n        } else {\n          saveLessonChapterSection(lessonChapterSection.value, function () {\n            success(\"新增成功\");\n            hideChapterSection();\n            loadContent();\n          });\n        }\n      });\n    };\n    // 上传视频成功\n    const onUploadVideoSuccess = res => {\n      lessonChapterSection.value.url = res.data;\n      uploadVideoData.value.files = [{\n        name: lessonChapterSection.value.title + \".mp4\",\n        url: res.data\n      }];\n    };\n    // 删除视频\n    const onUploadVideoRemove = () => {\n      lessonChapterSection.value.url = \"\";\n      uploadVideoData.value.files = [];\n    };\n    const onBeforeUploadVideo = file => {\n      let videoUrl = URL.createObjectURL(file);\n      let audioElement = new Audio(videoUrl);\n      audioElement.addEventListener(\"loadedmetadata\", () => {\n        //时长为秒，小数，182.36\n        lessonChapterSection.value.totalTime = audioElement.duration;\n      });\n    };\n    // 拖拽事件\n    const onDraggableChange = () => {\n      console.log(contentList.value);\n      const chapterList = [];\n      for (const content of contentList.value) {\n        const subData = [];\n        if (content.chapterSectionList && content.chapterSectionList.length) {\n          for (const sub of content.chapterSectionList) {\n            subData.push({\n              id: sub.id,\n              list: []\n            });\n          }\n        }\n        chapterList.push({\n          id: content.id,\n          list: subData\n        });\n      }\n      const params = {\n        id: lesson.value.id,\n        list: chapterList\n      };\n      updateSortOrder(params, () => {\n        success(\"排序更新成功\");\n      });\n      console.log(params);\n    };\n    // 作业\n    const homeworkRef = ref(null);\n    const homeworkRules = ref({\n      content: [{\n        required: true,\n        message: \"请输入作业内容\",\n        trigger: \"blur\"\n      }]\n    });\n    // 上传附件成功\n    const onUploadHomeworkAttachmentSuccess = res => {\n      homework.value.attachment = res.data;\n    };\n    // 删除附件成功\n    const onUploadHomeworkAttachmentRemove = () => {\n      homework.value.attachment = \"\";\n      uploadHomeworkData.value.files = [];\n    };\n    const submitHomework = () => {\n      homework.value.lessonId = route.query.id || lesson.value.id;\n      homeworkRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (homework.value.id) {\n          updateHomework(homework.value, () => {\n            success(\"编辑成功\");\n            showStep.value = \"publish\";\n            let path = route.fullPath;\n            router.push({\n              path,\n              query: {\n                id: lesson.value.id,\n                step: \"publish\"\n              }\n            });\n          });\n        } else {\n          saveHomework(homework.value, res => {\n            homework.value = res;\n            success(\"编辑成功\");\n            showStep.value = \"publish\";\n            let path = route.fullPath;\n            router.push({\n              path,\n              query: {\n                id: lesson.value.id,\n                step: \"publish\"\n              }\n            });\n          });\n        }\n      });\n    };\n    // 发布页面\n    const statusMap = {\n      unpublished: \"草稿箱\",\n      published: \"已发布\",\n      deleted: \"已删除\"\n    };\n    const publish = () => {\n      publishLesson({\n        id: lesson.value.id\n      }, () => {\n        success(\"发布成功\");\n        lesson.value.status = \"published\";\n      });\n    };\n    const unPublish = () => {\n      unPublishLesson({\n        id: lesson.value.id\n      }, () => {\n        success(\"取消发布成功\");\n        lesson.value.status = \"unpublished\";\n      });\n    };\n    // 步骤条\n    const init = () => {\n      // 初始化加载\n      if (route.query.step) {\n        showStep.value = route.query.step;\n      } else {\n        showStep.value = \"base\";\n      }\n      lesson.value.id = route.query.id || \"\";\n      loadCategory();\n      loadContent();\n    };\n    init();\n    // 步骤条点击切换\n    const stepClick = key => {\n      if (!isUpdate && loadStepActiveArray().indexOf(key) < 0) {\n        return;\n      }\n      showStep.value = key;\n      let path = route.fullPath;\n      router.push({\n        path,\n        query: {\n          id: lesson.value.id,\n          step: key\n        }\n      });\n    };\n    loadStepActiveArray();\n\n    // 证书\n    const certificateTemplate = ref({});\n    const showCertificateTemplateFlag = ref(false);\n    const showCertificateTemplate = () => {\n      showCertificateTemplateFlag.value = true;\n    };\n    const hideCertificateTemplate = () => {\n      showCertificateTemplateFlag.value = false;\n    };\n    const selectCertificateTemplate = val => {\n      console.log(\"val\", val);\n      if (val.length > 1) {\n        error(\"只能选择一个证书\");\n        return;\n      }\n      if (val.length > 0) {\n        certificateTemplate.value = val[0];\n        if (certificateTemplate.value) {\n          lesson.value.certificateId = certificateTemplate.value.id;\n        }\n      }\n      console.log(\"lesson.value.certificateId\", lesson.value.certificateId);\n      hideCertificateTemplate();\n    };\n    const showPreviewViewFlag = ref(false);\n    const showPreview = () => {\n      if (!certificateTemplate.value.id) {\n        error(\"请先选择证书\");\n        return;\n      }\n      showPreviewViewFlag.value = true;\n    };\n    const hidePreview = () => {\n      showPreviewViewFlag.value = false;\n    };\n    const submitCertificateTemplate = () => {\n      if (!certificateTemplate.value.id) {\n        error(\"请先选择证书\");\n        return;\n      }\n      if (typeof lesson.value.startTime == \"string\") {\n        lesson.value.startTime = new Date(lesson.value.startTime);\n      }\n      if (typeof lesson.value.endTime == \"string\") {\n        lesson.value.endTime = new Date(lesson.value.endTime);\n      }\n      lesson.value.certificateId = certificateTemplate.value.id;\n      updateBaseInfo(lesson.value, function (res) {\n        if (res && res.id) {\n          lesson.value = res;\n          success(\"关联证书成功\");\n        }\n      });\n    };\n    // 返回参数与方法\n    return {\n      // 证书\n      baseLoading,\n      hidePreview,\n      showPreview,\n      showPreviewViewFlag,\n      certificateTemplate,\n      showCertificateTemplateFlag,\n      showCertificateTemplate,\n      hideCertificateTemplate,\n      selectCertificateTemplate,\n      submitCertificateTemplate,\n      // 基本信息\n      uploadData,\n      categoryOptions,\n      lesson,\n      selectCidList,\n      lessonRules,\n      lessonRef,\n      changeCategory,\n      changeStartTime,\n      changeEndTime,\n      onUploadImageSuccess,\n      onUploadImageRemove,\n      submitBaseInfo,\n      // 内容列表\n      contentList,\n      showChapterDialog,\n      lessonChapter,\n      lessonChapterRules,\n      showChapterSectionDialog,\n      lessonChapterSection,\n      lessonChapterSectionRules,\n      lessonChapterRef,\n      lessonChapterSectionRef,\n      showChapter,\n      hideChapter,\n      showChapterSection,\n      hideChapterSection,\n      deleteChapter,\n      deleteChapterSection,\n      submitChapter,\n      submitChapterSection,\n      uploadVideoData,\n      linkVideo,\n      urlBlur,\n      onBeforeUploadVideo,\n      onUploadVideoSuccess,\n      onUploadVideoRemove,\n      onDraggableChange,\n      // 作业\n      homework,\n      homeworkRef,\n      homeworkRules,\n      uploadHomeworkData,\n      submitHomework,\n      onUploadHomeworkAttachmentSuccess,\n      onUploadHomeworkAttachmentRemove,\n      // 发布页面\n      statusMap,\n      publish,\n      unPublish,\n      // 步骤条\n      steps,\n      stepActive,\n      showStep,\n      stepClick,\n      loadWangEditorFlag\n    };\n  }\n};", "map": {"version": 3, "names": ["router", "WangEditor", "Upload", "ref", "useRoute", "VueDraggableNext", "success", "confirm", "error", "findCategoryList", "toTree", "getAllParent", "saveBaseInfo", "updateBaseInfo", "getBaseInfo", "<PERSON><PERSON><PERSON><PERSON>", "unPublish<PERSON><PERSON><PERSON>", "saveLessonChapter", "updateLessonChapter", "delete<PERSON>esson<PERSON>hapter", "getLessonChapterList", "updateSortOrder", "saveLessonChapterSection", "updateLessonChapterSection", "deleteLessonChapterSection", "saveHomework", "updateHomework", "getHomework", "CertificateTemplateList", "CertificatePreview", "name", "components", "draggable", "data", "expandedFlag", "expandedRows", "methods", "toggleRowExpansion", "row", "index", "indexOf", "push", "$refs", "table", "splice", "handleExpandChange", "expanded", "console", "log", "setup", "loadWangEditorFlag", "route", "isUpdate", "query", "id", "showStep", "steps", "key", "stepActive", "loadStepActiveArray", "stepActiveArray", "i", "length", "step", "value", "uploadData", "url", "process", "env", "VUE_APP_BASE_API", "files", "getCurrentHour", "now", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "date", "getDate", "hour", "getHours", "infiniteDate", "categoryOptions", "selectCidList", "lesson", "startTime", "endTime", "price", "originalPrice", "image", "cidList", "phrase", "introduction", "timeType", "lessonRules", "required", "message", "trigger", "baseLoading", "loadBaseInfo", "res", "valElement", "certificateTemplate", "certificate", "loadCategory", "changeCategory", "val", "changeStartTime", "changeEndTime", "onUploadImageSuccess", "onUploadImageRemove", "lessonRef", "submitBaseInfo", "validate", "valid", "path", "fullPath", "contentList", "showChapterDialog", "lessonChapter", "lessonId", "title", "lessonChapterRules", "showChapterSectionDialog", "lessonChapterSection", "lessonChapterId", "type", "totalTime", "lessonChapterSectionRules", "homework", "content", "attachment", "uploadHomeworkData", "loadContent", "list", "showChapter", "chapter", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadVideoData", "videoLoaded", "showChapterSection", "chapterSection", "hideChapterSection", "deleteChapter", "deleteChapterSection", "lessonChapterRef", "submitChapter", "linkVideo", "url<PERSON>lur", "addEventListener", "duration", "lessonChapterSectionRef", "submitChapterSection", "onUploadVideoSuccess", "onUploadVideoRemove", "onBeforeUploadVideo", "file", "videoUrl", "URL", "createObjectURL", "audioElement", "Audio", "onDraggableChange", "chapterList", "subData", "chapterSectionList", "sub", "params", "homeworkRef", "homeworkRules", "onUploadHomeworkAttachmentSuccess", "onUploadHomeworkAttachmentRemove", "submitHomework", "statusMap", "unpublished", "published", "deleted", "publish", "status", "unPublish", "init", "step<PERSON>lick", "showCertificateTemplateFlag", "showCertificateTemplate", "hideCertificateTemplate", "selectCertificateTemplate", "certificateId", "showPreviewViewFlag", "showPreview", "hidePreview", "submitCertificateTemplate"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/learn/lesson/edit/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row>\n      <el-col :span=\"20\">\n        <div v-if=\"showStep === 'base'\" class=\"base\">\n          <el-form :model=\"lesson\" :rules=\"lessonRules\" ref=\"lessonRef\" label-width=\"120px\">\n            <el-form-item label=\"名称：\" prop=\"name\">\n              <el-input size=\"small\" v-model=\"lesson.name\" placeholder=\"请输入标题\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"有效期：\" prop=\"timeType\">\n              <el-radio-group v-model=\"lesson.timeType\">\n                <el-radio label=\"infinite\">永久有效</el-radio>\n                <el-radio label=\"customize\">自定义</el-radio>\n              </el-radio-group>\n            </el-form-item>\n            <el-form-item label=\"开始时间：\" prop=\"startTime\" v-if=\"lesson.timeType !== 'infinite'\">\n              <el-date-picker\n                v-model=\"lesson.startTime\"\n                type=\"datetime\"\n                placeholder=\"选择开始时间\"\n                class=\"input-text\"\n                :default-time=\"new Date(2000, 0, 1, 0, 0, 0)\"\n                size=\"small\"\n                @change=\"changeStartTime\"\n                style=\"width: 100%;\"></el-date-picker>\n            </el-form-item>\n            <el-form-item label=\"结束时间：\" prop=\"endTime\" v-if=\"lesson.timeType !== 'infinite'\">\n              <el-date-picker\n                v-model=\"lesson.endTime\"\n                type=\"datetime\"\n                placeholder=\"选择结束时间\"\n                class=\"input-text\"\n                :default-time=\"new Date(2000, 0, 1, 22, 0, 0)\"\n                size=\"small\"\n                @change=\"changeEndTime\"\n                style=\"width: 100%;\"></el-date-picker>\n            </el-form-item>\n            <el-form-item label=\"分类：\" prop=\"cidList\">\n              <el-cascader style=\"width: 100%;\"\n                           size=\"small\"\n                           v-model=\"selectCidList\"\n                           :props=\"{ multiple: true, checkStrictly: true }\"\n                           :options=\"categoryOptions\"\n                           @change=\"changeCategory\">\n              </el-cascader>\n            </el-form-item>\n            <el-form-item label=\"简介：\" prop=\"phrase\">\n              <el-input size=\"small\" v-model=\"lesson.phrase\" placeholder=\"请输入简介\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"价格：\" prop=\"price\">\n              <el-input-number class=\"input-number\" v-model=\"lesson.price\" placeholder=\"请输入价格\" :precision=\"2\" :step=\"1\" :min=\"0\"></el-input-number>\n              <el-input-number class=\"input-number\" v-model=\"lesson.originalPrice\" placeholder=\"请输入原价\" :precision=\"2\" :step=\"1\" :min=\"0\"></el-input-number>\n            </el-form-item>\n            <el-form-item label=\"海报：\" prop=\"image\">\n              <upload\n                :class=\"{'no-plus': lesson.image}\"\n                :on-upload-success=\"onUploadImageSuccess\"\n                :on-upload-remove=\"onUploadImageRemove\"\n                :files=\"uploadData.files\"\n                :upload-url=\"uploadData.url\"\n                :limit=\"1\"\n                accept=\"image/jpeg,image/gif,image/png\">\n              </upload>\n              <span class=\"upload-image-tips\">图片建议：尺寸 1920 x 1200 像素，大小7M以下</span>\n            </el-form-item>\n            <el-form-item label=\"详情描述：\" prop=\"introduction\">\n              <wang-editor v-if=\"loadWangEditorFlag\" v-model=\"lesson.introduction\"></wang-editor>\n            </el-form-item>\n            <div style=\"margin:50px auto;text-align: center;\">\n              <el-button size=\"small\" @click=\"stepClick('content')\" v-if=\"lesson.id\">下一步</el-button>\n              <el-button size=\"small\" @click=\"submitBaseInfo\">提交</el-button>\n            </div>\n          </el-form>\n        </div>\n        <div v-if=\"showStep === 'content'\" class=\"content\">\n          <div class=\"content-header\">\n            <el-button size=\"small\" @click=\"stepClick('base')\">上一步</el-button>\n            <el-button size=\"small\" @click=\"stepClick('homework')\">下一步</el-button>\n            <el-button size=\"small\" @click=\"showChapter\">新增章节</el-button>\n          </div>\n          <div style=\"margin-top: 20px;\">\n            <el-table ref=\"table\" @expand-change=\"handleExpandChange\" :default-expand-all=\"false\" :data=\"contentList\" :show-header=\"false\" :highlight-current-row=\"true\" style=\"width: 100%\">\n              <el-table-column type=\"expand\">\n                <template #default=\"props\">\n                  <div v-if=\"props.row.phrase\" class=\"tips\">{{props.row.phrase}}</div>\n                  <el-card class=\"box-card\" v-for=\"section in props.row.chapterSectionList\" :key=\"section.title\">\n                    <template #header>\n                      <div class=\"clearfix\" style=\"line-height: 28px;\">\n                        <span>{{section.title}}</span>\n                        <span class=\"opt-btn\">\n                          <el-button type=\"text\" size=\"small\" @click=\"section.isPreview = !section.isPreview\">预览</el-button>\n                          <el-button type=\"text\" size=\"small\" @click=\"showChapterSection(props.row.id, section)\">修改</el-button>\n                          <el-button type=\"text\" size=\"small\" @click=\"deleteChapterSection(section.id)\">删除</el-button>\n                        </span>\n                      </div>\n                    </template>\n                    <div class=\"table-wrapper\" :class=\"{'show': section.isPreview}\">\n                      <div v-if=\"section.phrase\" class=\"tips\">{{section.phrase}}</div>\n                      <div class=\"video-box\">\n                        <video :src=\"section.url\" controls=\"controls\" :style=\"{'margin-top:20px;': !!section.phrase}\"></video>\n                      </div>\n                    </div>\n                  </el-card>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"title\" label=\"标题\">\n                <template #default=\"{ row }\">\n                  <div\n                    class=\"expandable-cell\"\n                    @click=\"toggleRowExpansion(row)\"\n                  >\n                    {{ row.title }}\n                  </div>\n                </template>\n              </el-table-column>\n              <el-table-column label=\"操作\">\n                <template #default=\"r\">\n                  <span class=\"opt-btn\">\n                    <el-button type=\"text\" @click=\"showChapterSection(r.row.id)\" size=\"small\">新增章节内容</el-button>\n                    <el-button type=\"text\" @click=\"showChapter(r.row)\" size=\"small\">修改</el-button>\n                    <el-button type=\"text\" @click=\"deleteChapter(r.row.id)\" size=\"small\">删除</el-button>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n        </div>\n        <div v-if=\"showStep === 'homework'\" class=\"homework\">\n          <el-form :model=\"homework\" :rules=\"homeworkRules\" ref=\"homeworkRef\" label-width=\"120px\">\n            <el-form-item label=\"作业内容：\" prop=\"content\">\n              <el-input size=\"small\" type=\"textarea\" v-model=\"homework.content\" :rows=\"20\" placeholder=\"请输入作业内容\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"作业附件：\">\n              <upload\n                list-type=\"text\"\n                :on-upload-success=\"onUploadHomeworkAttachmentSuccess\"\n                :on-upload-remove=\"onUploadHomeworkAttachmentRemove\"\n                :files=\"uploadHomeworkData.files\"\n                :upload-url=\"uploadHomeworkData.url\"\n                :limit=\"1\"\n                accept=\"image/*,video/*,audio/*,application/*\">\n              </upload>\n            </el-form-item>\n            <div style=\"margin:50px auto;text-align: center;\">\n              <el-button size=\"small\" @click=\"stepClick('content')\">上一步</el-button>\n              <el-button size=\"small\" @click=\"stepClick('certificate')\">下一步</el-button>\n              <el-button size=\"small\" @click=\"submitHomework\">提交</el-button>\n            </div>\n          </el-form>\n        </div>\n        <div v-if=\"showStep === 'certificate'\" class=\"certificate\">\n          <div class=\"certificate-select\">\n            <div class=\"certificate-select-main\">\n              <div class=\"certificate-select-label\">选择证书：</div>\n              <div class=\"certificate-select-value\" v-loading=\"baseLoading\">{{certificateTemplate.id ? certificateTemplate.name : '未选择'}}</div>\n              <div class=\"certificate-select-btn\">\n                <div>\n                  <el-button size=\"small\" @click=\"showCertificateTemplate\">选择</el-button>\n                </div>\n                <div>\n                  <el-button size=\"small\" @click=\"showPreview\">预览</el-button>\n                </div>\n              </div>\n            </div>\n            <el-dialog style=\"min-width: 840px\" title=\"证书预览\" v-model=\"showPreviewViewFlag\" :before-close=\"hidePreview\">\n              <div>\n                <certificate-preview v-if=\"showPreviewViewFlag\" :download=\"false\" :certificate=\"certificateTemplate\" />\n              </div>\n              <template #footer>\n                <div class=\"dialog-footer\">\n                  <el-button size=\"small\" @click=\"hidePreview\">取 消</el-button>\n                </div>\n              </template>\n            </el-dialog>\n          </div>\n          <div style=\"margin:50px auto;text-align: center;\">\n            <el-button size=\"small\" @click=\"stepClick('homework')\">上一步</el-button>\n            <el-button size=\"small\" @click=\"stepClick('publish')\">下一步</el-button>\n            <el-button size=\"small\" @click=\"submitCertificateTemplate\">提交</el-button>\n          </div>\n        </div>\n        <div v-if=\"showStep === 'publish'\" class=\"publish\">\n          <div class=\"publish-box\">\n            <div class=\"current-status\" v-if=\"lesson.status\">\n              <el-alert :title=\"statusMap[lesson.status]\" effect=\"dark\" type=\"success\" :closable=\"false\" show-icon v-if=\"lesson.status === 'published'\"></el-alert>\n              <el-alert :title=\"statusMap[lesson.status]\" effect=\"dark\" type=\"warning\" :closable=\"false\" show-icon v-else-if=\"lesson.status === 'unpublished'\"> </el-alert>\n              <el-alert :title=\"statusMap[lesson.status]\" effect=\"dark\" type=\"error\" :closable=\"false\" show-icon v-else> </el-alert>\n            </div>\n            <div class=\"btn-list\">\n              <el-button size=\"small\" @click=\"stepClick('certificate')\">上一步</el-button>\n              <el-button size=\"small\" @click=\"publish\" v-if=\"lesson.status === 'unpublished'\">马上发布</el-button>\n              <el-button size=\"small\" @click=\"unPublish\" v-if=\"lesson.status === 'published'\">移入草稿</el-button>\n            </div>\n          </div>\n        </div>\n      </el-col>\n      <el-col :span=\"4\" style=\"position: relative;\">\n        <el-affix :offset=\"60\" class=\"affix\">\n          <div class=\"step-list\">\n            <div class=\"title\">\n              步骤导航\n            </div>\n            <el-steps class=\"steps\" finish-status=\"success\" direction=\"vertical\" :active=\"stepActive\">\n              <el-step v-for=\"(step) in steps\" :key=\"step.key\" @click=\"stepClick(step.key)\" :class=\"{'step-active': showStep === step.key}\" :title=\"step.name\"></el-step>\n            </el-steps>\n          </div>\n          <div class=\"draggable\" v-if=\"showStep === 'content'\">\n            <div class=\"title\">\n              章节目录（拖动排序）\n            </div>\n            <draggable class=\"item-list\" v-model=\"contentList\" chosen-class=\"chosen\" force-fallback=\"true\" group=\"item\" animation=\"1000\" @change=\"onDraggableChange\">\n              <transition-group>\n                <div class=\"item\" v-for=\"item in contentList\" :key=\"item.id\">\n                  <div class=\"item-title\">{{item.title}}</div>\n                  <div class=\"sub-item-list\" v-if=\"item.chapterSectionList && item.chapterSectionList.length\">\n                    <draggable v-model=\"item.chapterSectionList\" chosen-class=\"chosen\" force-fallback=\"true\" group=\"sub-item\" animation=\"1000\" @change=\"onDraggableChange\">\n                      <div class=\"sub-item\" v-for=\"subItem in item.chapterSectionList\" :key=\"subItem.id\">{{subItem.title}}</div>\n                    </draggable>\n                  </div>\n                </div>\n              </transition-group>\n            </draggable>\n          </div>\n        </el-affix>\n      </el-col>\n    </el-row>\n    <el-dialog title=\"编辑章节\" v-model=\"showChapterDialog\" :before-close=\"hideChapter\">\n      <el-form :model=\"lessonChapter\" :rules=\"lessonChapterRules\" ref=\"lessonChapterRef\">\n        <el-form-item label=\"标题：\" label-width=\"120px\" prop=\"title\">\n          <el-input size=\"small\" v-model=\"lessonChapter.title\" placeholder=\"请输入标题\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"简介：\" label-width=\"120px\" prop=\"phrase\">\n          <el-input size=\"small\" v-model=\"lessonChapter.phrase\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入简介\"></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button size=\"small\" @click=\"hideChapter\">取 消</el-button>\n          <el-button size=\"small\" type=\"primary\" @click=\"submitChapter\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n    <el-dialog title=\"编辑章节内容\" v-model=\"showChapterSectionDialog\" :before-close=\"hideChapterSection\">\n      <el-form :model=\"lessonChapterSection\" :rules=\"lessonChapterSectionRules\" ref=\"lessonChapterSectionRef\">\n        <el-form-item label=\"标题：\" label-width=\"120px\" prop=\"title\">\n          <el-input size=\"small\" v-model=\"lessonChapterSection.title\" placeholder=\"请输入标题\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"视频方式：\" label-width=\"120px\" prop=\"type\">\n          <el-radio v-model=\"lessonChapterSection.type\" label=\"link\">视频链接</el-radio>\n          <el-radio v-model=\"lessonChapterSection.type\" label=\"upload\">视频上传</el-radio>\n        </el-form-item>\n        <el-form-item label=\"视频链接：\" label-width=\"120px\" prop=\"url\" v-if=\"lessonChapterSection.type === 'link'\">\n          <el-input size=\"small\" @blur=\"urlBlur\" v-model=\"lessonChapterSection.url\" placeholder=\"请输入视频地址\" autocomplete=\"off\"></el-input>\n          <video ref=\"linkVideo\" style=\"display: none;\" :src=\"lessonChapterSection.url\"></video>\n        </el-form-item>\n        <el-form-item label=\"视频上传：\" label-width=\"120px\" prop=\"url\" v-else>\n          <upload\n            :on-before-upload=\"onBeforeUploadVideo\"\n            :on-upload-success=\"onUploadVideoSuccess\"\n            :on-upload-remove=\"onUploadVideoRemove\"\n            :files=\"uploadVideoData.files\"\n            :upload-url=\"uploadVideoData.url\"\n            :limit=\"1\"\n            listType=\"text\"\n            accept=\"audio/mp4,video/mp4\">\n          </upload>\n        </el-form-item>\n        <el-form-item label=\"视频时长：\" label-width=\"120px\" prop=\"totalTime\">\n          <el-input size=\"small\" v-model=\"lessonChapterSection.totalTime\" placeholder=\"请输入时长\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"简介：\" label-width=\"120px\" prop=\"phrase\">\n          <el-input size=\"small\" v-model=\"lessonChapterSection.phrase\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入简介\"></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button size=\"small\" @click=\"hideChapterSection\">取 消</el-button>\n          <el-button size=\"small\" type=\"primary\" @click=\"submitChapterSection\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n    <el-dialog custom-class=\"custom-dialog\" title=\"选择证书\" v-model=\"showCertificateTemplateFlag\" :before-close=\"hideCertificateTemplate\" width=\"80%\">\n      <certificate-template-list :cancel-callback=\"hideCertificateTemplate\" :select-callback=\"selectCertificateTemplate\" :is-component=\"true\"/>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport router from \"@/router\"\nimport WangEditor from \"@/components/WangEditor/index.vue\"\nimport Upload from \"@/components/Uplaod\"\nimport {ref} from \"vue\"\nimport {useRoute} from \"vue-router\"\nimport {VueDraggableNext} from \"vue-draggable-next\"\nimport {success, confirm, error} from \"@/util/tipsUtils\"\nimport {findCategoryList, toTree, getAllParent} from \"@/api/learn/category\"\nimport {saveBaseInfo, updateBaseInfo, getBaseInfo, publishLesson, unPublishLesson,\n    saveLessonChapter, updateLessonChapter, deleteLessonChapter, getLessonChapterList, updateSortOrder,\n    saveLessonChapterSection, updateLessonChapterSection, deleteLessonChapterSection, saveHomework, updateHomework, getHomework} from \"@/api/learn/lesson\"\nimport CertificateTemplateList from \"@/views/certificate/template/index.vue\";\nimport CertificatePreview from \"@/views/certificate/preview/index.vue\";\n\n  export default {\n  name: \"LearnLessonEdit\",\n    components:{\n      CertificatePreview,\n      CertificateTemplateList,\n      Upload,\n      WangEditor,\n      draggable: VueDraggableNext\n    },\n    data() {\n      return {\n        expandedFlag: false,\n        expandedRows: [], // 用于存储已展开的行\n      };\n    },\n    methods: {\n      // 自定义切换行展开\n      toggleRowExpansion(row) {\n        this.expandedFlag = true;\n        const index = this.expandedRows.indexOf(row);\n        if (index === -1) {\n          this.expandedRows.push(row);\n          this.$refs.table.toggleRowExpansion(row, true);\n        } else {\n          this.expandedRows.splice(index, 1);\n          this.$refs.table.toggleRowExpansion(row, false);\n        }\n        this.expandedFlag = false;\n      },\n      handleExpandChange(row, expanded) {\n        console.log(\"222222: \", this.expandedFlag, expanded)\n        if (!this.expandedFlag) {\n          if (expanded) {\n            const index = this.expandedRows.indexOf(row);\n            if (index === -1) {\n              this.expandedRows.push(row);\n            }\n          } else {\n            console.log(\"\")\n            const index = this.expandedRows.indexOf(row);\n            if (index !== -1) {\n              this.expandedRows.splice(index, 1)\n            }\n          }\n        }\n        this.expandedFlag = false;\n      }\n    },\n    setup() {\n      const loadWangEditorFlag = ref(false)\n      const route = useRoute()\n      let isUpdate = !!route.query.id\n      let showStep = ref(\"\")\n      const steps = [\n        {key: \"base\", name: \"基础信息\"},\n        {key: \"content\", name: \"章节内容\"},\n        {key: \"homework\", name: \"课后作业\"},\n        {key: \"certificate\", name: \"关联证书\"},\n        {key: \"publish\", name: \"发布状态\"},\n      ]\n      const stepActive = ref(0)\n      const loadStepActiveArray = () => {\n        const stepActiveArray = [];\n        for (let i = 0; i < steps.length; i++) {\n          const step = steps[i];\n          stepActiveArray.push(step.key);\n          if (step.key === showStep.value) {\n            stepActive.value = i;\n            break;\n          }\n        }\n        if (isUpdate) {\n          stepActive.value = steps.length;\n        }\n        return stepActiveArray;\n      }\n      // 基本信息\n      const uploadData = ref({\n        url: process.env.VUE_APP_BASE_API + \"/oss/learn/lesson/image\",\n        files: []\n      })\n      function getCurrentHour() {\n        const now = new Date();\n        const year = now.getFullYear();\n        const month = String(now.getMonth() + 1).padStart(2, '0');\n        const date = String(now.getDate()).padStart(2, '0');\n        const hour = String(now.getHours()).padStart(2, '0');\n        return `${year}-${month}-${date} ${hour}:00:00`;\n      }\n      const infiniteDate = \"2037-12-31 23:59:59\";\n      const categoryOptions = ref([])\n      const selectCidList = ref([])\n      const lesson = ref({\n        id: \"\",\n        name: \"\",\n        startTime: getCurrentHour(),\n        endTime: infiniteDate,\n        price: 0,\n        originalPrice: 0,\n        image: \"\",\n        cidList: [],\n        phrase: \"\",\n        introduction: \"\",\n        timeType: \"infinite\"\n      })\n      const lessonRules = {\n        name: [{ required: true, message: \"请输入标题\", trigger: \"blur\" }],\n        startTime: [{ required: true, message: \"请选择时间\", trigger: \"change\" }],\n        endTime: [{ required: true, message: \"请选择时间\", trigger: \"change\" }],\n        phrase: [{ required: true, message: \"请输入简介\", trigger: \"blur\" }],\n        price: [{ required: true, message: \"请输入价格\", trigger: \"blur\" }],\n        cidList: [{ required: true, message: \"请选择分类\", trigger: \"change\" }],\n        introduction: [{ required: true, message: \"请输入描述\", trigger: \"blur\" }],\n        image: [{ required: true, message: \"请选择海报\", trigger: \"change\" }],\n      }\n      const baseLoading = ref(true)\n      // 加载基本信息\n      const loadBaseInfo = () => {\n        let id = route.query.id;\n        if (!id) {\n          loadWangEditorFlag.value = true;\n          return;\n        }\n        getBaseInfo(id, function (res) {\n          baseLoading.value = false\n          lesson.value = res;\n          if (res && res.endTime) {\n            if (res.endTime === new Date(infiniteDate)) {\n              lesson.value.timeType = 'infinite'\n            } else {\n              lesson.value.timeType = 'customize'\n            }\n          }\n          selectCidList.value = getAllParent(categoryOptions.value, res.cidList);\n          lesson.value.cidList = []\n          uploadData.value.files = [\n            {\n              name: \"海报\",\n              url: lesson.value.image\n            }\n          ]\n          for (const valElement of selectCidList.value) {\n            lesson.value.cidList.push(valElement[valElement.length - 1])\n          }\n          loadWangEditorFlag.value = true;\n\n          // 获取证书\n          certificateTemplate.value = lesson.value.certificate\n\n        })\n      }\n      // 获取分类\n      const loadCategory = () => {\n        findCategoryList(0, true, (res) => {\n          if (res && res.length) {\n            categoryOptions.value = toTree(res);\n            loadBaseInfo();\n          }\n        })\n      }\n      // 选择分类\n      const changeCategory = (val) => {\n        lesson.value.cidList = []\n        for (const valElement of val) {\n          lesson.value.cidList.push(valElement[valElement.length - 1])\n        }\n      }\n      // 选择时间\n      const changeStartTime = (val) => {\n        lesson.value.startTime = val\n      }\n      // 选择时间\n      const changeEndTime = (val) => {\n        lesson.value.endTime = val\n      }\n      // 上传图片成功\n      const onUploadImageSuccess = (res) => {\n        lesson.value.image = res.data\n      }\n      // 删除图片\n      const onUploadImageRemove = () => {\n        lesson.value.image = \"\"\n        uploadData.value.files = []\n      }\n      // 提交基本信息\n      const lessonRef = ref(null)\n      const submitBaseInfo = () => {\n        lessonRef.value.validate((valid) => {\n          if (!valid) { return false }\n          if (isUpdate) {\n            if(typeof lesson.value.startTime == \"string\") {\n              lesson.value.startTime = new Date(lesson.value.startTime);\n            }\n            if(typeof lesson.value.endTime == \"string\") {\n              lesson.value.endTime = new Date(lesson.value.endTime);\n            }\n            updateBaseInfo(lesson.value, function (res) {\n              if (res && res.id) {\n                lesson.value = res;\n                success(\"编辑成功\")\n                showStep.value = \"content\";\n                loadStepActiveArray()\n                let path = route.fullPath;\n                router.push({path, query: {id: lesson.value.id, step: \"content\"} });\n              }\n            })\n          } else {\n            if (lesson.value.timeType === 'infinite') {\n              lesson.value.startTime = new Date(getCurrentHour());\n              lesson.value.endTime = new Date(infiniteDate);\n            }\n            saveBaseInfo(lesson.value, function (res) {\n              if (res && res.id) {\n                lesson.value = res;\n                success(\"新增成功\")\n                showStep.value = \"content\";\n                loadStepActiveArray()\n                let path = route.fullPath;\n                router.push({path, query: {id: lesson.value.id, step: \"content\"} });\n              }\n            })\n          }\n        })\n      }\n\n      // 内容\n      const contentList = ref([])\n      const showChapterDialog = ref(false)\n      const lessonChapter = ref({\n        id: \"\",\n        lessonId: \"\",\n        title: \"\",\n        phrase: \"\"\n      })\n      const lessonChapterRules = {\n        title: [{ required: true, message: \"请输入标题\", trigger: \"blur\" }],\n      }\n      const showChapterSectionDialog = ref(false)\n      const lessonChapterSection = ref({\n        id: \"\",\n        lessonChapterId: \"\",\n        type: \"link\",\n        title: \"\",\n        url: \"\",\n        phrase: \"\",\n        totalTime: \"\"\n      })\n      const lessonChapterSectionRules = ref({\n        title: [{ required: true, message: \"请输入标题\", trigger: \"blur\" }],\n        url: [{ required: true, message: \"请输入视频地址\", trigger: \"blur\" }],\n        type: [{ required: true, message: \"请选择类型\", trigger: \"change\" }],\n        totalTime: [{ required: true, message: \"请输入时长\", trigger: \"blur\" }]\n      })\n      const homework = ref({\n        lessonId: \"\",\n        content: \"\",\n        attachment: \"\",\n      })\n      const uploadHomeworkData = ref({\n        url: process.env.VUE_APP_BASE_API + \"/oss/learn/homework/file\",\n        files: []\n      })\n      const loadContent = () => {\n        let id = route.query.id;\n        if (!id) { return; }\n        getLessonChapterList({lessonId: id}, (res) => {\n          if (res && res.list) {\n            contentList.value = res.list;\n          }\n        })\n        getHomework({lessonId: route.query.id}, (res) => {\n          homework.value = res\n          if (homework.value.url) {\n            uploadHomeworkData.value.files = [\n              {\n                name: \"作业附件\",\n                url: homework.value.url\n              }\n            ]\n          }\n        })\n      }\n      const showChapter = (chapter) => {\n        showChapterDialog.value = true;\n        if (chapter && chapter.id) {\n          lessonChapter.value = chapter;\n        } else {\n          lessonChapter.value = {\n            lessonId: lesson.value.id,\n            id: \"\",\n            title: \"\",\n            phrase: \"\"\n          }\n        }\n      }\n      const hideChapter = () => {\n        showChapterDialog.value = false;\n        lessonChapter.value = {id: \"\", lessonId: \"\", title: \"\", phrase: \"\"}\n      }\n      const uploadVideoData = ref({\n        url: process.env.VUE_APP_BASE_API + \"/oss/learn/lesson/video\",\n        files: []\n      })\n      let videoLoaded = false;\n      const showChapterSection = (lessonChapterId, chapterSection) => {\n        showChapterSectionDialog.value = true;\n        if (chapterSection && chapterSection.id) {\n          lessonChapterSection.value = chapterSection;\n          uploadVideoData.value.files = [\n            {\n              name: lessonChapterSection.value.title + \".mp4\",\n              url: lessonChapterSection.value.url\n            }\n          ]\n        } else {\n          videoLoaded = false\n          lessonChapterSection.value = {\n            lessonChapterId: lessonChapterId,\n            id: \"\",\n            title: \"\",\n            url: \"\",\n            phrase: \"\",\n            type: \"link\",\n            totalTime: \"\"\n          }\n        }\n      }\n      const hideChapterSection = () => {\n        videoLoaded = false\n        showChapterSectionDialog.value = false;\n        lessonChapterSection.value = {\n          id: \"\",\n          lessonChapterId: \"\",\n          title: \"\",\n          url: \"\",\n          phrase: \"\",\n          type: \"link\",\n          totalTime: \"\"\n        }\n      }\n      const deleteChapter = (id) => {\n        confirm(\"确认删除吗？\", \"提示\", () => {\n          deleteLessonChapter({id: id}, () => {\n            success(\"删除成功\")\n            loadContent()\n          })\n        })\n      }\n      const deleteChapterSection = (id) => {\n        confirm(\"确认删除吗？\", \"提示\", () => {\n          deleteLessonChapterSection({id: id}, () => {\n            success(\"删除成功\")\n            loadContent()\n          })\n        })\n      }\n      const lessonChapterRef = ref(null)\n      const submitChapter = () => {\n        lessonChapterRef.value.validate((valid) => {\n          if (!valid) { return false }\n          if (lessonChapter.value.id) {\n            updateLessonChapter(lessonChapter.value, function () {\n              success(\"编辑成功\")\n              hideChapter()\n              loadContent()\n            })\n          } else {\n            saveLessonChapter(lessonChapter.value, function () {\n              success(\"新增成功\")\n              hideChapter()\n              loadContent()\n              stepActive.value = steps.length;\n              isUpdate = true;\n            })\n          }\n        })\n      }\n      const linkVideo = ref(null)\n      const urlBlur = () => {\n        if (lessonChapterSection.value.type === \"link\") {\n          linkVideo.value.addEventListener(\"loadedmetadata\", () => {\n            //时长为秒，小数，182.36\n            lessonChapterSection.value.totalTime = linkVideo.value.duration;\n            videoLoaded = true\n          });\n        }\n      }\n      const lessonChapterSectionRef = ref(null)\n      const submitChapterSection = () => {\n        if (lessonChapterSection.value.type === \"link\") {\n          if (!lessonChapterSection.value.id && !videoLoaded) {\n            error(\"正在计算视频时长，请稍后再试\");\n          }\n        }\n        lessonChapterSectionRef.value.validate((valid) => {\n          if (!valid) { return false }\n          if (lessonChapterSection.value.id) {\n            updateLessonChapterSection(lessonChapterSection.value, function () {\n              success(\"编辑成功\")\n              hideChapterSection()\n              loadContent()\n            })\n          } else {\n            saveLessonChapterSection(lessonChapterSection.value, function () {\n              success(\"新增成功\")\n              hideChapterSection()\n              loadContent()\n            })\n          }\n        })\n      }\n      // 上传视频成功\n      const onUploadVideoSuccess = (res) => {\n        lessonChapterSection.value.url = res.data\n        uploadVideoData.value.files = [\n            {\n              name: lessonChapterSection.value.title + \".mp4\",\n              url: res.data\n            }\n        ]\n      }\n      // 删除视频\n      const onUploadVideoRemove = () => {\n        lessonChapterSection.value.url = \"\"\n        uploadVideoData.value.files = []\n      }\n      const onBeforeUploadVideo = (file) => {\n        let videoUrl = URL.createObjectURL(file);\n        let audioElement = new Audio(videoUrl);\n        audioElement.addEventListener(\"loadedmetadata\", () => {\n          //时长为秒，小数，182.36\n          lessonChapterSection.value.totalTime = audioElement.duration;\n        });\n      }\n      // 拖拽事件\n      const onDraggableChange = () => {\n        console.log(contentList.value)\n        const chapterList = []\n        for (const content of contentList.value) {\n          const subData = []\n          if (content.chapterSectionList && content.chapterSectionList.length) {\n            for (const sub of content.chapterSectionList) {\n              subData.push({id: sub.id, list: []})\n            }\n          }\n          chapterList.push({id: content.id, list: subData});\n        }\n        const params = {id: lesson.value.id, list: chapterList}\n        updateSortOrder(params, () => {\n          success(\"排序更新成功\")\n        })\n        console.log(params)\n      }\n      // 作业\n      const homeworkRef = ref(null)\n      const homeworkRules = ref({\n        content: [{ required: true, message: \"请输入作业内容\", trigger: \"blur\" }],\n      })\n      // 上传附件成功\n      const onUploadHomeworkAttachmentSuccess = (res) => {\n        homework.value.attachment = res.data\n      }\n      // 删除附件成功\n      const onUploadHomeworkAttachmentRemove = () => {\n        homework.value.attachment = \"\"\n        uploadHomeworkData.value.files = []\n      }\n      const submitHomework = () => {\n        homework.value.lessonId = route.query.id || lesson.value.id\n        homeworkRef.value.validate((valid) => {\n          if (!valid) {return false}\n          if (homework.value.id) {\n            updateHomework(homework.value, () => {\n              success(\"编辑成功\")\n              showStep.value = \"publish\";\n              let path = route.fullPath;\n              router.push({path, query: {id: lesson.value.id, step: \"publish\"} });\n            })\n          } else {\n            saveHomework(homework.value, (res) => {\n              homework.value = res\n              success(\"编辑成功\")\n              showStep.value = \"publish\";\n              let path = route.fullPath;\n              router.push({path, query: {id: lesson.value.id, step: \"publish\"} });\n            })\n          }\n        })\n      }\n      // 发布页面\n      const statusMap = {\n        unpublished: \"草稿箱\",\n        published: \"已发布\",\n        deleted: \"已删除\"\n      }\n      const publish = () => {\n        publishLesson({id: lesson.value.id}, () => {\n          success(\"发布成功\")\n          lesson.value.status = \"published\"\n        })\n      }\n      const unPublish = () => {\n        unPublishLesson({id: lesson.value.id}, () => {\n          success(\"取消发布成功\")\n          lesson.value.status = \"unpublished\"\n        })\n      }\n      // 步骤条\n      const init = () => {\n        // 初始化加载\n        if (route.query.step) {\n          showStep.value = route.query.step;\n        } else {\n          showStep.value = \"base\"\n        }\n        lesson.value.id = route.query.id || \"\"\n        loadCategory();\n        loadContent();\n      }\n      init()\n      // 步骤条点击切换\n      const stepClick = (key) => {\n        if (!isUpdate && loadStepActiveArray().indexOf(key) < 0) {\n          return;\n        }\n        showStep.value = key;\n        let path = route.fullPath;\n        router.push({path, query: {id: lesson.value.id, step: key} });\n      }\n      loadStepActiveArray();\n\n      // 证书\n      const certificateTemplate = ref({})\n      const showCertificateTemplateFlag = ref(false)\n      const showCertificateTemplate = () => {\n        showCertificateTemplateFlag.value = true\n      }\n      const hideCertificateTemplate = () => {\n        showCertificateTemplateFlag.value = false\n      }\n      const selectCertificateTemplate = (val) => {\n        console.log(\"val\", val)\n        if (val.length > 1) {\n          error(\"只能选择一个证书\");\n          return\n        }\n        if (val.length > 0) {\n          certificateTemplate.value = val[0]\n          if (certificateTemplate.value) {\n            lesson.value.certificateId = certificateTemplate.value.id\n          }\n        }\n        console.log(\"lesson.value.certificateId\", lesson.value.certificateId)\n        hideCertificateTemplate()\n      }\n      const showPreviewViewFlag = ref(false);\n      const showPreview = () => {\n        if (!certificateTemplate.value.id) {\n          error(\"请先选择证书\")\n          return\n        }\n        showPreviewViewFlag.value = true;\n      }\n      const hidePreview = () => {\n        showPreviewViewFlag.value = false;\n      }\n      const submitCertificateTemplate = () => {\n        if (!certificateTemplate.value.id) {\n          error(\"请先选择证书\")\n          return;\n        }\n        if(typeof lesson.value.startTime == \"string\") {\n          lesson.value.startTime = new Date(lesson.value.startTime);\n        }\n        if(typeof lesson.value.endTime == \"string\") {\n          lesson.value.endTime = new Date(lesson.value.endTime);\n        }\n        lesson.value.certificateId = certificateTemplate.value.id;\n        updateBaseInfo(lesson.value, function (res) {\n          if (res && res.id) {\n            lesson.value = res;\n            success(\"关联证书成功\")\n          }\n        })\n      }\n      // 返回参数与方法\n      return {\n        // 证书\n        baseLoading,\n        hidePreview,\n        showPreview,\n        showPreviewViewFlag,\n        certificateTemplate,\n        showCertificateTemplateFlag,\n        showCertificateTemplate,\n        hideCertificateTemplate,\n        selectCertificateTemplate,\n        submitCertificateTemplate,\n        // 基本信息\n        uploadData,\n        categoryOptions,\n        lesson,\n        selectCidList,\n        lessonRules,\n        lessonRef,\n        changeCategory,\n        changeStartTime,\n        changeEndTime,\n        onUploadImageSuccess,\n        onUploadImageRemove,\n        submitBaseInfo,\n        // 内容列表\n        contentList,\n        showChapterDialog,\n        lessonChapter,\n        lessonChapterRules,\n        showChapterSectionDialog,\n        lessonChapterSection,\n        lessonChapterSectionRules,\n        lessonChapterRef,\n        lessonChapterSectionRef,\n        showChapter,\n        hideChapter,\n        showChapterSection,\n        hideChapterSection,\n        deleteChapter,\n        deleteChapterSection,\n        submitChapter,\n        submitChapterSection,\n        uploadVideoData,\n        linkVideo,\n        urlBlur,\n        onBeforeUploadVideo,\n        onUploadVideoSuccess,\n        onUploadVideoRemove,\n        onDraggableChange,\n        // 作业\n        homework,\n        homeworkRef,\n        homeworkRules,\n        uploadHomeworkData,\n        submitHomework,\n        onUploadHomeworkAttachmentSuccess,\n        onUploadHomeworkAttachmentRemove,\n        // 发布页面\n        statusMap,\n        publish,\n        unPublish,\n        // 步骤条\n        steps,\n        stepActive,\n        showStep,\n        stepClick,\n        loadWangEditorFlag\n      };\n    }\n  }\n</script>\n<style scoped lang=\"scss\">\n  .app-container {\n    margin: 20px;\n    .base {\n      .upload-image-tips {\n        font-size: 12px;\n        color: #999999;\n      }\n      ::v-deep .el-upload--picture-card,\n      ::v-deep .el-upload-list--picture-card .el-upload-list__item {\n        //width: 100%;\n        height: 62.5%;\n        border: none;\n        display: flex;\n        margin: 0;\n        min-height: 146px;\n        justify-content: center;\n        flex-direction: column;\n        max-height: 400px;\n        background-color: #ffffff;\n      }\n      .no-plus {\n        ::v-deep .el-upload--picture-card {\n          min-height: inherit;\n          justify-content: inherit;\n          flex-direction: inherit;\n          display: none;\n        }\n        img {\n          max-height: 460px;\n        }\n      }\n      .input-number {\n        margin-right: 20px;\n      }\n    }\n    .content {\n      position: relative;\n      min-height: 500px;\n      .content-header {\n        text-align: right;\n        ::v-deep .el-button {\n          border-color: #f3f5f8;\n        }\n      }\n      .tips {\n        font-size: 12px;\n        color: #999999;\n        padding: 15px 20px;\n      }\n    }\n    .publish {\n      .publish-box {\n        margin: 50px auto;\n        text-align: center;\n        .current-status {\n          margin: 0 auto 20px;\n          width: 180px;\n        }\n        .btn-list{\n          margin: 0 auto;\n          width: 180px;\n          text-align: center;\n        }\n      }\n    }\n  }\n  ::v-deep .el-input__inner, ::v-deep .el-input-number {\n    height: 34px;\n    line-height: 34px;\n    font-size: 12px;\n    border-color: #f3f5f8;\n    //border: none;\n    &:focus, &:hover {\n      border-color: #f3f5f8;\n    }\n    .el-input-number__decrease, .el-input-number__increase {\n      background: #FFFFFF;\n      line-height: 32px;\n      border: none;\n      &:focus, &:hover {\n        border-color: #f3f5f8;\n      }\n    }\n  }\n  ::v-deep .el-textarea__inner {\n    border-color: #f3f5f8;\n    &:focus, &:hover {\n      border-color: #f3f5f8;\n    }\n  }\n  ::v-deep .el-cascader .el-input .el-input__inner:focus {\n    border-color: #f3f5f8;\n  }\n  ::v-deep .el-input__icon {\n    line-height: 34px;\n    cursor: pointer;\n    &:hover {\n      color: $--color-primary;\n    }\n  }\n  ::v-deep .el-form-item__label {\n    font-size: 12px;\n  }\n  ::v-deep .el-table th,\n  ::v-deep .el-table td {\n    padding: 5px 0;\n    font-size: 12px;\n    color: #000000;\n  }\n  ::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td {\n    background-color: #FFFFFF;\n  }\n  ::v-deep .el-table__body tr.current-row > td {\n    background-color: #FFFFFF;\n  }\n  ::v-deep .el-button--text {\n    color: #999999;\n    font-size: 12px;\n    &:hover {\n      color: $--color-primary;\n    }\n  }\n  ::v-deep .el-cascader:not(.is-disabled):hover .el-input__inner {\n    cursor: pointer;\n    border-color: #f3f5f8;\n  }\n  .box-card {\n    padding: 0 30px 10px;\n    .el-card {\n      box-shadow: none;\n    }\n    ::v-deep .el-card__header {\n      padding: 5px 20px;\n      font-size: 12px;\n      border: 0;\n    }\n    ::v-deep .el-card__body {\n      padding: 0;\n      .table-wrapper {\n        display: none;\n        .video-box {\n          padding: 0 20px 15px;\n          display: flex;\n          justify-content: center;\n          video {\n            background: #000;\n            width: 320px;\n            height: 240px;\n          }\n        }\n      }\n      .show {\n        display: block;\n      }\n    }\n  }\n  .opt-btn {\n    float: right;\n    ::v-deep .el-button {\n      margin: 0;\n      padding: 5px;\n    }\n  }\n  .affix {\n    min-height: 720px;\n    .step-list {\n      padding: 10px 20px;\n      .title {\n        padding: 0 0 20px 0;\n        font-size: 12px;\n      }\n      .steps {\n        height: 120px;\n        padding-left: 10px;\n        ::v-deep .el-step__title {\n          font-size: 14px;\n        }\n        ::v-deep .el-step__icon {\n          width: 20px;\n          height: 20px;\n        }\n        ::v-deep .el-step.is-vertical .el-step__head {\n          width: 20px;\n        }\n        ::v-deep .el-step.is-vertical .el-step__title{\n          cursor:pointer;\n        }\n        ::v-deep .el-step.is-vertical .el-step__line {\n          width: 1px;\n          left: 10px;\n          top: 2px;\n        }\n        ::v-deep .el-step__icon.is-text {\n          border-width: 1px;\n          cursor:pointer;\n        }\n        ::v-deep .step-active .el-step__head.is-finish {\n          color: red;\n        }\n      }\n    }\n    .draggable {\n      padding: 10px 0 10px 10px;\n      .title {\n        padding: 10px 0 10px;\n        font-size: 12px;\n      }\n      .item-list {\n        padding: 0 0 0 10px;\n        .item {\n          font-size: 12px;\n          line-height: 20px;\n          padding: 5px 0;\n          .sub-item-list {\n            background: #ffffff;\n            padding: 0 10px;\n            border-radius: 4px;\n            margin-top: 5px;\n            .sub-item {\n              line-height: 20px;\n              padding: 5px 0;\n              color: #666666;\n              &:first-child {\n                padding-top: 10px;\n              }\n              &:last-child {\n                padding-bottom: 10px;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  ::v-deep .el-upload--text {\n    font-size: 12px;\n  }\n  ::v-deep .el-affix--fixed {\n    z-index: 98!important;\n  }\n  ::v-deep .el-table__empty-block {\n    line-height: 400px;\n    .el-table__empty-text {\n      line-height: 400px;\n    }\n  }\n  .certificate {\n    .certificate-select {\n      margin: 40px 0 20px;\n      .certificate-select-main {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        .certificate-select-label {\n\n        }\n        .certificate-select-value {\n\n        }\n        .certificate-select-btn {\n          display: flex;\n          margin-left: 20px;\n        }\n      }\n    }\n  }\n</style>\n"], "mappings": ";AA+RA,OAAOA,MAAK,MAAO,UAAS;AAC5B,OAAOC,UAAS,MAAO,mCAAkC;AACzD,OAAOC,MAAK,MAAO,qBAAoB;AACvC,SAAQC,GAAG,QAAO,KAAI;AACtB,SAAQC,QAAQ,QAAO,YAAW;AAClC,SAAQC,gBAAgB,QAAO,oBAAmB;AAClD,SAAQC,OAAO,EAAEC,OAAO,EAAEC,KAAK,QAAO,kBAAiB;AACvD,SAAQC,gBAAgB,EAAEC,MAAM,EAAEC,YAAY,QAAO,sBAAqB;AAC1E,SAAQC,YAAY,EAAEC,cAAc,EAAEC,WAAW,EAAEC,aAAa,EAAEC,eAAe,EAC7EC,iBAAiB,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,eAAe,EAClGC,wBAAwB,EAAEC,0BAA0B,EAAEC,0BAA0B,EAAEC,YAAY,EAAEC,cAAc,EAAEC,WAAW,QAAO,oBAAmB;AACzJ,OAAOC,uBAAsB,MAAO,wCAAwC;AAC5E,OAAOC,kBAAiB,MAAO,uCAAuC;AAEpE,eAAe;EACfC,IAAI,EAAE,iBAAiB;EACrBC,UAAU,EAAC;IACTF,kBAAkB;IAClBD,uBAAuB;IACvB1B,MAAM;IACND,UAAU;IACV+B,SAAS,EAAE3B;EACb,CAAC;EACD4B,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,EAAE,CAAE;IACpB,CAAC;EACH,CAAC;;EACDC,OAAO,EAAE;IACP;IACAC,kBAAkBA,CAACC,GAAG,EAAE;MACtB,IAAI,CAACJ,YAAW,GAAI,IAAI;MACxB,MAAMK,KAAI,GAAI,IAAI,CAACJ,YAAY,CAACK,OAAO,CAACF,GAAG,CAAC;MAC5C,IAAIC,KAAI,KAAM,CAAC,CAAC,EAAE;QAChB,IAAI,CAACJ,YAAY,CAACM,IAAI,CAACH,GAAG,CAAC;QAC3B,IAAI,CAACI,KAAK,CAACC,KAAK,CAACN,kBAAkB,CAACC,GAAG,EAAE,IAAI,CAAC;MAChD,OAAO;QACL,IAAI,CAACH,YAAY,CAACS,MAAM,CAACL,KAAK,EAAE,CAAC,CAAC;QAClC,IAAI,CAACG,KAAK,CAACC,KAAK,CAACN,kBAAkB,CAACC,GAAG,EAAE,KAAK,CAAC;MACjD;MACA,IAAI,CAACJ,YAAW,GAAI,KAAK;IAC3B,CAAC;IACDW,kBAAkBA,CAACP,GAAG,EAAEQ,QAAQ,EAAE;MAChCC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACd,YAAY,EAAEY,QAAQ;MACnD,IAAI,CAAC,IAAI,CAACZ,YAAY,EAAE;QACtB,IAAIY,QAAQ,EAAE;UACZ,MAAMP,KAAI,GAAI,IAAI,CAACJ,YAAY,CAACK,OAAO,CAACF,GAAG,CAAC;UAC5C,IAAIC,KAAI,KAAM,CAAC,CAAC,EAAE;YAChB,IAAI,CAACJ,YAAY,CAACM,IAAI,CAACH,GAAG,CAAC;UAC7B;QACF,OAAO;UACLS,OAAO,CAACC,GAAG,CAAC,EAAE;UACd,MAAMT,KAAI,GAAI,IAAI,CAACJ,YAAY,CAACK,OAAO,CAACF,GAAG,CAAC;UAC5C,IAAIC,KAAI,KAAM,CAAC,CAAC,EAAE;YAChB,IAAI,CAACJ,YAAY,CAACS,MAAM,CAACL,KAAK,EAAE,CAAC;UACnC;QACF;MACF;MACA,IAAI,CAACL,YAAW,GAAI,KAAK;IAC3B;EACF,CAAC;EACDe,KAAKA,CAAA,EAAG;IACN,MAAMC,kBAAiB,GAAI/C,GAAG,CAAC,KAAK;IACpC,MAAMgD,KAAI,GAAI/C,QAAQ,EAAC;IACvB,IAAIgD,QAAO,GAAI,CAAC,CAACD,KAAK,CAACE,KAAK,CAACC,EAAC;IAC9B,IAAIC,QAAO,GAAIpD,GAAG,CAAC,EAAE;IACrB,MAAMqD,KAAI,GAAI,CACZ;MAACC,GAAG,EAAE,MAAM;MAAE3B,IAAI,EAAE;IAAM,CAAC,EAC3B;MAAC2B,GAAG,EAAE,SAAS;MAAE3B,IAAI,EAAE;IAAM,CAAC,EAC9B;MAAC2B,GAAG,EAAE,UAAU;MAAE3B,IAAI,EAAE;IAAM,CAAC,EAC/B;MAAC2B,GAAG,EAAE,aAAa;MAAE3B,IAAI,EAAE;IAAM,CAAC,EAClC;MAAC2B,GAAG,EAAE,SAAS;MAAE3B,IAAI,EAAE;IAAM,CAAC,CAChC;IACA,MAAM4B,UAAS,GAAIvD,GAAG,CAAC,CAAC;IACxB,MAAMwD,mBAAkB,GAAIA,CAAA,KAAM;MAChC,MAAMC,eAAc,GAAI,EAAE;MAC1B,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIL,KAAK,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;QACrC,MAAME,IAAG,GAAIP,KAAK,CAACK,CAAC,CAAC;QACrBD,eAAe,CAACnB,IAAI,CAACsB,IAAI,CAACN,GAAG,CAAC;QAC9B,IAAIM,IAAI,CAACN,GAAE,KAAMF,QAAQ,CAACS,KAAK,EAAE;UAC/BN,UAAU,CAACM,KAAI,GAAIH,CAAC;UACpB;QACF;MACF;MACA,IAAIT,QAAQ,EAAE;QACZM,UAAU,CAACM,KAAI,GAAIR,KAAK,CAACM,MAAM;MACjC;MACA,OAAOF,eAAe;IACxB;IACA;IACA,MAAMK,UAAS,GAAI9D,GAAG,CAAC;MACrB+D,GAAG,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAe,GAAI,yBAAyB;MAC7DC,KAAK,EAAE;IACT,CAAC;IACD,SAASC,cAAcA,CAAA,EAAG;MACxB,MAAMC,GAAE,GAAI,IAAIC,IAAI,EAAE;MACtB,MAAMC,IAAG,GAAIF,GAAG,CAACG,WAAW,EAAE;MAC9B,MAAMC,KAAI,GAAIC,MAAM,CAACL,GAAG,CAACM,QAAQ,EAAC,GAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACzD,MAAMC,IAAG,GAAIH,MAAM,CAACL,GAAG,CAACS,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACnD,MAAMG,IAAG,GAAIL,MAAM,CAACL,GAAG,CAACW,QAAQ,EAAE,CAAC,CAACJ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACpD,OAAQ,GAAEL,IAAK,IAAGE,KAAM,IAAGI,IAAI,IAAIE,IAAK,QAAO;IACjD;IACA,MAAME,YAAW,GAAI,qBAAqB;IAC1C,MAAMC,eAAc,GAAIlF,GAAG,CAAC,EAAE;IAC9B,MAAMmF,aAAY,GAAInF,GAAG,CAAC,EAAE;IAC5B,MAAMoF,MAAK,GAAIpF,GAAG,CAAC;MACjBmD,EAAE,EAAE,EAAE;MACNxB,IAAI,EAAE,EAAE;MACR0D,SAAS,EAAEjB,cAAc,EAAE;MAC3BkB,OAAO,EAAEL,YAAY;MACrBM,KAAK,EAAE,CAAC;MACRC,aAAa,EAAE,CAAC;MAChBC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,YAAY,EAAE,EAAE;MAChBC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAMC,WAAU,GAAI;MAClBnE,IAAI,EAAE,CAAC;QAAEoE,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC7DZ,SAAS,EAAE,CAAC;QAAEU,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC,CAAC;MACpEX,OAAO,EAAE,CAAC;QAAES,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC,CAAC;MAClEN,MAAM,EAAE,CAAC;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC/DV,KAAK,EAAE,CAAC;QAAEQ,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9DP,OAAO,EAAE,CAAC;QAAEK,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC,CAAC;MAClEL,YAAY,EAAE,CAAC;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MACrER,KAAK,EAAE,CAAC;QAAEM,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC;IACjE;IACA,MAAMC,WAAU,GAAIlG,GAAG,CAAC,IAAI;IAC5B;IACA,MAAMmG,YAAW,GAAIA,CAAA,KAAM;MACzB,IAAIhD,EAAC,GAAIH,KAAK,CAACE,KAAK,CAACC,EAAE;MACvB,IAAI,CAACA,EAAE,EAAE;QACPJ,kBAAkB,CAACc,KAAI,GAAI,IAAI;QAC/B;MACF;MACAlD,WAAW,CAACwC,EAAE,EAAE,UAAUiD,GAAG,EAAE;QAC7BF,WAAW,CAACrC,KAAI,GAAI,KAAI;QACxBuB,MAAM,CAACvB,KAAI,GAAIuC,GAAG;QAClB,IAAIA,GAAE,IAAKA,GAAG,CAACd,OAAO,EAAE;UACtB,IAAIc,GAAG,CAACd,OAAM,KAAM,IAAIhB,IAAI,CAACW,YAAY,CAAC,EAAE;YAC1CG,MAAM,CAACvB,KAAK,CAACgC,QAAO,GAAI,UAAS;UACnC,OAAO;YACLT,MAAM,CAACvB,KAAK,CAACgC,QAAO,GAAI,WAAU;UACpC;QACF;QACAV,aAAa,CAACtB,KAAI,GAAIrD,YAAY,CAAC0E,eAAe,CAACrB,KAAK,EAAEuC,GAAG,CAACV,OAAO,CAAC;QACtEN,MAAM,CAACvB,KAAK,CAAC6B,OAAM,GAAI,EAAC;QACxB5B,UAAU,CAACD,KAAK,CAACM,KAAI,GAAI,CACvB;UACExC,IAAI,EAAE,IAAI;UACVoC,GAAG,EAAEqB,MAAM,CAACvB,KAAK,CAAC4B;QACpB,EACF;QACA,KAAK,MAAMY,UAAS,IAAKlB,aAAa,CAACtB,KAAK,EAAE;UAC5CuB,MAAM,CAACvB,KAAK,CAAC6B,OAAO,CAACpD,IAAI,CAAC+D,UAAU,CAACA,UAAU,CAAC1C,MAAK,GAAI,CAAC,CAAC;QAC7D;QACAZ,kBAAkB,CAACc,KAAI,GAAI,IAAI;;QAE/B;QACAyC,mBAAmB,CAACzC,KAAI,GAAIuB,MAAM,CAACvB,KAAK,CAAC0C,WAAU;MAErD,CAAC;IACH;IACA;IACA,MAAMC,YAAW,GAAIA,CAAA,KAAM;MACzBlG,gBAAgB,CAAC,CAAC,EAAE,IAAI,EAAG8F,GAAG,IAAK;QACjC,IAAIA,GAAE,IAAKA,GAAG,CAACzC,MAAM,EAAE;UACrBuB,eAAe,CAACrB,KAAI,GAAItD,MAAM,CAAC6F,GAAG,CAAC;UACnCD,YAAY,EAAE;QAChB;MACF,CAAC;IACH;IACA;IACA,MAAMM,cAAa,GAAKC,GAAG,IAAK;MAC9BtB,MAAM,CAACvB,KAAK,CAAC6B,OAAM,GAAI,EAAC;MACxB,KAAK,MAAMW,UAAS,IAAKK,GAAG,EAAE;QAC5BtB,MAAM,CAACvB,KAAK,CAAC6B,OAAO,CAACpD,IAAI,CAAC+D,UAAU,CAACA,UAAU,CAAC1C,MAAK,GAAI,CAAC,CAAC;MAC7D;IACF;IACA;IACA,MAAMgD,eAAc,GAAKD,GAAG,IAAK;MAC/BtB,MAAM,CAACvB,KAAK,CAACwB,SAAQ,GAAIqB,GAAE;IAC7B;IACA;IACA,MAAME,aAAY,GAAKF,GAAG,IAAK;MAC7BtB,MAAM,CAACvB,KAAK,CAACyB,OAAM,GAAIoB,GAAE;IAC3B;IACA;IACA,MAAMG,oBAAmB,GAAKT,GAAG,IAAK;MACpChB,MAAM,CAACvB,KAAK,CAAC4B,KAAI,GAAIW,GAAG,CAACtE,IAAG;IAC9B;IACA;IACA,MAAMgF,mBAAkB,GAAIA,CAAA,KAAM;MAChC1B,MAAM,CAACvB,KAAK,CAAC4B,KAAI,GAAI,EAAC;MACtB3B,UAAU,CAACD,KAAK,CAACM,KAAI,GAAI,EAAC;IAC5B;IACA;IACA,MAAM4C,SAAQ,GAAI/G,GAAG,CAAC,IAAI;IAC1B,MAAMgH,cAAa,GAAIA,CAAA,KAAM;MAC3BD,SAAS,CAAClD,KAAK,CAACoD,QAAQ,CAAEC,KAAK,IAAK;QAClC,IAAI,CAACA,KAAK,EAAE;UAAE,OAAO,KAAI;QAAE;QAC3B,IAAIjE,QAAQ,EAAE;UACZ,IAAG,OAAOmC,MAAM,CAACvB,KAAK,CAACwB,SAAQ,IAAK,QAAQ,EAAE;YAC5CD,MAAM,CAACvB,KAAK,CAACwB,SAAQ,GAAI,IAAIf,IAAI,CAACc,MAAM,CAACvB,KAAK,CAACwB,SAAS,CAAC;UAC3D;UACA,IAAG,OAAOD,MAAM,CAACvB,KAAK,CAACyB,OAAM,IAAK,QAAQ,EAAE;YAC1CF,MAAM,CAACvB,KAAK,CAACyB,OAAM,GAAI,IAAIhB,IAAI,CAACc,MAAM,CAACvB,KAAK,CAACyB,OAAO,CAAC;UACvD;UACA5E,cAAc,CAAC0E,MAAM,CAACvB,KAAK,EAAE,UAAUuC,GAAG,EAAE;YAC1C,IAAIA,GAAE,IAAKA,GAAG,CAACjD,EAAE,EAAE;cACjBiC,MAAM,CAACvB,KAAI,GAAIuC,GAAG;cAClBjG,OAAO,CAAC,MAAM;cACdiD,QAAQ,CAACS,KAAI,GAAI,SAAS;cAC1BL,mBAAmB,EAAC;cACpB,IAAI2D,IAAG,GAAInE,KAAK,CAACoE,QAAQ;cACzBvH,MAAM,CAACyC,IAAI,CAAC;gBAAC6E,IAAI;gBAAEjE,KAAK,EAAE;kBAACC,EAAE,EAAEiC,MAAM,CAACvB,KAAK,CAACV,EAAE;kBAAES,IAAI,EAAE;gBAAS;cAAE,CAAC,CAAC;YACrE;UACF,CAAC;QACH,OAAO;UACL,IAAIwB,MAAM,CAACvB,KAAK,CAACgC,QAAO,KAAM,UAAU,EAAE;YACxCT,MAAM,CAACvB,KAAK,CAACwB,SAAQ,GAAI,IAAIf,IAAI,CAACF,cAAc,EAAE,CAAC;YACnDgB,MAAM,CAACvB,KAAK,CAACyB,OAAM,GAAI,IAAIhB,IAAI,CAACW,YAAY,CAAC;UAC/C;UACAxE,YAAY,CAAC2E,MAAM,CAACvB,KAAK,EAAE,UAAUuC,GAAG,EAAE;YACxC,IAAIA,GAAE,IAAKA,GAAG,CAACjD,EAAE,EAAE;cACjBiC,MAAM,CAACvB,KAAI,GAAIuC,GAAG;cAClBjG,OAAO,CAAC,MAAM;cACdiD,QAAQ,CAACS,KAAI,GAAI,SAAS;cAC1BL,mBAAmB,EAAC;cACpB,IAAI2D,IAAG,GAAInE,KAAK,CAACoE,QAAQ;cACzBvH,MAAM,CAACyC,IAAI,CAAC;gBAAC6E,IAAI;gBAAEjE,KAAK,EAAE;kBAACC,EAAE,EAAEiC,MAAM,CAACvB,KAAK,CAACV,EAAE;kBAAES,IAAI,EAAE;gBAAS;cAAE,CAAC,CAAC;YACrE;UACF,CAAC;QACH;MACF,CAAC;IACH;;IAEA;IACA,MAAMyD,WAAU,GAAIrH,GAAG,CAAC,EAAE;IAC1B,MAAMsH,iBAAgB,GAAItH,GAAG,CAAC,KAAK;IACnC,MAAMuH,aAAY,GAAIvH,GAAG,CAAC;MACxBmD,EAAE,EAAE,EAAE;MACNqE,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACT9B,MAAM,EAAE;IACV,CAAC;IACD,MAAM+B,kBAAiB,GAAI;MACzBD,KAAK,EAAE,CAAC;QAAE1B,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC;IAC/D;IACA,MAAM0B,wBAAuB,GAAI3H,GAAG,CAAC,KAAK;IAC1C,MAAM4H,oBAAmB,GAAI5H,GAAG,CAAC;MAC/BmD,EAAE,EAAE,EAAE;MACN0E,eAAe,EAAE,EAAE;MACnBC,IAAI,EAAE,MAAM;MACZL,KAAK,EAAE,EAAE;MACT1D,GAAG,EAAE,EAAE;MACP4B,MAAM,EAAE,EAAE;MACVoC,SAAS,EAAE;IACb,CAAC;IACD,MAAMC,yBAAwB,GAAIhI,GAAG,CAAC;MACpCyH,KAAK,EAAE,CAAC;QAAE1B,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9DlC,GAAG,EAAE,CAAC;QAAEgC,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9D6B,IAAI,EAAE,CAAC;QAAE/B,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC,CAAC;MAC/D8B,SAAS,EAAE,CAAC;QAAEhC,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC;IACnE,CAAC;IACD,MAAMgC,QAAO,GAAIjI,GAAG,CAAC;MACnBwH,QAAQ,EAAE,EAAE;MACZU,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE;IACd,CAAC;IACD,MAAMC,kBAAiB,GAAIpI,GAAG,CAAC;MAC7B+D,GAAG,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAe,GAAI,0BAA0B;MAC9DC,KAAK,EAAE;IACT,CAAC;IACD,MAAMkE,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAIlF,EAAC,GAAIH,KAAK,CAACE,KAAK,CAACC,EAAE;MACvB,IAAI,CAACA,EAAE,EAAE;QAAE;MAAQ;MACnBlC,oBAAoB,CAAC;QAACuG,QAAQ,EAAErE;MAAE,CAAC,EAAGiD,GAAG,IAAK;QAC5C,IAAIA,GAAE,IAAKA,GAAG,CAACkC,IAAI,EAAE;UACnBjB,WAAW,CAACxD,KAAI,GAAIuC,GAAG,CAACkC,IAAI;QAC9B;MACF,CAAC;MACD9G,WAAW,CAAC;QAACgG,QAAQ,EAAExE,KAAK,CAACE,KAAK,CAACC;MAAE,CAAC,EAAGiD,GAAG,IAAK;QAC/C6B,QAAQ,CAACpE,KAAI,GAAIuC,GAAE;QACnB,IAAI6B,QAAQ,CAACpE,KAAK,CAACE,GAAG,EAAE;UACtBqE,kBAAkB,CAACvE,KAAK,CAACM,KAAI,GAAI,CAC/B;YACExC,IAAI,EAAE,MAAM;YACZoC,GAAG,EAAEkE,QAAQ,CAACpE,KAAK,CAACE;UACtB,EACF;QACF;MACF,CAAC;IACH;IACA,MAAMwE,WAAU,GAAKC,OAAO,IAAK;MAC/BlB,iBAAiB,CAACzD,KAAI,GAAI,IAAI;MAC9B,IAAI2E,OAAM,IAAKA,OAAO,CAACrF,EAAE,EAAE;QACzBoE,aAAa,CAAC1D,KAAI,GAAI2E,OAAO;MAC/B,OAAO;QACLjB,aAAa,CAAC1D,KAAI,GAAI;UACpB2D,QAAQ,EAAEpC,MAAM,CAACvB,KAAK,CAACV,EAAE;UACzBA,EAAE,EAAE,EAAE;UACNsE,KAAK,EAAE,EAAE;UACT9B,MAAM,EAAE;QACV;MACF;IACF;IACA,MAAM8C,WAAU,GAAIA,CAAA,KAAM;MACxBnB,iBAAiB,CAACzD,KAAI,GAAI,KAAK;MAC/B0D,aAAa,CAAC1D,KAAI,GAAI;QAACV,EAAE,EAAE,EAAE;QAAEqE,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAE9B,MAAM,EAAE;MAAE;IACpE;IACA,MAAM+C,eAAc,GAAI1I,GAAG,CAAC;MAC1B+D,GAAG,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAe,GAAI,yBAAyB;MAC7DC,KAAK,EAAE;IACT,CAAC;IACD,IAAIwE,WAAU,GAAI,KAAK;IACvB,MAAMC,kBAAiB,GAAIA,CAACf,eAAe,EAAEgB,cAAc,KAAK;MAC9DlB,wBAAwB,CAAC9D,KAAI,GAAI,IAAI;MACrC,IAAIgF,cAAa,IAAKA,cAAc,CAAC1F,EAAE,EAAE;QACvCyE,oBAAoB,CAAC/D,KAAI,GAAIgF,cAAc;QAC3CH,eAAe,CAAC7E,KAAK,CAACM,KAAI,GAAI,CAC5B;UACExC,IAAI,EAAEiG,oBAAoB,CAAC/D,KAAK,CAAC4D,KAAI,GAAI,MAAM;UAC/C1D,GAAG,EAAE6D,oBAAoB,CAAC/D,KAAK,CAACE;QAClC,EACF;MACF,OAAO;QACL4E,WAAU,GAAI,KAAI;QAClBf,oBAAoB,CAAC/D,KAAI,GAAI;UAC3BgE,eAAe,EAAEA,eAAe;UAChC1E,EAAE,EAAE,EAAE;UACNsE,KAAK,EAAE,EAAE;UACT1D,GAAG,EAAE,EAAE;UACP4B,MAAM,EAAE,EAAE;UACVmC,IAAI,EAAE,MAAM;UACZC,SAAS,EAAE;QACb;MACF;IACF;IACA,MAAMe,kBAAiB,GAAIA,CAAA,KAAM;MAC/BH,WAAU,GAAI,KAAI;MAClBhB,wBAAwB,CAAC9D,KAAI,GAAI,KAAK;MACtC+D,oBAAoB,CAAC/D,KAAI,GAAI;QAC3BV,EAAE,EAAE,EAAE;QACN0E,eAAe,EAAE,EAAE;QACnBJ,KAAK,EAAE,EAAE;QACT1D,GAAG,EAAE,EAAE;QACP4B,MAAM,EAAE,EAAE;QACVmC,IAAI,EAAE,MAAM;QACZC,SAAS,EAAE;MACb;IACF;IACA,MAAMgB,aAAY,GAAK5F,EAAE,IAAK;MAC5B/C,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM;QAC5BY,mBAAmB,CAAC;UAACmC,EAAE,EAAEA;QAAE,CAAC,EAAE,MAAM;UAClChD,OAAO,CAAC,MAAM;UACdkI,WAAW,EAAC;QACd,CAAC;MACH,CAAC;IACH;IACA,MAAMW,oBAAmB,GAAK7F,EAAE,IAAK;MACnC/C,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM;QAC5BiB,0BAA0B,CAAC;UAAC8B,EAAE,EAAEA;QAAE,CAAC,EAAE,MAAM;UACzChD,OAAO,CAAC,MAAM;UACdkI,WAAW,EAAC;QACd,CAAC;MACH,CAAC;IACH;IACA,MAAMY,gBAAe,GAAIjJ,GAAG,CAAC,IAAI;IACjC,MAAMkJ,aAAY,GAAIA,CAAA,KAAM;MAC1BD,gBAAgB,CAACpF,KAAK,CAACoD,QAAQ,CAAEC,KAAK,IAAK;QACzC,IAAI,CAACA,KAAK,EAAE;UAAE,OAAO,KAAI;QAAE;QAC3B,IAAIK,aAAa,CAAC1D,KAAK,CAACV,EAAE,EAAE;UAC1BpC,mBAAmB,CAACwG,aAAa,CAAC1D,KAAK,EAAE,YAAY;YACnD1D,OAAO,CAAC,MAAM;YACdsI,WAAW,EAAC;YACZJ,WAAW,EAAC;UACd,CAAC;QACH,OAAO;UACLvH,iBAAiB,CAACyG,aAAa,CAAC1D,KAAK,EAAE,YAAY;YACjD1D,OAAO,CAAC,MAAM;YACdsI,WAAW,EAAC;YACZJ,WAAW,EAAC;YACZ9E,UAAU,CAACM,KAAI,GAAIR,KAAK,CAACM,MAAM;YAC/BV,QAAO,GAAI,IAAI;UACjB,CAAC;QACH;MACF,CAAC;IACH;IACA,MAAMkG,SAAQ,GAAInJ,GAAG,CAAC,IAAI;IAC1B,MAAMoJ,OAAM,GAAIA,CAAA,KAAM;MACpB,IAAIxB,oBAAoB,CAAC/D,KAAK,CAACiE,IAAG,KAAM,MAAM,EAAE;QAC9CqB,SAAS,CAACtF,KAAK,CAACwF,gBAAgB,CAAC,gBAAgB,EAAE,MAAM;UACvD;UACAzB,oBAAoB,CAAC/D,KAAK,CAACkE,SAAQ,GAAIoB,SAAS,CAACtF,KAAK,CAACyF,QAAQ;UAC/DX,WAAU,GAAI,IAAG;QACnB,CAAC,CAAC;MACJ;IACF;IACA,MAAMY,uBAAsB,GAAIvJ,GAAG,CAAC,IAAI;IACxC,MAAMwJ,oBAAmB,GAAIA,CAAA,KAAM;MACjC,IAAI5B,oBAAoB,CAAC/D,KAAK,CAACiE,IAAG,KAAM,MAAM,EAAE;QAC9C,IAAI,CAACF,oBAAoB,CAAC/D,KAAK,CAACV,EAAC,IAAK,CAACwF,WAAW,EAAE;UAClDtI,KAAK,CAAC,gBAAgB,CAAC;QACzB;MACF;MACAkJ,uBAAuB,CAAC1F,KAAK,CAACoD,QAAQ,CAAEC,KAAK,IAAK;QAChD,IAAI,CAACA,KAAK,EAAE;UAAE,OAAO,KAAI;QAAE;QAC3B,IAAIU,oBAAoB,CAAC/D,KAAK,CAACV,EAAE,EAAE;UACjC/B,0BAA0B,CAACwG,oBAAoB,CAAC/D,KAAK,EAAE,YAAY;YACjE1D,OAAO,CAAC,MAAM;YACd2I,kBAAkB,EAAC;YACnBT,WAAW,EAAC;UACd,CAAC;QACH,OAAO;UACLlH,wBAAwB,CAACyG,oBAAoB,CAAC/D,KAAK,EAAE,YAAY;YAC/D1D,OAAO,CAAC,MAAM;YACd2I,kBAAkB,EAAC;YACnBT,WAAW,EAAC;UACd,CAAC;QACH;MACF,CAAC;IACH;IACA;IACA,MAAMoB,oBAAmB,GAAKrD,GAAG,IAAK;MACpCwB,oBAAoB,CAAC/D,KAAK,CAACE,GAAE,GAAIqC,GAAG,CAACtE,IAAG;MACxC4G,eAAe,CAAC7E,KAAK,CAACM,KAAI,GAAI,CAC1B;QACExC,IAAI,EAAEiG,oBAAoB,CAAC/D,KAAK,CAAC4D,KAAI,GAAI,MAAM;QAC/C1D,GAAG,EAAEqC,GAAG,CAACtE;MACX,EACJ;IACF;IACA;IACA,MAAM4H,mBAAkB,GAAIA,CAAA,KAAM;MAChC9B,oBAAoB,CAAC/D,KAAK,CAACE,GAAE,GAAI,EAAC;MAClC2E,eAAe,CAAC7E,KAAK,CAACM,KAAI,GAAI,EAAC;IACjC;IACA,MAAMwF,mBAAkB,GAAKC,IAAI,IAAK;MACpC,IAAIC,QAAO,GAAIC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;MACxC,IAAII,YAAW,GAAI,IAAIC,KAAK,CAACJ,QAAQ,CAAC;MACtCG,YAAY,CAACX,gBAAgB,CAAC,gBAAgB,EAAE,MAAM;QACpD;QACAzB,oBAAoB,CAAC/D,KAAK,CAACkE,SAAQ,GAAIiC,YAAY,CAACV,QAAQ;MAC9D,CAAC,CAAC;IACJ;IACA;IACA,MAAMY,iBAAgB,GAAIA,CAAA,KAAM;MAC9BtH,OAAO,CAACC,GAAG,CAACwE,WAAW,CAACxD,KAAK;MAC7B,MAAMsG,WAAU,GAAI,EAAC;MACrB,KAAK,MAAMjC,OAAM,IAAKb,WAAW,CAACxD,KAAK,EAAE;QACvC,MAAMuG,OAAM,GAAI,EAAC;QACjB,IAAIlC,OAAO,CAACmC,kBAAiB,IAAKnC,OAAO,CAACmC,kBAAkB,CAAC1G,MAAM,EAAE;UACnE,KAAK,MAAM2G,GAAE,IAAKpC,OAAO,CAACmC,kBAAkB,EAAE;YAC5CD,OAAO,CAAC9H,IAAI,CAAC;cAACa,EAAE,EAAEmH,GAAG,CAACnH,EAAE;cAAEmF,IAAI,EAAE;YAAE,CAAC;UACrC;QACF;QACA6B,WAAW,CAAC7H,IAAI,CAAC;UAACa,EAAE,EAAE+E,OAAO,CAAC/E,EAAE;UAAEmF,IAAI,EAAE8B;QAAO,CAAC,CAAC;MACnD;MACA,MAAMG,MAAK,GAAI;QAACpH,EAAE,EAAEiC,MAAM,CAACvB,KAAK,CAACV,EAAE;QAAEmF,IAAI,EAAE6B;MAAW;MACtDjJ,eAAe,CAACqJ,MAAM,EAAE,MAAM;QAC5BpK,OAAO,CAAC,QAAQ;MAClB,CAAC;MACDyC,OAAO,CAACC,GAAG,CAAC0H,MAAM;IACpB;IACA;IACA,MAAMC,WAAU,GAAIxK,GAAG,CAAC,IAAI;IAC5B,MAAMyK,aAAY,GAAIzK,GAAG,CAAC;MACxBkI,OAAO,EAAE,CAAC;QAAEnC,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC;IACnE,CAAC;IACD;IACA,MAAMyE,iCAAgC,GAAKtE,GAAG,IAAK;MACjD6B,QAAQ,CAACpE,KAAK,CAACsE,UAAS,GAAI/B,GAAG,CAACtE,IAAG;IACrC;IACA;IACA,MAAM6I,gCAA+B,GAAIA,CAAA,KAAM;MAC7C1C,QAAQ,CAACpE,KAAK,CAACsE,UAAS,GAAI,EAAC;MAC7BC,kBAAkB,CAACvE,KAAK,CAACM,KAAI,GAAI,EAAC;IACpC;IACA,MAAMyG,cAAa,GAAIA,CAAA,KAAM;MAC3B3C,QAAQ,CAACpE,KAAK,CAAC2D,QAAO,GAAIxE,KAAK,CAACE,KAAK,CAACC,EAAC,IAAKiC,MAAM,CAACvB,KAAK,CAACV,EAAC;MAC1DqH,WAAW,CAAC3G,KAAK,CAACoD,QAAQ,CAAEC,KAAK,IAAK;QACpC,IAAI,CAACA,KAAK,EAAE;UAAC,OAAO,KAAK;QAAA;QACzB,IAAIe,QAAQ,CAACpE,KAAK,CAACV,EAAE,EAAE;UACrB5B,cAAc,CAAC0G,QAAQ,CAACpE,KAAK,EAAE,MAAM;YACnC1D,OAAO,CAAC,MAAM;YACdiD,QAAQ,CAACS,KAAI,GAAI,SAAS;YAC1B,IAAIsD,IAAG,GAAInE,KAAK,CAACoE,QAAQ;YACzBvH,MAAM,CAACyC,IAAI,CAAC;cAAC6E,IAAI;cAAEjE,KAAK,EAAE;gBAACC,EAAE,EAAEiC,MAAM,CAACvB,KAAK,CAACV,EAAE;gBAAES,IAAI,EAAE;cAAS;YAAE,CAAC,CAAC;UACrE,CAAC;QACH,OAAO;UACLtC,YAAY,CAAC2G,QAAQ,CAACpE,KAAK,EAAGuC,GAAG,IAAK;YACpC6B,QAAQ,CAACpE,KAAI,GAAIuC,GAAE;YACnBjG,OAAO,CAAC,MAAM;YACdiD,QAAQ,CAACS,KAAI,GAAI,SAAS;YAC1B,IAAIsD,IAAG,GAAInE,KAAK,CAACoE,QAAQ;YACzBvH,MAAM,CAACyC,IAAI,CAAC;cAAC6E,IAAI;cAAEjE,KAAK,EAAE;gBAACC,EAAE,EAAEiC,MAAM,CAACvB,KAAK,CAACV,EAAE;gBAAES,IAAI,EAAE;cAAS;YAAE,CAAC,CAAC;UACrE,CAAC;QACH;MACF,CAAC;IACH;IACA;IACA,MAAMiH,SAAQ,GAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE;IACX;IACA,MAAMC,OAAM,GAAIA,CAAA,KAAM;MACpBrK,aAAa,CAAC;QAACuC,EAAE,EAAEiC,MAAM,CAACvB,KAAK,CAACV;MAAE,CAAC,EAAE,MAAM;QACzChD,OAAO,CAAC,MAAM;QACdiF,MAAM,CAACvB,KAAK,CAACqH,MAAK,GAAI,WAAU;MAClC,CAAC;IACH;IACA,MAAMC,SAAQ,GAAIA,CAAA,KAAM;MACtBtK,eAAe,CAAC;QAACsC,EAAE,EAAEiC,MAAM,CAACvB,KAAK,CAACV;MAAE,CAAC,EAAE,MAAM;QAC3ChD,OAAO,CAAC,QAAQ;QAChBiF,MAAM,CAACvB,KAAK,CAACqH,MAAK,GAAI,aAAY;MACpC,CAAC;IACH;IACA;IACA,MAAME,IAAG,GAAIA,CAAA,KAAM;MACjB;MACA,IAAIpI,KAAK,CAACE,KAAK,CAACU,IAAI,EAAE;QACpBR,QAAQ,CAACS,KAAI,GAAIb,KAAK,CAACE,KAAK,CAACU,IAAI;MACnC,OAAO;QACLR,QAAQ,CAACS,KAAI,GAAI,MAAK;MACxB;MACAuB,MAAM,CAACvB,KAAK,CAACV,EAAC,GAAIH,KAAK,CAACE,KAAK,CAACC,EAAC,IAAK,EAAC;MACrCqD,YAAY,EAAE;MACd6B,WAAW,EAAE;IACf;IACA+C,IAAI,EAAC;IACL;IACA,MAAMC,SAAQ,GAAK/H,GAAG,IAAK;MACzB,IAAI,CAACL,QAAO,IAAKO,mBAAmB,EAAE,CAACnB,OAAO,CAACiB,GAAG,IAAI,CAAC,EAAE;QACvD;MACF;MACAF,QAAQ,CAACS,KAAI,GAAIP,GAAG;MACpB,IAAI6D,IAAG,GAAInE,KAAK,CAACoE,QAAQ;MACzBvH,MAAM,CAACyC,IAAI,CAAC;QAAC6E,IAAI;QAAEjE,KAAK,EAAE;UAACC,EAAE,EAAEiC,MAAM,CAACvB,KAAK,CAACV,EAAE;UAAES,IAAI,EAAEN;QAAG;MAAE,CAAC,CAAC;IAC/D;IACAE,mBAAmB,EAAE;;IAErB;IACA,MAAM8C,mBAAkB,GAAItG,GAAG,CAAC,CAAC,CAAC;IAClC,MAAMsL,2BAA0B,GAAItL,GAAG,CAAC,KAAK;IAC7C,MAAMuL,uBAAsB,GAAIA,CAAA,KAAM;MACpCD,2BAA2B,CAACzH,KAAI,GAAI,IAAG;IACzC;IACA,MAAM2H,uBAAsB,GAAIA,CAAA,KAAM;MACpCF,2BAA2B,CAACzH,KAAI,GAAI,KAAI;IAC1C;IACA,MAAM4H,yBAAwB,GAAK/E,GAAG,IAAK;MACzC9D,OAAO,CAACC,GAAG,CAAC,KAAK,EAAE6D,GAAG;MACtB,IAAIA,GAAG,CAAC/C,MAAK,GAAI,CAAC,EAAE;QAClBtD,KAAK,CAAC,UAAU,CAAC;QACjB;MACF;MACA,IAAIqG,GAAG,CAAC/C,MAAK,GAAI,CAAC,EAAE;QAClB2C,mBAAmB,CAACzC,KAAI,GAAI6C,GAAG,CAAC,CAAC;QACjC,IAAIJ,mBAAmB,CAACzC,KAAK,EAAE;UAC7BuB,MAAM,CAACvB,KAAK,CAAC6H,aAAY,GAAIpF,mBAAmB,CAACzC,KAAK,CAACV,EAAC;QAC1D;MACF;MACAP,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEuC,MAAM,CAACvB,KAAK,CAAC6H,aAAa;MACpEF,uBAAuB,EAAC;IAC1B;IACA,MAAMG,mBAAkB,GAAI3L,GAAG,CAAC,KAAK,CAAC;IACtC,MAAM4L,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAI,CAACtF,mBAAmB,CAACzC,KAAK,CAACV,EAAE,EAAE;QACjC9C,KAAK,CAAC,QAAQ;QACd;MACF;MACAsL,mBAAmB,CAAC9H,KAAI,GAAI,IAAI;IAClC;IACA,MAAMgI,WAAU,GAAIA,CAAA,KAAM;MACxBF,mBAAmB,CAAC9H,KAAI,GAAI,KAAK;IACnC;IACA,MAAMiI,yBAAwB,GAAIA,CAAA,KAAM;MACtC,IAAI,CAACxF,mBAAmB,CAACzC,KAAK,CAACV,EAAE,EAAE;QACjC9C,KAAK,CAAC,QAAQ;QACd;MACF;MACA,IAAG,OAAO+E,MAAM,CAACvB,KAAK,CAACwB,SAAQ,IAAK,QAAQ,EAAE;QAC5CD,MAAM,CAACvB,KAAK,CAACwB,SAAQ,GAAI,IAAIf,IAAI,CAACc,MAAM,CAACvB,KAAK,CAACwB,SAAS,CAAC;MAC3D;MACA,IAAG,OAAOD,MAAM,CAACvB,KAAK,CAACyB,OAAM,IAAK,QAAQ,EAAE;QAC1CF,MAAM,CAACvB,KAAK,CAACyB,OAAM,GAAI,IAAIhB,IAAI,CAACc,MAAM,CAACvB,KAAK,CAACyB,OAAO,CAAC;MACvD;MACAF,MAAM,CAACvB,KAAK,CAAC6H,aAAY,GAAIpF,mBAAmB,CAACzC,KAAK,CAACV,EAAE;MACzDzC,cAAc,CAAC0E,MAAM,CAACvB,KAAK,EAAE,UAAUuC,GAAG,EAAE;QAC1C,IAAIA,GAAE,IAAKA,GAAG,CAACjD,EAAE,EAAE;UACjBiC,MAAM,CAACvB,KAAI,GAAIuC,GAAG;UAClBjG,OAAO,CAAC,QAAQ;QAClB;MACF,CAAC;IACH;IACA;IACA,OAAO;MACL;MACA+F,WAAW;MACX2F,WAAW;MACXD,WAAW;MACXD,mBAAmB;MACnBrF,mBAAmB;MACnBgF,2BAA2B;MAC3BC,uBAAuB;MACvBC,uBAAuB;MACvBC,yBAAyB;MACzBK,yBAAyB;MACzB;MACAhI,UAAU;MACVoB,eAAe;MACfE,MAAM;MACND,aAAa;MACbW,WAAW;MACXiB,SAAS;MACTN,cAAc;MACdE,eAAe;MACfC,aAAa;MACbC,oBAAoB;MACpBC,mBAAmB;MACnBE,cAAc;MACd;MACAK,WAAW;MACXC,iBAAiB;MACjBC,aAAa;MACbG,kBAAkB;MAClBC,wBAAwB;MACxBC,oBAAoB;MACpBI,yBAAyB;MACzBiB,gBAAgB;MAChBM,uBAAuB;MACvBhB,WAAW;MACXE,WAAW;MACXG,kBAAkB;MAClBE,kBAAkB;MAClBC,aAAa;MACbC,oBAAoB;MACpBE,aAAa;MACbM,oBAAoB;MACpBd,eAAe;MACfS,SAAS;MACTC,OAAO;MACPO,mBAAmB;MACnBF,oBAAoB;MACpBC,mBAAmB;MACnBQ,iBAAiB;MACjB;MACAjC,QAAQ;MACRuC,WAAW;MACXC,aAAa;MACbrC,kBAAkB;MAClBwC,cAAc;MACdF,iCAAiC;MACjCC,gCAAgC;MAChC;MACAE,SAAS;MACTI,OAAO;MACPE,SAAS;MACT;MACA9H,KAAK;MACLE,UAAU;MACVH,QAAQ;MACRiI,SAAS;MACTtI;IACF,CAAC;EACH;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}