{"ast": null, "code": "import { get, post, put, del } from \"@/util/requestUtils\";\nexport function findList(params, success) {\n  return get(\"/exam/question-lib/question\", params, success);\n}\nexport function saveBaseInfo(params, success) {\n  return post(\"/exam/question-lib/question\", params, success);\n}\nexport function updateBaseInfo(params, success) {\n  return put(\"/exam/question-lib/question\", params, success);\n}\nexport function getBaseInfo(id, success) {\n  return get(\"/exam/question-lib/question/\" + id, {}, success);\n}\nexport function delQuestion(id, success) {\n  return del(\"/exam/question-lib/question\", {\n    id: id\n  }, success);\n}", "map": {"version": 3, "names": ["get", "post", "put", "del", "findList", "params", "success", "saveBaseInfo", "updateBaseInfo", "getBaseInfo", "id", "delQuestion"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/api/exam/question-lib/question.js"], "sourcesContent": ["import { get, post, put, del } from \"@/util/requestUtils\"\n\nexport function findList(params, success) {\n  return get(\"/exam/question-lib/question\", params, success)\n}\n\nexport function saveBaseInfo(params, success) {\n  return post(\"/exam/question-lib/question\", params, success)\n}\n\nexport function updateBaseInfo(params, success) {\n  return put(\"/exam/question-lib/question\", params, success)\n}\n\nexport function getBaseInfo(id, success) {\n  return get(\"/exam/question-lib/question/\" + id, {}, success)\n}\n\nexport function delQuestion(id, success) {\n  return del(\"/exam/question-lib/question\", { id: id }, success)\n}\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAQ,qBAAqB;AAEzD,OAAO,SAASC,QAAQA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACxC,OAAON,GAAG,CAAC,6BAA6B,EAAEK,MAAM,EAAEC,OAAO,CAAC;AAC5D;AAEA,OAAO,SAASC,YAAYA,CAACF,MAAM,EAAEC,OAAO,EAAE;EAC5C,OAAOL,IAAI,CAAC,6BAA6B,EAAEI,MAAM,EAAEC,OAAO,CAAC;AAC7D;AAEA,OAAO,SAASE,cAAcA,CAACH,MAAM,EAAEC,OAAO,EAAE;EAC9C,OAAOJ,GAAG,CAAC,6BAA6B,EAAEG,MAAM,EAAEC,OAAO,CAAC;AAC5D;AAEA,OAAO,SAASG,WAAWA,CAACC,EAAE,EAAEJ,OAAO,EAAE;EACvC,OAAON,GAAG,CAAC,8BAA8B,GAAGU,EAAE,EAAE,CAAC,CAAC,EAAEJ,OAAO,CAAC;AAC9D;AAEA,OAAO,SAASK,WAAWA,CAACD,EAAE,EAAEJ,OAAO,EAAE;EACvC,OAAOH,GAAG,CAAC,6BAA6B,EAAE;IAAEO,EAAE,EAAEA;EAAG,CAAC,EAAEJ,OAAO,CAAC;AAChE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}