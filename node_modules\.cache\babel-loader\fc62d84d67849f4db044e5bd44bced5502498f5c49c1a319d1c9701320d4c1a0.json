{"ast": null, "code": "import { ref } from \"vue\";\nimport Page from \"@/components/Page\";\nimport { Search } from \"@element-plus/icons-vue\";\nimport { findCategoryList, toTree } from \"@/api/learn/category\";\nimport { getCompanyStudyReport } from \"@/api/learn/lesson\";\nimport { formatSeconds } from \"@/util/dateUtils\";\nimport { findMemberCompanyList } from \"@/api/member\";\nexport default {\n  name: \"LearnReportIndex\",\n  methods: {\n    formatSeconds\n  },\n  components: {\n    Search,\n    Page\n  },\n  setup() {\n    const loading = ref(true);\n    const total = ref(0);\n    const dataList = ref([]);\n    const c = {\n      current: 1,\n      size: 20,\n      companyIdList: [],\n      year: ''\n    };\n    const params = ref(c);\n    const selectCidList = ref([]);\n    const categoryOptions = ref([]);\n    // 加载分类\n    const loadCategory = () => {\n      findCategoryList(0, true, res => {\n        if (res) {\n          categoryOptions.value = toTree(res);\n        }\n      });\n    };\n    loadCategory();\n    const loadList = () => {\n      loading.value = true;\n      getCompanyStudyReport(params.value, res => {\n        dataList.value = res.list;\n        total.value = res.total;\n        loading.value = false;\n      }).catch(() => {\n        loading.value = false;\n      });\n    };\n    loadList();\n    const currentChange = c => {\n      params.value.current = c;\n      loadList();\n    };\n    const sizeChange = s => {\n      params.value.size = s;\n      loadList();\n    };\n    const search = () => {\n      if (selectCidList.value && selectCidList.value.length > 0) {\n        params.value.cid = selectCidList.value[selectCidList.value.length - 1];\n      }\n      params.value.current = 1;\n      loadList();\n    };\n    const resetParams = () => {\n      params.value = c;\n    };\n    const customIndexFn = index => {\n      return (params.value.current - 1) * params.value.size + index + 1;\n    };\n    const memberCompanyList = ref([]);\n    findMemberCompanyList({\n      current: 1,\n      size: 10000\n    }, resp => {\n      memberCompanyList.value = resp.list;\n    });\n    return {\n      memberCompanyList,\n      customIndexFn,\n      loading,\n      dataList,\n      selectCidList,\n      categoryOptions,\n      params,\n      total,\n      currentChange,\n      sizeChange,\n      search,\n      resetParams\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "Page", "Search", "findCategoryList", "toTree", "getCompanyStudyReport", "formatSeconds", "findMemberCompanyList", "name", "methods", "components", "setup", "loading", "total", "dataList", "c", "current", "size", "companyIdList", "year", "params", "selectCidList", "categoryOptions", "loadCategory", "res", "value", "loadList", "list", "catch", "currentChange", "sizeChange", "s", "search", "length", "cid", "resetParams", "customIndexFn", "index", "memberCompanyList", "resp"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/learn/report/companystudy/index.vue"], "sourcesContent": ["<template>\n  <div class=\"report\">\n    <div class=\"header\">\n      <el-form :inline=\"true\" :model=\"params\" class=\"form-inline\">\n        <el-form-item label=\"公司\" v-if=\"memberCompanyList && memberCompanyList.length\">\n          <el-select v-model=\"params.companyIdList\" clearable multiple filterable @change=\"search\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option v-for=\"company in memberCompanyList\" :label=\"company.name\"  :value=\"company.id\" :key=\"company.id\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"年份\" class=\"select\">\n          <el-input @keydown.enter=\"search\" class=\"search-input\" v-model=\"params.year\" placeholder=\"请输入年份\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"search()\">\n            <el-icon style=\"vertical-align: middle\">\n              <Search />\n            </el-icon>\n            <span style=\"vertical-align: middle\">搜索</span>\n          </el-button>\n          <el-button @click=\"resetParams()\">\n            <span style=\"vertical-align: middle;\">重置</span>\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"report-main\">\n      <el-table :data=\"dataList\" v-loading=\"loading\">\n        <el-table-column label=\"序号\" type=\"index\" :index=\"customIndexFn\"></el-table-column>\n        <el-table-column label=\"公司\" prop=\"companyName\"></el-table-column>\n        <el-table-column label=\"年份\" prop=\"year\"></el-table-column>\n        <el-table-column label=\"报名会员数\" prop=\"signUpMemberQty\"></el-table-column>\n        <el-table-column label=\"报名次数\" prop=\"signUpQty\"></el-table-column>\n        <el-table-column label=\"已取得证书的会员数量\" prop=\"certificateMemberQty\"></el-table-column>\n        <el-table-column label=\"取得的证书数量\" prop=\"certificateQty\"></el-table-column>\n<!--        <el-table-column label=\"公司总会员数\" prop=\"memberQty\"></el-table-column>-->\n      </el-table>\n      <page :total=\"total\" :size-change=\"sizeChange\" :current-change=\"currentChange\" :page-size=\"params.size\"/>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {ref} from \"vue\"\nimport Page from \"@/components/Page\";\nimport {Search} from \"@element-plus/icons-vue\";\nimport {findCategoryList, toTree} from \"@/api/learn/category\";\nimport {getCompanyStudyReport} from \"@/api/learn/lesson\";\nimport {formatSeconds} from \"@/util/dateUtils\";\nimport {findMemberCompanyList} from \"@/api/member\";\nexport default {\n  name: \"LearnReportIndex\",\n  methods: {formatSeconds},\n  components: {Search, Page},\n  setup() {\n    const loading = ref(true)\n    const total = ref(0)\n    const dataList = ref([])\n    const c = {\n      current: 1,\n      size: 20,\n      companyIdList: [],\n      year: ''\n    }\n    const params = ref(c)\n    const selectCidList = ref([])\n    const categoryOptions = ref([])\n    // 加载分类\n    const loadCategory = () => {\n      findCategoryList(0, true, (res) => {if (res) { categoryOptions.value = toTree(res);}})\n    }\n    loadCategory();\n\n    const loadList = () => {\n      loading.value = true\n      getCompanyStudyReport(params.value, res => {\n        dataList.value = res.list\n        total.value = res.total\n        loading.value = false\n      }).catch(() => {\n        loading.value = false\n      })\n    }\n    loadList()\n    const currentChange = (c) => {\n      params.value.current = c;\n      loadList();\n    }\n    const sizeChange = (s) => {\n      params.value.size = s;\n      loadList();\n    }\n    const search = () => {\n      if (selectCidList.value && selectCidList.value.length > 0) {\n        params.value.cid = selectCidList.value[selectCidList.value.length - 1];\n      }\n      params.value.current = 1\n      loadList()\n    }\n    const resetParams = () => {\n      params.value = c\n    }\n    const customIndexFn = (index) => {\n      return (params.value.current - 1) * params.value.size + index + 1;\n    }\n    const memberCompanyList = ref([])\n    findMemberCompanyList({current: 1, size: 10000}, resp => {\n      memberCompanyList.value = resp.list\n    })\n    return {\n      memberCompanyList,\n      customIndexFn,\n      loading,\n      dataList,\n      selectCidList,\n      categoryOptions,\n      params,\n      total,\n      currentChange,\n      sizeChange,\n      search,\n      resetParams\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.report {\n  margin: 20px;\n  font-size: 12px;\n  .report-main {\n    ::v-deep .el-table {\n      font-size: 12px;\n      .el-table__empty-block {\n        line-height: 400px;\n        .el-table__empty-text {\n          line-height: 400px;\n        }\n      }\n      th, td {\n        padding: 6px 0;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": "AA2CA,SAAQA,GAAG,QAAO,KAAI;AACtB,OAAOC,IAAG,MAAO,mBAAmB;AACpC,SAAQC,MAAM,QAAO,yBAAyB;AAC9C,SAAQC,gBAAgB,EAAEC,MAAM,QAAO,sBAAsB;AAC7D,SAAQC,qBAAqB,QAAO,oBAAoB;AACxD,SAAQC,aAAa,QAAO,kBAAkB;AAC9C,SAAQC,qBAAqB,QAAO,cAAc;AAClD,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,OAAO,EAAE;IAACH;EAAa,CAAC;EACxBI,UAAU,EAAE;IAACR,MAAM;IAAED;EAAI,CAAC;EAC1BU,KAAKA,CAAA,EAAG;IACN,MAAMC,OAAM,GAAIZ,GAAG,CAAC,IAAI;IACxB,MAAMa,KAAI,GAAIb,GAAG,CAAC,CAAC;IACnB,MAAMc,QAAO,GAAId,GAAG,CAAC,EAAE;IACvB,MAAMe,CAAA,GAAI;MACRC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,aAAa,EAAE,EAAE;MACjBC,IAAI,EAAE;IACR;IACA,MAAMC,MAAK,GAAIpB,GAAG,CAACe,CAAC;IACpB,MAAMM,aAAY,GAAIrB,GAAG,CAAC,EAAE;IAC5B,MAAMsB,eAAc,GAAItB,GAAG,CAAC,EAAE;IAC9B;IACA,MAAMuB,YAAW,GAAIA,CAAA,KAAM;MACzBpB,gBAAgB,CAAC,CAAC,EAAE,IAAI,EAAGqB,GAAG,IAAK;QAAC,IAAIA,GAAG,EAAE;UAAEF,eAAe,CAACG,KAAI,GAAIrB,MAAM,CAACoB,GAAG,CAAC;QAAC;MAAC,CAAC;IACvF;IACAD,YAAY,EAAE;IAEd,MAAMG,QAAO,GAAIA,CAAA,KAAM;MACrBd,OAAO,CAACa,KAAI,GAAI,IAAG;MACnBpB,qBAAqB,CAACe,MAAM,CAACK,KAAK,EAAED,GAAE,IAAK;QACzCV,QAAQ,CAACW,KAAI,GAAID,GAAG,CAACG,IAAG;QACxBd,KAAK,CAACY,KAAI,GAAID,GAAG,CAACX,KAAI;QACtBD,OAAO,CAACa,KAAI,GAAI,KAAI;MACtB,CAAC,CAAC,CAACG,KAAK,CAAC,MAAM;QACbhB,OAAO,CAACa,KAAI,GAAI,KAAI;MACtB,CAAC;IACH;IACAC,QAAQ,EAAC;IACT,MAAMG,aAAY,GAAKd,CAAC,IAAK;MAC3BK,MAAM,CAACK,KAAK,CAACT,OAAM,GAAID,CAAC;MACxBW,QAAQ,EAAE;IACZ;IACA,MAAMI,UAAS,GAAKC,CAAC,IAAK;MACxBX,MAAM,CAACK,KAAK,CAACR,IAAG,GAAIc,CAAC;MACrBL,QAAQ,EAAE;IACZ;IACA,MAAMM,MAAK,GAAIA,CAAA,KAAM;MACnB,IAAIX,aAAa,CAACI,KAAI,IAAKJ,aAAa,CAACI,KAAK,CAACQ,MAAK,GAAI,CAAC,EAAE;QACzDb,MAAM,CAACK,KAAK,CAACS,GAAE,GAAIb,aAAa,CAACI,KAAK,CAACJ,aAAa,CAACI,KAAK,CAACQ,MAAK,GAAI,CAAC,CAAC;MACxE;MACAb,MAAM,CAACK,KAAK,CAACT,OAAM,GAAI;MACvBU,QAAQ,EAAC;IACX;IACA,MAAMS,WAAU,GAAIA,CAAA,KAAM;MACxBf,MAAM,CAACK,KAAI,GAAIV,CAAA;IACjB;IACA,MAAMqB,aAAY,GAAKC,KAAK,IAAK;MAC/B,OAAO,CAACjB,MAAM,CAACK,KAAK,CAACT,OAAM,GAAI,CAAC,IAAII,MAAM,CAACK,KAAK,CAACR,IAAG,GAAIoB,KAAI,GAAI,CAAC;IACnE;IACA,MAAMC,iBAAgB,GAAItC,GAAG,CAAC,EAAE;IAChCO,qBAAqB,CAAC;MAACS,OAAO,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAK,CAAC,EAAEsB,IAAG,IAAK;MACvDD,iBAAiB,CAACb,KAAI,GAAIc,IAAI,CAACZ,IAAG;IACpC,CAAC;IACD,OAAO;MACLW,iBAAiB;MACjBF,aAAa;MACbxB,OAAO;MACPE,QAAQ;MACRO,aAAa;MACbC,eAAe;MACfF,MAAM;MACNP,KAAK;MACLgB,aAAa;MACbC,UAAU;MACVE,MAAM;MACNG;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}