{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref } from \"vue\";\nimport router from \"@/router\";\nimport { deleteNews, findList, saveNewsTop, deleteNewsTop, saveNewsRecommend, deleteNewsRecommend } from \"@/api/content/news\";\nimport Page from \"@/components/Page\";\nimport { confirm, success } from \"@/util/tipsUtils\";\nimport CommentDrawer from \"@/views/comment/commentDrawer\";\nexport default {\n  name: \"NewsContentIndex\",\n  components: {\n    CommentDrawer,\n    Page\n  },\n  setup() {\n    const statusMap = {\n      \"draft\": \"草稿\",\n      \"published\": \"已发布\",\n      \"deleted\": \"已删除\"\n    };\n    const list = ref([]);\n    const total = ref(0);\n    const dataLoading = ref(true);\n    const searchParam = ref({\n      keyword: \"\",\n      status: \"\",\n      size: 20,\n      current: 1\n    });\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true;\n      findList(searchParam.value, res => {\n        dataLoading.value = false;\n        if (!res) {\n          return;\n        }\n        list.value = res.list;\n        total.value = res.total;\n      });\n    };\n    loadList();\n    // 搜索\n    const search = () => {\n      loadList();\n    };\n    // 编辑\n    const edit = id => {\n      router.push({\n        path: \"/news/edit\",\n        query: {\n          id: id\n        }\n      });\n    };\n    // 编辑\n    const remove = item => {\n      confirm(\"确认删除新闻 \" + item.title + \" 吗？\", \"提示\", () => {\n        deleteNews(item.id, () => {\n          success(\"删除成功\");\n          loadList();\n        });\n      }, () => {});\n    };\n    const currentChange = currentPage => {\n      searchParam.value.current = currentPage;\n      loadList();\n    };\n    const sizeChange = s => {\n      searchParam.value.size = s;\n      loadList();\n    };\n    const selectTopic = ref({});\n    const drawer = ref(false);\n    const drawerClose = done => {\n      drawer.value = false;\n      done();\n    };\n    const commentView = item => {\n      drawer.value = true;\n      selectTopic.value = item;\n    };\n    const top = item => {\n      if (item.top) {\n        deleteNewsTop(item.id, () => {\n          success(\"取消置顶成功\");\n          loadList();\n        });\n      } else {\n        saveNewsTop(item.id, () => {\n          success(\"置顶成功\");\n          loadList();\n        });\n      }\n    };\n    const recommend = item => {\n      if (item.recommend) {\n        deleteNewsRecommend(item.id, () => {\n          success(\"取消推荐成功\");\n          loadList();\n        });\n      } else {\n        saveNewsRecommend(item.id, () => {\n          success(\"推荐成功\");\n          loadList();\n        });\n      }\n    };\n    return {\n      list,\n      total,\n      searchParam,\n      search,\n      edit,\n      currentChange,\n      sizeChange,\n      remove,\n      commentView,\n      selectTopic,\n      drawer,\n      drawerClose,\n      statusMap,\n      dataLoading,\n      top,\n      recommend\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "router", "deleteNews", "findList", "saveNewsTop", "deleteNewsTop", "saveNewsRecommend", "deleteNewsRecommend", "Page", "confirm", "success", "CommentDrawer", "name", "components", "setup", "statusMap", "list", "total", "dataLoading", "searchParam", "keyword", "status", "size", "current", "loadList", "value", "res", "search", "edit", "id", "push", "path", "query", "remove", "item", "title", "currentChange", "currentPage", "sizeChange", "s", "selectTopic", "drawer", "drawerClose", "done", "commentView", "top", "recommend"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/news/content/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"header\">\n      <el-form :inline=\"true\" :model=\"searchParam\" class=\"demo-form-inline\">\n        <el-form-item label=\"\">\n          <el-input size=\"small\" class=\"search-input\" v-model=\"searchParam.keyword\" placeholder=\"请输入关键字\"></el-input>\n          <el-button size=\"small\" class=\"search-btn\" type=\"primary\" @click=\"search\">搜索</el-button>\n        </el-form-item>\n        <el-form-item label=\"状态\" class=\"status\">\n          <el-select size=\"small\" v-model=\"searchParam.status\" @change=\"search\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"草稿\" value=\"draft\"></el-option>\n            <el-option label=\"已发布\" value=\"published\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" type=\"primary\" @click=\"edit()\">\n            <el-icon><Plus /></el-icon>\n            新增\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"content\" v-loading=\"dataLoading\">\n      <div class=\"content-list\">\n        <el-empty v-if=\"!list || !list.length\"/>\n        <div class=\"content-item\" v-for=\"item in list\" :key=\"item.id + ''\">\n          <div class=\"content-item-warp\">\n            <a class=\"image\">\n              <img :src=\"item.image\">\n            </a>\n            <div class=\"article-card-bone\">\n              <div class=\"title-wrap\">\n                <a class=\"title\">{{item.title}}</a>\n                <span class=\"label create-time\">{{item.createTime}}</span>\n              </div>\n              <div class=\"abstruct\">\n                <div class=\"status\" :class=\"item.status\">{{statusMap[item.status]}}</div>\n                <div class=\"divider\" v-if=\"item.top\"></div>\n                <div class=\"status\" style=\"background: #07c160;color: #fff;\" v-if=\"item.top\">已置顶</div>\n                <div class=\"divider\" v-if=\"item.recommend\"></div>\n                <div class=\"status\" style=\"background: green;color: #fff;\" v-if=\"item.recommend\">已推荐</div>\n                <div class=\"divider\"></div>\n                <div class=\"icon\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" tabindex=\"0\">\n                    <g fill=\"none\" fill-rule=\"evenodd\">\n                      <path fill=\"#666\" fill-rule=\"nonzero\" d=\"M10.218 12.852H8.977l-.401-1.226H6.635l-.393 1.226H5L6.957 7.5H8.3l1.917 5.352zm-1.91-2.106L7.704 8.89a2.072 2.072 0 01-.086-.464h-.031a2.253 2.253 0 01-.095.456l-.613 1.87h1.43v-.007zm2.146 2.106V7.508H12.3c1.901 0 2.852.872 2.852 2.609 0 .825-.267 1.493-.801 1.988-.535.503-1.218.755-2.06.755h-1.838v-.008zm1.14-4.417v3.482h.62c.542 0 .967-.165 1.273-.488.307-.322.464-.762.464-1.312 0-.534-.165-.943-.487-1.242-.323-.298-.74-.448-1.258-.448h-.613v.008z\"></path>\n                      <path stroke=\"#FFF\" stroke-width=\"2.5\" d=\"M16.667 3.333L3.333 16.667\"></path>\n                      <path stroke=\"#666\" stroke-width=\"1.5\" d=\"M16.667 3.333L3.333 16.667\"></path>\n                      <path stroke=\"#666\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4.333 3.333h11.334a1 1 0 011 1v11.334a1 1 0 01-1 1H4.333a1 1 0 01-1-1V4.333a1 1 0 011-1z\"></path>\n                    </g>\n                  </svg>\n                </div>\n              </div>\n              <div class=\"count-wrapper\">\n                <ul class=\"count\">\n                  <li>阅读 {{item.watchNum || 0}}</li>\n                  <li>点赞 {{item.likeNum || 0}}</li>\n                  <li>收藏 {{item.favoriteNum || 0}}</li>\n                  <li>评论 {{item.commentNum || 0}}</li>\n                </ul>\n                <div class=\"article-action-list\">\n                  <span class=\"icon-label\" @click=\"top(item)\">{{item.top ? \"取消置顶\" : \"置顶\"}}</span>\n                  <span class=\"icon-label\" @click=\"recommend(item)\">{{item.recommend ? \"取消推荐\" : \"推荐\"}}</span>\n                  <span class=\"icon-label\" @click=\"commentView(item)\">查看评论</span>\n                  <span class=\"icon-label\" @click=\"edit(item.id)\">修改</span>\n                  <span class=\"icon-label\" @click=\"remove(item)\">删除</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <comment-drawer :topic=\"selectTopic\" :show-drawer=\"drawer\" topic-type=\"news\" :drawer-close=\"drawerClose\"/>\n    <page :total=\"total\" :current-change=\"currentChange\" :size-change=\"sizeChange\" :page-size=\"searchParam.size\"></page>\n  </div>\n</template>\n\n<script>\n  import {ref} from \"vue\"\n  import router from \"@/router\"\n  import {deleteNews, findList, saveNewsTop, deleteNewsTop, saveNewsRecommend, deleteNewsRecommend} from \"@/api/content/news\"\n  import Page from \"@/components/Page\"\n  import {confirm, success} from \"@/util/tipsUtils\";\n  import CommentDrawer from \"@/views/comment/commentDrawer\";\n\n  export default {\n    name: \"NewsContentIndex\",\n  components: {\n    CommentDrawer,\n    Page,\n  },\n  setup() {\n    const statusMap = {\n      \"draft\": \"草稿\",\n      \"published\": \"已发布\",\n      \"deleted\": \"已删除\"\n    }\n    const list = ref([])\n    const total = ref(0)\n    const dataLoading = ref(true)\n    const searchParam = ref({\n      keyword: \"\",\n      status: \"\",\n      size: 20,\n      current: 1\n    })\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true\n      findList(searchParam.value, (res) => {\n        dataLoading.value = false\n        if (!res) {return;}\n        list.value = res.list;\n        total.value = res.total;\n      })\n    }\n    loadList();\n    // 搜索\n    const search = () => {\n      loadList();\n    }\n    // 编辑\n    const edit = (id) => {\n      router.push({path: \"/news/edit\", query: { id : id }})\n    }\n    // 编辑\n    const remove = (item) => {\n      confirm(\"确认删除新闻 \" + item.title + \" 吗？\", \"提示\", () => {\n        deleteNews(item.id, () => {\n          success(\"删除成功\")\n          loadList()\n        })\n      }, () => {\n      })\n    }\n    const currentChange = (currentPage) => {\n      searchParam.value.current = currentPage;\n      loadList();\n    }\n    const sizeChange = (s) => {\n      searchParam.value.size = s;\n      loadList();\n    }\n    const selectTopic = ref({})\n    const drawer = ref(false)\n    const drawerClose = (done) => {\n      drawer.value = false\n      done()\n    }\n    const commentView = (item) => {\n      drawer.value = true\n      selectTopic.value = item\n    }\n    const top = (item) => {\n      if (item.top) {\n        deleteNewsTop(item.id, () => {\n          success(\"取消置顶成功\")\n          loadList()\n        })\n      } else {\n        saveNewsTop(item.id, () => {\n          success(\"置顶成功\")\n          loadList()\n        })\n      }\n    }\n    const recommend = (item) => {\n      if (item.recommend) {\n        deleteNewsRecommend(item.id, () => {\n          success(\"取消推荐成功\")\n          loadList()\n        })\n      } else {\n        saveNewsRecommend(item.id, () => {\n          success(\"推荐成功\")\n          loadList()\n        })\n      }\n    }\n    return {\n      list,\n      total,\n      searchParam,\n      search,\n      edit,\n      currentChange,\n      sizeChange,\n      remove,\n      commentView,\n      selectTopic,\n      drawer,\n      drawerClose,\n      statusMap,\n      dataLoading,\n      top,\n      recommend\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n  .app-container {\n    margin: 20px;\n    .content-list {\n      margin: 0;\n      padding: 0;\n      border: 0;\n      font: inherit;\n      vertical-align: baseline;\n      .content-item {\n        padding: 24px 12px;\n        line-height: 1;\n        font-size: 14px;\n        color: #666;\n        border-bottom: 1px solid #e8e8e8;\n        position: relative;\n        background: #ffffff;\n        &:last-child {\n          border-bottom: 0;\n        }\n        .content-item-warp {\n          position: relative;\n          display: flex;\n          .image {\n            width: 168px;\n            min-width: 168px;\n            height: 108px;\n            margin-right: 24px;\n            position: relative;\n            overflow: hidden;\n            border-radius: 4px;\n            border: 1px solid #e8e8e8;\n            cursor: default;\n            img {\n              width: 100%;\n              height: 100%;\n              transition: all .5s ease-out .1s;\n              -o-object-fit: cover;\n              object-fit: cover;\n              -o-object-position: center;\n              object-position: center;\n              &:hover {\n                transform: matrix(1.04,0,0,1.04,0,0);\n                -webkit-backface-visibility: hidden;\n                backface-visibility: hidden;\n              }\n            }\n          }\n          .article-card-bone {\n            width: 100%;\n            display: flex;\n            flex-direction: column;\n            min-width: 0;\n            .title-wrap {\n              display: flex;\n              justify-content: space-between;\n              margin-top: 0;\n              .title {\n                font-size: 16px;\n                overflow: hidden;\n                white-space: nowrap;\n                text-overflow: ellipsis;\n                line-height: 24px;\n                font-weight: 600;\n                display: block;\n                color: #222;\n                cursor: text;\n              }\n              .create-time {\n                color: #999;\n                line-height: 24px;\n                margin-left: 12px;\n                flex-shrink: 0;\n              }\n            }\n            .abstruct {\n              line-height: 20px;\n              margin-top: 20px;\n              height: 20px;\n              display: flex;\n              align-items: flex-end;\n              .status {\n                color: #999;\n                border: none;\n                background-color: #f5f5f5;\n                padding: 0 8px;\n                line-height: 20px;\n                font-size: 12px;\n                border-radius: 2px;\n                white-space: nowrap;\n                display: inline-block;\n                box-sizing: border-box;\n                transition: all .3s;\n                margin-right: 8px;\n              }\n              .published {\n                background-color: #000000;\n                color: #FFFFFF;\n              }\n              .deleted {\n                background-color: #ff5000;\n                color: #FFFFFF;\n              }\n              .article-card .byte-tag-simple {\n                margin-right: 8px;\n              }\n              .divider {\n                width: 1px;\n                height: 12px;\n                margin: 4px 10px 4px 4px;\n                background: #bfbfbf;\n              }\n              .icon {\n                margin-right: 8px;\n                svg {\n                  vertical-align: bottom;\n                  &:focus {\n                    outline: none;\n                  }\n                }\n              }\n            }\n            .count-wrapper {\n              margin-top: 24px;\n              display: flex;\n              justify-content: space-between;\n              .count {\n                line-height: 20px;\n                position: relative;\n                li {\n                  display: inline-block;\n                  margin-right: 24px;\n                  &:after {\n                    content: \"\\ff65\";\n                    font-size: 20px;\n                    margin: 0 8px;\n                    line-height: 0;\n                    position: absolute;\n                    top: 10px;\n                    color: #666;\n                  }\n                  &:last-child:after {\n                    content: \"\"\n                  }\n                }\n              }\n              .article-action-list {\n                display: flex;\n                line-height: 20px;\n                flex: 1 0 auto;\n                justify-content: flex-end;\n                .icon-label {\n                  cursor: pointer;\n                  font-size: 14px;\n                  line-height: 20px;\n                  display: flex;\n                  color: #222;\n                  font-weight: 400;\n                  margin-left: 24px;\n                  &:first-child {\n                    margin-left: 0;\n                  }\n                  &:hover {\n                    color: $--color-primary;\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    .search-input {\n      width: 242px;\n    }\n  }\n</style>\n"], "mappings": ";AAgFE,SAAQA,GAAG,QAAO,KAAI;AACtB,OAAOC,MAAK,MAAO,UAAS;AAC5B,SAAQC,UAAU,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,mBAAmB,QAAO,oBAAmB;AAC1H,OAAOC,IAAG,MAAO,mBAAkB;AACnC,SAAQC,OAAO,EAAEC,OAAO,QAAO,kBAAkB;AACjD,OAAOC,aAAY,MAAO,+BAA+B;AAEzD,eAAe;EACbC,IAAI,EAAE,kBAAkB;EAC1BC,UAAU,EAAE;IACVF,aAAa;IACbH;EACF,CAAC;EACDM,KAAKA,CAAA,EAAG;IACN,MAAMC,SAAQ,GAAI;MAChB,OAAO,EAAE,IAAI;MACb,WAAW,EAAE,KAAK;MAClB,SAAS,EAAE;IACb;IACA,MAAMC,IAAG,GAAIhB,GAAG,CAAC,EAAE;IACnB,MAAMiB,KAAI,GAAIjB,GAAG,CAAC,CAAC;IACnB,MAAMkB,WAAU,GAAIlB,GAAG,CAAC,IAAI;IAC5B,MAAMmB,WAAU,GAAInB,GAAG,CAAC;MACtBoB,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC;IACD;IACA,MAAMC,QAAO,GAAIA,CAAA,KAAM;MACrBN,WAAW,CAACO,KAAI,GAAI,IAAG;MACvBtB,QAAQ,CAACgB,WAAW,CAACM,KAAK,EAAGC,GAAG,IAAK;QACnCR,WAAW,CAACO,KAAI,GAAI,KAAI;QACxB,IAAI,CAACC,GAAG,EAAE;UAAC;QAAO;QAClBV,IAAI,CAACS,KAAI,GAAIC,GAAG,CAACV,IAAI;QACrBC,KAAK,CAACQ,KAAI,GAAIC,GAAG,CAACT,KAAK;MACzB,CAAC;IACH;IACAO,QAAQ,EAAE;IACV;IACA,MAAMG,MAAK,GAAIA,CAAA,KAAM;MACnBH,QAAQ,EAAE;IACZ;IACA;IACA,MAAMI,IAAG,GAAKC,EAAE,IAAK;MACnB5B,MAAM,CAAC6B,IAAI,CAAC;QAACC,IAAI,EAAE,YAAY;QAAEC,KAAK,EAAE;UAAEH,EAAC,EAAIA;QAAG;MAAC,CAAC;IACtD;IACA;IACA,MAAMI,MAAK,GAAKC,IAAI,IAAK;MACvBzB,OAAO,CAAC,SAAQ,GAAIyB,IAAI,CAACC,KAAI,GAAI,KAAK,EAAE,IAAI,EAAE,MAAM;QAClDjC,UAAU,CAACgC,IAAI,CAACL,EAAE,EAAE,MAAM;UACxBnB,OAAO,CAAC,MAAM;UACdc,QAAQ,EAAC;QACX,CAAC;MACH,CAAC,EAAE,MAAM,CACT,CAAC;IACH;IACA,MAAMY,aAAY,GAAKC,WAAW,IAAK;MACrClB,WAAW,CAACM,KAAK,CAACF,OAAM,GAAIc,WAAW;MACvCb,QAAQ,EAAE;IACZ;IACA,MAAMc,UAAS,GAAKC,CAAC,IAAK;MACxBpB,WAAW,CAACM,KAAK,CAACH,IAAG,GAAIiB,CAAC;MAC1Bf,QAAQ,EAAE;IACZ;IACA,MAAMgB,WAAU,GAAIxC,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAMyC,MAAK,GAAIzC,GAAG,CAAC,KAAK;IACxB,MAAM0C,WAAU,GAAKC,IAAI,IAAK;MAC5BF,MAAM,CAAChB,KAAI,GAAI,KAAI;MACnBkB,IAAI,EAAC;IACP;IACA,MAAMC,WAAU,GAAKV,IAAI,IAAK;MAC5BO,MAAM,CAAChB,KAAI,GAAI,IAAG;MAClBe,WAAW,CAACf,KAAI,GAAIS,IAAG;IACzB;IACA,MAAMW,GAAE,GAAKX,IAAI,IAAK;MACpB,IAAIA,IAAI,CAACW,GAAG,EAAE;QACZxC,aAAa,CAAC6B,IAAI,CAACL,EAAE,EAAE,MAAM;UAC3BnB,OAAO,CAAC,QAAQ;UAChBc,QAAQ,EAAC;QACX,CAAC;MACH,OAAO;QACLpB,WAAW,CAAC8B,IAAI,CAACL,EAAE,EAAE,MAAM;UACzBnB,OAAO,CAAC,MAAM;UACdc,QAAQ,EAAC;QACX,CAAC;MACH;IACF;IACA,MAAMsB,SAAQ,GAAKZ,IAAI,IAAK;MAC1B,IAAIA,IAAI,CAACY,SAAS,EAAE;QAClBvC,mBAAmB,CAAC2B,IAAI,CAACL,EAAE,EAAE,MAAM;UACjCnB,OAAO,CAAC,QAAQ;UAChBc,QAAQ,EAAC;QACX,CAAC;MACH,OAAO;QACLlB,iBAAiB,CAAC4B,IAAI,CAACL,EAAE,EAAE,MAAM;UAC/BnB,OAAO,CAAC,MAAM;UACdc,QAAQ,EAAC;QACX,CAAC;MACH;IACF;IACA,OAAO;MACLR,IAAI;MACJC,KAAK;MACLE,WAAW;MACXQ,MAAM;MACNC,IAAI;MACJQ,aAAa;MACbE,UAAU;MACVL,MAAM;MACNW,WAAW;MACXJ,WAAW;MACXC,MAAM;MACNC,WAAW;MACX3B,SAAS;MACTG,WAAW;MACX2B,GAAG;MACHC;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}