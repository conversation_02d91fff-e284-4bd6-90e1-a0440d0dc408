{"ast": null, "code": "import { createApp } from \"vue\";\nimport App from \"./App.vue\";\n// ElementPlus\nimport ElementPlus from '@/plugins/element';\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue';\n// 页面路由\nimport router from \"@/router\";\nimport \"@/router/guard\";\n// SVG图标\n// import svgIcon from \"@/assets/svg\";\n// 缓存\nimport store from \"@/store\";\n// 登录页面\nimport Login from \"@/views/login/Login\";\nconst app = createApp(App);\nexport default app.use(store).use(router).use(ElementPlus).use(Login).mount(\"#app\");\n// ElementPlusIconsVue全部图标\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component);\n}\n// _ResizeObserver报错\nconst debounce = (fn, delay) => {\n  let timer = null;\n  return function () {\n    let context = this;\n    let args = arguments;\n    clearTimeout(timer);\n    timer = setTimeout(function () {\n      fn.apply(context, args);\n    }, delay);\n  };\n};\nconst _ResizeObserver = window.ResizeObserver;\nwindow.ResizeObserver = class ResizeObserver extends _ResizeObserver {\n  constructor(callback) {\n    callback = debounce(callback, 16);\n    super(callback);\n  }\n};", "map": {"version": 3, "names": ["createApp", "App", "ElementPlus", "ElementPlusIconsVue", "router", "store", "<PERSON><PERSON>", "app", "use", "mount", "key", "component", "Object", "entries", "debounce", "fn", "delay", "timer", "context", "args", "arguments", "clearTimeout", "setTimeout", "apply", "_ResizeObserver", "window", "ResizeObserver", "constructor", "callback"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/main.js"], "sourcesContent": ["import { createApp } from \"vue\";\nimport App from \"./App.vue\";\n// ElementPlus\nimport ElementPlus from '@/plugins/element'\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue'\n// 页面路由\nimport router from \"@/router\";\nimport \"@/router/guard\";\n// SVG图标\n// import svgIcon from \"@/assets/svg\";\n// 缓存\nimport store from \"@/store\";\n// 登录页面\nimport Login from \"@/views/login/Login\";\n\nconst app = createApp(App)\nexport default app.use(store).use(router).use(ElementPlus).use(Login).mount(\"#app\");\n// ElementPlusIconsVue全部图标\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component)\n}\n// _ResizeObserver报错\nconst debounce = (fn, delay) => {\n  let timer = null;\n  return function () {\n    let context = this;\n    let args = arguments;\n    clearTimeout(timer);\n    timer = setTimeout(function () {\n      fn.apply(context, args);\n    }, delay);\n  }\n}\nconst _ResizeObserver = window.ResizeObserver;\nwindow.ResizeObserver = class ResizeObserver extends _ResizeObserver{\n  constructor(callback) {\n    callback = debounce(callback, 16);\n    super(callback);\n  }\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B;AACA,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAO,KAAKC,mBAAmB,MAAM,yBAAyB;AAC9D;AACA,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAO,gBAAgB;AACvB;AACA;AACA;AACA,OAAOC,KAAK,MAAM,SAAS;AAC3B;AACA,OAAOC,KAAK,MAAM,qBAAqB;AAEvC,MAAMC,GAAG,GAAGP,SAAS,CAACC,GAAG,CAAC;AAC1B,eAAeM,GAAG,CAACC,GAAG,CAACH,KAAK,CAAC,CAACG,GAAG,CAACJ,MAAM,CAAC,CAACI,GAAG,CAACN,WAAW,CAAC,CAACM,GAAG,CAACF,KAAK,CAAC,CAACG,KAAK,CAAC,MAAM,CAAC;AACnF;AACA,KAAK,MAAM,CAACC,GAAG,EAAEC,SAAS,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACV,mBAAmB,CAAC,EAAE;EAClEI,GAAG,CAACI,SAAS,CAACD,GAAG,EAAEC,SAAS,CAAC;AAC/B;AACA;AACA,MAAMG,QAAQ,GAAGA,CAACC,EAAE,EAAEC,KAAK,KAAK;EAC9B,IAAIC,KAAK,GAAG,IAAI;EAChB,OAAO,YAAY;IACjB,IAAIC,OAAO,GAAG,IAAI;IAClB,IAAIC,IAAI,GAAGC,SAAS;IACpBC,YAAY,CAACJ,KAAK,CAAC;IACnBA,KAAK,GAAGK,UAAU,CAAC,YAAY;MAC7BP,EAAE,CAACQ,KAAK,CAACL,OAAO,EAAEC,IAAI,CAAC;IACzB,CAAC,EAAEH,KAAK,CAAC;EACX,CAAC;AACH,CAAC;AACD,MAAMQ,eAAe,GAAGC,MAAM,CAACC,cAAc;AAC7CD,MAAM,CAACC,cAAc,GAAG,MAAMA,cAAc,SAASF,eAAe;EAClEG,WAAWA,CAACC,QAAQ,EAAE;IACpBA,QAAQ,GAAGd,QAAQ,CAACc,QAAQ,EAAE,EAAE,CAAC;IACjC,KAAK,CAACA,QAAQ,CAAC;EACjB;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}