{"ast": null, "code": "import { resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-4aa088a9\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"category-edit\"\n};\nconst _hoisted_2 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_cascader = _resolveComponent(\"el-cascader\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_switch = _resolveComponent(\"el-switch\");\n  const _component_upload_image = _resolveComponent(\"upload-image\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"categoryRef\",\n    rules: $setup.rules,\n    model: $setup.category,\n    \"label-width\": \"120px\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      label: \"上级分类：\",\n      prop: \"pid\"\n    }, {\n      default: _withCtx(() => [$setup.parentCategory.name ? (_openBlock(), _createBlock(_component_el_input, {\n        key: 0,\n        size: \"small\",\n        type: \"text\",\n        class: \"input-text\",\n        disabled: \"\",\n        modelValue: $setup.parentCategory.name,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.parentCategory.name = $event)\n      }, null, 8 /* PROPS */, [\"modelValue\"])) : (_openBlock(), _createBlock(_component_el_cascader, {\n        key: 1,\n        class: \"input-text\",\n        props: {\n          checkStrictly: true\n        },\n        modelValue: $setup.selectedPidList,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.selectedPidList = $event),\n        options: $setup.categoryOptions,\n        placeholder: \"请选择上级分类\",\n        onChange: $setup.changeParentCategory\n      }, null, 8 /* PROPS */, [\"modelValue\", \"options\", \"onChange\"]))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"分类名称：\",\n      prop: \"name\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"small\",\n        maxlength: \"15\",\n        \"show-word-limit\": \"\",\n        class: \"input-text\",\n        modelValue: $setup.category.name,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.category.name = $event)\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"排序：\",\n      prop: \"sortOrder\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"small\",\n        class: \"input-text\",\n        modelValue: $setup.category.sortOrder,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.category.sortOrder = $event),\n        placeholder: \"数据越大显示越前\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"是否显示：\",\n      prop: \"isShow\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_switch, {\n        size: \"small\",\n        id: \"isShow\",\n        \"active-color\": \"#13ce66\",\n        modelValue: $setup.category.isShow,\n        \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.category.isShow = $event)\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"首页显示：\",\n      prop: \"isShowIndex\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_switch, {\n        size: \"small\",\n        id: \"isShowIndex\",\n        \"active-color\": \"#13ce66\",\n        modelValue: $setup.category.isShowIndex,\n        \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.category.isShowIndex = $event)\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"分类图片：\",\n      prop: \"image\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_upload_image, {\n        limit: 1,\n        files: $setup.uploadData.files,\n        \"on-upload-success\": $setup.onUploadSuccess,\n        \"on-upload-remove\": $setup.onUploadRemove,\n        \"upload-url\": $setup.uploadData.url\n      }, null, 8 /* PROPS */, [\"files\", \"on-upload-success\", \"on-upload-remove\", \"upload-url\"])]),\n      _: 1 /* STABLE */\n    })]),\n\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"rules\", \"model\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n    size: \"small\",\n    onClick: _cache[6] || (_cache[6] = $event => $setup.cancel())\n  }, {\n    default: _withCtx(() => [_createTextVNode(\"取 消\")]),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_button, {\n    size: \"small\",\n    type: \"primary\",\n    onClick: _cache[7] || (_cache[7] = $event => $setup.submit())\n  }, {\n    default: _withCtx(() => [_createTextVNode(\"确 定\")]),\n    _: 1 /* STABLE */\n  })])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "rules", "$setup", "model", "category", "_component_el_form_item", "label", "prop", "parentCategory", "name", "_createBlock", "_component_el_input", "size", "type", "disabled", "$event", "_component_el_cascader", "props", "checkStrictly", "selectedPidList", "options", "categoryOptions", "placeholder", "onChange", "changeParentCategory", "maxlength", "sortOrder", "_component_el_switch", "id", "isShow", "isShowIndex", "_component_upload_image", "limit", "files", "uploadData", "onUploadSuccess", "onUploadRemove", "url", "_createElementVNode", "_hoisted_2", "_component_el_button", "onClick", "_cache", "cancel", "submit"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/learn/topic/category/edit/index.vue"], "sourcesContent": ["<template>\n  <div class=\"category-edit\">\n    <el-form ref=\"categoryRef\" :rules=\"rules\" :model=\"category\" label-width=\"120px\">\n      <el-form-item label=\"上级分类：\" prop=\"pid\">\n        <el-input size=\"small\" v-if=\"parentCategory.name\" type=\"text\" class=\"input-text\" disabled v-model=\"parentCategory.name\"></el-input>\n        <el-cascader v-else class=\"input-text\" :props=\"{checkStrictly: true}\" v-model=\"selectedPidList\" :options=\"categoryOptions\" placeholder=\"请选择上级分类\" @change=\"changeParentCategory\"></el-cascader>\n      </el-form-item>\n      <el-form-item label=\"分类名称：\" prop=\"name\">\n        <el-input size=\"small\" maxlength=\"15\" show-word-limit class=\"input-text\" v-model=\"category.name\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"排序：\" prop=\"sortOrder\">\n        <el-input size=\"small\" class=\"input-text\" v-model=\"category.sortOrder\" placeholder=\"数据越大显示越前\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"是否显示：\" prop=\"isShow\">\n        <el-switch size=\"small\" id=\"isShow\" active-color=\"#13ce66\" v-model=\"category.isShow\"></el-switch>\n      </el-form-item>\n      <el-form-item label=\"首页显示：\" prop=\"isShowIndex\">\n        <el-switch size=\"small\" id=\"isShowIndex\" active-color=\"#13ce66\" v-model=\"category.isShowIndex\"></el-switch>\n      </el-form-item>\n      <el-form-item label=\"分类图片：\" prop=\"image\">\n        <upload-image :limit=\"1\" :files=\"uploadData.files\" :on-upload-success=\"onUploadSuccess\" :on-upload-remove=\"onUploadRemove\" :upload-url=\"uploadData.url\"></upload-image>\n      </el-form-item>\n    </el-form>\n    <div class=\"dialog-footer\">\n      <el-button size=\"small\" @click=\"cancel()\">取 消</el-button>\n      <el-button size=\"small\" type=\"primary\" @click=\"submit()\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\n  import router from \"@/router\"\n  import uploadImage from \"@/components/Uplaod\";\n  import {ref, watch} from \"vue\"\n  import {findCategoryList, toTree, getCategory, saveCategory, updateCategory} from \"@/api/learn/topicCategory\"\n  import {success, error} from \"@/util/tipsUtils\";\n  export default {\n    name: \"LearnTopicCategoryEdit\",\n    components: {\n      uploadImage\n    },\n    props: {\n      data: {\n        type: Object,\n        required: true\n      },\n      pid: {\n        type: Number,\n        required: true\n      },\n      editSuccess: {\n        type: Function\n      },\n      editCancel: {\n        type: Function\n      }\n    },\n    setup(props) {\n      let selectedPidList = ref([])\n      const categoryOptions = ref([])\n      const parentCategory = ref({})\n      const uploadData = {\n        url: process.env.VUE_APP_BASE_API + \"/oss/learn/topic/image\",\n        files: []\n      }\n      const rules = {\n        pid: [{ required: true, message: \"请选择上级分类\", trigger: \"blur\" }],\n        name: [{ required: true, message: \"请输入分类名称\", trigger: \"blur\" }],\n        picture: [{ required: true, message: \"请上传分类图片\", trigger: \"blur\" }]\n      }\n      let category = ref({\n        pid: 0,\n        name: \"\",\n        image: \"\",\n        sortOrder: 1,\n        isShow: true,\n        isShowIndex: true\n      })\n      const init = (item, pid) => {\n        if (pid) {\n          getCategory(pid, res => {\n            if (!res) {\n              error(\"没有找到该分类\")\n              return;\n            }\n            parentCategory.value = res;\n          });\n        } else {\n          parentCategory.value = {id: 0, name: \"全部\"};\n        }\n        if (item && item.id) {\n          category = ref(item);\n          if (item.image) {\n            uploadData.files = [{name: item.name, url: item.image}]\n          }\n        }\n        category.value.pid = pid || 0;\n        selectedPidList.value.push(category.value.pid);\n      }\n      init(props.data, props.pid)\n      watch(() => props.data, (nv) => {\n        init(nv, nv.pid)\n        category = ref(nv)\n      })\n      const loadCategory = () => {\n        findCategoryList(0, true).then(function (response) {\n          if (response) {\n            categoryOptions.value = toTree(response);\n          }\n        });\n      }\n      loadCategory();\n      const changeParentCategory = () => {\n        if (category.value.selectedPidList && category.value.selectedPidList.length > 0) {\n          let id = selectedPidList.value[selectedPidList.value.length - 1];\n          if (id === category.value.id) {\n            error(\"不能选择自己为上级分类\")\n            return;\n          }\n          category.value.pid = id;\n        }\n      }\n      const cancel = () => {\n        props.editCancel && props.editCancel()\n      }\n      const onUploadSuccess = (res) => {\n        category.value.image = res.data;\n      }\n      const onUploadRemove = () => {\n        if (!category.value.image) {\n          return;\n        }\n        category.value.image = \"\";\n        uploadData.value.files = [];\n      }\n      const categoryRef = ref(null)\n      const submit = () => {\n        categoryRef.value.validate(valid => {\n          if (!valid) {\n            return false;\n          }\n          if (!category.value.pid && category.value.pid !== 0) {\n            error(\"请选择上级分类\")\n            return false;\n          }\n          if (category.value.id) {\n            updateCategory(category.value, (res) => {\n              success(\"编辑成功\")\n              router.push({path: \"/learn/topic/category\", query:{ id: res[\"id\"]}});\n              props.editSuccess && props.editSuccess(res[\"id\"])\n            })\n          } else {\n            saveCategory(category.value, (res) => {\n              success(\"新增成功\")\n              router.push({path: \"/learn/topic/category\", query:{ id: res[\"id\"]}});\n              props.editSuccess && props.editSuccess(res[\"id\"])\n            })\n          }\n        });\n      }\n      return {\n        selectedPidList,\n        categoryOptions,\n        parentCategory,\n        category,\n        rules,\n        uploadData,\n        categoryRef,\n        loadCategory,\n        changeParentCategory,\n        cancel,\n        onUploadSuccess,\n        onUploadRemove,\n        submit\n      }\n    }\n  }\n</script>\n<style scoped lang=\"scss\">\n.category-edit {\n  ::v-deep .el-upload-list--picture-card .el-upload-list__item {\n    width: 260px;\n    height: 160px;\n  }\n  .dialog-footer {\n    padding-top: 20px;\n    text-align: center;\n    ::v-deep .el-button {\n      border-color: #f3f5f8;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAe;;EAsBnBA,KAAK,EAAC;AAAe;;;;;;;;;uBAtB5BC,mBAAA,CA0BM,OA1BNC,UA0BM,GAzBJC,YAAA,CAoBUC,kBAAA;IApBDC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAEC,MAAA,CAAAD,KAAK;IAAGE,KAAK,EAAED,MAAA,CAAAE,QAAQ;IAAE,aAAW,EAAC;;sBACtE,MAGe,CAHfN,YAAA,CAGeO,uBAAA;MAHDC,KAAK,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAC/B,MAAmI,CAAtGL,MAAA,CAAAM,cAAc,CAACC,IAAI,I,cAAhDC,YAAA,CAAmIC,mBAAA;;QAAzHC,IAAI,EAAC,OAAO;QAA4BC,IAAI,EAAC,MAAM;QAAClB,KAAK,EAAC,YAAY;QAACmB,QAAQ,EAAR,EAAQ;oBAAUZ,MAAA,CAAAM,cAAc,CAACC,IAAI;mEAAnBP,MAAA,CAAAM,cAAc,CAACC,IAAI,GAAAM,MAAA;gEACtHL,YAAA,CAA8LM,sBAAA;;QAA1KrB,KAAK,EAAC,YAAY;QAAEsB,KAAK,EAAE;UAAAC,aAAA;QAAA,CAAqB;oBAAWhB,MAAA,CAAAiB,eAAe;mEAAfjB,MAAA,CAAAiB,eAAe,GAAAJ,MAAA;QAAGK,OAAO,EAAElB,MAAA,CAAAmB,eAAe;QAAEC,WAAW,EAAC,SAAS;QAAEC,QAAM,EAAErB,MAAA,CAAAsB;;;QAE5J1B,YAAA,CAEeO,uBAAA;MAFDC,KAAK,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAC/B,MAA4G,CAA5GT,YAAA,CAA4Ga,mBAAA;QAAlGC,IAAI,EAAC,OAAO;QAACa,SAAS,EAAC,IAAI;QAAC,iBAAe,EAAf,EAAe;QAAC9B,KAAK,EAAC,YAAY;oBAAUO,MAAA,CAAAE,QAAQ,CAACK,IAAI;mEAAbP,MAAA,CAAAE,QAAQ,CAACK,IAAI,GAAAM,MAAA;;;QAEjGjB,YAAA,CAEeO,uBAAA;MAFDC,KAAK,EAAC,KAAK;MAACC,IAAI,EAAC;;wBAC7B,MAAyG,CAAzGT,YAAA,CAAyGa,mBAAA;QAA/FC,IAAI,EAAC,OAAO;QAACjB,KAAK,EAAC,YAAY;oBAAUO,MAAA,CAAAE,QAAQ,CAACsB,SAAS;mEAAlBxB,MAAA,CAAAE,QAAQ,CAACsB,SAAS,GAAAX,MAAA;QAAEO,WAAW,EAAC;;;QAErFxB,YAAA,CAEeO,uBAAA;MAFDC,KAAK,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAC/B,MAAiG,CAAjGT,YAAA,CAAiG6B,oBAAA;QAAtFf,IAAI,EAAC,OAAO;QAACgB,EAAE,EAAC,QAAQ;QAAC,cAAY,EAAC,SAAS;oBAAU1B,MAAA,CAAAE,QAAQ,CAACyB,MAAM;mEAAf3B,MAAA,CAAAE,QAAQ,CAACyB,MAAM,GAAAd,MAAA;;;QAErFjB,YAAA,CAEeO,uBAAA;MAFDC,KAAK,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAC/B,MAA2G,CAA3GT,YAAA,CAA2G6B,oBAAA;QAAhGf,IAAI,EAAC,OAAO;QAACgB,EAAE,EAAC,aAAa;QAAC,cAAY,EAAC,SAAS;oBAAU1B,MAAA,CAAAE,QAAQ,CAAC0B,WAAW;mEAApB5B,MAAA,CAAAE,QAAQ,CAAC0B,WAAW,GAAAf,MAAA;;;QAE/FjB,YAAA,CAEeO,uBAAA;MAFDC,KAAK,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAC/B,MAAuK,CAAvKT,YAAA,CAAuKiC,uBAAA;QAAxJC,KAAK,EAAE,CAAC;QAAGC,KAAK,EAAE/B,MAAA,CAAAgC,UAAU,CAACD,KAAK;QAAG,mBAAiB,EAAE/B,MAAA,CAAAiC,eAAe;QAAG,kBAAgB,EAAEjC,MAAA,CAAAkC,cAAc;QAAG,YAAU,EAAElC,MAAA,CAAAgC,UAAU,CAACG;;;;;;yCAGvJC,mBAAA,CAGM,OAHNC,UAGM,GAFJzC,YAAA,CAAyD0C,oBAAA;IAA9C5B,IAAI,EAAC,OAAO;IAAE6B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA3B,MAAA,IAAEb,MAAA,CAAAyC,MAAM;;sBAAI,MAAG,C,iBAAH,KAAG,E;;MAC7C7C,YAAA,CAAwE0C,oBAAA;IAA7D5B,IAAI,EAAC,OAAO;IAACC,IAAI,EAAC,SAAS;IAAE4B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA3B,MAAA,IAAEb,MAAA,CAAA0C,MAAM;;sBAAI,MAAG,C,iBAAH,KAAG,E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}