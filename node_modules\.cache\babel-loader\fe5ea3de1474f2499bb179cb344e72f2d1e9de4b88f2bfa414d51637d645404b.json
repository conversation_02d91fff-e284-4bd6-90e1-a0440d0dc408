{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref } from \"vue\";\nimport { findCategoryList, toTree, getAllParent } from \"@/api/exam/question-lib/category\";\nimport { saveBaseInfo, updateBaseInfo, getBaseInfo } from \"@/api/exam/question-lib/question\";\nimport { useRoute } from \"vue-router\";\nimport { success } from \"@/util/tipsUtils\";\nimport router from \"@/router\";\nexport default {\n  name: \"ExamQuestionLibSingleChoice\",\n  setup() {\n    const route = useRoute();\n    const colors = [\"#99A9BF\", \"#F7BA2A\", \"#FF9900\"];\n    const question = ref({\n      id: \"\",\n      title: \"\",\n      note: \"\",\n      type: \"single_choice\",\n      score: \"\",\n      difficulty: 2,\n      referenceAnswer: \"\",\n      referenceAnswerNote: \"\",\n      options: \"\",\n      cidList: []\n    });\n    const questionRules = {\n      title: [{\n        required: true,\n        message: \"请输入题干\",\n        trigger: \"blur\"\n      }],\n      score: [{\n        required: true,\n        message: \"请输入分数\",\n        trigger: \"blur\"\n      }],\n      cidList: [{\n        required: true,\n        message: \"请选择分类\",\n        trigger: \"change\"\n      }],\n      referenceAnswer: [{\n        required: true,\n        message: \"请选择参考答案\",\n        trigger: \"change\"\n      }],\n      referenceAnswerNote: [{\n        required: true,\n        message: \"请输入答案解析\",\n        trigger: \"blur\"\n      }],\n      options: [{\n        required: true,\n        message: \"请添加选项\",\n        trigger: \"blur\"\n      }]\n    };\n    const serialNumber = [\"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\", \"H\", \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\", \"R\", \"S\", \"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\"];\n    const optionList = ref([]);\n    const categoryOptions = ref([]);\n    const selectCidList = ref([]);\n    // 获取分类\n    findCategoryList(0, true, res => {\n      if (res && res.length) {\n        categoryOptions.value = toTree(res);\n        categoryOptions.value.splice(0, 1);\n        if (route.query.id) {\n          // 获取试题信息\n          getBaseInfo(route.query.id, function (res) {\n            console.log(res);\n            question.value = res;\n            optionList.value = JSON.parse(res.options);\n            selectCidList.value = getAllParent(categoryOptions.value, res.cidList);\n            question.value.cidList = [];\n            for (const valElement of selectCidList.value) {\n              question.value.cidList.push(valElement[valElement.length - 1]);\n            }\n          });\n        }\n      }\n    });\n    // 选择分类\n    const changeCategory = val => {\n      question.value.cidList = [];\n      for (const valElement of val) {\n        question.value.cidList.push(valElement[valElement.length - 1]);\n      }\n    };\n    let optionIndex = -1;\n    const option = ref(\"\");\n    const showAddOptionInput = ref(false);\n    const addOption = () => {\n      showAddOptionInput.value = true;\n    };\n    const optionBlur = () => {\n      showAddOptionInput.value = false;\n      if (!option.value) {\n        return;\n      }\n      if (optionIndex > -1) {\n        optionList.value[optionIndex].value = option.value;\n      } else {\n        optionList.value.push({\n          value: option.value,\n          key: serialNumber[optionList.value.length]\n        });\n      }\n      question.value.options = JSON.stringify(optionList.value);\n      option.value = \"\";\n      optionIndex = -1;\n    };\n    const editOption = index => {\n      const o = optionList.value[index];\n      option.value = o.value;\n      optionIndex = index;\n      showAddOptionInput.value = true;\n    };\n    const deleteOption = index => {\n      if (optionList.value && optionList.value.length) {\n        optionList.value.splice(index, 1);\n        optionList.value.forEach((item, index) => {\n          item.key = serialNumber[index];\n        });\n        question.value.options = JSON.stringify(optionList.value);\n      } else {\n        question.value.options = \"\";\n      }\n    };\n    const questionRef = ref();\n    const submitBaseInfo = () => {\n      questionRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (question.value.id) {\n          updateBaseInfo(question.value, function () {\n            success(\"编辑成功\");\n            router.push({\n              path: \"/exam/question-lib\"\n            });\n          });\n        } else {\n          saveBaseInfo(question.value, function () {\n            success(\"新增成功\");\n            router.push({\n              path: \"/exam/question-lib\"\n            });\n          });\n        }\n      });\n    };\n    return {\n      colors,\n      question,\n      questionRules,\n      categoryOptions,\n      selectCidList,\n      serialNumber,\n      option,\n      optionList,\n      showAddOptionInput,\n      questionRef,\n      changeCategory,\n      addOption,\n      optionBlur,\n      editOption,\n      deleteOption,\n      submitBaseInfo\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "findCategoryList", "toTree", "getAllParent", "saveBaseInfo", "updateBaseInfo", "getBaseInfo", "useRoute", "success", "router", "name", "setup", "route", "colors", "question", "id", "title", "note", "type", "score", "difficulty", "referenceAnswer", "referenceAnswerNote", "options", "cidList", "questionRules", "required", "message", "trigger", "serialNumber", "optionList", "categoryOptions", "selectCidList", "res", "length", "value", "splice", "query", "console", "log", "JSON", "parse", "valElement", "push", "changeCategory", "val", "optionIndex", "option", "showAddOptionInput", "addOption", "optionBlur", "key", "stringify", "editOption", "index", "o", "deleteOption", "for<PERSON>ach", "item", "questionRef", "submitBaseInfo", "validate", "valid", "path"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/exam/question-lib/single-choice/index.vue"], "sourcesContent": ["<template>\n  <div class=\"question-box\">\n    <el-form :model=\"question\" :rules=\"questionRules\" ref=\"questionRef\" label-width=\"120px\">\n      <el-form-item label=\"分类：\" prop=\"cidList\">\n        <el-cascader size=\"mini\" style=\"width: 100%;\"\n                     v-model=\"selectCidList\"\n                     :props=\"{ multiple: true, checkStrictly: true }\"\n                     :options=\"categoryOptions\"\n                     @change=\"changeCategory\">\n        </el-cascader>\n      </el-form-item>\n      <el-form-item label=\"题干：\" prop=\"title\">\n        <el-input size=\"mini\" v-model=\"question.title\" placeholder=\"请输入题干\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"描述：\" prop=\"note\">\n        <el-input size=\"mini\" type=\"textarea\" :rows=\"5\" v-model=\"question.note\" placeholder=\"请输入题干描述\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"选项：\" prop=\"options\">\n        <el-card size=\"mini\" shadow=\"never\">\n          <template #header>\n            <div class=\"clearfix\">\n              <el-button size=\"mini\" style=\"padding: 10px;\" type=\"text\" @click=\"addOption\">添加选项</el-button>\n            </div>\n          </template>\n          <div v-if=\"!(optionList && optionList.length > 0) && !showAddOptionInput\">请添加选项</div>\n          <div v-else-if=\"optionList && optionList.length > 0\" v-for=\"(o, index) in optionList\" :key=\"o.key\" class=\"text item\">\n            <span>{{o.key + '. ' + o.value}}</span>\n            <el-icon class=\"option-delete\" @click=\"editOption(index)\"><Edit/></el-icon>\n            <el-icon class=\"option-delete\" @click=\"deleteOption(index)\"><Delete/></el-icon>\n          </div>\n          <el-input size=\"mini\" placeholder=\"请输入选项内容\" v-if=\"showAddOptionInput\" v-model=\"option\" @blur=\"optionBlur\" @keypress.enter=\"optionBlur\"/>\n        </el-card>\n      </el-form-item>\n      <el-form-item label=\"参考答案：\"  prop=\"referenceAnswer\">\n        <el-radio v-model=\"question.referenceAnswer\" v-for=\"item in optionList\" :key=\"item.key\" :label=\"item.key\">{{item.key}}</el-radio>\n      </el-form-item>\n      <el-form-item label=\"答案解析：\" prop=\"referenceAnswerNote\">\n        <el-input size=\"mini\" type=\"textarea\" :rows=\"5\" v-model=\"question.referenceAnswerNote\" placeholder=\"请输入答案解析\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"分数：\" prop=\"score\">\n        <el-input size=\"mini\" v-model=\"question.score\" placeholder=\"请输入试题分数\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"难度：\" prop=\"difficulty\">\n        <el-rate style=\"line-height: 48px;\" v-model=\"question.difficulty\" :colors=\"colors\"></el-rate>\n      </el-form-item>\n    </el-form>\n    <el-button size=\"mini\" style=\"display:block;margin:50px auto;\" @click=\"submitBaseInfo\">提交</el-button>\n  </div>\n</template>\n<script>\n  import {ref} from \"vue\"\n  import {findCategoryList, toTree, getAllParent} from \"@/api/exam/question-lib/category\"\n  import {saveBaseInfo, updateBaseInfo, getBaseInfo} from \"@/api/exam/question-lib/question\"\n  import {useRoute} from \"vue-router\";\n  import {success} from \"@/util/tipsUtils\";\n  import router from \"@/router\";\n\n  export default {\n    name: \"ExamQuestionLibSingleChoice\",\n    setup() {\n      const route = useRoute()\n      const colors = [\"#99A9BF\", \"#F7BA2A\", \"#FF9900\"]\n      const question = ref({\n        id: \"\",\n        title: \"\",\n        note: \"\",\n        type: \"single_choice\",\n        score: \"\",\n        difficulty: 2,\n        referenceAnswer: \"\",\n        referenceAnswerNote: \"\",\n        options: \"\",\n        cidList: []\n      })\n      const questionRules = {\n        title: [{ required: true, message: \"请输入题干\", trigger: \"blur\" }],\n        score: [{ required: true, message: \"请输入分数\", trigger: \"blur\" }],\n        cidList: [{ required: true, message: \"请选择分类\", trigger: \"change\" }],\n        referenceAnswer: [{ required: true, message: \"请选择参考答案\", trigger: \"change\" }],\n        referenceAnswerNote: [{ required: true, message: \"请输入答案解析\", trigger: \"blur\" }],\n        options: [{ required: true, message: \"请添加选项\", trigger: \"blur\" }],\n      }\n      const serialNumber = [\"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\", \"H\", \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\", \"R\", \"S\", \"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\"]\n      const optionList = ref([])\n      const categoryOptions = ref([])\n      const selectCidList = ref([])\n      // 获取分类\n      findCategoryList(0, true, (res) => {\n        if (res && res.length) {\n          categoryOptions.value = toTree(res);\n          categoryOptions.value.splice(0, 1);\n          if (route.query.id) {\n            // 获取试题信息\n            getBaseInfo(route.query.id, function (res) {\n              console.log(res)\n              question.value = res;\n              optionList.value = JSON.parse(res.options);\n              selectCidList.value = getAllParent(categoryOptions.value, res.cidList);\n              question.value.cidList = []\n              for (const valElement of selectCidList.value) {\n                question.value.cidList.push(valElement[valElement.length - 1])\n              }\n            })\n          }\n        }\n      })\n      // 选择分类\n      const changeCategory = (val) => {\n        question.value.cidList = []\n        for (const valElement of val) {\n          question.value.cidList.push(valElement[valElement.length - 1])\n        }\n      }\n      let optionIndex = -1;\n      const option = ref(\"\")\n      const showAddOptionInput = ref(false)\n      const addOption = () => {\n        showAddOptionInput.value = true\n      }\n      const optionBlur = () => {\n        showAddOptionInput.value = false\n        if (!option.value) {\n          return\n        }\n        if (optionIndex > -1) {\n          optionList.value[optionIndex].value = option.value\n        } else {\n          optionList.value.push({value: option.value, key: serialNumber[optionList.value.length]})\n        }\n        question.value.options = JSON.stringify(optionList.value)\n        option.value = \"\"\n        optionIndex = -1;\n      }\n      const editOption = (index) => {\n        const o = optionList.value[index];\n        option.value = o.value;\n        optionIndex = index;\n        showAddOptionInput.value = true\n      }\n      const deleteOption = (index) => {\n        if (optionList.value && optionList.value.length) {\n          optionList.value.splice(index, 1);\n          optionList.value.forEach((item, index) => {\n            item.key = serialNumber[index]\n          })\n          question.value.options = JSON.stringify(optionList.value)\n        } else {\n          question.value.options = \"\"\n        }\n      }\n      const questionRef = ref();\n      const submitBaseInfo = () => {\n        questionRef.value.validate((valid) => {\n          if (!valid) { return false }\n          if (question.value.id) {\n            updateBaseInfo(question.value, function () {\n              success(\"编辑成功\")\n              router.push({path: \"/exam/question-lib\"})\n            })\n          } else {\n            saveBaseInfo(question.value, function () {\n              success(\"新增成功\")\n              router.push({path: \"/exam/question-lib\"})\n            })\n          }\n        })\n      }\n      return {\n        colors,\n        question,\n        questionRules,\n        categoryOptions,\n        selectCidList,\n        serialNumber,\n        option,\n        optionList,\n        showAddOptionInput,\n        questionRef,\n        changeCategory,\n        addOption,\n        optionBlur,\n        editOption,\n        deleteOption,\n        submitBaseInfo\n      }\n    }\n  }\n</script>\n<style scoped lang=\"scss\">\n.question-box {\n  margin: 20px;\n  .option-delete {\n    margin-left: 20px;\n    cursor: pointer;\n  }\n  .option-delete:hover {\n    color: $--color-primary;\n  }\n  ::v-deep .el-card__header{\n    padding: 0!important;\n  }\n}\n</style>\n"], "mappings": ";AAkDE,SAAQA,GAAG,QAAO,KAAI;AACtB,SAAQC,gBAAgB,EAAEC,MAAM,EAAEC,YAAY,QAAO,kCAAiC;AACtF,SAAQC,YAAY,EAAEC,cAAc,EAAEC,WAAW,QAAO,kCAAiC;AACzF,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,OAAO,QAAO,kBAAkB;AACxC,OAAOC,MAAK,MAAO,UAAU;AAE7B,eAAe;EACbC,IAAI,EAAE,6BAA6B;EACnCC,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIL,QAAQ,EAAC;IACvB,MAAMM,MAAK,GAAI,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;IAC/C,MAAMC,QAAO,GAAId,GAAG,CAAC;MACnBe,EAAE,EAAE,EAAE;MACNC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,EAAE;MACTC,UAAU,EAAE,CAAC;MACbC,eAAe,EAAE,EAAE;MACnBC,mBAAmB,EAAE,EAAE;MACvBC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE;IACX,CAAC;IACD,MAAMC,aAAY,GAAI;MACpBT,KAAK,EAAE,CAAC;QAAEU,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9DT,KAAK,EAAE,CAAC;QAAEO,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9DJ,OAAO,EAAE,CAAC;QAAEE,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC,CAAC;MAClEP,eAAe,EAAE,CAAC;QAAEK,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAS,CAAC,CAAC;MAC5EN,mBAAmB,EAAE,CAAC;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9EL,OAAO,EAAE,CAAC;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC;IACjE;IACA,MAAMC,YAAW,GAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACtJ,MAAMC,UAAS,GAAI9B,GAAG,CAAC,EAAE;IACzB,MAAM+B,eAAc,GAAI/B,GAAG,CAAC,EAAE;IAC9B,MAAMgC,aAAY,GAAIhC,GAAG,CAAC,EAAE;IAC5B;IACAC,gBAAgB,CAAC,CAAC,EAAE,IAAI,EAAGgC,GAAG,IAAK;MACjC,IAAIA,GAAE,IAAKA,GAAG,CAACC,MAAM,EAAE;QACrBH,eAAe,CAACI,KAAI,GAAIjC,MAAM,CAAC+B,GAAG,CAAC;QACnCF,eAAe,CAACI,KAAK,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;QAClC,IAAIxB,KAAK,CAACyB,KAAK,CAACtB,EAAE,EAAE;UAClB;UACAT,WAAW,CAACM,KAAK,CAACyB,KAAK,CAACtB,EAAE,EAAE,UAAUkB,GAAG,EAAE;YACzCK,OAAO,CAACC,GAAG,CAACN,GAAG;YACfnB,QAAQ,CAACqB,KAAI,GAAIF,GAAG;YACpBH,UAAU,CAACK,KAAI,GAAIK,IAAI,CAACC,KAAK,CAACR,GAAG,CAACV,OAAO,CAAC;YAC1CS,aAAa,CAACG,KAAI,GAAIhC,YAAY,CAAC4B,eAAe,CAACI,KAAK,EAAEF,GAAG,CAACT,OAAO,CAAC;YACtEV,QAAQ,CAACqB,KAAK,CAACX,OAAM,GAAI,EAAC;YAC1B,KAAK,MAAMkB,UAAS,IAAKV,aAAa,CAACG,KAAK,EAAE;cAC5CrB,QAAQ,CAACqB,KAAK,CAACX,OAAO,CAACmB,IAAI,CAACD,UAAU,CAACA,UAAU,CAACR,MAAK,GAAI,CAAC,CAAC;YAC/D;UACF,CAAC;QACH;MACF;IACF,CAAC;IACD;IACA,MAAMU,cAAa,GAAKC,GAAG,IAAK;MAC9B/B,QAAQ,CAACqB,KAAK,CAACX,OAAM,GAAI,EAAC;MAC1B,KAAK,MAAMkB,UAAS,IAAKG,GAAG,EAAE;QAC5B/B,QAAQ,CAACqB,KAAK,CAACX,OAAO,CAACmB,IAAI,CAACD,UAAU,CAACA,UAAU,CAACR,MAAK,GAAI,CAAC,CAAC;MAC/D;IACF;IACA,IAAIY,WAAU,GAAI,CAAC,CAAC;IACpB,MAAMC,MAAK,GAAI/C,GAAG,CAAC,EAAE;IACrB,MAAMgD,kBAAiB,GAAIhD,GAAG,CAAC,KAAK;IACpC,MAAMiD,SAAQ,GAAIA,CAAA,KAAM;MACtBD,kBAAkB,CAACb,KAAI,GAAI,IAAG;IAChC;IACA,MAAMe,UAAS,GAAIA,CAAA,KAAM;MACvBF,kBAAkB,CAACb,KAAI,GAAI,KAAI;MAC/B,IAAI,CAACY,MAAM,CAACZ,KAAK,EAAE;QACjB;MACF;MACA,IAAIW,WAAU,GAAI,CAAC,CAAC,EAAE;QACpBhB,UAAU,CAACK,KAAK,CAACW,WAAW,CAAC,CAACX,KAAI,GAAIY,MAAM,CAACZ,KAAI;MACnD,OAAO;QACLL,UAAU,CAACK,KAAK,CAACQ,IAAI,CAAC;UAACR,KAAK,EAAEY,MAAM,CAACZ,KAAK;UAAEgB,GAAG,EAAEtB,YAAY,CAACC,UAAU,CAACK,KAAK,CAACD,MAAM;QAAC,CAAC;MACzF;MACApB,QAAQ,CAACqB,KAAK,CAACZ,OAAM,GAAIiB,IAAI,CAACY,SAAS,CAACtB,UAAU,CAACK,KAAK;MACxDY,MAAM,CAACZ,KAAI,GAAI,EAAC;MAChBW,WAAU,GAAI,CAAC,CAAC;IAClB;IACA,MAAMO,UAAS,GAAKC,KAAK,IAAK;MAC5B,MAAMC,CAAA,GAAIzB,UAAU,CAACK,KAAK,CAACmB,KAAK,CAAC;MACjCP,MAAM,CAACZ,KAAI,GAAIoB,CAAC,CAACpB,KAAK;MACtBW,WAAU,GAAIQ,KAAK;MACnBN,kBAAkB,CAACb,KAAI,GAAI,IAAG;IAChC;IACA,MAAMqB,YAAW,GAAKF,KAAK,IAAK;MAC9B,IAAIxB,UAAU,CAACK,KAAI,IAAKL,UAAU,CAACK,KAAK,CAACD,MAAM,EAAE;QAC/CJ,UAAU,CAACK,KAAK,CAACC,MAAM,CAACkB,KAAK,EAAE,CAAC,CAAC;QACjCxB,UAAU,CAACK,KAAK,CAACsB,OAAO,CAAC,CAACC,IAAI,EAAEJ,KAAK,KAAK;UACxCI,IAAI,CAACP,GAAE,GAAItB,YAAY,CAACyB,KAAK;QAC/B,CAAC;QACDxC,QAAQ,CAACqB,KAAK,CAACZ,OAAM,GAAIiB,IAAI,CAACY,SAAS,CAACtB,UAAU,CAACK,KAAK;MAC1D,OAAO;QACLrB,QAAQ,CAACqB,KAAK,CAACZ,OAAM,GAAI,EAAC;MAC5B;IACF;IACA,MAAMoC,WAAU,GAAI3D,GAAG,EAAE;IACzB,MAAM4D,cAAa,GAAIA,CAAA,KAAM;MAC3BD,WAAW,CAACxB,KAAK,CAAC0B,QAAQ,CAAEC,KAAK,IAAK;QACpC,IAAI,CAACA,KAAK,EAAE;UAAE,OAAO,KAAI;QAAE;QAC3B,IAAIhD,QAAQ,CAACqB,KAAK,CAACpB,EAAE,EAAE;UACrBV,cAAc,CAACS,QAAQ,CAACqB,KAAK,EAAE,YAAY;YACzC3B,OAAO,CAAC,MAAM;YACdC,MAAM,CAACkC,IAAI,CAAC;cAACoB,IAAI,EAAE;YAAoB,CAAC;UAC1C,CAAC;QACH,OAAO;UACL3D,YAAY,CAACU,QAAQ,CAACqB,KAAK,EAAE,YAAY;YACvC3B,OAAO,CAAC,MAAM;YACdC,MAAM,CAACkC,IAAI,CAAC;cAACoB,IAAI,EAAE;YAAoB,CAAC;UAC1C,CAAC;QACH;MACF,CAAC;IACH;IACA,OAAO;MACLlD,MAAM;MACNC,QAAQ;MACRW,aAAa;MACbM,eAAe;MACfC,aAAa;MACbH,YAAY;MACZkB,MAAM;MACNjB,UAAU;MACVkB,kBAAkB;MAClBW,WAAW;MACXf,cAAc;MACdK,SAAS;MACTC,UAAU;MACVG,UAAU;MACVG,YAAY;MACZI;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}