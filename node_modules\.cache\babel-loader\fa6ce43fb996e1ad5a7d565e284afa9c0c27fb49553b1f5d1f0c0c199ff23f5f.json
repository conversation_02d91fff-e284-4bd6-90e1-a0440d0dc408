{"ast": null, "code": "import { ref, watch, nextTick, computed, onMounted, onActivated, onDeactivated, onUnmounted } from \"vue\";\n/**\n * docs:\n * https://panjiachen.github.io/vue-element-admin-site/feature/component/rich-editor.html#tinymce\n */\nimport plugins from \"./plugins\";\nimport toolbar from \"./toolbar\";\nimport load from \"./dynamicLoadScript\";\nimport { error } from \"../../util/tipsUtils\";\n\n// why use this cdn, detail see https://github.com/PanJiaChen/tinymce-all-in-one\n// const tinyMceCdn = \"https://cdn.jsdelivr.net/npm/tinymce-all-in-one@4.9.5/tinymce.min.js\"\nconst tinyMceCdn = \"../../assets/tinymce-all-in-one-4.9.4/tinymce.min.js\";\nexport default {\n  name: \"TinyMce\",\n  props: {\n    id: {\n      type: String,\n      default: function () {\n        return \"vue-tiny-mce-\" + +new Date() + ((Math.random() * 1000).toFixed(0) + \"\");\n      }\n    },\n    modelValue: {\n      type: String,\n      default: \"\"\n    },\n    toolbar: {\n      type: Array,\n      required: false,\n      default() {\n        return [];\n      }\n    },\n    menubar: {\n      type: String,\n      default: \"file edit insert view format table\"\n    },\n    height: {\n      type: [Number, String],\n      required: false,\n      default: 360\n    },\n    width: {\n      type: [Number, String],\n      required: false,\n      default: \"auto\"\n    },\n    placeholder: {\n      type: String,\n      default: \"\"\n    }\n  },\n  setup(props, context) {\n    const hasChange = ref(false);\n    const hasInit = ref(false);\n    const fullscreen = ref(false);\n    const tinyMceId = ref(props.id);\n    watch(() => props.modelValue, val => {\n      if (!hasChange.value && hasInit.value) {\n        nextTick(() => window.tinymce.get(tinyMceId.value).setContent(val || \"\"));\n      }\n    });\n    const languageTypeList = {\n      \"en\": \"en\",\n      \"zh\": \"zh_CN\",\n      \"es\": \"es_MX\",\n      \"ja\": \"ja\"\n    };\n    const initTinyMce = function () {\n      window.tinymce.init({\n        selector: `#${tinyMceId.value}`,\n        language: languageTypeList[\"zh\"],\n        height: props.height,\n        body_class: \"panel-body \",\n        object_resizing: false,\n        toolbar: props.toolbar.length > 0 ? props.toolbar : toolbar,\n        menubar: props.menubar,\n        plugins: plugins,\n        end_container_on_empty_block: true,\n        powerpaste_word_import: \"clean\",\n        code_dialog_height: 450,\n        code_dialog_width: 1000,\n        advlist_bullet_styles: \"square\",\n        advlist_number_styles: \"default\",\n        imagetools_cors_hosts: [\"www.tinymce.com\", \"codepen.io\"],\n        default_link_target: \"_blank\",\n        link_title: false,\n        nonbreaking_force_tab: true,\n        // inserting nonbreaking space &nbsp; need Nonbreaking Space Plugin\n        init_instance_callback: editor => {\n          if (props.modelValue) {\n            editor.setContent(props.modelValue);\n          }\n          hasInit.value = true;\n          editor.on(\"NodeChange Change KeyUp SetContent\", () => {\n            hasChange.value = true;\n            context.emit(\"update:modelValue\", editor.getContent());\n          });\n        },\n        setup(editor) {\n          editor.on(\"FullscreenStateChanged\", e => {\n            fullscreen.value = e.state;\n          });\n        },\n        // it will try to keep these URLs intact\n        // https://www.tiny.cloud/docs-3x/reference/configuration/Configuration3x@convert_urls/\n        // https://stackoverflow.com/questions/5196205/disable-tinymce-absolute-to-relative-url-conversions\n        convert_urls: false\n        // 整合七牛上传\n        // images_dataimg_filter(img) {\n        //   setTimeout(() => {\n        //     const $image = $(img);\n        //     $image.removeAttr(\"width\");\n        //     $image.removeAttr(\"height\");\n        //     if ($image[0].height && $image[0].width) {\n        //       $image.attr(\"data-wscntype\", \"image\");\n        //       $image.attr(\"data-wscnh\", $image[0].height);\n        //       $image.attr(\"data-wscnw\", $image[0].width);\n        //       $image.addClass(\"wscnph\");\n        //     }\n        //   }, 0);\n        //   return img\n        // },\n        // images_upload_handler(blobInfo, success, failure, progress) {\n        //   progress(0);\n        //   const token = _this.$store.getters.token;\n        //   getToken(token).then(response => {\n        //     const url = response.data.qiniu_url;\n        //     const formData = new FormData();\n        //     formData.append(\"token\", response.data.qiniu_token);\n        //     formData.append(\"key\", response.data.qiniu_key);\n        //     formData.append(\"file\", blobInfo.blob(), url);\n        //     upload(formData).then(() => {\n        //       success(url);\n        //       progress(100);\n        //     })\n        //   }).catch(err => {\n        //     failure(\"出现未知问题，刷新页面，或者联系程序员\")\n        //     console.log(err);\n        //   });\n        // },\n      });\n    };\n\n    const init = function () {\n      // dynamic load tinymce from cdn\n      load(tinyMceCdn, err => {\n        if (err) {\n          error(err.message);\n          return;\n        }\n        initTinyMce();\n      });\n    };\n    const destroyTinyMce = function () {\n      if (!window.tinymce) {\n        return;\n      }\n      const tinyMce = window.tinymce.get(tinyMceId.value);\n      if (fullscreen.value) {\n        tinyMce.execCommand(\"mceFullScreen\");\n      }\n      if (tinyMce) {\n        tinyMce.destroy();\n      }\n    };\n    const setContent = function (value) {\n      window.tinymce.get(tinyMceId.value).setContent(value);\n    };\n    const getContent = function () {\n      window.tinymce.get(tinyMceId.value).getContent();\n    };\n    const containerWidth = computed(() => {\n      const width = props.width;\n      if (/^[\\d]+(\\.[\\d]+)?$/.test(width)) {\n        // matches `100`, `\"100\"`\n        return `${width}px`;\n      }\n      return width;\n    });\n    onMounted(() => {\n      init();\n    });\n    onActivated(() => {\n      if (window.tinymce) {\n        initTinyMce();\n      }\n    });\n    onDeactivated(() => {\n      destroyTinyMce();\n    });\n    onUnmounted(() => {\n      destroyTinyMce();\n    });\n    return {\n      hasChange,\n      hasInit,\n      tinyMceId,\n      fullscreen,\n      setContent,\n      getContent,\n      containerWidth\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "watch", "nextTick", "computed", "onMounted", "onActivated", "onDeactivated", "onUnmounted", "plugins", "toolbar", "load", "error", "tinyMceCdn", "name", "props", "id", "type", "String", "default", "Date", "Math", "random", "toFixed", "modelValue", "Array", "required", "menubar", "height", "Number", "width", "placeholder", "setup", "context", "hasChange", "hasInit", "fullscreen", "tinyMceId", "val", "value", "window", "<PERSON><PERSON><PERSON>", "get", "<PERSON><PERSON><PERSON><PERSON>", "languageTypeList", "initTinyMce", "init", "selector", "language", "body_class", "object_resizing", "length", "end_container_on_empty_block", "powerpaste_word_import", "code_dialog_height", "code_dialog_width", "advlist_bullet_styles", "advlist_number_styles", "imagetools_cors_hosts", "default_link_target", "link_title", "nonbreaking_force_tab", "init_instance_callback", "editor", "on", "emit", "get<PERSON>ontent", "e", "state", "convert_urls", "err", "message", "destroyTinyMce", "tinyMce", "execCommand", "destroy", "containerWidth", "test"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/components/Tinymce/index.vue"], "sourcesContent": ["<template>\n  <div :class=\"{fullscreen:fullscreen}\" class=\"tiny-mce-container\" :style=\"{width:containerWidth}\">\n    <textarea :placeholder=\"placeholder\" :id=\"tinyMceId\" class=\"tiny-mce-textarea\"/>\n    <div class=\"editor-custom-btn-container\"></div>\n  </div>\n</template>\n\n<script>\nimport {ref, watch, nextTick, computed, onMounted, onActivated, onDeactivated, onUnmounted} from \"vue\"\n/**\n * docs:\n * https://panjiachen.github.io/vue-element-admin-site/feature/component/rich-editor.html#tinymce\n */\nimport plugins from \"./plugins\"\nimport toolbar from \"./toolbar\"\nimport load from \"./dynamicLoadScript\"\nimport {error} from \"../../util/tipsUtils\";\n\n// why use this cdn, detail see https://github.com/PanJiaChen/tinymce-all-in-one\n// const tinyMceCdn = \"https://cdn.jsdelivr.net/npm/tinymce-all-in-one@4.9.5/tinymce.min.js\"\nconst tinyMceCdn = \"../../assets/tinymce-all-in-one-4.9.4/tinymce.min.js\"\n\nexport default {\n  name: \"TinyMce\",\n  props: {\n    id: {\n      type: String,\n      default: function() {\n        return \"vue-tiny-mce-\" + +new Date() + ((Math.random() * 1000).toFixed(0) + \"\")\n      }\n    },\n    modelValue: {\n      type: String,\n      default: \"\"\n    },\n    toolbar: {\n      type: Array,\n      required: false,\n      default() {\n        return []\n      }\n    },\n    menubar: {\n      type: String,\n      default: \"file edit insert view format table\"\n    },\n    height: {\n      type: [Number, String],\n      required: false,\n      default: 360\n    },\n    width: {\n      type: [Number, String],\n      required: false,\n      default: \"auto\"\n    },\n    placeholder: {\n      type: String,\n      default: \"\"\n    }\n  },\n  setup(props, context) {\n    const hasChange = ref(false)\n    const hasInit = ref(false)\n    const fullscreen = ref(false)\n    const tinyMceId = ref(props.id)\n    watch(() => props.modelValue, (val) => {\n      if (!hasChange.value && hasInit.value) {\n        nextTick(() => window.tinymce.get(tinyMceId.value).setContent(val || \"\"))\n      }\n    })\n    const languageTypeList = {\n      \"en\": \"en\",\n      \"zh\": \"zh_CN\",\n      \"es\": \"es_MX\",\n      \"ja\": \"ja\"\n    }\n    const initTinyMce = function() {\n      window.tinymce.init({\n        selector: `#${tinyMceId.value}`,\n        language: languageTypeList[\"zh\"],\n        height: props.height,\n        body_class: \"panel-body \",\n        object_resizing: false,\n        toolbar: props.toolbar.length > 0 ? props.toolbar : toolbar,\n        menubar: props.menubar,\n        plugins: plugins,\n        end_container_on_empty_block: true,\n        powerpaste_word_import: \"clean\",\n        code_dialog_height: 450,\n        code_dialog_width: 1000,\n        advlist_bullet_styles: \"square\",\n        advlist_number_styles: \"default\",\n        imagetools_cors_hosts: [\"www.tinymce.com\", \"codepen.io\"],\n        default_link_target: \"_blank\",\n        link_title: false,\n        nonbreaking_force_tab: true, // inserting nonbreaking space &nbsp; need Nonbreaking Space Plugin\n        init_instance_callback: editor => {\n          if (props.modelValue) {\n            editor.setContent(props.modelValue)\n          }\n          hasInit.value = true\n          editor.on(\"NodeChange Change KeyUp SetContent\", () => {\n            hasChange.value = true\n            context.emit(\"update:modelValue\", editor.getContent())\n          })\n        },\n        setup(editor) {\n          editor.on(\"FullscreenStateChanged\", (e) => {\n            fullscreen.value = e.state\n          })\n        },\n        // it will try to keep these URLs intact\n        // https://www.tiny.cloud/docs-3x/reference/configuration/Configuration3x@convert_urls/\n        // https://stackoverflow.com/questions/5196205/disable-tinymce-absolute-to-relative-url-conversions\n        convert_urls: false\n        // 整合七牛上传\n        // images_dataimg_filter(img) {\n        //   setTimeout(() => {\n        //     const $image = $(img);\n        //     $image.removeAttr(\"width\");\n        //     $image.removeAttr(\"height\");\n        //     if ($image[0].height && $image[0].width) {\n        //       $image.attr(\"data-wscntype\", \"image\");\n        //       $image.attr(\"data-wscnh\", $image[0].height);\n        //       $image.attr(\"data-wscnw\", $image[0].width);\n        //       $image.addClass(\"wscnph\");\n        //     }\n        //   }, 0);\n        //   return img\n        // },\n        // images_upload_handler(blobInfo, success, failure, progress) {\n        //   progress(0);\n        //   const token = _this.$store.getters.token;\n        //   getToken(token).then(response => {\n        //     const url = response.data.qiniu_url;\n        //     const formData = new FormData();\n        //     formData.append(\"token\", response.data.qiniu_token);\n        //     formData.append(\"key\", response.data.qiniu_key);\n        //     formData.append(\"file\", blobInfo.blob(), url);\n        //     upload(formData).then(() => {\n        //       success(url);\n        //       progress(100);\n        //     })\n        //   }).catch(err => {\n        //     failure(\"出现未知问题，刷新页面，或者联系程序员\")\n        //     console.log(err);\n        //   });\n        // },\n      })\n    }\n    const init = function() {\n      // dynamic load tinymce from cdn\n      load(tinyMceCdn, (err) => {\n        if (err) {\n          error(err.message)\n          return\n        }\n        initTinyMce()\n      })\n    }\n    const destroyTinyMce = function() {\n      if(!window.tinymce) {\n        return\n      }\n      const tinyMce = window.tinymce.get(tinyMceId.value)\n      if (fullscreen.value) {\n        tinyMce.execCommand(\"mceFullScreen\")\n      }\n      if (tinyMce) {\n        tinyMce.destroy()\n      }\n    }\n    const setContent = function(value) {\n      window.tinymce.get(tinyMceId.value).setContent(value)\n    }\n    const getContent = function() {\n      window.tinymce.get(tinyMceId.value).getContent()\n    }\n    const containerWidth = computed(() => {\n      const width = props.width\n      if (/^[\\d]+(\\.[\\d]+)?$/.test(width)) { // matches `100`, `\"100\"`\n        return `${width}px`\n      }\n      return width\n    })\n    onMounted(() => {\n      init()\n    })\n    onActivated(() => {\n      if (window.tinymce) {\n        initTinyMce()\n      }\n    })\n    onDeactivated(() => {\n      destroyTinyMce()\n    })\n    onUnmounted(() => {\n      destroyTinyMce()\n    })\n    return {\n      hasChange,\n      hasInit,\n      tinyMceId,\n      fullscreen,\n      setContent,\n      getContent,\n      containerWidth\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.tiny-mce-container {\n  position: relative;\n  line-height: normal;\n  ::v-deep {\n    .mce-branding {\n      display: none;\n    }\n  }\n}\n.tiny-mce-container {\n  ::v-deep {\n    .mce-fullscreen {\n      z-index: 10000;\n    }\n  }\n}\n.tiny-mce-textarea {\n  visibility: hidden;\n  z-index: -1;\n}\n.editor-custom-btn-container {\n  position: absolute;\n  right: 4px;\n  top: 4px;\n  /*z-index: 2005;*/\n}\n.fullscreen .editor-custom-btn-container {\n  z-index: 10000;\n  position: fixed;\n}\n.editor-upload-btn {\n  display: inline-block;\n}\n::v-deep .mce-tinymce {\n  border: none;\n  box-shadow: none;\n  .mce-menubar {\n    border: none;\n  }\n  .mce-panel {\n    border: none;\n  }\n  .mce-container, .mce-container-body {\n    font-size: 12px;\n  }\n  .mce-top-part::before {\n    box-shadow: none;\n    border-bottom: 1px solid #f1f1f1;\n  }\n}\n</style>\n"], "mappings": "AAQA,SAAQA,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,aAAa,EAAEC,WAAW,QAAO,KAAI;AACrG;;;;AAIA,OAAOC,OAAM,MAAO,WAAU;AAC9B,OAAOC,OAAM,MAAO,WAAU;AAC9B,OAAOC,IAAG,MAAO,qBAAoB;AACrC,SAAQC,KAAK,QAAO,sBAAsB;;AAE1C;AACA;AACA,MAAMC,UAAS,GAAI,sDAAqD;AAExE,eAAe;EACbC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE;IACLC,EAAE,EAAE;MACFC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,SAAAA,CAAA,EAAW;QAClB,OAAO,eAAc,GAAI,CAAC,IAAIC,IAAI,EAAC,IAAK,CAACC,IAAI,CAACC,MAAM,EAAC,GAAI,IAAI,EAAEC,OAAO,CAAC,CAAC,IAAI,EAAE;MAChF;IACF,CAAC;IACDC,UAAU,EAAE;MACVP,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDT,OAAO,EAAE;MACPO,IAAI,EAAEQ,KAAK;MACXC,QAAQ,EAAE,KAAK;MACfP,OAAOA,CAAA,EAAG;QACR,OAAO,EAAC;MACV;IACF,CAAC;IACDQ,OAAO,EAAE;MACPV,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDS,MAAM,EAAE;MACNX,IAAI,EAAE,CAACY,MAAM,EAAEX,MAAM,CAAC;MACtBQ,QAAQ,EAAE,KAAK;MACfP,OAAO,EAAE;IACX,CAAC;IACDW,KAAK,EAAE;MACLb,IAAI,EAAE,CAACY,MAAM,EAAEX,MAAM,CAAC;MACtBQ,QAAQ,EAAE,KAAK;MACfP,OAAO,EAAE;IACX,CAAC;IACDY,WAAW,EAAE;MACXd,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX;EACF,CAAC;EACDa,KAAKA,CAACjB,KAAK,EAAEkB,OAAO,EAAE;IACpB,MAAMC,SAAQ,GAAIjC,GAAG,CAAC,KAAK;IAC3B,MAAMkC,OAAM,GAAIlC,GAAG,CAAC,KAAK;IACzB,MAAMmC,UAAS,GAAInC,GAAG,CAAC,KAAK;IAC5B,MAAMoC,SAAQ,GAAIpC,GAAG,CAACc,KAAK,CAACC,EAAE;IAC9Bd,KAAK,CAAC,MAAMa,KAAK,CAACS,UAAU,EAAGc,GAAG,IAAK;MACrC,IAAI,CAACJ,SAAS,CAACK,KAAI,IAAKJ,OAAO,CAACI,KAAK,EAAE;QACrCpC,QAAQ,CAAC,MAAMqC,MAAM,CAACC,OAAO,CAACC,GAAG,CAACL,SAAS,CAACE,KAAK,CAAC,CAACI,UAAU,CAACL,GAAE,IAAK,EAAE,CAAC;MAC1E;IACF,CAAC;IACD,MAAMM,gBAAe,GAAI;MACvB,IAAI,EAAE,IAAI;MACV,IAAI,EAAE,OAAO;MACb,IAAI,EAAE,OAAO;MACb,IAAI,EAAE;IACR;IACA,MAAMC,WAAU,GAAI,SAAAA,CAAA,EAAW;MAC7BL,MAAM,CAACC,OAAO,CAACK,IAAI,CAAC;QAClBC,QAAQ,EAAG,IAAGV,SAAS,CAACE,KAAM,EAAC;QAC/BS,QAAQ,EAAEJ,gBAAgB,CAAC,IAAI,CAAC;QAChChB,MAAM,EAAEb,KAAK,CAACa,MAAM;QACpBqB,UAAU,EAAE,aAAa;QACzBC,eAAe,EAAE,KAAK;QACtBxC,OAAO,EAAEK,KAAK,CAACL,OAAO,CAACyC,MAAK,GAAI,IAAIpC,KAAK,CAACL,OAAM,GAAIA,OAAO;QAC3DiB,OAAO,EAAEZ,KAAK,CAACY,OAAO;QACtBlB,OAAO,EAAEA,OAAO;QAChB2C,4BAA4B,EAAE,IAAI;QAClCC,sBAAsB,EAAE,OAAO;QAC/BC,kBAAkB,EAAE,GAAG;QACvBC,iBAAiB,EAAE,IAAI;QACvBC,qBAAqB,EAAE,QAAQ;QAC/BC,qBAAqB,EAAE,SAAS;QAChCC,qBAAqB,EAAE,CAAC,iBAAiB,EAAE,YAAY,CAAC;QACxDC,mBAAmB,EAAE,QAAQ;QAC7BC,UAAU,EAAE,KAAK;QACjBC,qBAAqB,EAAE,IAAI;QAAE;QAC7BC,sBAAsB,EAAEC,MAAK,IAAK;UAChC,IAAIhD,KAAK,CAACS,UAAU,EAAE;YACpBuC,MAAM,CAACpB,UAAU,CAAC5B,KAAK,CAACS,UAAU;UACpC;UACAW,OAAO,CAACI,KAAI,GAAI,IAAG;UACnBwB,MAAM,CAACC,EAAE,CAAC,oCAAoC,EAAE,MAAM;YACpD9B,SAAS,CAACK,KAAI,GAAI,IAAG;YACrBN,OAAO,CAACgC,IAAI,CAAC,mBAAmB,EAAEF,MAAM,CAACG,UAAU,EAAE;UACvD,CAAC;QACH,CAAC;QACDlC,KAAKA,CAAC+B,MAAM,EAAE;UACZA,MAAM,CAACC,EAAE,CAAC,wBAAwB,EAAGG,CAAC,IAAK;YACzC/B,UAAU,CAACG,KAAI,GAAI4B,CAAC,CAACC,KAAI;UAC3B,CAAC;QACH,CAAC;QACD;QACA;QACA;QACAC,YAAY,EAAE;QACd;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACF,CAAC;IACH;;IACA,MAAMvB,IAAG,GAAI,SAAAA,CAAA,EAAW;MACtB;MACAnC,IAAI,CAACE,UAAU,EAAGyD,GAAG,IAAK;QACxB,IAAIA,GAAG,EAAE;UACP1D,KAAK,CAAC0D,GAAG,CAACC,OAAO;UACjB;QACF;QACA1B,WAAW,EAAC;MACd,CAAC;IACH;IACA,MAAM2B,cAAa,GAAI,SAAAA,CAAA,EAAW;MAChC,IAAG,CAAChC,MAAM,CAACC,OAAO,EAAE;QAClB;MACF;MACA,MAAMgC,OAAM,GAAIjC,MAAM,CAACC,OAAO,CAACC,GAAG,CAACL,SAAS,CAACE,KAAK;MAClD,IAAIH,UAAU,CAACG,KAAK,EAAE;QACpBkC,OAAO,CAACC,WAAW,CAAC,eAAe;MACrC;MACA,IAAID,OAAO,EAAE;QACXA,OAAO,CAACE,OAAO,EAAC;MAClB;IACF;IACA,MAAMhC,UAAS,GAAI,SAAAA,CAASJ,KAAK,EAAE;MACjCC,MAAM,CAACC,OAAO,CAACC,GAAG,CAACL,SAAS,CAACE,KAAK,CAAC,CAACI,UAAU,CAACJ,KAAK;IACtD;IACA,MAAM2B,UAAS,GAAI,SAAAA,CAAA,EAAW;MAC5B1B,MAAM,CAACC,OAAO,CAACC,GAAG,CAACL,SAAS,CAACE,KAAK,CAAC,CAAC2B,UAAU,EAAC;IACjD;IACA,MAAMU,cAAa,GAAIxE,QAAQ,CAAC,MAAM;MACpC,MAAM0B,KAAI,GAAIf,KAAK,CAACe,KAAI;MACxB,IAAI,mBAAmB,CAAC+C,IAAI,CAAC/C,KAAK,CAAC,EAAE;QAAE;QACrC,OAAQ,GAAEA,KAAM,IAAE;MACpB;MACA,OAAOA,KAAI;IACb,CAAC;IACDzB,SAAS,CAAC,MAAM;MACdyC,IAAI,EAAC;IACP,CAAC;IACDxC,WAAW,CAAC,MAAM;MAChB,IAAIkC,MAAM,CAACC,OAAO,EAAE;QAClBI,WAAW,EAAC;MACd;IACF,CAAC;IACDtC,aAAa,CAAC,MAAM;MAClBiE,cAAc,EAAC;IACjB,CAAC;IACDhE,WAAW,CAAC,MAAM;MAChBgE,cAAc,EAAC;IACjB,CAAC;IACD,OAAO;MACLtC,SAAS;MACTC,OAAO;MACPE,SAAS;MACTD,UAAU;MACVO,UAAU;MACVuB,UAAU;MACVU;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}