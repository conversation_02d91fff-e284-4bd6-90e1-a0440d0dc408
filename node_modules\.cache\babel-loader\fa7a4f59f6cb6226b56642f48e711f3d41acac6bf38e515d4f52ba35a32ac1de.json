{"ast": null, "code": "import { del, get, post, put } from \"@/util/requestUtils\";\nexport function getUserInfo(mobile, callback) {\n  const params = {\n    mobile: mobile\n  };\n  return get(\"/user-center/auth-api/by-mobile\", params, callback);\n}\nexport function getUserList(params, callback) {\n  return get(\"/user-center/list\", params, callback);\n}\nexport function updateUser(data, success) {\n  return put(\"/user-center/user\", data, success);\n}\nexport function saveUser(data, success) {\n  return post(\"/user-center/user\", data, success);\n}\nexport function deleteUser(id, success) {\n  return del(\"/user-center/user\", {\n    id: id\n  }, success);\n}\n\n// 重置密码\nexport function resetPwd(data, callback) {\n  return put(\"/member/public-api/pwd/reset\", data, callback);\n}", "map": {"version": 3, "names": ["del", "get", "post", "put", "getUserInfo", "mobile", "callback", "params", "getUserList", "updateUser", "data", "success", "saveUser", "deleteUser", "id", "resetPwd"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/api/organizational/user.js"], "sourcesContent": ["import {del, get, post, put} from \"@/util/requestUtils\";\n\nexport function getUserInfo(mobile, callback) {\n  const params = { mobile: mobile };\n  return get(\"/user-center/auth-api/by-mobile\", params, callback);\n}\n\nexport function getUserList(params, callback) {\n  return get(\"/user-center/list\", params, callback);\n}\n\nexport function updateUser(data, success) {\n  return put(\"/user-center/user\", data, success)\n}\n\nexport function saveUser(data, success) {\n  return post(\"/user-center/user\", data, success)\n}\n\nexport function deleteUser(id, success) {\n  return del(\"/user-center/user\", {id: id}, success)\n}\n\n// 重置密码\nexport function resetPwd(data, callback) {\n  return put(\"/member/public-api/pwd/reset\", data, callback);\n}\n"], "mappings": "AAAA,SAAQA,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,QAAO,qBAAqB;AAEvD,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC5C,MAAMC,MAAM,GAAG;IAAEF,MAAM,EAAEA;EAAO,CAAC;EACjC,OAAOJ,GAAG,CAAC,iCAAiC,EAAEM,MAAM,EAAED,QAAQ,CAAC;AACjE;AAEA,OAAO,SAASE,WAAWA,CAACD,MAAM,EAAED,QAAQ,EAAE;EAC5C,OAAOL,GAAG,CAAC,mBAAmB,EAAEM,MAAM,EAAED,QAAQ,CAAC;AACnD;AAEA,OAAO,SAASG,UAAUA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACxC,OAAOR,GAAG,CAAC,mBAAmB,EAAEO,IAAI,EAAEC,OAAO,CAAC;AAChD;AAEA,OAAO,SAASC,QAAQA,CAACF,IAAI,EAAEC,OAAO,EAAE;EACtC,OAAOT,IAAI,CAAC,mBAAmB,EAAEQ,IAAI,EAAEC,OAAO,CAAC;AACjD;AAEA,OAAO,SAASE,UAAUA,CAACC,EAAE,EAAEH,OAAO,EAAE;EACtC,OAAOX,GAAG,CAAC,mBAAmB,EAAE;IAACc,EAAE,EAAEA;EAAE,CAAC,EAAEH,OAAO,CAAC;AACpD;;AAEA;AACA,OAAO,SAASI,QAAQA,CAACL,IAAI,EAAEJ,QAAQ,EAAE;EACvC,OAAOH,GAAG,CAAC,8BAA8B,EAAEO,IAAI,EAAEJ,QAAQ,CAAC;AAC5D"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}