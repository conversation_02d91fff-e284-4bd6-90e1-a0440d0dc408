{"ast": null, "code": "import { get, post, put, del } from \"@/util/requestUtils\";\nexport function getMemberList(param, success) {\n  return get(\"/member/list\", param, success);\n}\nexport function getMemberUnauditedList(param, success) {\n  return get(\"/member/unaudited/list\", param, success);\n}\nexport function sealMember(data, success) {\n  return put(\"/member/seal\", data, success);\n}\nexport function unsealMember(data, success) {\n  return put(\"/member/unseal\", data, success);\n}\nexport function approvedMember(data, success) {\n  return put(\"/member/approved\", data, success);\n}\nexport function rejectMember(data, success) {\n  return put(\"/member/reject\", data, success);\n}\nexport function updateMember(data, success) {\n  return put(\"/member/update\", data, success);\n}\nexport function addMemberLevel(data, success) {\n  return post(\"/member/level\", data, success);\n}\nexport function editMemberLevel(data, success) {\n  return put(\"/member/level\", data, success);\n}\nexport function getMemberLevel(id, success) {\n  return get(\"/member/level\", {\n    id: id\n  }, success);\n}\nexport function listMemberLevel() {\n  return \"member/level/list\";\n}\nexport function removeMemberLevel(id, success) {\n  const data = {\n    id: id\n  };\n  return del(\"/member/level\", data, success);\n}\nexport function getListByIds(params, success) {\n  return get(\"/member/public-api/by-ids\", params, success);\n}", "map": {"version": 3, "names": ["get", "post", "put", "del", "getMemberList", "param", "success", "getMemberUnauditedList", "sealMember", "data", "unsealMember", "approvedMember", "rejectMember", "updateMember", "addMemberLevel", "editMemberLevel", "getMemberLevel", "id", "listMemberLevel", "removeMemberLevel", "getListByIds", "params"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/api/member/index.js"], "sourcesContent": ["import { get, post, put, del } from \"@/util/requestUtils\"\n\nexport function getMemberList(param, success) {\n  return get(\"/member/list\", param, success)\n}\n\nexport function getMemberUnauditedList(param, success) {\n  return get(\"/member/unaudited/list\", param, success)\n}\n\nexport function sealMember(data, success) {\n  return put(\"/member/seal\", data, success)\n}\n\nexport function unsealMember(data, success) {\n  return put(\"/member/unseal\", data, success)\n}\n\nexport function approvedMember(data, success) {\n  return put(\"/member/approved\", data, success)\n}\n\nexport function rejectMember(data, success) {\n  return put(\"/member/reject\", data, success)\n}\n\nexport function updateMember(data, success) {\n  return put(\"/member/update\", data, success)\n}\n\nexport function addMemberLevel(data, success) {\n  return post(\"/member/level\", data, success)\n}\n\nexport function editMemberLevel(data, success) {\n  return put(\"/member/level\", data, success)\n}\n\nexport function getMemberLevel(id, success) {\n  return get(\"/member/level\", { id: id }, success)\n}\n\nexport function listMemberLevel() {\n  return \"member/level/list\"\n}\n\nexport function removeMemberLevel(id, success) {\n  const data = { id: id }\n  return del(\"/member/level\", data, success)\n}\n\nexport function getListByIds(params, success) {\n  return get(\"/member/public-api/by-ids\", params, success)\n}\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAQ,qBAAqB;AAEzD,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC5C,OAAON,GAAG,CAAC,cAAc,EAAEK,KAAK,EAAEC,OAAO,CAAC;AAC5C;AAEA,OAAO,SAASC,sBAAsBA,CAACF,KAAK,EAAEC,OAAO,EAAE;EACrD,OAAON,GAAG,CAAC,wBAAwB,EAAEK,KAAK,EAAEC,OAAO,CAAC;AACtD;AAEA,OAAO,SAASE,UAAUA,CAACC,IAAI,EAAEH,OAAO,EAAE;EACxC,OAAOJ,GAAG,CAAC,cAAc,EAAEO,IAAI,EAAEH,OAAO,CAAC;AAC3C;AAEA,OAAO,SAASI,YAAYA,CAACD,IAAI,EAAEH,OAAO,EAAE;EAC1C,OAAOJ,GAAG,CAAC,gBAAgB,EAAEO,IAAI,EAAEH,OAAO,CAAC;AAC7C;AAEA,OAAO,SAASK,cAAcA,CAACF,IAAI,EAAEH,OAAO,EAAE;EAC5C,OAAOJ,GAAG,CAAC,kBAAkB,EAAEO,IAAI,EAAEH,OAAO,CAAC;AAC/C;AAEA,OAAO,SAASM,YAAYA,CAACH,IAAI,EAAEH,OAAO,EAAE;EAC1C,OAAOJ,GAAG,CAAC,gBAAgB,EAAEO,IAAI,EAAEH,OAAO,CAAC;AAC7C;AAEA,OAAO,SAASO,YAAYA,CAACJ,IAAI,EAAEH,OAAO,EAAE;EAC1C,OAAOJ,GAAG,CAAC,gBAAgB,EAAEO,IAAI,EAAEH,OAAO,CAAC;AAC7C;AAEA,OAAO,SAASQ,cAAcA,CAACL,IAAI,EAAEH,OAAO,EAAE;EAC5C,OAAOL,IAAI,CAAC,eAAe,EAAEQ,IAAI,EAAEH,OAAO,CAAC;AAC7C;AAEA,OAAO,SAASS,eAAeA,CAACN,IAAI,EAAEH,OAAO,EAAE;EAC7C,OAAOJ,GAAG,CAAC,eAAe,EAAEO,IAAI,EAAEH,OAAO,CAAC;AAC5C;AAEA,OAAO,SAASU,cAAcA,CAACC,EAAE,EAAEX,OAAO,EAAE;EAC1C,OAAON,GAAG,CAAC,eAAe,EAAE;IAAEiB,EAAE,EAAEA;EAAG,CAAC,EAAEX,OAAO,CAAC;AAClD;AAEA,OAAO,SAASY,eAAeA,CAAA,EAAG;EAChC,OAAO,mBAAmB;AAC5B;AAEA,OAAO,SAASC,iBAAiBA,CAACF,EAAE,EAAEX,OAAO,EAAE;EAC7C,MAAMG,IAAI,GAAG;IAAEQ,EAAE,EAAEA;EAAG,CAAC;EACvB,OAAOd,GAAG,CAAC,eAAe,EAAEM,IAAI,EAAEH,OAAO,CAAC;AAC5C;AAEA,OAAO,SAASc,YAAYA,CAACC,MAAM,EAAEf,OAAO,EAAE;EAC5C,OAAON,GAAG,CAAC,2BAA2B,EAAEqB,MAAM,EAAEf,OAAO,CAAC;AAC1D"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}