{"ast": null, "code": "import { ref } from \"vue\";\nimport { findList, updateLevel, saveLevel } from \"../../../api/member/level\";\nimport Page from \"../../../components/Page\";\nimport { success } from \"../../../util/tipsUtils\";\nexport default {\n  name: \"MemberLevelIndex\",\n  components: {\n    Page\n  },\n  setup() {\n    const list = ref([]);\n    const total = ref(0);\n    const dataLoading = ref(true);\n    const searchParam = ref({\n      keyword: \"\",\n      size: 20,\n      current: 1\n    });\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true;\n      findList(searchParam.value, res => {\n        dataLoading.value = false;\n        if (!res) {\n          return;\n        }\n        list.value = res.list;\n        total.value = res.total;\n      });\n    };\n    loadList();\n    const currentChange = currentPage => {\n      searchParam.value.current = currentPage;\n      loadList();\n    };\n    const sizeChange = s => {\n      searchParam.value.size = s;\n      loadList();\n    };\n    // 搜索\n    const search = () => {\n      loadList();\n    };\n    const memberLevelRules = {\n      name: [{\n        required: true,\n        message: \"请输入名称\",\n        trigger: \"blur\"\n      }],\n      description: [{\n        required: true,\n        message: \"请输入描述\",\n        trigger: \"blur\"\n      }],\n      conditions: [{\n        required: true,\n        message: \"请输入达成条件\",\n        trigger: \"blur\"\n      }]\n    };\n    const memberLevel = ref({});\n    const memberLevelRef = ref(null);\n    const showMemberLevelFormDialog = ref(false);\n    const hideMemberLevelForm = () => {\n      showMemberLevelFormDialog.value = false;\n      memberLevel.value = {};\n    };\n    const add = () => {\n      showMemberLevelFormDialog.value = true;\n    };\n    // 编辑\n    const edit = item => {\n      memberLevel.value = item;\n      showMemberLevelFormDialog.value = true;\n    };\n    //提交\n    const submitMemberLevel = () => {\n      memberLevelRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (memberLevel.value.id) {\n          updateLevel(memberLevel.value, () => {\n            success(\"修改成功\");\n            loadList();\n            hideMemberLevelForm();\n          });\n        } else {\n          saveLevel(memberLevel.value, () => {\n            success(\"新增成功\");\n            loadList();\n            hideMemberLevelForm();\n          });\n        }\n      });\n    };\n    return {\n      list,\n      total,\n      searchParam,\n      search,\n      currentChange,\n      sizeChange,\n      showMemberLevelFormDialog,\n      add,\n      memberLevel,\n      memberLevelRef,\n      edit,\n      hideMemberLevelForm,\n      submitMemberLevel,\n      memberLevelRules,\n      dataLoading\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "findList", "updateLevel", "saveLevel", "Page", "success", "name", "components", "setup", "list", "total", "dataLoading", "searchParam", "keyword", "size", "current", "loadList", "value", "res", "currentChange", "currentPage", "sizeChange", "s", "search", "memberLevelRules", "required", "message", "trigger", "description", "conditions", "memberLevel", "memberLevelRef", "showMemberLevelFormDialog", "hideMemberLevelForm", "add", "edit", "item", "submitMemberLevel", "validate", "valid", "id"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/member/level/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"header\">\n      <el-form :inline=\"true\" :model=\"searchParam\" class=\"demo-form-inline\">\n        <el-form-item label=\"\">\n          <el-input size=\"small\" class=\"search-input\" v-model=\"searchParam.keyword\" placeholder=\"请输入关键字\">\n            <template #append>\n              <el-button size=\"small\" class=\"search-btn\" type=\"primary\" @click=\"search\">搜索</el-button>\n            </template>\n          </el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" type=\"primary\" @click=\"add\">创建会员等级</el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"content\">\n      <div class=\"content-list\">\n        <el-table v-loading=\"dataLoading\" :data=\"list\" size=\"small\" style=\"width: 100%;\">\n          <el-table-column prop=\"id\" label=\"ID\" width=\"50\"/>\n          <el-table-column prop=\"name\" label=\"名称\"/>\n          <el-table-column prop=\"description\" label=\"描述\"/>\n          <el-table-column prop=\"conditions\" label=\"达成条件\">\n            <template #default=\"scope\">\n              {{scope.row.conditions || 0}} 积分\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"50\">\n            <template #default=\"scope\">\n              <el-button type=\"text\" size=\"small\" @click=\"edit(scope.row)\">编辑</el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n    </div>\n    <page style=\"margin-top: 20px;\" :total=\"total\" :current-change=\"currentChange\" :size-change=\"sizeChange\" :page-size=\"searchParam.size\"></page>\n    <el-dialog title=\"新增/编辑积分\" v-model=\"showMemberLevelFormDialog\" :before-close=\"hideMemberLevelForm\">\n      <el-form :model=\"memberLevel\" :rules=\"memberLevelRules\" ref=\"memberLevelRef\">\n        <el-form-item label=\"名称：\" label-width=\"150px\" prop=\"name\">\n          <el-input size=\"small\" v-model=\"memberLevel.name\" placeholder=\"请输入名称\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"描述：\" label-width=\"150px\" prop=\"description\">\n          <el-input size=\"small\" v-model=\"memberLevel.description\" placeholder=\"请输入描述\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"达成条件：\" label-width=\"150px\" prop=\"conditions\">\n          <el-input size=\"small\" v-model=\"memberLevel.conditions\" placeholder=\"请输入大于0的整数\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button size=\"small\" @click=\"hideMemberLevelForm\">取 消</el-button>\n          <el-button size=\"small\" type=\"primary\" @click=\"submitMemberLevel\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import {ref} from \"vue\"\n  import {findList, updateLevel, saveLevel} from \"../../../api/member/level\"\n  import Page from \"../../../components/Page\"\n  import {success} from \"../../../util/tipsUtils\";\n\n  export default {\n    name: \"MemberLevelIndex\",\n    components: {\n      Page\n    },\n    setup() {\n      const list = ref([])\n      const total = ref(0)\n      const dataLoading = ref(true)\n      const searchParam = ref({\n        keyword: \"\",\n        size: 20,\n        current: 1\n      })\n      // 加载列表\n      const loadList = () => {\n        dataLoading.value = true\n        findList(searchParam.value, (res) => {\n          dataLoading.value = false\n          if (!res) {return;}\n          list.value = res.list;\n          total.value = res.total;\n        })\n      }\n      loadList();\n      const currentChange = (currentPage) => {\n        searchParam.value.current = currentPage;\n        loadList();\n      }\n      const sizeChange = (s) => {\n        searchParam.value.size = s;\n        loadList();\n      }\n      // 搜索\n      const search = () => {\n        loadList();\n      }\n      const memberLevelRules = {\n        name: [{ required: true, message: \"请输入名称\", trigger: \"blur\" }],\n        description: [{ required: true, message: \"请输入描述\", trigger: \"blur\" }],\n        conditions: [{ required: true, message: \"请输入达成条件\", trigger: \"blur\" }],\n      }\n      const memberLevel = ref({})\n      const memberLevelRef = ref(null)\n      const showMemberLevelFormDialog = ref(false)\n      const hideMemberLevelForm = () => {\n        showMemberLevelFormDialog.value = false;\n        memberLevel.value = {}\n      }\n      const add = () => {\n        showMemberLevelFormDialog.value = true;\n      }\n      // 编辑\n      const edit = (item) => {\n        memberLevel.value = item\n        showMemberLevelFormDialog.value = true;\n      }\n      //提交\n      const submitMemberLevel = () => {\n        memberLevelRef.value.validate(valid => {\n          if (!valid) {\n            return false;\n          }\n          if (memberLevel.value.id) {\n            updateLevel(memberLevel.value, () => {\n              success(\"修改成功\")\n              loadList()\n              hideMemberLevelForm()\n            });\n          } else {\n            saveLevel(memberLevel.value, () => {\n              success(\"新增成功\")\n              loadList()\n              hideMemberLevelForm()\n            });\n          }\n        })\n      }\n      return {\n        list,\n        total,\n        searchParam,\n        search,\n        currentChange,\n        sizeChange,\n        showMemberLevelFormDialog,\n        add,\n        memberLevel,\n        memberLevelRef,\n        edit,\n        hideMemberLevelForm,\n        submitMemberLevel,\n        memberLevelRules,\n        dataLoading,\n      };\n    }\n  };\n</script>\n<style lang=\"scss\">\n  .header {\n    .el-form {\n      .el-form-item {\n        .el-form-item__content {\n          line-height: 28px;\n          .search-btn {\n            &:hover {\n              color: $--color-primary;\n            }\n          }\n        }\n      }\n    }\n  }\n</style>\n<style scoped lang=\"scss\">\n  .app-container {\n    margin: 20px;\n    .content-list {\n      margin: 0;\n      padding: 0;\n      border: 0;\n      font: inherit;\n      vertical-align: baseline;\n    }\n    .search-input {\n      width: 242px;\n    }\n  }\n</style>\n"], "mappings": "AA2DE,SAAQA,GAAG,QAAO,KAAI;AACtB,SAAQC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAO,2BAA0B;AACzE,OAAOC,IAAG,MAAO,0BAAyB;AAC1C,SAAQC,OAAO,QAAO,yBAAyB;AAE/C,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,UAAU,EAAE;IACVH;EACF,CAAC;EACDI,KAAKA,CAAA,EAAG;IACN,MAAMC,IAAG,GAAIT,GAAG,CAAC,EAAE;IACnB,MAAMU,KAAI,GAAIV,GAAG,CAAC,CAAC;IACnB,MAAMW,WAAU,GAAIX,GAAG,CAAC,IAAI;IAC5B,MAAMY,WAAU,GAAIZ,GAAG,CAAC;MACtBa,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC;IACD;IACA,MAAMC,QAAO,GAAIA,CAAA,KAAM;MACrBL,WAAW,CAACM,KAAI,GAAI,IAAG;MACvBhB,QAAQ,CAACW,WAAW,CAACK,KAAK,EAAGC,GAAG,IAAK;QACnCP,WAAW,CAACM,KAAI,GAAI,KAAI;QACxB,IAAI,CAACC,GAAG,EAAE;UAAC;QAAO;QAClBT,IAAI,CAACQ,KAAI,GAAIC,GAAG,CAACT,IAAI;QACrBC,KAAK,CAACO,KAAI,GAAIC,GAAG,CAACR,KAAK;MACzB,CAAC;IACH;IACAM,QAAQ,EAAE;IACV,MAAMG,aAAY,GAAKC,WAAW,IAAK;MACrCR,WAAW,CAACK,KAAK,CAACF,OAAM,GAAIK,WAAW;MACvCJ,QAAQ,EAAE;IACZ;IACA,MAAMK,UAAS,GAAKC,CAAC,IAAK;MACxBV,WAAW,CAACK,KAAK,CAACH,IAAG,GAAIQ,CAAC;MAC1BN,QAAQ,EAAE;IACZ;IACA;IACA,MAAMO,MAAK,GAAIA,CAAA,KAAM;MACnBP,QAAQ,EAAE;IACZ;IACA,MAAMQ,gBAAe,GAAI;MACvBlB,IAAI,EAAE,CAAC;QAAEmB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC7DC,WAAW,EAAE,CAAC;QAAEH,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MACpEE,UAAU,EAAE,CAAC;QAAEJ,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC;IACtE;IACA,MAAMG,WAAU,GAAI9B,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAM+B,cAAa,GAAI/B,GAAG,CAAC,IAAI;IAC/B,MAAMgC,yBAAwB,GAAIhC,GAAG,CAAC,KAAK;IAC3C,MAAMiC,mBAAkB,GAAIA,CAAA,KAAM;MAChCD,yBAAyB,CAACf,KAAI,GAAI,KAAK;MACvCa,WAAW,CAACb,KAAI,GAAI,CAAC;IACvB;IACA,MAAMiB,GAAE,GAAIA,CAAA,KAAM;MAChBF,yBAAyB,CAACf,KAAI,GAAI,IAAI;IACxC;IACA;IACA,MAAMkB,IAAG,GAAKC,IAAI,IAAK;MACrBN,WAAW,CAACb,KAAI,GAAImB,IAAG;MACvBJ,yBAAyB,CAACf,KAAI,GAAI,IAAI;IACxC;IACA;IACA,MAAMoB,iBAAgB,GAAIA,CAAA,KAAM;MAC9BN,cAAc,CAACd,KAAK,CAACqB,QAAQ,CAACC,KAAI,IAAK;QACrC,IAAI,CAACA,KAAK,EAAE;UACV,OAAO,KAAK;QACd;QACA,IAAIT,WAAW,CAACb,KAAK,CAACuB,EAAE,EAAE;UACxBtC,WAAW,CAAC4B,WAAW,CAACb,KAAK,EAAE,MAAM;YACnCZ,OAAO,CAAC,MAAM;YACdW,QAAQ,EAAC;YACTiB,mBAAmB,EAAC;UACtB,CAAC,CAAC;QACJ,OAAO;UACL9B,SAAS,CAAC2B,WAAW,CAACb,KAAK,EAAE,MAAM;YACjCZ,OAAO,CAAC,MAAM;YACdW,QAAQ,EAAC;YACTiB,mBAAmB,EAAC;UACtB,CAAC,CAAC;QACJ;MACF,CAAC;IACH;IACA,OAAO;MACLxB,IAAI;MACJC,KAAK;MACLE,WAAW;MACXW,MAAM;MACNJ,aAAa;MACbE,UAAU;MACVW,yBAAyB;MACzBE,GAAG;MACHJ,WAAW;MACXC,cAAc;MACdI,IAAI;MACJF,mBAAmB;MACnBI,iBAAiB;MACjBb,gBAAgB;MAChBb;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}