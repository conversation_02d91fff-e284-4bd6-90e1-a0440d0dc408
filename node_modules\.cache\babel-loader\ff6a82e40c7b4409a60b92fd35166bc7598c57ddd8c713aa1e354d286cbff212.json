{"ast": null, "code": "import { createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-4e6f274c\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"path\", {\n  d: \"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 0 0 0-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0 0 14.4 7z\"\n}, null, -1 /* HOISTED */));\nconst _hoisted_2 = [_hoisted_1];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", {\n    style: {\n      \"padding\": \"0 10px\"\n    },\n    onClick: _cache[0] || (_cache[0] = (...args) => $setup.toggleClick && $setup.toggleClick(...args))\n  }, [(_openBlock(), _createElementBlock(\"svg\", {\n    class: _normalizeClass([{\n      'is-active': $props.isActive\n    }, \"hamburger\"]),\n    viewBox: \"0 0 1024 1024\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"64\",\n    height: \"64\"\n  }, _hoisted_2, 2 /* CLASS */))]);\n}", "map": {"version": 3, "names": ["_createElementVNode", "d", "_hoisted_1", "_createElementBlock", "style", "onClick", "_cache", "args", "$setup", "toggleClick", "class", "_normalizeClass", "$props", "isActive", "viewBox", "xmlns", "width", "height"], "sources": ["D:\\sourcecodeAndDocument\\learning-platform\\admin\\src\\components\\Hamburger\\index.vue"], "sourcesContent": ["<template>\n  <div style=\"padding: 0 10px;\" @click=\"toggleClick\">\n    <svg\n      :class=\"{'is-active': isActive}\"\n      class=\"hamburger\"\n      viewBox=\"0 0 1024 1024\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      width=\"64\"\n      height=\"64\">\n      <path d=\"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 0 0 0-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0 0 14.4 7z\"/>\n    </svg>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"HamburgerIndex\",\n  props: {\n    isActive: {\n      type: Boolean,\n      default: false\n    }\n  },\n  setup(props, context) {\n    const toggleClick = () => {\n      context.emit(\"toggleClick\")\n    }\n    return {\n      toggleClick\n    }\n  }\n}\n</script>\n\n<style scoped>\n.hamburger {\n  display: inline-block;\n  vertical-align: middle;\n  width: 14px;\n  height: 14px;\n}\n.hamburger.is-active {\n  transform: rotate(180deg);\n}\n</style>\n"], "mappings": ";;gEASMA,mBAAA,CAA4d;EAAtdC,CAAC,EAAC;AAAkd;oBAA1dC,UAA4d,C;;uBARheC,mBAAA,CAUM;IAVDC,KAAwB,EAAxB;MAAA;IAAA,CAAwB;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAAC,WAAA,IAAAF,IAAA,CAAW;qBAC/CJ,mBAAA,CAQM;IAPHO,KAAK,EAAAC,eAAA;MAAA,aAAgBC,MAAA,CAAAC;IAAQ,GACxB,WAAW;IACjBC,OAAO,EAAC,eAAe;IACvBC,KAAK,EAAC,4BAA4B;IAClCC,KAAK,EAAC,IAAI;IACVC,MAAM,EAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}