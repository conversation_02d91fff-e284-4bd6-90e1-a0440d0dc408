{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-f8a71758\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"container\"\n};\nconst _hoisted_2 = {\n  class: \"header\"\n};\nconst _hoisted_3 = {\n  class: \"content\"\n};\nconst _hoisted_4 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"clearfix\"\n}, [/*#__PURE__*/_createElementVNode(\"span\", null, \"基础信息\")], -1));\nconst _hoisted_5 = {\n  class: \"table-wrapper\"\n};\nconst _hoisted_6 = {\n  class: \"fl-table\",\n  style: {\n    \"width\": \"100%\"\n  }\n};\nconst _hoisted_7 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"td\", null, \"题干：\", -1));\nconst _hoisted_8 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"td\", {\n  width: \"120\"\n}, \"创建时间：\", -1));\nconst _hoisted_9 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"td\", null, \"题干描述：\", -1));\nconst _hoisted_10 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"clearfix\"\n}, [/*#__PURE__*/_createElementVNode(\"span\", null, \"选项\")], -1));\nconst _hoisted_11 = {\n  class: \"fl-table\"\n};\nconst _hoisted_12 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"clearfix\"\n}, [/*#__PURE__*/_createElementVNode(\"span\", null, \"答案\")], -1));\nconst _hoisted_13 = {\n  class: \"table-wrapper\"\n};\nconst _hoisted_14 = {\n  class: \"fl-table\",\n  style: {\n    \"width\": \"100%\"\n  }\n};\nconst _hoisted_15 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"td\", {\n  width: \"120\"\n}, \"参考答案：\", -1));\nconst _hoisted_16 = {\n  key: 0\n};\nconst _hoisted_17 = {\n  style: {\n    \"color\": \"#999999\"\n  }\n};\nconst _hoisted_18 = {\n  key: 1\n};\nconst _hoisted_19 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"td\", null, \"答案解析：\", -1));\nconst _hoisted_20 = {\n  key: 0,\n  class: \"dialog-footer\",\n  style: {\n    \"text-align\": \"right\",\n    \"margin-top\": \"30px\"\n  }\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_cascader = _resolveComponent(\"el-cascader\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_rate = _resolveComponent(\"el-rate\");\n  const _component_page = _resolveComponent(\"page\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_form, {\n    inline: true,\n    model: $setup.params,\n    class: \"demo-form-inline\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      label: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"mini\",\n        class: \"search-input\",\n        modelValue: $setup.params.keyword,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.params.keyword = $event),\n        placeholder: \"请输入关键字\"\n      }, null, 8, [\"modelValue\"]), _createVNode(_component_el_button, {\n        size: \"mini\",\n        class: \"search-btn\",\n        type: \"primary\",\n        onClick: $setup.search\n      }, {\n        default: _withCtx(() => [_createTextVNode(\"搜索\")]),\n        _: 1\n      }, 8, [\"onClick\"])]),\n      _: 1\n    }), _createVNode(_component_el_form_item, {\n      label: \"题型\",\n      class: \"status\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_select, {\n        size: \"mini\",\n        modelValue: $setup.params.type,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.params.type = $event),\n        onChange: $setup.search\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_option, {\n          label: \"全部\",\n          value: \"\"\n        }), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.questionTypeMap, (key, value) => {\n          return _openBlock(), _createBlock(_component_el_option, {\n            label: key,\n            value: value,\n            key: value\n          }, null, 8, [\"label\", \"value\"]);\n        }), 128))]),\n        _: 1\n      }, 8, [\"modelValue\", \"onChange\"])]),\n      _: 1\n    }), _createVNode(_component_el_form_item, {\n      label: \"状态\",\n      class: \"status\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_select, {\n        size: \"mini\",\n        modelValue: $setup.params.status,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.params.status = $event),\n        onChange: $setup.search\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_option, {\n          label: \"全部\",\n          value: \"\"\n        }), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.statusMap, (key, value) => {\n          return _openBlock(), _createBlock(_component_el_option, {\n            label: key,\n            value: value,\n            key: value\n          }, null, 8, [\"label\", \"value\"]);\n        }), 128))]),\n        _: 1\n      }, 8, [\"modelValue\", \"onChange\"])]),\n      _: 1\n    }), !$props.isComponent ? (_openBlock(), _createBlock(_component_el_form_item, {\n      key: 0,\n      label: \"分类\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_cascader, {\n        size: \"mini\",\n        modelValue: $setup.selectCidList,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.selectCidList = $event),\n        options: $setup.categoryOptions,\n        props: {\n          checkStrictly: true\n        },\n        onChange: $setup.search,\n        clearable: \"\"\n      }, null, 8, [\"modelValue\", \"options\", \"onChange\"])]),\n      _: 1\n    })) : _createCommentVNode(\"\", true)]),\n    _: 1\n  }, 8, [\"model\"])]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_table, {\n    ref: \"multipleTable\",\n    onSelectionChange: $setup.selectItem,\n    data: $setup.list,\n    style: {\n      \"width\": \"100%\"\n    },\n    onExpandChange: $setup.expandChange\n  }, {\n    default: _withCtx(() => [$props.isComponent ? (_openBlock(), _createBlock(_component_el_table_column, {\n      key: 0,\n      type: \"selection\",\n      width: \"30\"\n    })) : _createCommentVNode(\"\", true), _createVNode(_component_el_table_column, {\n      type: \"expand\"\n    }, {\n      default: _withCtx(scope => [_createVNode(_component_el_card, {\n        class: \"box-card\"\n      }, {\n        header: _withCtx(() => [_hoisted_4]),\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"table\", _hoisted_6, [_createElementVNode(\"tr\", null, [_hoisted_7, _createElementVNode(\"td\", null, _toDisplayString(scope.row.title), 1)]), _createElementVNode(\"tr\", null, [_hoisted_8, _createElementVNode(\"td\", null, _toDisplayString(scope.row.createTime), 1)]), _createElementVNode(\"tr\", null, [_hoisted_9, _createElementVNode(\"td\", null, _toDisplayString(scope.row.note), 1)])])])]),\n        _: 2\n      }, 1024), scope.row.type !== 'subjective' && scope.row.type !== 'fill_blank' ? (_openBlock(), _createBlock(_component_el_card, {\n        key: 0,\n        style: {\n          \"margin-top\": \"20px\"\n        }\n      }, {\n        header: _withCtx(() => [_hoisted_10]),\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_11, [scope.row.options ? (_openBlock(), _createBlock(_component_el_table, {\n          key: 0,\n          data: JSON.parse(scope.row.options),\n          \"show-header\": false,\n          style: {\n            \"width\": \"100%\"\n          }\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_table_column, {\n            width: \"40px\",\n            label: \"序号\"\n          }, {\n            default: _withCtx(scope => [_createTextVNode(_toDisplayString(scope.row.key + \".\"), 1)]),\n            _: 2\n          }, 1024), _createVNode(_component_el_table_column, {\n            prop: \"value\",\n            label: \"内容\"\n          })]),\n          _: 2\n        }, 1032, [\"data\"])) : _createCommentVNode(\"\", true)])]),\n        _: 2\n      }, 1024)) : _createCommentVNode(\"\", true), _createVNode(_component_el_card, {\n        style: {\n          \"margin-top\": \"20px\"\n        }\n      }, {\n        header: _withCtx(() => [_hoisted_12]),\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"table\", _hoisted_14, [_createElementVNode(\"tbody\", null, [_createElementVNode(\"tr\", null, [_hoisted_15, scope.row.type === 'fill_blank' ? (_openBlock(), _createElementBlock(\"td\", _hoisted_16, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(scope.row.referenceAnswer.split('[_]'), (item, index) => {\n          return _openBlock(), _createElementBlock(\"div\", {\n            key: item,\n            style: {\n              \"line-height\": \"40px\"\n            }\n          }, [_createElementVNode(\"span\", _hoisted_17, \"填空 \" + _toDisplayString(index + 1) + \" ：\", 1), _createTextVNode(\" \" + _toDisplayString(item), 1)]);\n        }), 128))])) : (_openBlock(), _createElementBlock(\"td\", _hoisted_18, _toDisplayString(scope.row.referenceAnswer), 1))]), _createElementVNode(\"tr\", null, [_hoisted_19, _createElementVNode(\"td\", null, _toDisplayString(scope.row.referenceAnswerNote), 1)])])])])]),\n        _: 2\n      }, 1024)]),\n      _: 1\n    }), _createVNode(_component_el_table_column, {\n      prop: \"id\",\n      label: \"ID\",\n      width: \"50\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"题型\",\n      width: \"80\"\n    }, {\n      default: _withCtx(scope => [_createTextVNode(_toDisplayString($setup.questionTypeMap[scope.row.type]), 1)]),\n      _: 1\n    }), _createVNode(_component_el_table_column, {\n      prop: \"title\",\n      label: \"题干\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"score\",\n      label: \"分数\",\n      width: \"80\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"difficulty\",\n      label: \"难度\",\n      width: \"140\"\n    }, {\n      default: _withCtx(scope => [_createVNode(_component_el_rate, {\n        disabled: true,\n        modelValue: scope.row.difficulty,\n        \"onUpdate:modelValue\": $event => scope.row.difficulty = $event,\n        colors: $setup.colors\n      }, null, 8, [\"modelValue\", \"onUpdate:modelValue\", \"colors\"])]),\n      _: 1\n    }), _createVNode(_component_el_table_column, {\n      prop: \"difficulty\",\n      label: \"状态\",\n      width: \"80\"\n    }, {\n      default: _withCtx(scope => [_createTextVNode(_toDisplayString($setup.statusMap[scope.row.status]), 1)]),\n      _: 1\n    }), !$props.isComponent ? (_openBlock(), _createBlock(_component_el_table_column, {\n      key: 1,\n      label: \"操作\",\n      width: \"100\"\n    }, {\n      default: _withCtx(scope => [_createVNode(_component_el_button, {\n        class: \"right-btn\",\n        type: \"text\",\n        onClick: $event => $setup.edit(scope.row),\n        size: \"mini\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(\"编辑\")]),\n        _: 2\n      }, 1032, [\"onClick\"]), _createVNode(_component_el_button, {\n        class: \"right-btn\",\n        type: \"text\",\n        onClick: $event => $setup.remove(scope.row.id),\n        size: \"mini\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(\"删除\")]),\n        _: 2\n      }, 1032, [\"onClick\"])]),\n      _: 1\n    })) : _createCommentVNode(\"\", true)]),\n    _: 1\n  }, 8, [\"onSelectionChange\", \"data\", \"onExpandChange\"])]), _createVNode(_component_page, {\n    total: $setup.total,\n    \"page-size\": $setup.params.size,\n    \"current-change\": $setup.pageChange,\n    \"size-change\": $setup.sizeChange\n  }, null, 8, [\"total\", \"page-size\", \"current-change\", \"size-change\"])]), $props.isComponent ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_createVNode(_component_el_button, {\n    onClick: $props.hideComponent\n  }, {\n    default: _withCtx(() => [_createTextVNode(\"取 消\")]),\n    _: 1\n  }, 8, [\"onClick\"]), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: _cache[4] || (_cache[4] = $event => $props.selectionChangeCallback($setup.commodityIdList))\n  }, {\n    default: _withCtx(() => [_createTextVNode(\"确 定\")]),\n    _: 1\n  })])) : _createCommentVNode(\"\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementVNode", "style", "width", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_createVNode", "_component_el_form", "inline", "model", "$setup", "params", "_component_el_form_item", "label", "_component_el_input", "size", "keyword", "$event", "placeholder", "_component_el_button", "type", "onClick", "search", "_component_el_select", "onChange", "_component_el_option", "value", "_Fragment", "_renderList", "questionTypeMap", "key", "_createBlock", "status", "statusMap", "$props", "isComponent", "_component_el_cascader", "selectCidList", "options", "categoryOptions", "props", "checkStrictly", "clearable", "_hoisted_3", "_component_el_table", "ref", "onSelectionChange", "selectItem", "data", "list", "onExpandChange", "expandChange", "_component_el_table_column", "default", "_withCtx", "scope", "_component_el_card", "header", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_toDisplayString", "row", "title", "_hoisted_8", "createTime", "_hoisted_9", "note", "_hoisted_10", "_hoisted_11", "JSON", "parse", "prop", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "referenceAnswer", "split", "item", "index", "_hoisted_17", "_hoisted_18", "_hoisted_19", "referenceAnswerNote", "_component_el_rate", "disabled", "difficulty", "colors", "edit", "remove", "id", "_component_page", "total", "pageChange", "sizeChange", "_hoisted_20", "hideComponent", "_cache", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commodityIdList"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/exam/question-lib/index.vue"], "sourcesContent": ["<template>\n  <div>\n    <div class=\"container\">\n      <div class=\"header\">\n        <el-form :inline=\"true\" :model=\"params\" class=\"demo-form-inline\">\n          <el-form-item label=\"\">\n            <el-input size=\"mini\" class=\"search-input\" v-model=\"params.keyword\" placeholder=\"请输入关键字\"></el-input>\n            <el-button size=\"mini\" class=\"search-btn\" type=\"primary\" @click=\"search\">搜索</el-button>\n          </el-form-item>\n          <el-form-item label=\"题型\" class=\"status\">\n            <el-select size=\"mini\" v-model=\"params.type\" @change=\"search\">\n              <el-option label=\"全部\" value=\"\"></el-option>\n              <el-option :label=\"key\" :value=\"value\" v-for=\"(key, value) in questionTypeMap\" :key=\"value\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"状态\" class=\"status\">\n            <el-select size=\"mini\" v-model=\"params.status\" @change=\"search\">\n              <el-option label=\"全部\" value=\"\"></el-option>\n              <el-option :label=\"key\" :value=\"value\" v-for=\"(key, value) in statusMap\" :key=\"value\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"分类\" v-if=\"!isComponent\">\n            <el-cascader size=\"mini\" v-model=\"selectCidList\" :options=\"categoryOptions\" :props=\"{ checkStrictly: true }\" @change=\"search\" clearable></el-cascader>\n          </el-form-item>\n        </el-form>\n      </div>\n      <div class=\"content\">\n        <el-table ref=\"multipleTable\" @selection-change=\"selectItem\" :data=\"list\" style=\"width: 100%;\" @expand-change=\"expandChange\">\n          <el-table-column v-if=\"isComponent\" type=\"selection\" width=\"30\"></el-table-column>\n          <el-table-column type=\"expand\">\n            <template #default=\"scope\">\n              <el-card class=\"box-card\">\n                <template #header>\n                  <div class=\"clearfix\">\n                    <span>基础信息</span>\n                  </div>\n                </template>\n                <div class=\"table-wrapper\">\n                  <table class=\"fl-table\" style=\"width: 100%;\">\n                    <tr><td>题干：</td><td>{{scope.row.title}}</td></tr>\n                    <tr><td width=\"120\">创建时间：</td><td>{{scope.row.createTime}}</td></tr>\n                    <tr><td>题干描述：</td><td>{{scope.row.note}}</td></tr>\n                  </table>\n                </div>\n              </el-card>\n              <el-card style=\"margin-top: 20px;\" v-if=\"scope.row.type !== 'subjective' && scope.row.type !== 'fill_blank'\">\n                <template #header>\n                  <div class=\"clearfix\">\n                    <span>选项</span>\n                  </div>\n                </template>\n                <div class=\"fl-table\">\n                  <el-table :data=\"JSON.parse(scope.row.options)\" v-if=\"scope.row.options\" :show-header=\"false\" style=\"width: 100%;\">\n                    <el-table-column width=\"40px\" label=\"序号\">\n                      <template #default=\"scope\">\n                        {{scope.row.key + \".\"}}\n                      </template>\n                    </el-table-column>\n                    <el-table-column prop=\"value\" label=\"内容\"></el-table-column>\n                  </el-table>\n                </div>\n              </el-card>\n              <el-card style=\"margin-top: 20px;\">\n                <template #header>\n                  <div class=\"clearfix\">\n                    <span>答案</span>\n                  </div>\n                </template>\n                <div class=\"table-wrapper\">\n                  <table class=\"fl-table\" style=\"width: 100%;\">\n                    <tbody>\n                      <tr>\n                        <td width=\"120\">参考答案：</td>\n                        <td v-if=\"scope.row.type === 'fill_blank'\">\n                          <div v-for=\"(item, index) in scope.row.referenceAnswer.split('[_]')\" :key=\"item\" style=\"line-height: 40px;\">\n                            <span style=\"color: #999999;\">填空 {{index + 1}} ：</span>\n                            {{item}}</div>\n                        </td>\n                        <td v-else>{{scope.row.referenceAnswer}}</td>\n                      </tr>\n                      <tr><td>答案解析：</td><td>{{scope.row.referenceAnswerNote}}</td></tr>\n                    </tbody>\n                  </table>\n                </div>\n              </el-card>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"id\" label=\"ID\" width=\"50\"></el-table-column>\n          <el-table-column label=\"题型\" width=\"80\">\n            <template #default=\"scope\">\n              {{questionTypeMap[scope.row.type]}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"title\" label=\"题干\"></el-table-column>\n          <el-table-column prop=\"score\" label=\"分数\" width=\"80\"></el-table-column>\n          <el-table-column prop=\"difficulty\" label=\"难度\" width=\"140\">\n            <template #default=\"scope\">\n              <el-rate :disabled=\"true\" v-model=\"scope.row.difficulty\" :colors=\"colors\"></el-rate>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"difficulty\" label=\"状态\" width=\"80\">\n            <template #default=\"scope\">\n              {{statusMap[scope.row.status]}}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" v-if=\"!isComponent\" width=\"100\">\n            <template #default=\"scope\">\n              <el-button class=\"right-btn\" type=\"text\" @click=\"edit(scope.row)\" size=\"mini\">编辑</el-button>\n              <el-button class=\"right-btn\" type=\"text\" @click=\"remove(scope.row.id)\" size=\"mini\">删除</el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n      <page :total=\"total\" :page-size=\"params.size\" :current-change=\"pageChange\" :size-change=\"sizeChange\"></page>\n    </div>\n    <template v-if=\"isComponent\">\n      <div class=\"dialog-footer\" style=\"text-align: right;margin-top: 30px;\">\n        <el-button @click=\"hideComponent\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"selectionChangeCallback(commodityIdList)\">确 定</el-button>\n      </div>\n    </template>\n  </div>\n</template>\n\n<script>\nimport {ref} from \"vue\"\nimport {findCategoryList, toTree} from \"@/api/exam/question-lib/category\"\nimport {findList, delQuestion} from \"@/api/exam/question-lib/question\"\nimport Page from \"@/components/Page\"\nimport {confirm, success} from \"@/util/tipsUtils\";\nimport router from \"@/router\";\n\nexport default {\n  name: \"questionLib\",\n  components: {\n    Page\n  },\n  props: {\n    isComponent: {\n      type: Boolean,\n      default: false\n    },\n    selectionChangeCallback: {\n      type: Function,\n      default: (a) => {\n        console.log(a)\n      }\n    },\n    componentCid: {\n      type: Number,\n      default: 0\n    },\n    hideComponent: {\n      type: Function,\n      default: () => {\n      }\n    }\n  },\n  setup(props) {\n    const selectCidList = ref([])\n    const commodityIdList = ref([])\n    const categoryOptions = ref([])\n    const list = ref([])\n    const total = ref(0)\n    const params = ref({\n      keyword: \"\",\n      cid: \"\",\n      type: \"\",\n      size: 20,\n      current: 1\n    })\n    const colors = [\"#99A9BF\", \"#F7BA2A\", \"#FF9900\"]\n    const questionTypeMap = {\n      \"single_choice\": \"单选题\",\n      \"multi_choice\": \"多选题\",\n      \"judgment\": \"判断题\",\n      \"fill_blank\": \"填空题\",\n      \"subjective\": \"简答题\",\n    }\n    const statusMap = {\n      \"draft\": \"草稿\",\n      \"published\": \"已发布\",\n      \"deleted\": \"已删除\"\n    }\n    // 加载分类\n    const loadCategory = () => {\n      findCategoryList(0, true, (res) => {\n        if (res) {\n          categoryOptions.value = toTree(res);\n        }\n      })\n    }\n    loadCategory()\n    // 加载列表\n    const loadList = () => {\n      if (props.isComponent) {\n        params.value.cid = props.componentCid;\n      }\n      findList(params.value, (res) => {\n        if (!res) {return;}\n        list.value = res.list;\n        total.value = res.total;\n      })\n    }\n    loadList()\n    // 搜索\n    const search = () => {\n      if (selectCidList.value && selectCidList.value.length) {\n        params.value.cid = selectCidList.value[selectCidList.value.length - 1];\n      }\n      loadList();\n    }\n    // 编辑\n    const edit = (item) => {\n      router.push({path: \"/exam/question-lib/\" + item.type.replace(\"_\", \"-\"), query: { id : item.id }})\n    }\n    const remove = (id) => {\n      confirm(\"确认删除试题?\", \"提示\", () => {\n        delQuestion(id, () => {\n          success(\"删除成功\")\n          loadList()\n        })\n      })\n    }\n    const pageChange = (c) => {\n      console.log(\"========, c:\",c)\n      params.value.current = c;\n      loadList();\n    }\n    const sizeChange =function(size){\n      params.value.size = size;\n      loadList();\n    }\n    const expandChange = (row, expandedRows) => {\n      // 展开\n      if(expandedRows.length>0) {\n        console.log(row, expandedRows)\n      }\n    }\n    // 选择列表项\n    const selectItem = (val) => {\n      commodityIdList.value = [];\n      if (val && val.length > 0) {\n        for (const valElement of val) {\n          commodityIdList.value.push(valElement.id);\n        }\n      }\n    }\n    return {\n      colors,\n      questionTypeMap,\n      statusMap,\n      selectCidList,\n      commodityIdList,\n      categoryOptions,\n      list,\n      total,\n      params,\n      search,\n      selectItem,\n      edit,\n      remove,\n      pageChange,\n      sizeChange,\n      expandChange\n    }\n  }\n};\n</script>\n\n<style  scoped lang=\"scss\">\n  .container {\n    margin: 20px;\n  }\n  .image {\n    height: 60px;\n    display: inline-block;\n  }\n  .right-btn{\n    margin: 5px 10px 5px 0;\n  }\n  .search-input {\n    width: 242px;\n  }\n  ::v-deep .el-table-column--selection .cell{\n    padding-left: 14px;\n    padding-right: 14px;\n  }\n  ::v-deep .el-table tbody tr:hover > td {\n    background-color: transparent;\n  }\n  .fl-table {\n    tr:last-child, ::v-deep tr:last-child {\n      td {\n        border: 0;\n      }\n    }\n  }\n  .dialog-footer {\n    text-align: center;\n    margin-top: 40px;\n  }\n</style>\n"], "mappings": ";;;EAESA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAQ;;EAuBdA,KAAK,EAAC;AAAS;gEAORC,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAU,I,aACnBC,mBAAA,CAAiB,cAAX,MAAI,E;;EAGTD,KAAK,EAAC;AAAe;;EACjBA,KAAK,EAAC,UAAU;EAACE,KAAoB,EAApB;IAAA;EAAA;;gEAClBD,mBAAA,CAAY,YAAR,KAAG;gEACPA,mBAAA,CAA0B;EAAtBE,KAAK,EAAC;AAAK,GAAC,OAAK;gEACrBF,mBAAA,CAAc,YAAV,OAAK;iEAMfA,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAU,I,aACnBC,mBAAA,CAAe,cAAT,IAAE,E;;EAGPD,KAAK,EAAC;AAAU;iEAanBC,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAU,I,aACnBC,mBAAA,CAAe,cAAT,IAAE,E;;EAGPD,KAAK,EAAC;AAAe;;EACjBA,KAAK,EAAC,UAAU;EAACE,KAAoB,EAApB;IAAA;EAAA;;iEAGlBD,mBAAA,CAA0B;EAAtBE,KAAK,EAAC;AAAK,GAAC,OAAK;;;;;EAGXD,KAAuB,EAAvB;IAAA;EAAA;AAAuB;;;;iEAK/BD,mBAAA,CAAc,YAAV,OAAK;;;EAoCxBD,KAAK,EAAC,eAAe;EAACE,KAA2C,EAA3C;IAAA;IAAA;EAAA;;;;;;;;;;;;;;;uBAnH/BE,mBAAA,CAwHM,cAvHJH,mBAAA,CAgHM,OAhHNI,UAgHM,GA/GJJ,mBAAA,CAsBM,OAtBNK,UAsBM,GArBJC,YAAA,CAoBUC,kBAAA;IApBAC,MAAM,EAAE,IAAI;IAAGC,KAAK,EAAEC,MAAA,CAAAC,MAAM;IAAEZ,KAAK,EAAC;;sBAC5C,MAGe,CAHfO,YAAA,CAGeM,uBAAA;MAHDC,KAAK,EAAC;IAAE;wBACpB,MAAoG,CAApGP,YAAA,CAAoGQ,mBAAA;QAA1FC,IAAI,EAAC,MAAM;QAAChB,KAAK,EAAC,cAAc;oBAAUW,MAAA,CAAAC,MAAM,CAACK,OAAO;mEAAdN,MAAA,CAAAC,MAAM,CAACK,OAAO,GAAAC,MAAA;QAAEC,WAAW,EAAC;mCAChFZ,YAAA,CAAuFa,oBAAA;QAA5EJ,IAAI,EAAC,MAAM;QAAChB,KAAK,EAAC,YAAY;QAACqB,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAEX,MAAA,CAAAY;;0BAAQ,MAAE,C,iBAAF,IAAE,E;;;;QAE7EhB,YAAA,CAKeM,uBAAA;MALDC,KAAK,EAAC,IAAI;MAACd,KAAK,EAAC;;wBAC7B,MAGY,CAHZO,YAAA,CAGYiB,oBAAA;QAHDR,IAAI,EAAC,MAAM;oBAAUL,MAAA,CAAAC,MAAM,CAACS,IAAI;mEAAXV,MAAA,CAAAC,MAAM,CAACS,IAAI,GAAAH,MAAA;QAAGO,QAAM,EAAEd,MAAA,CAAAY;;0BACpD,MAA2C,CAA3ChB,YAAA,CAA2CmB,oBAAA;UAAhCZ,KAAK,EAAC,IAAI;UAACa,KAAK,EAAC;+BAC5BvB,mBAAA,CAAwGwB,SAAA,QAAAC,WAAA,CAA1ClB,MAAA,CAAAmB,eAAe,GAA9BC,GAAG,EAAEJ,KAAK;+BAAzDK,YAAA,CAAwGN,oBAAA;YAA5FZ,KAAK,EAAEiB,GAAG;YAAGJ,KAAK,EAAEA,KAAK;YAA2CI,GAAG,EAAEJ;;;;;;QAGzFpB,YAAA,CAKeM,uBAAA;MALDC,KAAK,EAAC,IAAI;MAACd,KAAK,EAAC;;wBAC7B,MAGY,CAHZO,YAAA,CAGYiB,oBAAA;QAHDR,IAAI,EAAC,MAAM;oBAAUL,MAAA,CAAAC,MAAM,CAACqB,MAAM;mEAAbtB,MAAA,CAAAC,MAAM,CAACqB,MAAM,GAAAf,MAAA;QAAGO,QAAM,EAAEd,MAAA,CAAAY;;0BACtD,MAA2C,CAA3ChB,YAAA,CAA2CmB,oBAAA;UAAhCZ,KAAK,EAAC,IAAI;UAACa,KAAK,EAAC;+BAC5BvB,mBAAA,CAAkGwB,SAAA,QAAAC,WAAA,CAApClB,MAAA,CAAAuB,SAAS,GAAxBH,GAAG,EAAEJ,KAAK;+BAAzDK,YAAA,CAAkGN,oBAAA;YAAtFZ,KAAK,EAAEiB,GAAG;YAAGJ,KAAK,EAAEA,KAAK;YAAqCI,GAAG,EAAEJ;;;;;;SAGnDQ,MAAA,CAAAC,WAAW,I,cAA3CJ,YAAA,CAEenB,uBAAA;;MAFDC,KAAK,EAAC;;wBAClB,MAAsJ,CAAtJP,YAAA,CAAsJ8B,sBAAA;QAAzIrB,IAAI,EAAC,MAAM;oBAAUL,MAAA,CAAA2B,aAAa;mEAAb3B,MAAA,CAAA2B,aAAa,GAAApB,MAAA;QAAGqB,OAAO,EAAE5B,MAAA,CAAA6B,eAAe;QAAGC,KAAK,EAAE;UAAAC,aAAA;QAAA,CAAuB;QAAGjB,QAAM,EAAEd,MAAA,CAAAY,MAAM;QAAEoB,SAAS,EAAT;;;;;sBAIpI1C,mBAAA,CAsFM,OAtFN2C,UAsFM,GArFJrC,YAAA,CAoFWsC,mBAAA;IApFDC,GAAG,EAAC,eAAe;IAAEC,iBAAgB,EAAEpC,MAAA,CAAAqC,UAAU;IAAGC,IAAI,EAAEtC,MAAA,CAAAuC,IAAI;IAAEhD,KAAoB,EAApB;MAAA;IAAA,CAAoB;IAAEiD,cAAa,EAAExC,MAAA,CAAAyC;;sBAC7G,MAAkF,CAA3DjB,MAAA,CAAAC,WAAW,I,cAAlCJ,YAAA,CAAkFqB,0BAAA;;MAA9ChC,IAAI,EAAC,WAAW;MAAClB,KAAK,EAAC;yCAC3DI,YAAA,CAyDkB8C,0BAAA;MAzDDhC,IAAI,EAAC;IAAQ;MACjBiC,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACvBjD,YAAA,CAaUkD,kBAAA;QAbDzD,KAAK,EAAC;MAAU;QACZ0D,MAAM,EAAAH,QAAA,CACf,MAEM,CAFNI,UAEM,C;0BAER,MAMM,CANN1D,mBAAA,CAMM,OANN2D,UAMM,GALJ3D,mBAAA,CAIQ,SAJR4D,UAIQ,GAHN5D,mBAAA,CAAiD,aAA7C6D,UAAY,EAAA7D,mBAAA,CAA4B,YAAA8D,gBAAA,CAAtBP,KAAK,CAACQ,GAAG,CAACC,KAAK,M,GACrChE,mBAAA,CAAoE,aAAhEiE,UAA0B,EAAAjE,mBAAA,CAAiC,YAAA8D,gBAAA,CAA3BP,KAAK,CAACQ,GAAG,CAACG,UAAU,M,GACxDlE,mBAAA,CAAkD,aAA9CmE,UAAc,EAAAnE,mBAAA,CAA2B,YAAA8D,gBAAA,CAArBP,KAAK,CAACQ,GAAG,CAACK,IAAI,M;;gBAIHb,KAAK,CAACQ,GAAG,CAAC3C,IAAI,qBAAqBmC,KAAK,CAACQ,GAAG,CAAC3C,IAAI,qB,cAA1FW,YAAA,CAgBUyB,kBAAA;;QAhBDvD,KAAyB,EAAzB;UAAA;QAAA;;QACIwD,MAAM,EAAAH,QAAA,CACf,MAEM,CAFNe,WAEM,C;0BAER,MASM,CATNrE,mBAAA,CASM,OATNsE,WASM,GARkDf,KAAK,CAACQ,GAAG,CAACzB,OAAO,I,cAAvEP,YAAA,CAOWa,mBAAA;;UAPAI,IAAI,EAAEuB,IAAI,CAACC,KAAK,CAACjB,KAAK,CAACQ,GAAG,CAACzB,OAAO;UAA6B,aAAW,EAAE,KAAK;UAAErC,KAAoB,EAApB;YAAA;UAAA;;4BAC5F,MAIkB,CAJlBK,YAAA,CAIkB8C,0BAAA;YAJDlD,KAAK,EAAC,MAAM;YAACW,KAAK,EAAC;;YACvBwC,OAAO,EAAAC,QAAA,CAAEC,KAAK,K,kCACrBA,KAAK,CAACQ,GAAG,CAACjC,GAAG,Y;;oBAGnBxB,YAAA,CAA2D8C,0BAAA;YAA1CqB,IAAI,EAAC,OAAO;YAAC5D,KAAK,EAAC;;;;;iDAI1CP,YAAA,CAsBUkD,kBAAA;QAtBDvD,KAAyB,EAAzB;UAAA;QAAA;MAAyB;QACrBwD,MAAM,EAAAH,QAAA,CACf,MAEM,CAFNoB,WAEM,C;0BAER,MAeM,CAfN1E,mBAAA,CAeM,OAfN2E,WAeM,GAdJ3E,mBAAA,CAaQ,SAbR4E,WAaQ,GAZN5E,mBAAA,CAWQ,gBAVNA,mBAAA,CAQK,aAPH6E,WAA0B,EAChBtB,KAAK,CAACQ,GAAG,CAAC3C,IAAI,qB,cAAxBjB,mBAAA,CAIK,MAAA2E,WAAA,I,kBAHH3E,mBAAA,CAEgBwB,SAAA,QAAAC,WAAA,CAFa2B,KAAK,CAACQ,GAAG,CAACgB,eAAe,CAACC,KAAK,UAA/CC,IAAI,EAAEC,KAAK;+BAAxB/E,mBAAA,CAEgB;YAFsD2B,GAAG,EAAEmD,IAAI;YAAEhF,KAA0B,EAA1B;cAAA;YAAA;cAC/ED,mBAAA,CAAuD,QAAvDmF,WAAuD,EAAzB,KAAG,GAAArB,gBAAA,CAAEoB,KAAK,QAAM,IAAE,M,iBAAO,GACvD,GAAApB,gBAAA,CAAEmB,IAAI,M;sCAEV9E,mBAAA,CAA6C,MAAAiF,WAAA,EAAAtB,gBAAA,CAAhCP,KAAK,CAACQ,GAAG,CAACgB,eAAe,O,GAExC/E,mBAAA,CAAiE,aAA7DqF,WAAc,EAAArF,mBAAA,CAA0C,YAAA8D,gBAAA,CAApCP,KAAK,CAACQ,GAAG,CAACuB,mBAAmB,M;;;;QAOjEhF,YAAA,CAAmE8C,0BAAA;MAAlDqB,IAAI,EAAC,IAAI;MAAC5D,KAAK,EAAC,IAAI;MAACX,KAAK,EAAC;QAC5CI,YAAA,CAIkB8C,0BAAA;MAJDvC,KAAK,EAAC,IAAI;MAACX,KAAK,EAAC;;MACrBmD,OAAO,EAAAC,QAAA,CAAEC,KAAK,K,kCACrB7C,MAAA,CAAAmB,eAAe,CAAC0B,KAAK,CAACQ,GAAG,CAAC3C,IAAI,O;;QAGpCd,YAAA,CAA2D8C,0BAAA;MAA1CqB,IAAI,EAAC,OAAO;MAAC5D,KAAK,EAAC;QACpCP,YAAA,CAAsE8C,0BAAA;MAArDqB,IAAI,EAAC,OAAO;MAAC5D,KAAK,EAAC,IAAI;MAACX,KAAK,EAAC;QAC/CI,YAAA,CAIkB8C,0BAAA;MAJDqB,IAAI,EAAC,YAAY;MAAC5D,KAAK,EAAC,IAAI;MAACX,KAAK,EAAC;;MACvCmD,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACvBjD,YAAA,CAAoFiF,kBAAA;QAA1EC,QAAQ,EAAE,IAAI;oBAAWjC,KAAK,CAACQ,GAAG,CAAC0B,UAAU;yCAApBlC,KAAK,CAACQ,GAAG,CAAC0B,UAAU,GAAAxE,MAAA;QAAGyE,MAAM,EAAEhF,MAAA,CAAAgF;;;QAGtEpF,YAAA,CAIkB8C,0BAAA;MAJDqB,IAAI,EAAC,YAAY;MAAC5D,KAAK,EAAC,IAAI;MAACX,KAAK,EAAC;;MACvCmD,OAAO,EAAAC,QAAA,CAAEC,KAAK,K,kCACrB7C,MAAA,CAAAuB,SAAS,CAACsB,KAAK,CAACQ,GAAG,CAAC/B,MAAM,O;;SAGGE,MAAA,CAAAC,WAAW,I,cAA9CJ,YAAA,CAKkBqB,0BAAA;;MALDvC,KAAK,EAAC,IAAI;MAAqBX,KAAK,EAAC;;MACzCmD,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACvBjD,YAAA,CAA4Fa,oBAAA;QAAjFpB,KAAK,EAAC,WAAW;QAACqB,IAAI,EAAC,MAAM;QAAEC,OAAK,EAAAJ,MAAA,IAAEP,MAAA,CAAAiF,IAAI,CAACpC,KAAK,CAACQ,GAAG;QAAGhD,IAAI,EAAC;;0BAAO,MAAE,C,iBAAF,IAAE,E;;6BAChFT,YAAA,CAAiGa,oBAAA;QAAtFpB,KAAK,EAAC,WAAW;QAACqB,IAAI,EAAC,MAAM;QAAEC,OAAK,EAAAJ,MAAA,IAAEP,MAAA,CAAAkF,MAAM,CAACrC,KAAK,CAACQ,GAAG,CAAC8B,EAAE;QAAG9E,IAAI,EAAC;;0BAAO,MAAE,C,iBAAF,IAAE,E;;;;;;4DAK7FT,YAAA,CAA4GwF,eAAA;IAArGC,KAAK,EAAErF,MAAA,CAAAqF,KAAK;IAAG,WAAS,EAAErF,MAAA,CAAAC,MAAM,CAACI,IAAI;IAAG,gBAAc,EAAEL,MAAA,CAAAsF,UAAU;IAAG,aAAW,EAAEtF,MAAA,CAAAuF;0EAE3E/D,MAAA,CAAAC,WAAW,I,cACzBhC,mBAAA,CAGM,OAHN+F,WAGM,GAFJ5F,YAAA,CAAiDa,oBAAA;IAArCE,OAAK,EAAEa,MAAA,CAAAiE;EAAa;sBAAE,MAAG,C,iBAAH,KAAG,E;;sBACrC7F,YAAA,CAA2Fa,oBAAA;IAAhFC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAA+E,MAAA,QAAAA,MAAA,MAAAnF,MAAA,IAAEiB,MAAA,CAAAmE,uBAAuB,CAAC3F,MAAA,CAAA4F,eAAe;;sBAAG,MAAG,C,iBAAH,KAAG,E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}