{"ast": null, "code": "import { get, post, put, del, uploadFile } from \"@/util/requestUtils\";\nexport function getMemberList(param, success) {\n  return get(\"/member/list\", param, success);\n}\nexport function getMemberUnauditedList(param, success) {\n  return get(\"/member/unaudited/list\", param, success);\n}\nexport function sealMember(data, success) {\n  return put(\"/member/seal\", data, success);\n}\nexport function unsealMember(data, success) {\n  return put(\"/member/unseal\", data, success);\n}\nexport function approvedMember(data, success) {\n  return put(\"/member/approved\", data, success);\n}\nexport function rejectMember(data, success) {\n  return put(\"/member/reject\", data, success);\n}\nexport function createMember(data, success) {\n  return post(\"/member/create\", data, success);\n}\nexport function updateMember(data, success) {\n  return put(\"/member/update\", data, success);\n}\nexport function memberPwdReset(data, success) {\n  return put(\"/member/pwd/reset\", data, success);\n}\nexport function addMemberLevel(data, success) {\n  return post(\"/member/level\", data, success);\n}\nexport function editMemberLevel(data, success) {\n  return put(\"/member/level\", data, success);\n}\nexport function getMemberLevel(id, success) {\n  return get(\"/member/level\", {\n    id: id\n  }, success);\n}\nexport function listMemberLevel() {\n  return \"member/level/list\";\n}\nexport function removeMemberLevel(id, success) {\n  const data = {\n    id: id\n  };\n  return del(\"/member/level\", data, success);\n}\nexport function getListByIds(params, success) {\n  return get(\"/member/public-api/by-ids\", params, success);\n}\nexport function findMemberCompanyList(params, success) {\n  return get(\"/member/company/list\", params, success);\n}\nexport function batchUploadMember(formData, success) {\n  return uploadFile(\"/member/import/excel\", formData, success);\n}\nexport function removeMember(data, success) {\n  return del(\"/member\", data, success);\n}", "map": {"version": 3, "names": ["get", "post", "put", "del", "uploadFile", "getMemberList", "param", "success", "getMemberUnauditedList", "sealMember", "data", "unsealMember", "approvedMember", "rejectMember", "createMember", "updateMember", "memberPwdReset", "addMemberLevel", "editMemberLevel", "getMemberLevel", "id", "listMemberLevel", "removeMemberLevel", "getListByIds", "params", "findMemberCompanyList", "batchUploadMember", "formData", "removeMember"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/api/member/index.js"], "sourcesContent": ["import {get, post, put, del, uploadFile} from \"@/util/requestUtils\"\n\nexport function getMemberList(param, success) {\n  return get(\"/member/list\", param, success)\n}\n\nexport function getMemberUnauditedList(param, success) {\n  return get(\"/member/unaudited/list\", param, success)\n}\n\nexport function sealMember(data, success) {\n  return put(\"/member/seal\", data, success)\n}\n\nexport function unsealMember(data, success) {\n  return put(\"/member/unseal\", data, success)\n}\n\nexport function approvedMember(data, success) {\n  return put(\"/member/approved\", data, success)\n}\n\nexport function rejectMember(data, success) {\n  return put(\"/member/reject\", data, success)\n}\n\nexport function createMember(data, success) {\n  return post(\"/member/create\", data, success)\n}\n\nexport function updateMember(data, success) {\n  return put(\"/member/update\", data, success)\n}\n\nexport function memberPwdReset(data, success) {\n  return put(\"/member/pwd/reset\", data, success)\n}\n\nexport function addMemberLevel(data, success) {\n  return post(\"/member/level\", data, success)\n}\n\nexport function editMemberLevel(data, success) {\n  return put(\"/member/level\", data, success)\n}\n\nexport function getMemberLevel(id, success) {\n  return get(\"/member/level\", { id: id }, success)\n}\n\nexport function listMemberLevel() {\n  return \"member/level/list\"\n}\n\nexport function removeMemberLevel(id, success) {\n  const data = { id: id }\n  return del(\"/member/level\", data, success)\n}\n\nexport function getListByIds(params, success) {\n  return get(\"/member/public-api/by-ids\", params, success)\n}\n\nexport function findMemberCompanyList(params, success) {\n  return get(\"/member/company/list\", params, success)\n}\n\nexport function batchUploadMember(formData, success) {\n  return uploadFile(\"/member/import/excel\", formData, success)\n}\n\nexport function removeMember(data, success) {\n  return del(\"/member\", data, success)\n}\n"], "mappings": "AAAA,SAAQA,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,UAAU,QAAO,qBAAqB;AAEnE,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC5C,OAAOP,GAAG,CAAC,cAAc,EAAEM,KAAK,EAAEC,OAAO,CAAC;AAC5C;AAEA,OAAO,SAASC,sBAAsBA,CAACF,KAAK,EAAEC,OAAO,EAAE;EACrD,OAAOP,GAAG,CAAC,wBAAwB,EAAEM,KAAK,EAAEC,OAAO,CAAC;AACtD;AAEA,OAAO,SAASE,UAAUA,CAACC,IAAI,EAAEH,OAAO,EAAE;EACxC,OAAOL,GAAG,CAAC,cAAc,EAAEQ,IAAI,EAAEH,OAAO,CAAC;AAC3C;AAEA,OAAO,SAASI,YAAYA,CAACD,IAAI,EAAEH,OAAO,EAAE;EAC1C,OAAOL,GAAG,CAAC,gBAAgB,EAAEQ,IAAI,EAAEH,OAAO,CAAC;AAC7C;AAEA,OAAO,SAASK,cAAcA,CAACF,IAAI,EAAEH,OAAO,EAAE;EAC5C,OAAOL,GAAG,CAAC,kBAAkB,EAAEQ,IAAI,EAAEH,OAAO,CAAC;AAC/C;AAEA,OAAO,SAASM,YAAYA,CAACH,IAAI,EAAEH,OAAO,EAAE;EAC1C,OAAOL,GAAG,CAAC,gBAAgB,EAAEQ,IAAI,EAAEH,OAAO,CAAC;AAC7C;AAEA,OAAO,SAASO,YAAYA,CAACJ,IAAI,EAAEH,OAAO,EAAE;EAC1C,OAAON,IAAI,CAAC,gBAAgB,EAAES,IAAI,EAAEH,OAAO,CAAC;AAC9C;AAEA,OAAO,SAASQ,YAAYA,CAACL,IAAI,EAAEH,OAAO,EAAE;EAC1C,OAAOL,GAAG,CAAC,gBAAgB,EAAEQ,IAAI,EAAEH,OAAO,CAAC;AAC7C;AAEA,OAAO,SAASS,cAAcA,CAACN,IAAI,EAAEH,OAAO,EAAE;EAC5C,OAAOL,GAAG,CAAC,mBAAmB,EAAEQ,IAAI,EAAEH,OAAO,CAAC;AAChD;AAEA,OAAO,SAASU,cAAcA,CAACP,IAAI,EAAEH,OAAO,EAAE;EAC5C,OAAON,IAAI,CAAC,eAAe,EAAES,IAAI,EAAEH,OAAO,CAAC;AAC7C;AAEA,OAAO,SAASW,eAAeA,CAACR,IAAI,EAAEH,OAAO,EAAE;EAC7C,OAAOL,GAAG,CAAC,eAAe,EAAEQ,IAAI,EAAEH,OAAO,CAAC;AAC5C;AAEA,OAAO,SAASY,cAAcA,CAACC,EAAE,EAAEb,OAAO,EAAE;EAC1C,OAAOP,GAAG,CAAC,eAAe,EAAE;IAAEoB,EAAE,EAAEA;EAAG,CAAC,EAAEb,OAAO,CAAC;AAClD;AAEA,OAAO,SAASc,eAAeA,CAAA,EAAG;EAChC,OAAO,mBAAmB;AAC5B;AAEA,OAAO,SAASC,iBAAiBA,CAACF,EAAE,EAAEb,OAAO,EAAE;EAC7C,MAAMG,IAAI,GAAG;IAAEU,EAAE,EAAEA;EAAG,CAAC;EACvB,OAAOjB,GAAG,CAAC,eAAe,EAAEO,IAAI,EAAEH,OAAO,CAAC;AAC5C;AAEA,OAAO,SAASgB,YAAYA,CAACC,MAAM,EAAEjB,OAAO,EAAE;EAC5C,OAAOP,GAAG,CAAC,2BAA2B,EAAEwB,MAAM,EAAEjB,OAAO,CAAC;AAC1D;AAEA,OAAO,SAASkB,qBAAqBA,CAACD,MAAM,EAAEjB,OAAO,EAAE;EACrD,OAAOP,GAAG,CAAC,sBAAsB,EAAEwB,MAAM,EAAEjB,OAAO,CAAC;AACrD;AAEA,OAAO,SAASmB,iBAAiBA,CAACC,QAAQ,EAAEpB,OAAO,EAAE;EACnD,OAAOH,UAAU,CAAC,sBAAsB,EAAEuB,QAAQ,EAAEpB,OAAO,CAAC;AAC9D;AAEA,OAAO,SAASqB,YAAYA,CAAClB,IAAI,EAAEH,OAAO,EAAE;EAC1C,OAAOJ,GAAG,CAAC,SAAS,EAAEO,IAAI,EAAEH,OAAO,CAAC;AACtC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}