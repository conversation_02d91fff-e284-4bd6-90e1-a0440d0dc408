{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport store from \"../store\";\nimport { loginOut } from \"@/api/login\";\nimport { success } from \"@/util/tipsUtils\";\nimport router from \"@/router\";\nimport { getUser } from \"@/util/userUtils\";\nimport { ref } from \"vue\";\nimport { getRouteList } from \"@/util/authorityUtils\";\nexport default {\n  name: \"LayoutHeader\",\n  components: {},\n  computed: {\n    opened() {\n      return !store.getters.getAsideStatus;\n    }\n  },\n  setup() {\n    const logout = () => {\n      loginOut();\n      success(\"登出成功\");\n      router.push(\"/login\");\n    };\n    const user = getUser();\n    const isShowFunctionMenu = ref(false);\n    const functionMenuDrawerBeforeClose = done => {\n      done();\n    };\n    const routeList = getRouteList();\n    // 头部菜单栏\n    const functionList = ref([]);\n    const moreMenu = ref(false);\n    if (routeList && routeList.length > 13) {\n      for (let i = 0; i < routeList.length; i++) {\n        if (functionList.value.length === 12) {\n          moreMenu.value = true;\n          functionList.value.push({\n            title: \"更多\",\n            path: \"\",\n            children: []\n          });\n        }\n        if (i < 12) {\n          functionList.value.push(routeList[i]);\n        } else {\n          functionList.value[functionList.value.length - 1].children.push(routeList[i]);\n        }\n      }\n    } else {\n      functionList.value = routeList;\n      functionList.value.push({\n        title: \"更多\",\n        path: \"\",\n        children: []\n      });\n    }\n    function getRandomArrayItem(arr, count) {\n      let shuffled = arr.slice(0),\n        i = arr.length,\n        min = i - count,\n        temp,\n        index;\n      while (i-- > min) {\n        index = Math.floor((i + 1) * Math.random());\n        temp = shuffled[index];\n        shuffled[index] = shuffled[i];\n        shuffled[i] = temp;\n      }\n      return shuffled.slice(min);\n    }\n    const randomRouteList = getRandomArrayItem(routeList, 3);\n    const allFunctionList = [];\n    const itemMap = {\n      \"\": allFunctionList\n    };\n    if (routeList && routeList.length) {\n      for (const r of routeList) {\n        if (r.menuList && r.menuList.length) {\n          const itemMapList = [];\n          const rc = [];\n          for (const child of r.menuList) {\n            if (child.menuList && child.menuList.length) {\n              allFunctionList.push(child);\n              itemMapList.push(child);\n            } else {\n              rc.push(child);\n            }\n          }\n          if (rc.length) {\n            const c = {\n              title: r.title,\n              path: r.path,\n              menuList: rc\n            };\n            allFunctionList.push(c);\n            itemMapList.push(c);\n          }\n          itemMap[r.path] = itemMapList;\n        }\n      }\n    }\n    const functionItemList = ref([]);\n    const keyword = ref(\"\");\n    const formatItemList = itemList => {\n      // 过滤\n      if (keyword.value) {\n        const temps = [];\n        for (const item of itemList) {\n          if (item.title.indexOf(keyword.value) > -1) {\n            temps.push(item);\n            continue;\n          }\n          const subItemTemps = [];\n          for (const subItem of item.menuList) {\n            if (subItem.title.indexOf(keyword.value) > -1) {\n              subItemTemps.push(subItem);\n            }\n          }\n          if (subItemTemps.length) {\n            item.menuList = subItemTemps;\n            temps.push(item);\n          }\n        }\n        itemList = temps;\n      }\n      functionItemList.value[0] = [];\n      functionItemList.value[1] = [];\n      functionItemList.value[2] = [];\n      functionItemList.value[3] = [];\n      if (itemList && itemList.length) {\n        const num = functionItemList.value.length;\n        let i = 0;\n        for (const r of itemList) {\n          if (i % num === 0) {\n            functionItemList.value[0].push(r);\n          } else if (i % num === 1) {\n            functionItemList.value[1].push(r);\n          } else if (i % num === 2) {\n            functionItemList.value[2].push(r);\n          } else {\n            functionItemList.value[3].push(r);\n          }\n          i++;\n        }\n      }\n    };\n    formatItemList(allFunctionList);\n    const navActive = ref(\"\");\n    const changeNav = path => {\n      navActive.value = path;\n      formatItemList(itemMap[path]);\n    };\n    const searchFunction = () => {\n      formatItemList(itemMap[navActive.value]);\n    };\n    const goto = path => {\n      if (!path) {\n        return;\n      }\n      isShowFunctionMenu.value = false;\n      router.push({\n        path: path\n      });\n    };\n    return {\n      moreMenu,\n      user,\n      logout,\n      routeList,\n      functionList,\n      isShowFunctionMenu,\n      functionMenuDrawerBeforeClose,\n      functionItemList,\n      navActive,\n      changeNav,\n      goto,\n      keyword,\n      searchFunction,\n      randomRouteList\n    };\n  }\n};", "map": {"version": 3, "names": ["store", "loginOut", "success", "router", "getUser", "ref", "getRouteList", "name", "components", "computed", "opened", "getters", "getAsideStatus", "setup", "logout", "push", "user", "isShowFunctionMenu", "functionMenuDrawerBeforeClose", "done", "routeList", "functionList", "moreMenu", "length", "i", "value", "title", "path", "children", "getRandomArrayItem", "arr", "count", "shuffled", "slice", "min", "temp", "index", "Math", "floor", "random", "randomRouteList", "allFunctionList", "itemMap", "r", "menuList", "itemMapList", "rc", "child", "c", "functionItemList", "keyword", "formatItemList", "itemList", "temps", "item", "indexOf", "subItemTemps", "subItem", "num", "navActive", "changeNav", "searchFunction", "goto"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/components/LayoutHeader.vue"], "sourcesContent": ["<template>\n  <div>\n    <div class=\"layout-header-nav\" :class=\"{'absolute': isShowFunctionMenu}\">\n      <div class=\"layout-header-nav-start\">\n        <div class=\"logo\">\n          <a href=\"/\" title=\"知否在线学习平台\">\n            <span style=\"font-size: 20px;font-weight: 700;color: #000000;\">知否系统管理</span>\n<!--            <img src=\"../assets/logo.svg\" alt=\"知道在线学习平台\">-->\n          </a>\n        </div>\n        <!--        <a class=\"btn dropdown-item\" :class=\"{'is-active' : isShowFunctionMenu}\" @click=\"isShowFunctionMenu = !isShowFunctionMenu\">-->\n        <!--          所有功能<i class=\"el-icon-arrow-down el-icon&#45;&#45;right\"></i>-->\n        <!--        </a>-->\n        <router-link class=\"btn\" :to=\"{path: '/'}\">总览</router-link>\n      </div>\n      <div class=\"layout-header-nav-center\">\n        <div>\n          <a class=\"btn\" @click=\"goto(item.path)\" v-for=\"(item, index) in functionList\" :key=\"item.path\">\n            <span v-if=\"index !== functionList.length - 1\">{{item.title}}</span>\n            <el-dropdown v-else-if=\"moreMenu\">\n              <span class=\"el-dropdown-link\">\n                {{item.title}}\n                <el-icon><ArrowDown /></el-icon>\n              </span>\n              <template #dropdown>\n                <el-dropdown-menu class=\"more-function-dropdown\">\n                  <el-dropdown-item @click=\"goto(r.path)\" v-for=\"r in item.children\" :key=\"r.path\">{{r.title}}</el-dropdown-item>\n                </el-dropdown-menu>\n              </template>\n            </el-dropdown>\n          </a>\n        </div>\n      </div>\n      <div class=\"layout-header-nav-end\">\n        <div class=\"right-menu\">\n          <div class=\"right-menu-item\">\n            <i class=\"icon el-icon-message\"></i>\n          </div>\n          <el-dropdown class=\"avatar-container\" trigger=\"click\">\n            <div class=\"avatar-wrapper\">\n              <span style=\"margin-right: 5px;\">{{user.name}}</span>\n              <el-icon style=\"vertical-align: middle\">\n                <More />\n              </el-icon>\n            </div>\n            <template #dropdown>\n              <el-dropdown-menu class=\"user-dropdown\">\n                <router-link to=\"/account\">\n                  <el-dropdown-item>\n                    账号信息\n                  </el-dropdown-item>\n                </router-link>\n                <el-dropdown-item divided>\n                  <span style=\"display:block;\" @click=\"logout\">退出登录</span>\n                </el-dropdown-item>\n              </el-dropdown-menu>\n            </template>\n          </el-dropdown>\n        </div>\n      </div>\n    </div>\n    <el-drawer\n      :before-close=\"functionMenuDrawerBeforeClose\"\n      v-model=\"isShowFunctionMenu\"\n      :withHeader=\"false\"\n      direction=\"ttb\"\n      custom-class=\"function-menu\"\n      modal-class=\"function-menu-modal\"\n      destroy-on-close>\n      <el-row>\n        <el-col :span=\"4\" class=\"left-box\">\n          <div class=\"function-menu-nav\">\n            <div class=\"function-menu-nav-label\">\n              <span @mouseover=\"changeNav('')\" class=\"title\" :class=\"{'is-active': navActive === ''}\">所有功能</span>\n            </div>\n            <div class=\"function-menu-nav-list\">\n              <ul class=\"function-menu-nav-list-ul\">\n                <li @mouseover=\"changeNav(item.path)\" @click=\"goto(item.path)\" class=\"function-menu-nav-item\" :class=\"{'is-active': navActive === item.path}\" v-for=\"item in routeList\" :key=\"item.path\">{{item.title}}</li>\n              </ul>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"20\" class=\"right-box\">\n          <el-row class=\"right-box-row\" :gutter=\"20\">\n            <el-col :span=\"16\">\n              <div class=\"function-menu-content\">\n                <div class=\"function-menu-header\">\n                  <div class=\"function-menu-search\">\n                    <el-input v-model=\"keyword\" @keydown.enter=\"searchFunction\" placeholder=\"搜索功能名称\" class=\"function-menu-search-input\">\n                      <template #suffix>\n                        <i @click=\"searchFunction\" class=\"el-input__icon el-icon-search\"></i>\n                      </template>\n                    </el-input>\n                  </div>\n                  <div class=\"function-menu-tags\">\n                    <div class=\"function-menu-tag\" @click=\"goto(item.path)\" v-for=\"item in randomRouteList\" :key=\"item.path\">{{item.title}}</div>\n                  </div>\n                </div>\n                <div class=\"function-menu-content-main\">\n                  <ul class=\"function-menu-content-main-ul\" v-for=\"(itemList, index) in functionItemList\" :key=\"index\">\n                    <li class=\"menu-box\" v-for=\"(item, i) in itemList\" :key=\"i\">\n                      <div class=\"menu-box-title\" @click=\"goto(item.path)\">{{item.title}}</div>\n                      <div class=\"menu-box-content\">\n                        <ul>\n                          <li v-for=\"(m, idx) in item.menuList\" :key=\"idx\">\n                            <div class=\"menu-title\" @click=\"goto(m.path)\">{{m.title}}</div>\n                          </li>\n                        </ul>\n                      </div>\n                    </li>\n                  </ul>\n                </div>\n              </div>\n            </el-col>\n            <el-col :span=\"8\">\n              <div class=\"right-row\">\n                <div class=\"block\">\n                  <div class=\"title\">一站式解决方案</div>\n                  <div class=\"desc\">方便易搭建，功能丰富易用</div>\n                </div>\n                <div class=\"block\">\n                  <div class=\"title\">多终端支持</div>\n                  <div class=\"desc\">支持电脑、手机、APP、微信、H5、小程序观看</div>\n                </div>\n                <div class=\"block\">\n                  <div class=\"title\">定制化服务</div>\n                  <div class=\"desc\">多功能自由组合，量身定制专属企业培训系统</div>\n                </div>\n              </div>\n            </el-col>\n          </el-row>\n        </el-col>\n      </el-row>\n    </el-drawer>\n  </div>\n</template>\n\n<script>\nimport store from \"../store\";\nimport {loginOut} from \"@/api/login\";\nimport {success} from \"@/util/tipsUtils\";\nimport router from \"@/router\";\nimport {getUser} from \"@/util/userUtils\";\nimport {ref} from \"vue\"\nimport {getRouteList} from \"@/util/authorityUtils\";\n\nexport default {\n  name: \"LayoutHeader\",\n  components: {\n  },\n  computed: {\n    opened() {\n      return !store.getters.getAsideStatus\n    }\n  },\n  setup() {\n    const logout = () => {\n      loginOut()\n      success(\"登出成功\")\n      router.push(\"/login\")\n    }\n    const user = getUser()\n    const isShowFunctionMenu = ref(false)\n    const functionMenuDrawerBeforeClose = (done) => {\n      done()\n    }\n    const routeList = getRouteList()\n    // 头部菜单栏\n    const functionList = ref([])\n    const moreMenu = ref(false)\n    if (routeList && routeList.length > 13) {\n      for (let i = 0; i < routeList.length; i++) {\n        if (functionList.value.length === 12) {\n          moreMenu.value = true\n          functionList.value.push({title: \"更多\", path: \"\", children: []});\n        }\n        if (i < 12) {\n         functionList.value.push(routeList[i]);\n        } else {\n          functionList.value[functionList.value.length - 1].children.push(routeList[i])\n        }\n      }\n    } else {\n      functionList.value = routeList\n      functionList.value.push({title: \"更多\", path: \"\", children: []});\n    }\n    function getRandomArrayItem(arr, count) {\n      let shuffled = arr.slice(0), i = arr.length, min = i - count, temp, index;\n      while (i-- > min) {\n        index = Math.floor((i + 1) * Math.random());\n        temp = shuffled[index];\n        shuffled[index] = shuffled[i];\n        shuffled[i] = temp;\n      }\n      return shuffled.slice(min);\n    }\n    const randomRouteList = getRandomArrayItem(routeList, 3);\n    const allFunctionList = []\n    const itemMap = {\"\": allFunctionList}\n    if (routeList && routeList.length) {\n      for (const r of routeList) {\n        if (r.menuList && r.menuList.length) {\n          const itemMapList = [];\n          const rc = [];\n          for (const child of r.menuList) {\n            if (child.menuList && child.menuList.length) {\n              allFunctionList.push(child)\n              itemMapList.push(child)\n            } else {\n              rc.push(child)\n            }\n          }\n          if (rc.length) {\n            const c = {title: r.title, path: r.path, menuList: rc}\n            allFunctionList.push(c)\n            itemMapList.push(c)\n          }\n          itemMap[r.path] = itemMapList\n        }\n      }\n    }\n    const functionItemList = ref([]);\n    const keyword = ref(\"\");\n    const formatItemList = (itemList) => {\n      // 过滤\n      if (keyword.value) {\n        const temps = []\n        for (const item of itemList) {\n          if (item.title.indexOf(keyword.value) > -1) {\n            temps.push(item)\n            continue;\n          }\n          const subItemTemps = []\n          for (const subItem of item.menuList) {\n            if (subItem.title.indexOf(keyword.value) > -1) {\n              subItemTemps.push(subItem);\n            }\n          }\n          if (subItemTemps.length) {\n            item.menuList = subItemTemps\n            temps.push(item)\n          }\n        }\n        itemList = temps\n      }\n      functionItemList.value[0] = [];\n      functionItemList.value[1] = [];\n      functionItemList.value[2] = [];\n      functionItemList.value[3] = [];\n      if (itemList && itemList.length) {\n        const num = functionItemList.value.length;\n        let i = 0\n        for (const r of itemList) {\n          if (i % num === 0) {\n            functionItemList.value[0].push(r)\n          } else if (i % num === 1) {\n            functionItemList.value[1].push(r)\n          } else if (i % num === 2) {\n            functionItemList.value[2].push(r)\n          } else {\n            functionItemList.value[3].push(r)\n          }\n          i++;\n        }\n      }\n    }\n    formatItemList(allFunctionList);\n    const navActive = ref(\"\")\n    const changeNav = (path) => {\n      navActive.value = path\n      formatItemList(itemMap[path])\n    }\n    const searchFunction = () => {\n      formatItemList(itemMap[navActive.value])\n    }\n    const goto = (path) => {\n      if (!path) {\n        return\n      }\n      isShowFunctionMenu.value = false\n      router.push({path: path})\n    }\n    return {\n      moreMenu,\n      user,\n      logout,\n      routeList,\n      functionList,\n      isShowFunctionMenu,\n      functionMenuDrawerBeforeClose,\n      functionItemList,\n      navActive,\n      changeNav,\n      goto,\n      keyword,\n      searchFunction,\n      randomRouteList\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.layout-header-nav {\n  background: #FFFFFF;\n  color: #000000;\n  height: 50px;\n  box-sizing: border-box;\n  width: 100%;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 1px 1px 0 #f5f5f5;\n  z-index: 99999;\n  .layout-header-nav-start {\n    flex: 0 0 auto;\n    display: flex;\n    align-items: center;\n    height: 100%;\n    .logo {\n      display: flex;\n      font-size: 0;\n      margin-right: 20px;\n      padding-left: 20px;\n      align-items: center;\n      img {\n        width: 108px;\n        height: 30px;\n      }\n    }\n    .btn {\n      display: inline-flex;\n      flex-shrink: 0;\n      align-items: center;\n      margin: 0 5px;\n      white-space: nowrap;\n      height: 100%;\n      color: #000000;\n      &:hover {\n        color: $--color-primary;\n      }\n      ::v-deep .el-dropdown {\n        font-size: 12px;\n        color: #000000;\n      }\n    }\n    .dropdown-item {\n      display: inline-flex;\n      align-items: center;\n    }\n    .is-active {\n      color: $--color-primary;\n    }\n  }\n  .layout-header-nav-center {\n    flex: 1 1 600px;\n    justify-content: space-between;\n    display: flex;\n    align-items: center;\n    height: 100%;\n    .btn {\n      display: inline-flex;\n      flex-shrink: 0;\n      align-items: center;\n      margin: 0 10px;\n      white-space: nowrap;\n      height: 100%;\n      color: #000000;\n      &:hover {\n        color: $--color-primary;\n      }\n      ::v-deep .el-dropdown {\n        font-size: 12px;\n        color: #000000;\n      }\n    }\n  }\n  .layout-header-nav-end {\n    justify-content: flex-end;\n    flex: 0 0 auto;\n    display: flex;\n    align-items: center;\n    height: 100%;\n    .right-menu {\n      float: right;\n      height: 100%;\n      &:focus {\n        outline: none;\n      }\n      .right-menu-item {\n        display: inline-block;\n        padding: 0 8px;\n        height: 100%;\n        font-size: 18px;\n        color: #000;\n        vertical-align: middle;\n        .icon {\n          padding: 15px 0;\n        }\n        &.hover-effect {\n          cursor: pointer;\n          transition: background .3s;\n          &:hover {\n            background: rgba(0, 0, 0, .025)\n          }\n        }\n      }\n      .avatar-container {\n        margin-right: 20px;\n        .avatar-wrapper {\n          position: relative;\n          cursor: pointer;\n          margin: 0;\n          padding: 0 10px;\n          font-size: 12px;\n          color: #000000;\n          span {\n            line-height: 50px;\n          }\n          .el-icon-caret-bottom {\n            cursor: pointer;\n            position: absolute;\n            right: -20px;\n            top: 25px;\n            font-size: 12px;\n          }\n        }\n      }\n    }\n  }\n}\n::v-deep .function-menu {\n  margin: 0;\n  padding: 0;\n  left: 0;\n  top: 50px!important;\n  background: #FFFFFF;\n  box-shadow: 0 8px 20px 0 rgb(55 99 170 / 10%);\n  min-height: 600px;\n  .el-row {\n    min-height: 100%;\n    .left-box {\n      .function-menu-nav {\n        padding-top: 20px;\n        .function-menu-nav-label {\n          padding: 10px 0;\n          .title {\n            font-size: 12px;\n            cursor: default;\n            padding-left: 30%;\n            &:hover, &.is-active {\n              color: $--color-primary;\n            }\n          }\n        }\n        .function-menu-nav-list {\n          .function-menu-nav-list-ul {\n            .function-menu-nav-item {\n              font-size: 12px;\n              cursor: pointer;\n              padding: 8px 0 6px 30%;\n              &:hover, &.is-active {\n                color: $--color-primary;\n              }\n            }\n          }\n        }\n      }\n    }\n    .right-box {\n      background: #f3f5f8;\n      .right-box-row{\n        height: 600px;\n        overflow-y: auto;\n      }\n      .function-menu-content {\n        padding-top: 20px;\n        padding-left: 20px;\n        .function-menu-header {\n          margin-bottom: 20px;\n          .function-menu-search {\n            display: inline-block;\n            .function-menu-search-input {\n              width: 300px;\n              height: 34px;\n              line-height: 34px;\n            }\n          }\n          .function-menu-tags {\n            margin-left: 20px;\n            display: inline-block;\n            line-height: 34px;\n            vertical-align: bottom;\n            .function-menu-tag {\n              display: inline-block;\n              background: rgba(205, 216, 229, .5);\n              margin: 0 10px;\n              padding: 0 20px;\n              border-radius: 2px;\n              cursor: pointer;\n              &:hover {\n                color: #FFFFFF;\n                background: $--color-primary;\n              }\n            }\n          }\n        }\n        .function-menu-content-main {\n          display: flex;\n          justify-content: space-between;\n          flex-flow: wrap;\n          .function-menu-content-main-ul {\n            flex: 0.22;\n          }\n          .menu-box {\n            margin-bottom: 20px;\n            .menu-box-title {\n              font-size: 14px;\n              color: #0052d9;\n              font-weight: 500;\n              border-bottom: 1px solid #e6e6e6;\n              padding: 10px 0;\n              cursor: pointer;\n              &:hover {\n                color: $--color-primary;\n              }\n            }\n            .menu-box-content {\n              .menu-title {\n                padding: 6px 0;\n                cursor: pointer;\n                &:hover {\n                  color: $--color-primary;\n                }\n                &:first-child {\n                  padding-top: 12px;\n                }\n              }\n            }\n          }\n        }\n      }\n      .right-row {\n        padding: 20px 20px 0 0;\n        .block {\n          display: block;\n          background-image: linear-gradient(0deg,#fff,#f3f5f8);\n          border: 2px solid #fff;\n          box-shadow: 8px 8px 20px 0 rgb(55 99 170 / 10%), -8px -8px 20px 0 #fff;\n          border-radius: 4px;\n          box-sizing: border-box;\n          flex: 1;\n          height: 92px;\n          padding: 18px;\n          transition: all .3s linear;\n          position: relative;\n          pointer-events: auto;\n          margin-bottom: 20px;\n          .title {\n            margin-bottom: 8px;\n            font-size: 14px;\n            font-weight: 500;\n            color: #000;\n            line-height: 24px;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n          }\n          .desc {\n            font-size: 12px;\n            color: #98a3b7;\n            line-height: 20px;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n          }\n        }\n      }\n    }\n  }\n}\n.absolute {\n  position: absolute;\n  box-shadow: 0 8px 20px 0 rgb(55 99 170 / 10%);\n}\n::v-deep .function-menu-modal {\n  background-color: rgba(0, 0, 0, 0.5);\n}\n.user-dropdown {\n  ::v-deep .el-dropdown-menu__item {\n    font-size: 12px;\n    line-height: 24px;\n    &:hover {\n      background-color: #FFFFFF;\n      color: $--color-primary;\n    }\n  }\n}\n.more-function-dropdown {\n  ::v-deep .el-dropdown-menu__item {\n    font-size: 12px;\n    line-height: 34px;\n    color: #000000;\n    &:hover {\n      background-color: #FFFFFF;\n      color: $--color-primary;\n    }\n  }\n}\n::v-deep .el-input__inner {\n  height: 34px;\n  line-height: 34px;\n  border-color: #f3f5f8;\n  &:focus, &:hover {\n    border-color: #f3f5f8;\n  }\n}\n::v-deep .el-input__icon {\n  line-height: 34px;\n  cursor: pointer;\n  &:hover {\n    color: $--color-primary;\n  }\n}\n.layout-header-nav .layout-header-nav-center .btn[data-v-bafa59f2] .el-dropdown {\n  color: #000000;\n}\n:focus-visible {\n  outline: none;\n}\n</style>\n"], "mappings": ";AA0IA,OAAOA,KAAI,MAAO,UAAU;AAC5B,SAAQC,QAAQ,QAAO,aAAa;AACpC,SAAQC,OAAO,QAAO,kBAAkB;AACxC,OAAOC,MAAK,MAAO,UAAU;AAC7B,SAAQC,OAAO,QAAO,kBAAkB;AACxC,SAAQC,GAAG,QAAO,KAAI;AACtB,SAAQC,YAAY,QAAO,uBAAuB;AAElD,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE,CACZ,CAAC;EACDC,QAAQ,EAAE;IACRC,MAAMA,CAAA,EAAG;MACP,OAAO,CAACV,KAAK,CAACW,OAAO,CAACC,cAAa;IACrC;EACF,CAAC;EACDC,KAAKA,CAAA,EAAG;IACN,MAAMC,MAAK,GAAIA,CAAA,KAAM;MACnBb,QAAQ,EAAC;MACTC,OAAO,CAAC,MAAM;MACdC,MAAM,CAACY,IAAI,CAAC,QAAQ;IACtB;IACA,MAAMC,IAAG,GAAIZ,OAAO,EAAC;IACrB,MAAMa,kBAAiB,GAAIZ,GAAG,CAAC,KAAK;IACpC,MAAMa,6BAA4B,GAAKC,IAAI,IAAK;MAC9CA,IAAI,EAAC;IACP;IACA,MAAMC,SAAQ,GAAId,YAAY,EAAC;IAC/B;IACA,MAAMe,YAAW,GAAIhB,GAAG,CAAC,EAAE;IAC3B,MAAMiB,QAAO,GAAIjB,GAAG,CAAC,KAAK;IAC1B,IAAIe,SAAQ,IAAKA,SAAS,CAACG,MAAK,GAAI,EAAE,EAAE;MACtC,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIJ,SAAS,CAACG,MAAM,EAAEC,CAAC,EAAE,EAAE;QACzC,IAAIH,YAAY,CAACI,KAAK,CAACF,MAAK,KAAM,EAAE,EAAE;UACpCD,QAAQ,CAACG,KAAI,GAAI,IAAG;UACpBJ,YAAY,CAACI,KAAK,CAACV,IAAI,CAAC;YAACW,KAAK,EAAE,IAAI;YAAEC,IAAI,EAAE,EAAE;YAAEC,QAAQ,EAAE;UAAE,CAAC,CAAC;QAChE;QACA,IAAIJ,CAAA,GAAI,EAAE,EAAE;UACXH,YAAY,CAACI,KAAK,CAACV,IAAI,CAACK,SAAS,CAACI,CAAC,CAAC,CAAC;QACtC,OAAO;UACLH,YAAY,CAACI,KAAK,CAACJ,YAAY,CAACI,KAAK,CAACF,MAAK,GAAI,CAAC,CAAC,CAACK,QAAQ,CAACb,IAAI,CAACK,SAAS,CAACI,CAAC,CAAC;QAC9E;MACF;IACF,OAAO;MACLH,YAAY,CAACI,KAAI,GAAIL,SAAQ;MAC7BC,YAAY,CAACI,KAAK,CAACV,IAAI,CAAC;QAACW,KAAK,EAAE,IAAI;QAAEC,IAAI,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAE,CAAC,CAAC;IAChE;IACA,SAASC,kBAAkBA,CAACC,GAAG,EAAEC,KAAK,EAAE;MACtC,IAAIC,QAAO,GAAIF,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC;QAAET,CAAA,GAAIM,GAAG,CAACP,MAAM;QAAEW,GAAE,GAAIV,CAAA,GAAIO,KAAK;QAAEI,IAAI;QAAEC,KAAK;MACzE,OAAOZ,CAAC,EAAC,GAAIU,GAAG,EAAE;QAChBE,KAAI,GAAIC,IAAI,CAACC,KAAK,CAAC,CAACd,CAAA,GAAI,CAAC,IAAIa,IAAI,CAACE,MAAM,EAAE,CAAC;QAC3CJ,IAAG,GAAIH,QAAQ,CAACI,KAAK,CAAC;QACtBJ,QAAQ,CAACI,KAAK,IAAIJ,QAAQ,CAACR,CAAC,CAAC;QAC7BQ,QAAQ,CAACR,CAAC,IAAIW,IAAI;MACpB;MACA,OAAOH,QAAQ,CAACC,KAAK,CAACC,GAAG,CAAC;IAC5B;IACA,MAAMM,eAAc,GAAIX,kBAAkB,CAACT,SAAS,EAAE,CAAC,CAAC;IACxD,MAAMqB,eAAc,GAAI,EAAC;IACzB,MAAMC,OAAM,GAAI;MAAC,EAAE,EAAED;IAAe;IACpC,IAAIrB,SAAQ,IAAKA,SAAS,CAACG,MAAM,EAAE;MACjC,KAAK,MAAMoB,CAAA,IAAKvB,SAAS,EAAE;QACzB,IAAIuB,CAAC,CAACC,QAAO,IAAKD,CAAC,CAACC,QAAQ,CAACrB,MAAM,EAAE;UACnC,MAAMsB,WAAU,GAAI,EAAE;UACtB,MAAMC,EAAC,GAAI,EAAE;UACb,KAAK,MAAMC,KAAI,IAAKJ,CAAC,CAACC,QAAQ,EAAE;YAC9B,IAAIG,KAAK,CAACH,QAAO,IAAKG,KAAK,CAACH,QAAQ,CAACrB,MAAM,EAAE;cAC3CkB,eAAe,CAAC1B,IAAI,CAACgC,KAAK;cAC1BF,WAAW,CAAC9B,IAAI,CAACgC,KAAK;YACxB,OAAO;cACLD,EAAE,CAAC/B,IAAI,CAACgC,KAAK;YACf;UACF;UACA,IAAID,EAAE,CAACvB,MAAM,EAAE;YACb,MAAMyB,CAAA,GAAI;cAACtB,KAAK,EAAEiB,CAAC,CAACjB,KAAK;cAAEC,IAAI,EAAEgB,CAAC,CAAChB,IAAI;cAAEiB,QAAQ,EAAEE;YAAE;YACrDL,eAAe,CAAC1B,IAAI,CAACiC,CAAC;YACtBH,WAAW,CAAC9B,IAAI,CAACiC,CAAC;UACpB;UACAN,OAAO,CAACC,CAAC,CAAChB,IAAI,IAAIkB,WAAU;QAC9B;MACF;IACF;IACA,MAAMI,gBAAe,GAAI5C,GAAG,CAAC,EAAE,CAAC;IAChC,MAAM6C,OAAM,GAAI7C,GAAG,CAAC,EAAE,CAAC;IACvB,MAAM8C,cAAa,GAAKC,QAAQ,IAAK;MACnC;MACA,IAAIF,OAAO,CAACzB,KAAK,EAAE;QACjB,MAAM4B,KAAI,GAAI,EAAC;QACf,KAAK,MAAMC,IAAG,IAAKF,QAAQ,EAAE;UAC3B,IAAIE,IAAI,CAAC5B,KAAK,CAAC6B,OAAO,CAACL,OAAO,CAACzB,KAAK,IAAI,CAAC,CAAC,EAAE;YAC1C4B,KAAK,CAACtC,IAAI,CAACuC,IAAI;YACf;UACF;UACA,MAAME,YAAW,GAAI,EAAC;UACtB,KAAK,MAAMC,OAAM,IAAKH,IAAI,CAACV,QAAQ,EAAE;YACnC,IAAIa,OAAO,CAAC/B,KAAK,CAAC6B,OAAO,CAACL,OAAO,CAACzB,KAAK,IAAI,CAAC,CAAC,EAAE;cAC7C+B,YAAY,CAACzC,IAAI,CAAC0C,OAAO,CAAC;YAC5B;UACF;UACA,IAAID,YAAY,CAACjC,MAAM,EAAE;YACvB+B,IAAI,CAACV,QAAO,GAAIY,YAAW;YAC3BH,KAAK,CAACtC,IAAI,CAACuC,IAAI;UACjB;QACF;QACAF,QAAO,GAAIC,KAAI;MACjB;MACAJ,gBAAgB,CAACxB,KAAK,CAAC,CAAC,IAAI,EAAE;MAC9BwB,gBAAgB,CAACxB,KAAK,CAAC,CAAC,IAAI,EAAE;MAC9BwB,gBAAgB,CAACxB,KAAK,CAAC,CAAC,IAAI,EAAE;MAC9BwB,gBAAgB,CAACxB,KAAK,CAAC,CAAC,IAAI,EAAE;MAC9B,IAAI2B,QAAO,IAAKA,QAAQ,CAAC7B,MAAM,EAAE;QAC/B,MAAMmC,GAAE,GAAIT,gBAAgB,CAACxB,KAAK,CAACF,MAAM;QACzC,IAAIC,CAAA,GAAI;QACR,KAAK,MAAMmB,CAAA,IAAKS,QAAQ,EAAE;UACxB,IAAI5B,CAAA,GAAIkC,GAAE,KAAM,CAAC,EAAE;YACjBT,gBAAgB,CAACxB,KAAK,CAAC,CAAC,CAAC,CAACV,IAAI,CAAC4B,CAAC;UAClC,OAAO,IAAInB,CAAA,GAAIkC,GAAE,KAAM,CAAC,EAAE;YACxBT,gBAAgB,CAACxB,KAAK,CAAC,CAAC,CAAC,CAACV,IAAI,CAAC4B,CAAC;UAClC,OAAO,IAAInB,CAAA,GAAIkC,GAAE,KAAM,CAAC,EAAE;YACxBT,gBAAgB,CAACxB,KAAK,CAAC,CAAC,CAAC,CAACV,IAAI,CAAC4B,CAAC;UAClC,OAAO;YACLM,gBAAgB,CAACxB,KAAK,CAAC,CAAC,CAAC,CAACV,IAAI,CAAC4B,CAAC;UAClC;UACAnB,CAAC,EAAE;QACL;MACF;IACF;IACA2B,cAAc,CAACV,eAAe,CAAC;IAC/B,MAAMkB,SAAQ,GAAItD,GAAG,CAAC,EAAE;IACxB,MAAMuD,SAAQ,GAAKjC,IAAI,IAAK;MAC1BgC,SAAS,CAAClC,KAAI,GAAIE,IAAG;MACrBwB,cAAc,CAACT,OAAO,CAACf,IAAI,CAAC;IAC9B;IACA,MAAMkC,cAAa,GAAIA,CAAA,KAAM;MAC3BV,cAAc,CAACT,OAAO,CAACiB,SAAS,CAAClC,KAAK,CAAC;IACzC;IACA,MAAMqC,IAAG,GAAKnC,IAAI,IAAK;MACrB,IAAI,CAACA,IAAI,EAAE;QACT;MACF;MACAV,kBAAkB,CAACQ,KAAI,GAAI,KAAI;MAC/BtB,MAAM,CAACY,IAAI,CAAC;QAACY,IAAI,EAAEA;MAAI,CAAC;IAC1B;IACA,OAAO;MACLL,QAAQ;MACRN,IAAI;MACJF,MAAM;MACNM,SAAS;MACTC,YAAY;MACZJ,kBAAkB;MAClBC,6BAA6B;MAC7B+B,gBAAgB;MAChBU,SAAS;MACTC,SAAS;MACTE,IAAI;MACJZ,OAAO;MACPW,cAAc;MACdrB;IACF;EACF;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}