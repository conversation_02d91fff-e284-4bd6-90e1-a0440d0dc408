{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.unshift.js\";\nimport { get, post, put, del } from \"../../util/requestUtils\";\nexport function findCategoryList(id, fetchAll, success) {\n  return get(\"/ask/category/admin/list\", {\n    id: id,\n    fetchAll: fetchAll\n  }, success);\n}\nexport function getCategory(id, success) {\n  return get(\"/ask/category/\" + id, {}, success);\n}\nexport function saveCategory(data, success) {\n  return post(\"/ask/category\", data, success);\n}\nexport function updateCategory(data, success) {\n  return put(\"/ask/category\", data, success);\n}\nexport function removeCategory(id, success) {\n  return del(\"/ask/category/\" + id, {}, success);\n}\nexport function toTree(data) {\n  const resData = data;\n  const tree = [];\n  for (let i = 0; i < resData.length; i++) {\n    if (resData[i].pid === 0) {\n      const obj = {\n        value: resData[i].id,\n        label: resData[i].name\n      };\n      tree.push(obj);\n      resData.splice(i, 1);\n      i--;\n    }\n  }\n  function run(chiArr) {\n    if (resData.length !== 0 && chiArr) {\n      for (let i = 0; i < chiArr.length; i++) {\n        for (let j = 0; j < resData.length; j++) {\n          if (chiArr[i].value === resData[j].pid) {\n            const obj = {\n              value: resData[j].id,\n              label: resData[j].name\n            };\n            if (!chiArr[i].children) {\n              chiArr[i].children = [];\n            }\n            chiArr[i].children.push(obj);\n            resData.splice(j, 1);\n            j--;\n          }\n        }\n        run(chiArr[i].children);\n      }\n    }\n  }\n  run(tree);\n  tree.unshift({\n    value: 0,\n    label: \"全部\"\n  });\n  return tree;\n}\nexport function getAllParent(categoryList, cidList) {\n  const fullPidArray = [];\n  function getFullParentCid(categoryList, id) {\n    if (!categoryList || categoryList.length <= 0 || id === 0) {\n      return [id];\n    }\n    const getRootCategory = function (categoryList, id) {\n      for (const category of categoryList) {\n        if (category.children && category.children.length > 0) {\n          const c = getRootCategory(category.children, id);\n          if (c && c.length > 0) {\n            c.unshift(category.value);\n            return c;\n          }\n        }\n        if (category.value === id) {\n          return [category.value];\n        }\n      }\n    };\n    return getRootCategory(categoryList, id);\n  }\n  for (const id of cidList) {\n    fullPidArray.push(getFullParentCid(categoryList, id));\n  }\n  return fullPidArray;\n}", "map": {"version": 3, "names": ["get", "post", "put", "del", "findCategoryList", "id", "fetchAll", "success", "getCategory", "saveCategory", "data", "updateCategory", "removeCategory", "toTree", "resData", "tree", "i", "length", "pid", "obj", "value", "label", "name", "push", "splice", "run", "chi<PERSON><PERSON>", "j", "children", "unshift", "getAllParent", "categoryList", "cidList", "fullPidArray", "getFullParentCid", "getRootCategory", "category", "c"], "sources": ["D:/sourcecodeAndDocument/learning-platform/admin/src/api/ask/category.js"], "sourcesContent": ["import { get, post, put, del } from \"../../util/requestUtils\"\n\nexport function findCategoryList(id, fetchAll, success) {\n  return get(\"/ask/category/admin/list\", { id: id, fetchAll: fetchAll }, success)\n}\n\nexport function getCategory(id, success) {\n  return get(\"/ask/category/\" + id, {}, success)\n}\n\nexport function saveCategory(data, success) {\n  return post(\"/ask/category\", data, success)\n}\n\nexport function updateCategory(data, success) {\n  return put(\"/ask/category\", data, success)\n}\n\nexport function removeCategory(id, success) {\n  return del(\"/ask/category/\" + id, {}, success)\n}\n\nexport function toTree(data) {\n  const resData = data\n  const tree = []\n  for (let i = 0; i < resData.length; i++) {\n    if (resData[i].pid === 0) {\n      const obj = {\n        value: resData[i].id,\n        label: resData[i].name\n      }\n      tree.push(obj)\n      resData.splice(i, 1)\n      i--\n    }\n  }\n  function run(chiArr) {\n    if (resData.length !== 0 && chiArr) {\n      for (let i = 0; i < chiArr.length; i++) {\n        for (let j = 0; j < resData.length; j++) {\n          if (chiArr[i].value === resData[j].pid) {\n            const obj = {\n              value: resData[j].id,\n              label: resData[j].name\n            }\n            if (!chiArr[i].children) {\n              chiArr[i].children = []\n            }\n            chiArr[i].children.push(obj)\n            resData.splice(j, 1)\n            j--\n          }\n        }\n        run(chiArr[i].children)\n      }\n    }\n  }\n  run(tree)\n  tree.unshift({\n    value: 0,\n    label: \"全部\"\n  })\n  return tree\n}\n\nexport function getAllParent(categoryList, cidList) {\n  const fullPidArray = []\n  function getFullParentCid(categoryList, id) {\n    if (!categoryList || categoryList.length <= 0 || id === 0) {\n      return [id]\n    }\n    const getRootCategory = function(categoryList, id) {\n      for (const category of categoryList) {\n        if (category.children && category.children.length > 0) {\n          const c = getRootCategory(category.children, id)\n          if (c && c.length > 0) {\n            c.unshift(category.value)\n            return c\n          }\n        }\n        if (category.value === id) {\n          return [category.value]\n        }\n      }\n    }\n    return getRootCategory(categoryList, id)\n  }\n  for (const id of cidList) {\n    fullPidArray.push(getFullParentCid(categoryList, id))\n  }\n  return fullPidArray\n}\n"], "mappings": ";;AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAQ,yBAAyB;AAE7D,OAAO,SAASC,gBAAgBA,CAACC,EAAE,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACtD,OAAOP,GAAG,CAAC,0BAA0B,EAAE;IAAEK,EAAE,EAAEA,EAAE;IAAEC,QAAQ,EAAEA;EAAS,CAAC,EAAEC,OAAO,CAAC;AACjF;AAEA,OAAO,SAASC,WAAWA,CAACH,EAAE,EAAEE,OAAO,EAAE;EACvC,OAAOP,GAAG,CAAC,gBAAgB,GAAGK,EAAE,EAAE,CAAC,CAAC,EAAEE,OAAO,CAAC;AAChD;AAEA,OAAO,SAASE,YAAYA,CAACC,IAAI,EAAEH,OAAO,EAAE;EAC1C,OAAON,IAAI,CAAC,eAAe,EAAES,IAAI,EAAEH,OAAO,CAAC;AAC7C;AAEA,OAAO,SAASI,cAAcA,CAACD,IAAI,EAAEH,OAAO,EAAE;EAC5C,OAAOL,GAAG,CAAC,eAAe,EAAEQ,IAAI,EAAEH,OAAO,CAAC;AAC5C;AAEA,OAAO,SAASK,cAAcA,CAACP,EAAE,EAAEE,OAAO,EAAE;EAC1C,OAAOJ,GAAG,CAAC,gBAAgB,GAAGE,EAAE,EAAE,CAAC,CAAC,EAAEE,OAAO,CAAC;AAChD;AAEA,OAAO,SAASM,MAAMA,CAACH,IAAI,EAAE;EAC3B,MAAMI,OAAO,GAAGJ,IAAI;EACpB,MAAMK,IAAI,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,IAAIF,OAAO,CAACE,CAAC,CAAC,CAACE,GAAG,KAAK,CAAC,EAAE;MACxB,MAAMC,GAAG,GAAG;QACVC,KAAK,EAAEN,OAAO,CAACE,CAAC,CAAC,CAACX,EAAE;QACpBgB,KAAK,EAAEP,OAAO,CAACE,CAAC,CAAC,CAACM;MACpB,CAAC;MACDP,IAAI,CAACQ,IAAI,CAACJ,GAAG,CAAC;MACdL,OAAO,CAACU,MAAM,CAACR,CAAC,EAAE,CAAC,CAAC;MACpBA,CAAC,EAAE;IACL;EACF;EACA,SAASS,GAAGA,CAACC,MAAM,EAAE;IACnB,IAAIZ,OAAO,CAACG,MAAM,KAAK,CAAC,IAAIS,MAAM,EAAE;MAClC,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,MAAM,CAACT,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,OAAO,CAACG,MAAM,EAAEU,CAAC,EAAE,EAAE;UACvC,IAAID,MAAM,CAACV,CAAC,CAAC,CAACI,KAAK,KAAKN,OAAO,CAACa,CAAC,CAAC,CAACT,GAAG,EAAE;YACtC,MAAMC,GAAG,GAAG;cACVC,KAAK,EAAEN,OAAO,CAACa,CAAC,CAAC,CAACtB,EAAE;cACpBgB,KAAK,EAAEP,OAAO,CAACa,CAAC,CAAC,CAACL;YACpB,CAAC;YACD,IAAI,CAACI,MAAM,CAACV,CAAC,CAAC,CAACY,QAAQ,EAAE;cACvBF,MAAM,CAACV,CAAC,CAAC,CAACY,QAAQ,GAAG,EAAE;YACzB;YACAF,MAAM,CAACV,CAAC,CAAC,CAACY,QAAQ,CAACL,IAAI,CAACJ,GAAG,CAAC;YAC5BL,OAAO,CAACU,MAAM,CAACG,CAAC,EAAE,CAAC,CAAC;YACpBA,CAAC,EAAE;UACL;QACF;QACAF,GAAG,CAACC,MAAM,CAACV,CAAC,CAAC,CAACY,QAAQ,CAAC;MACzB;IACF;EACF;EACAH,GAAG,CAACV,IAAI,CAAC;EACTA,IAAI,CAACc,OAAO,CAAC;IACXT,KAAK,EAAE,CAAC;IACRC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,OAAON,IAAI;AACb;AAEA,OAAO,SAASe,YAAYA,CAACC,YAAY,EAAEC,OAAO,EAAE;EAClD,MAAMC,YAAY,GAAG,EAAE;EACvB,SAASC,gBAAgBA,CAACH,YAAY,EAAE1B,EAAE,EAAE;IAC1C,IAAI,CAAC0B,YAAY,IAAIA,YAAY,CAACd,MAAM,IAAI,CAAC,IAAIZ,EAAE,KAAK,CAAC,EAAE;MACzD,OAAO,CAACA,EAAE,CAAC;IACb;IACA,MAAM8B,eAAe,GAAG,SAAAA,CAASJ,YAAY,EAAE1B,EAAE,EAAE;MACjD,KAAK,MAAM+B,QAAQ,IAAIL,YAAY,EAAE;QACnC,IAAIK,QAAQ,CAACR,QAAQ,IAAIQ,QAAQ,CAACR,QAAQ,CAACX,MAAM,GAAG,CAAC,EAAE;UACrD,MAAMoB,CAAC,GAAGF,eAAe,CAACC,QAAQ,CAACR,QAAQ,EAAEvB,EAAE,CAAC;UAChD,IAAIgC,CAAC,IAAIA,CAAC,CAACpB,MAAM,GAAG,CAAC,EAAE;YACrBoB,CAAC,CAACR,OAAO,CAACO,QAAQ,CAAChB,KAAK,CAAC;YACzB,OAAOiB,CAAC;UACV;QACF;QACA,IAAID,QAAQ,CAAChB,KAAK,KAAKf,EAAE,EAAE;UACzB,OAAO,CAAC+B,QAAQ,CAAChB,KAAK,CAAC;QACzB;MACF;IACF,CAAC;IACD,OAAOe,eAAe,CAACJ,YAAY,EAAE1B,EAAE,CAAC;EAC1C;EACA,KAAK,MAAMA,EAAE,IAAI2B,OAAO,EAAE;IACxBC,YAAY,CAACV,IAAI,CAACW,gBAAgB,CAACH,YAAY,EAAE1B,EAAE,CAAC,CAAC;EACvD;EACA,OAAO4B,YAAY;AACrB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}