{"ast": null, "code": "import { ref } from \"vue\";\nimport { findList, updateCompany, saveCompany } from \"@/api/member/company\";\nimport Page from \"../../../components/Page\";\nimport { error, success } from \"@/util/tipsUtils\";\nexport default {\n  name: \"MemberCompany\",\n  components: {\n    Page\n  },\n  props: {\n    cancelCallback: {\n      type: Function,\n      default: () => {}\n    },\n    selectCallback: {\n      type: Function,\n      default: () => {}\n    },\n    isComponent: {\n      type: Boolean,\n      default: false\n    }\n  },\n  setup(props) {\n    const list = ref([]);\n    const total = ref(0);\n    const dataLoading = ref(true);\n    const searchParam = ref({\n      name: \"\",\n      size: 20,\n      current: 1\n    });\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true;\n      findList(searchParam.value, res => {\n        dataLoading.value = false;\n        if (!res) {\n          return;\n        }\n        list.value = res.list;\n        total.value = res.total;\n      }).catch(() => {\n        dataLoading.value = false;\n      });\n    };\n    loadList();\n    const currentChange = currentPage => {\n      searchParam.value.current = currentPage;\n      loadList();\n    };\n    const sizeChange = s => {\n      searchParam.value.size = s;\n      loadList();\n    };\n    // 搜索\n    const search = () => {\n      loadList();\n    };\n    const memberCompanyRules = {\n      name: [{\n        required: true,\n        message: \"请输入名称\",\n        trigger: \"blur\"\n      }]\n    };\n    const memberCompany = ref({});\n    const memberCompanyRef = ref(null);\n    const showMemberCompanyFormDialog = ref(false);\n    const hideMemberCompanyForm = () => {\n      showMemberCompanyFormDialog.value = false;\n      memberCompany.value = {};\n    };\n    const add = () => {\n      showMemberCompanyFormDialog.value = true;\n    };\n    // 编辑\n    const edit = item => {\n      memberCompany.value = item;\n      showMemberCompanyFormDialog.value = true;\n    };\n    //提交\n    const submitMemberCompany = () => {\n      memberCompanyRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (memberCompany.value.id) {\n          updateCompany(memberCompany.value, () => {\n            success(\"修改成功\");\n            loadList();\n            hideMemberCompanyForm();\n          });\n        } else {\n          saveCompany(memberCompany.value, () => {\n            success(\"新增成功\");\n            loadList();\n            hideMemberCompanyForm();\n          });\n        }\n      });\n    };\n    const multipleSelection = ref([]);\n    const handleSelectionChange = val => {\n      multipleSelection.value = val;\n    };\n    const selectSelectionChange = () => {\n      if (!multipleSelection.value.length) {\n        error(\"请至少选择一个\");\n      }\n      props.selectCallback && props.selectCallback(multipleSelection.value);\n    };\n    return {\n      handleSelectionChange,\n      selectSelectionChange,\n      list,\n      total,\n      searchParam,\n      search,\n      currentChange,\n      sizeChange,\n      showMemberCompanyFormDialog,\n      add,\n      memberCompany,\n      memberCompanyRef,\n      edit,\n      hideMemberCompanyForm,\n      submitMemberCompany,\n      memberCompanyRules,\n      dataLoading\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "findList", "updateCompany", "saveCompany", "Page", "error", "success", "name", "components", "props", "cancelCallback", "type", "Function", "default", "selectCallback", "isComponent", "Boolean", "setup", "list", "total", "dataLoading", "searchParam", "size", "current", "loadList", "value", "res", "catch", "currentChange", "currentPage", "sizeChange", "s", "search", "memberCompanyRules", "required", "message", "trigger", "memberCompany", "memberCompanyRef", "showMemberCompanyFormDialog", "hideMemberCompanyForm", "add", "edit", "item", "submitMemberCompany", "validate", "valid", "id", "multipleSelection", "handleSelectionChange", "val", "selectSelectionChange", "length"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/member/company/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"header\">\n      <el-form :inline=\"true\" :model=\"searchParam\" class=\"demo-form-inline\">\n        <el-form-item label=\"\">\n          <el-input size=\"small\" class=\"search-input\" v-model=\"searchParam.name\" placeholder=\"请输入关键字\">\n            <template #append>\n              <el-button size=\"small\" class=\"search-btn\" type=\"primary\" @click=\"search\">搜索</el-button>\n            </template>\n          </el-input>\n        </el-form-item>\n        <el-form-item v-if=\"!isComponent\">\n          <el-button size=\"small\" type=\"primary\" @click=\"add\">创建公司</el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"content\">\n      <div class=\"content-list\">\n        <el-table v-loading=\"dataLoading\" :data=\"list\" size=\"small\" style=\"width: 100%;\" @selection-change=\"handleSelectionChange\">\n          <el-table-column type=\"selection\" width=\"45\" v-if=\"isComponent\"/>\n          <el-table-column label=\"序号\" type=\"index\"/>\n          <el-table-column prop=\"name\" label=\"名称\"/>\n          <el-table-column prop=\"sortOrder\" label=\"排序\"/>\n          <el-table-column prop=\"status\" label=\"状态\">\n            <template #default=\"scope\">\n              {{scope.row.status === 'normal' ? '启用' : '禁用'}}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"50\" v-if=\"!isComponent\">\n            <template #default=\"scope\">\n              <el-button type=\"text\" size=\"small\" @click=\"edit(scope.row)\">编辑</el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n    </div>\n    <page style=\"margin-top: 20px;\" :total=\"total\" :current-change=\"currentChange\" :size-change=\"sizeChange\" :page-size=\"searchParam.size\"></page>\n    <el-dialog title=\"编辑会员公司\" v-model=\"showMemberCompanyFormDialog\" :before-close=\"hideMemberCompanyForm\">\n      <el-form :model=\"memberCompany\" :rules=\"memberCompanyRules\" ref=\"memberCompanyRef\">\n        <el-form-item label=\"名称：\" label-width=\"150px\" prop=\"name\">\n          <el-input size=\"small\" v-model=\"memberCompany.name\" placeholder=\"请输入名称\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"排序：\" label-width=\"150px\" prop=\"sortOrder\">\n          <el-input size=\"small\" v-model=\"memberCompany.sortOrder\" placeholder=\"请输入排序，数值越大越靠前\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"状态：\" label-width=\"150px\" prop=\"status\">\n          <el-switch active-color=\"#13ce66\" :active-value=\"'normal'\" :inactive-value=\"'invalid'\"  v-model=\"memberCompany.status\"></el-switch>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button size=\"small\" @click=\"hideMemberCompanyForm\">取 消</el-button>\n          <el-button size=\"small\" type=\"primary\" @click=\"submitMemberCompany\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n    <template v-if=\"isComponent\">\n      <div class=\"dialog-footer\" style=\"text-align: right;margin-top: 30px;\">\n        <el-button size=\"small\" @click=\"cancelCallback\">取 消</el-button>\n        <el-button size=\"small\" type=\"primary\" @click=\"selectSelectionChange\">确 定</el-button>\n      </div>\n    </template>\n  </div>\n</template>\n\n<script>\n  import {ref} from \"vue\"\n  import {findList, updateCompany, saveCompany} from \"@/api/member/company\"\n  import Page from \"../../../components/Page\"\n  import {error, success} from \"@/util/tipsUtils\";\n\n  export default {\n    name: \"MemberCompany\",\n    components: {\n      Page\n    },\n    props: {\n      cancelCallback: {\n        type: Function,\n        default: () => {}\n      },\n      selectCallback: {\n        type: Function,\n        default: () => {}\n      },\n      isComponent: {\n        type: Boolean,\n        default: false\n      }\n    },\n    setup(props) {\n      const list = ref([])\n      const total = ref(0)\n      const dataLoading = ref(true)\n      const searchParam = ref({\n        name: \"\",\n        size: 20,\n        current: 1\n      })\n      // 加载列表\n      const loadList = () => {\n        dataLoading.value = true\n        findList(searchParam.value, (res) => {\n          dataLoading.value = false\n          if (!res) {return;}\n          list.value = res.list;\n          total.value = res.total;\n        }).catch(() => {\n          dataLoading.value = false\n        })\n      }\n      loadList();\n      const currentChange = (currentPage) => {\n        searchParam.value.current = currentPage;\n        loadList();\n      }\n      const sizeChange = (s) => {\n        searchParam.value.size = s;\n        loadList();\n      }\n      // 搜索\n      const search = () => {\n        loadList();\n      }\n      const memberCompanyRules = {\n        name: [{ required: true, message: \"请输入名称\", trigger: \"blur\" }],\n      }\n      const memberCompany = ref({})\n      const memberCompanyRef = ref(null)\n      const showMemberCompanyFormDialog = ref(false)\n      const hideMemberCompanyForm = () => {\n        showMemberCompanyFormDialog.value = false;\n        memberCompany.value = {}\n      }\n      const add = () => {\n        showMemberCompanyFormDialog.value = true;\n      }\n      // 编辑\n      const edit = (item) => {\n        memberCompany.value = item\n        showMemberCompanyFormDialog.value = true;\n      }\n      //提交\n      const submitMemberCompany = () => {\n        memberCompanyRef.value.validate(valid => {\n          if (!valid) {\n            return false;\n          }\n          if (memberCompany.value.id) {\n            updateCompany(memberCompany.value, () => {\n              success(\"修改成功\")\n              loadList()\n              hideMemberCompanyForm()\n            });\n          } else {\n            saveCompany(memberCompany.value, () => {\n              success(\"新增成功\")\n              loadList()\n              hideMemberCompanyForm()\n            });\n          }\n        })\n      }\n\n      const multipleSelection = ref([])\n      const handleSelectionChange = (val) => {\n        multipleSelection.value = val;\n      }\n      const selectSelectionChange = () => {\n        if (!multipleSelection.value.length) {\n          error(\"请至少选择一个\")\n        }\n        props.selectCallback && props.selectCallback(multipleSelection.value)\n      }\n\n      return {\n        handleSelectionChange,\n        selectSelectionChange,\n        list,\n        total,\n        searchParam,\n        search,\n        currentChange,\n        sizeChange,\n        showMemberCompanyFormDialog,\n        add,\n        memberCompany,\n        memberCompanyRef,\n        edit,\n        hideMemberCompanyForm,\n        submitMemberCompany,\n        memberCompanyRules,\n        dataLoading,\n      };\n    }\n  };\n</script>\n<style lang=\"scss\">\n  .header {\n    .el-form {\n      .el-form-item {\n        .el-form-item__content {\n          line-height: 28px;\n          .search-btn {\n            &:hover {\n              color: $--color-primary;\n            }\n          }\n        }\n      }\n    }\n  }\n</style>\n<style scoped lang=\"scss\">\n  .app-container {\n    margin: 20px;\n    .content-list {\n      margin: 0;\n      padding: 0;\n      border: 0;\n      font: inherit;\n      vertical-align: baseline;\n    }\n    .search-input {\n      width: 242px;\n    }\n  }\n</style>\n"], "mappings": "AAkEE,SAAQA,GAAG,QAAO,KAAI;AACtB,SAAQC,QAAQ,EAAEC,aAAa,EAAEC,WAAW,QAAO,sBAAqB;AACxE,OAAOC,IAAG,MAAO,0BAAyB;AAC1C,SAAQC,KAAK,EAAEC,OAAO,QAAO,kBAAkB;AAE/C,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,UAAU,EAAE;IACVJ;EACF,CAAC;EACDK,KAAK,EAAE;IACLC,cAAc,EAAE;MACdC,IAAI,EAAEC,QAAQ;MACdC,OAAO,EAAEA,CAAA,KAAM,CAAC;IAClB,CAAC;IACDC,cAAc,EAAE;MACdH,IAAI,EAAEC,QAAQ;MACdC,OAAO,EAAEA,CAAA,KAAM,CAAC;IAClB,CAAC;IACDE,WAAW,EAAE;MACXJ,IAAI,EAAEK,OAAO;MACbH,OAAO,EAAE;IACX;EACF,CAAC;EACDI,KAAKA,CAACR,KAAK,EAAE;IACX,MAAMS,IAAG,GAAIlB,GAAG,CAAC,EAAE;IACnB,MAAMmB,KAAI,GAAInB,GAAG,CAAC,CAAC;IACnB,MAAMoB,WAAU,GAAIpB,GAAG,CAAC,IAAI;IAC5B,MAAMqB,WAAU,GAAIrB,GAAG,CAAC;MACtBO,IAAI,EAAE,EAAE;MACRe,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC;IACD;IACA,MAAMC,QAAO,GAAIA,CAAA,KAAM;MACrBJ,WAAW,CAACK,KAAI,GAAI,IAAG;MACvBxB,QAAQ,CAACoB,WAAW,CAACI,KAAK,EAAGC,GAAG,IAAK;QACnCN,WAAW,CAACK,KAAI,GAAI,KAAI;QACxB,IAAI,CAACC,GAAG,EAAE;UAAC;QAAO;QAClBR,IAAI,CAACO,KAAI,GAAIC,GAAG,CAACR,IAAI;QACrBC,KAAK,CAACM,KAAI,GAAIC,GAAG,CAACP,KAAK;MACzB,CAAC,CAAC,CAACQ,KAAK,CAAC,MAAM;QACbP,WAAW,CAACK,KAAI,GAAI,KAAI;MAC1B,CAAC;IACH;IACAD,QAAQ,EAAE;IACV,MAAMI,aAAY,GAAKC,WAAW,IAAK;MACrCR,WAAW,CAACI,KAAK,CAACF,OAAM,GAAIM,WAAW;MACvCL,QAAQ,EAAE;IACZ;IACA,MAAMM,UAAS,GAAKC,CAAC,IAAK;MACxBV,WAAW,CAACI,KAAK,CAACH,IAAG,GAAIS,CAAC;MAC1BP,QAAQ,EAAE;IACZ;IACA;IACA,MAAMQ,MAAK,GAAIA,CAAA,KAAM;MACnBR,QAAQ,EAAE;IACZ;IACA,MAAMS,kBAAiB,GAAI;MACzB1B,IAAI,EAAE,CAAC;QAAE2B,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC;IAC9D;IACA,MAAMC,aAAY,GAAIrC,GAAG,CAAC,CAAC,CAAC;IAC5B,MAAMsC,gBAAe,GAAItC,GAAG,CAAC,IAAI;IACjC,MAAMuC,2BAA0B,GAAIvC,GAAG,CAAC,KAAK;IAC7C,MAAMwC,qBAAoB,GAAIA,CAAA,KAAM;MAClCD,2BAA2B,CAACd,KAAI,GAAI,KAAK;MACzCY,aAAa,CAACZ,KAAI,GAAI,CAAC;IACzB;IACA,MAAMgB,GAAE,GAAIA,CAAA,KAAM;MAChBF,2BAA2B,CAACd,KAAI,GAAI,IAAI;IAC1C;IACA;IACA,MAAMiB,IAAG,GAAKC,IAAI,IAAK;MACrBN,aAAa,CAACZ,KAAI,GAAIkB,IAAG;MACzBJ,2BAA2B,CAACd,KAAI,GAAI,IAAI;IAC1C;IACA;IACA,MAAMmB,mBAAkB,GAAIA,CAAA,KAAM;MAChCN,gBAAgB,CAACb,KAAK,CAACoB,QAAQ,CAACC,KAAI,IAAK;QACvC,IAAI,CAACA,KAAK,EAAE;UACV,OAAO,KAAK;QACd;QACA,IAAIT,aAAa,CAACZ,KAAK,CAACsB,EAAE,EAAE;UAC1B7C,aAAa,CAACmC,aAAa,CAACZ,KAAK,EAAE,MAAM;YACvCnB,OAAO,CAAC,MAAM;YACdkB,QAAQ,EAAC;YACTgB,qBAAqB,EAAC;UACxB,CAAC,CAAC;QACJ,OAAO;UACLrC,WAAW,CAACkC,aAAa,CAACZ,KAAK,EAAE,MAAM;YACrCnB,OAAO,CAAC,MAAM;YACdkB,QAAQ,EAAC;YACTgB,qBAAqB,EAAC;UACxB,CAAC,CAAC;QACJ;MACF,CAAC;IACH;IAEA,MAAMQ,iBAAgB,GAAIhD,GAAG,CAAC,EAAE;IAChC,MAAMiD,qBAAoB,GAAKC,GAAG,IAAK;MACrCF,iBAAiB,CAACvB,KAAI,GAAIyB,GAAG;IAC/B;IACA,MAAMC,qBAAoB,GAAIA,CAAA,KAAM;MAClC,IAAI,CAACH,iBAAiB,CAACvB,KAAK,CAAC2B,MAAM,EAAE;QACnC/C,KAAK,CAAC,SAAS;MACjB;MACAI,KAAK,CAACK,cAAa,IAAKL,KAAK,CAACK,cAAc,CAACkC,iBAAiB,CAACvB,KAAK;IACtE;IAEA,OAAO;MACLwB,qBAAqB;MACrBE,qBAAqB;MACrBjC,IAAI;MACJC,KAAK;MACLE,WAAW;MACXW,MAAM;MACNJ,aAAa;MACbE,UAAU;MACVS,2BAA2B;MAC3BE,GAAG;MACHJ,aAAa;MACbC,gBAAgB;MAChBI,IAAI;MACJF,qBAAqB;MACrBI,mBAAmB;MACnBX,kBAAkB;MAClBb;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}