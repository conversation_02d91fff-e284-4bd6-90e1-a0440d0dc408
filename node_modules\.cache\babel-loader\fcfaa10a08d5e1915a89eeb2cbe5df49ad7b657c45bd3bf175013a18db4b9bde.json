{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, resolveDirective as _resolveDirective, withDirectives as _withDirectives, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-8cc6b586\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"category-tree\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_tree = _resolveComponent(\"el-tree\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_input, {\n    size: \"small\",\n    placeholder: \"输入关键字进行搜索\",\n    modelValue: $setup.filterText,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.filterText = $event)\n  }, null, 8 /* PROPS */, [\"modelValue\"]), $setup.treeFlag ? _withDirectives((_openBlock(), _createBlock(_component_el_tree, {\n    key: 0,\n    size: \"small\",\n    ref: \"treeRef\",\n    \"current-node-key\": $setup.nodeKey,\n    \"node-key\": \"id\",\n    \"filter-node-method\": $setup.filterNode,\n    \"highlight-current\": true,\n    data: $setup.treeData,\n    props: $setup.defaultProps,\n    \"expand-on-click-node\": false,\n    onNodeClick: $setup.handleNodeClick,\n    class: \"el-tree\"\n  }, null, 8 /* PROPS */, [\"current-node-key\", \"filter-node-method\", \"data\", \"props\", \"onNodeClick\"])), [[_directive_loading, $setup.dataLoading]]) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_input", "size", "placeholder", "$setup", "filterText", "$event", "treeFlag", "_createBlock", "_component_el_tree", "ref", "nodeKey", "filterNode", "data", "treeData", "props", "defaultProps", "onNodeClick", "handleNodeClick", "dataLoading"], "sources": ["D:\\sourcecodeAndDocument\\learning-platform\\admin\\src\\views\\learn\\topic\\category\\tree\\index.vue"], "sourcesContent": ["<template>\n  <div class=\"category-tree\">\n    <el-input size=\"small\" placeholder=\"输入关键字进行搜索\" v-model=\"filterText\"></el-input>\n    <el-tree v-loading=\"dataLoading\" size=\"small\" ref=\"treeRef\" v-if=\"treeFlag\" :current-node-key=\"nodeKey\" node-key=\"id\" :filter-node-method=\"filterNode\" :highlight-current=\"true\" :data=\"treeData\" :props=\"defaultProps\" :expand-on-click-node=\"false\" @node-click=\"handleNodeClick\" class=\"el-tree\"></el-tree>\n  </div>\n</template>\n\n<script>\n// 目录API\nimport {findCategoryList} from \"@/api/learn/topicCategory\"\nimport {ref, watch, nextTick} from \"vue\";\nexport default {\n  name: \"LearnTopicCategoryTree\",\n  props: {\n    currentNodeKey: Number\n  },\n  setup(props, context) {\n    const filterText = ref(\"\");\n    const defaultProps = {\n      children: \"children\",\n      label: \"name\"\n    }\n    const treeData = ref([])\n    let treeRef = ref(null);\n    watch([filterText], (nv) => {\n      treeRef.value.filter(nv);\n    })\n    const dataLoading = ref(true)\n    const loadCategoryList = () => {\n      findCategoryList(0, true, res => {\n        // 获取部门列表中的根节点（父节点id为0的）（获取的根节点包含孩子）\n        function getRootNodes(nodeList) {\n          if (!nodeList || nodeList.length <= 0) {\n            return [];\n          }\n          // 递归获取节点的孩子节点\n          const getChildren = function(parent) {\n            const children = [];\n            for (let i = 0; i < nodeList.length; i++) {\n              const item = nodeList[i];\n              if (item.pid === parent.id) {\n                children.push(item);\n              }\n            }\n            parent.children = children\n            if (children.length === 0) {\n              return;\n            }\n            for (let i = 0; i < children.length; i++) {\n              getChildren(children[i]);\n            }\n          }\n          const result = [];\n          for (let i = 0; i < nodeList.length; i++) {\n            const item = nodeList[i];\n            if (item.pid === 0 || item.pid === null) {\n              result.push(item);\n              getChildren(item);\n            }\n          }\n          return result;\n        }\n        treeData.value = getRootNodes(res);\n        treeData.value = getRootNodes(res);\n        dataLoading.value = false\n      }).catch(() => {\n        dataLoading.value = false\n      })\n    }\n    loadCategoryList()\n    let nodeKey = ref(props.currentNodeKey)\n    const treeFlag = ref(true)\n    watch(() => props.currentNodeKey, (nv) => {\n      nodeKey.value = nv\n      treeFlag.value =false\n      nextTick(() => {\n        treeFlag.value =true\n      })\n      loadCategoryList()\n    })\n    const filterNode = function(value, data, node) {\n      console.log(node)\n      if (!value) {\n        return true;\n      }\n      return data.name.indexOf(value) !== -1;\n    }\n    const handleNodeClick = (data) => {\n      context.emit(\"node-click\", data, this);\n    }\n    return {\n      treeFlag,\n      nodeKey,\n      filterText,\n      defaultProps,\n      treeData,\n      treeRef,\n      filterNode,\n      handleNodeClick,\n      dataLoading\n    }\n  }\n}\n</script>\n<style scoped lang=\"scss\">\n.category-tree {\n  ::v-deep .el-input__inner, ::v-deep .el-input-number {\n    height: 34px;\n    line-height: 34px;\n    font-size: 12px;\n    border: none;\n    border-radius: 0;\n    border-bottom: 1px solid #f7f7f7;\n    &:focus, &:hover {\n      border-color: #f3f5f8;\n    }\n    .el-input-number__decrease, .el-input-number__increase {\n      background: #FFFFFF;\n      line-height: 32px;\n      border: none;\n      &:focus, &:hover {\n        border-color: #f3f5f8;\n      }\n    }\n  }\n  ::v-deep .el-tree {\n    min-height: 102px;\n    .el-tree-node {\n      &:focus, &:focus > .el-tree-node__content {\n        background-color: #FFFFFF;\n      }\n      .el-tree-node__content {\n        height: 30px;\n        &:hover {\n          background-color: #FFFFFF;\n          color: $--color-primary;\n        }\n        .el-tree-node__expand-icon {\n          font-size: 16px;\n        }\n        .el-tree-node__label {\n          font-size: 12px;\n        }\n      }\n    }\n  }\n  ::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {\n    background-color: #FFFFFF;\n    color: $--color-primary;\n  }\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAe;;;;;uBAA1BC,mBAAA,CAGM,OAHNC,UAGM,GAFJC,YAAA,CAA+EC,mBAAA;IAArEC,IAAI,EAAC,OAAO;IAACC,WAAW,EAAC,WAAW;gBAAUC,MAAA,CAAAC,UAAU;+<PERSON><PERSON><PERSON>,MAAA,CAAAC,UAAU,GAAAC,MAAA;2CACAF,MAAA,CAAAG,QAAQ,G,+BAA1EC,YAAA,CAA8SC,kBAAA;;IAA7QP,IAAI,EAAC,OAAO;IAACQ,GAAG,EAAC,SAAS;IAAkB,kBAAgB,EAAEN,MAAA,CAAAO,OAAO;IAAE,UAAQ,EAAC,IAAI;IAAE,oBAAkB,EAAEP,MAAA,CAAAQ,UAAU;IAAG,mBAAiB,EAAE,IAAI;IAAGC,IAAI,EAAET,MAAA,CAAAU,QAAQ;IAAGC,KAAK,EAAEX,MAAA,CAAAY,YAAY;IAAG,sBAAoB,EAAE,KAAK;IAAGC,WAAU,EAAEb,MAAA,CAAAc,eAAe;IAAErB,KAAK,EAAC;8HAAtQO,MAAA,CAAAe,WAAW,E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}