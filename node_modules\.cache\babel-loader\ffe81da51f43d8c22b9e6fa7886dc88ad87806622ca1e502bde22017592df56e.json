{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, watch } from \"vue\";\nimport router from \"../../../router\";\nimport { toTree } from \"@/api/resource/category\";\nimport { findTagList, getTag, saveTag, updateTag } from \"@/api/resource\";\n// import uploadImage from \"../../../components/Uplaod/index\";\nimport { success, error } from \"@/util/tipsUtils\";\nexport default {\n  name: \"ResourceCategoryEdit\",\n  components: {\n    // uploadImage\n  },\n  props: {\n    data: {\n      type: Object,\n      required: true\n    },\n    pid: {\n      type: Number,\n      required: true\n    },\n    editSuccess: {\n      type: Function\n    },\n    editCancel: {\n      type: Function\n    }\n  },\n  setup(props) {\n    let selectedPidList = ref([]);\n    const categoryOptions = ref([]);\n    const parentCategory = ref({});\n    const uploadData = {\n      url: process.env.VUE_APP_BASE_API + \"/oss/resource/category/image\",\n      files: []\n    };\n    const rules = {\n      name: [{\n        required: true,\n        message: \"请输入分类名称\",\n        trigger: \"blur\"\n      }]\n    };\n    let category = ref({\n      name: \"\",\n      status: true\n    });\n    const init = (item, pid) => {\n      if (pid) {\n        getTag(pid, res => {\n          if (!res) {\n            error(\"没有找到该标签\");\n            return;\n          }\n          parentCategory.value = res;\n        });\n      } else {\n        parentCategory.value = {\n          id: 0,\n          name: \"全部\"\n        };\n      }\n      if (item && item.id) {\n        category = ref(item);\n        if (item.image) {\n          uploadData.files = [{\n            name: item.name,\n            url: item.image\n          }];\n        }\n      }\n      category.value.pid = pid || 0;\n      selectedPidList.value.push(category.value.pid);\n    };\n    init(props.data, props.pid);\n    watch(() => props.data, nv => {\n      console.log(\"监听到data改变了\");\n      console.log(nv);\n      init(nv, nv.pid);\n      category = ref(nv);\n    });\n    const loadCategory = () => {\n      findTagList(0, true).then(function (response) {\n        if (response) {\n          categoryOptions.value = toTree(response);\n        }\n      });\n    };\n    loadCategory();\n    const changeParentCategory = () => {\n      if (category.value.selectedPidList && category.value.selectedPidList.length > 0) {\n        let id = selectedPidList.value[selectedPidList.value.length - 1];\n        if (id === category.value.id) {\n          error(\"不能选择自己为上级分类\");\n          return;\n        }\n        category.value.pid = id;\n      }\n    };\n    const cancel = () => {\n      props.editCancel && props.editCancel();\n    };\n    const onUploadSuccess = res => {\n      category.value.image = res.data;\n    };\n    const onUploadRemove = () => {\n      if (!category.value.image) {\n        return;\n      }\n      category.value.image = \"\";\n      uploadData.value.files = [];\n    };\n    const categoryRef = ref(null);\n    const submit = () => {\n      categoryRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (!category.value.pid && category.value.pid !== 0) {\n          error(\"请选择上级分类\");\n          return false;\n        }\n        if (category.value.id) {\n          updateTag(category.value, res => {\n            success(\"编辑成功\");\n            router.push({\n              path: \"/resource/tag\",\n              query: {\n                id: res[\"id\"]\n              }\n            });\n            props.editSuccess && props.editSuccess(res[\"id\"]);\n          });\n        } else {\n          saveTag(category.value, res => {\n            success(\"新增成功\");\n            router.push({\n              path: \"/resource/tag\",\n              query: {\n                id: res[\"id\"]\n              }\n            });\n            props.editSuccess && props.editSuccess(res[\"id\"]);\n          });\n        }\n      });\n    };\n    return {\n      selectedPidList,\n      categoryOptions,\n      parentCategory,\n      category,\n      rules,\n      uploadData,\n      categoryRef,\n      loadCategory,\n      changeParentCategory,\n      cancel,\n      onUploadSuccess,\n      onUploadRemove,\n      submit\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "watch", "router", "toTree", "findTagList", "getTag", "saveTag", "updateTag", "success", "error", "name", "components", "props", "data", "type", "Object", "required", "pid", "Number", "editSuccess", "Function", "editCancel", "setup", "selectedPidList", "categoryOptions", "parentCategory", "uploadData", "url", "process", "env", "VUE_APP_BASE_API", "files", "rules", "message", "trigger", "category", "status", "init", "item", "res", "value", "id", "image", "push", "nv", "console", "log", "loadCategory", "then", "response", "changeParentCategory", "length", "cancel", "onUploadSuccess", "onUploadRemove", "categoryRef", "submit", "validate", "valid", "path", "query"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/resource/tag/edit.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form ref=\"categoryRef\" :rules=\"rules\" :model=\"category\" label-width=\"110px\">\n<!--      <el-form-item label=\"上级分类\" prop=\"pid\">-->\n<!--        <el-input size=\"mini\" v-if=\"parentCategory.name\" type=\"text\" class=\"input-text\" disabled v-model=\"parentCategory.name\"></el-input>-->\n<!--        <el-cascader v-else class=\"input-text\" :props=\"{checkStrictly: true}\" v-model=\"selectedPidList\" :options=\"categoryOptions\" placeholder=\"请选择上级分类\" @change=\"changeParentCategory\"></el-cascader>-->\n<!--      </el-form-item>-->\n      <el-form-item label=\"分类名称\" prop=\"name\">\n        <el-input size=\"mini\" maxlength=\"15\" show-word-limit class=\"input-text\" v-model=\"category.name\"></el-input>\n      </el-form-item>\n<!--      <el-form-item label=\"分类图片\" prop=\"image\">-->\n<!--        <upload-image :limit=\"1\" :files=\"uploadData.files\" :on-upload-success=\"onUploadSuccess\" :on-upload-remove=\"onUploadRemove\" :upload-url=\"uploadData.url\"></upload-image>-->\n<!--      </el-form-item>-->\n<!--      <el-form-item label=\"排序\" prop=\"sortOrder\">-->\n<!--        <el-input size=\"mini\"  class=\"input-text\" v-model=\"category.sortOrder\" placeholder=\"数据越大显示越前\"></el-input>-->\n<!--      </el-form-item>-->\n<!--      <el-form-item label=\"是否显示\" prop=\"isShow\">-->\n<!--        <el-switch size=\"mini\"  id=\"isShow\" active-color=\"#13ce66\" v-model=\"category.isShow\"></el-switch>-->\n<!--      </el-form-item>-->\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-switch id=\"status\" active-color=\"#13ce66\" v-model=\"category.status\"></el-switch>\n      </el-form-item>\n    </el-form>\n    <div class=\"dialog-footer\">\n      <el-button size=\"mini\"  @click=\"cancel()\">取 消</el-button>\n      <el-button size=\"mini\"  type=\"primary\" @click=\"submit()\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\n  import {ref, watch} from \"vue\"\n  import router from \"../../../router\"\n  import { toTree } from \"@/api/resource/category\"\n  import {findTagList, getTag, saveTag, updateTag} from \"@/api/resource\"\n  // import uploadImage from \"../../../components/Uplaod/index\";\n  import {success, error} from \"@/util/tipsUtils\";\n  export default {\n    name: \"ResourceCategoryEdit\",\n    components: {\n      // uploadImage\n    },\n    props: {\n      data: {\n        type: Object,\n        required: true\n      },\n      pid: {\n        type: Number,\n        required: true\n      },\n      editSuccess: {\n        type: Function\n      },\n      editCancel: {\n        type: Function\n      }\n    },\n    setup(props) {\n      let selectedPidList = ref([])\n      const categoryOptions = ref([])\n      const parentCategory = ref({})\n      const uploadData = {\n        url: process.env.VUE_APP_BASE_API + \"/oss/resource/category/image\",\n        files: []\n      }\n      const rules = {\n        name: [{ required: true, message: \"请输入分类名称\", trigger: \"blur\" }],\n      }\n      let category = ref({\n        name: \"\",\n        status: true\n      })\n      const init = (item, pid) => {\n        if (pid) {\n          getTag(pid, res => {\n            if (!res) {\n              error(\"没有找到该标签\")\n              return;\n            }\n            parentCategory.value = res;\n          });\n        } else {\n          parentCategory.value = {id: 0, name: \"全部\"};\n        }\n        if (item && item.id) {\n          category = ref(item);\n          if (item.image) {\n            uploadData.files = [{name: item.name, url: item.image}]\n          }\n        }\n        category.value.pid = pid || 0;\n        selectedPidList.value.push(category.value.pid);\n      }\n      init(props.data, props.pid)\n      watch(() => props.data, (nv) => {\n        console.log(\"监听到data改变了\")\n        console.log(nv)\n        init(nv, nv.pid)\n        category = ref(nv)\n      })\n      const loadCategory = () => {\n        findTagList(0, true).then(function (response) {\n          if (response) {\n            categoryOptions.value = toTree(response);\n          }\n        });\n      }\n      loadCategory();\n      const changeParentCategory = () => {\n        if (category.value.selectedPidList && category.value.selectedPidList.length > 0) {\n          let id = selectedPidList.value[selectedPidList.value.length - 1];\n          if (id === category.value.id) {\n            error(\"不能选择自己为上级分类\")\n            return;\n          }\n          category.value.pid = id;\n        }\n      }\n      const cancel = () => {\n        props.editCancel && props.editCancel()\n      }\n      const onUploadSuccess = (res) => {\n        category.value.image = res.data;\n      }\n      const onUploadRemove = () => {\n        if (!category.value.image) {\n          return;\n        }\n        category.value.image = \"\";\n        uploadData.value.files = [];\n      }\n      const categoryRef = ref(null)\n      const submit = () => {\n        categoryRef.value.validate(valid => {\n          if (!valid) {\n            return false;\n          }\n          if (!category.value.pid && category.value.pid !== 0) {\n            error(\"请选择上级分类\")\n            return false;\n          }\n          if (category.value.id) {\n            updateTag(category.value, (res) => {\n              success(\"编辑成功\")\n              router.push({path: \"/resource/tag\", query:{ id: res[\"id\"]}});\n              props.editSuccess && props.editSuccess(res[\"id\"])\n            })\n          } else {\n            saveTag(category.value, (res) => {\n              success(\"新增成功\")\n              router.push({path: \"/resource/tag\", query:{ id: res[\"id\"]}});\n              props.editSuccess && props.editSuccess(res[\"id\"])\n            })\n          }\n        });\n      }\n      return {\n        selectedPidList,\n        categoryOptions,\n        parentCategory,\n        category,\n        rules,\n        uploadData,\n        categoryRef,\n        loadCategory,\n        changeParentCategory,\n        cancel,\n        onUploadSuccess,\n        onUploadRemove,\n        submit\n      }\n    }\n  }\n</script>\n<style scoped lang=\"scss\">\n.dialog-footer {\n  text-align: center;\n}\n.input-text {\n  width: 80%;\n}\n</style>\n"], "mappings": ";AA+BE,SAAQA,GAAG,EAAEC,KAAK,QAAO,KAAI;AAC7B,OAAOC,MAAK,MAAO,iBAAgB;AACnC,SAASC,MAAK,QAAS,yBAAwB;AAC/C,SAAQC,WAAW,EAAEC,MAAM,EAAEC,OAAO,EAAEC,SAAS,QAAO,gBAAe;AACrE;AACA,SAAQC,OAAO,EAAEC,KAAK,QAAO,kBAAkB;AAC/C,eAAe;EACbC,IAAI,EAAE,sBAAsB;EAC5BC,UAAU,EAAE;IACV;EAAA,CACD;EACDC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,GAAG,EAAE;MACHH,IAAI,EAAEI,MAAM;MACZF,QAAQ,EAAE;IACZ,CAAC;IACDG,WAAW,EAAE;MACXL,IAAI,EAAEM;IACR,CAAC;IACDC,UAAU,EAAE;MACVP,IAAI,EAAEM;IACR;EACF,CAAC;EACDE,KAAKA,CAACV,KAAK,EAAE;IACX,IAAIW,eAAc,GAAIvB,GAAG,CAAC,EAAE;IAC5B,MAAMwB,eAAc,GAAIxB,GAAG,CAAC,EAAE;IAC9B,MAAMyB,cAAa,GAAIzB,GAAG,CAAC,CAAC,CAAC;IAC7B,MAAM0B,UAAS,GAAI;MACjBC,GAAG,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAe,GAAI,8BAA8B;MAClEC,KAAK,EAAE;IACT;IACA,MAAMC,KAAI,GAAI;MACZtB,IAAI,EAAE,CAAC;QAAEM,QAAQ,EAAE,IAAI;QAAEiB,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC;IAChE;IACA,IAAIC,QAAO,GAAInC,GAAG,CAAC;MACjBU,IAAI,EAAE,EAAE;MACR0B,MAAM,EAAE;IACV,CAAC;IACD,MAAMC,IAAG,GAAIA,CAACC,IAAI,EAAErB,GAAG,KAAK;MAC1B,IAAIA,GAAG,EAAE;QACPZ,MAAM,CAACY,GAAG,EAAEsB,GAAE,IAAK;UACjB,IAAI,CAACA,GAAG,EAAE;YACR9B,KAAK,CAAC,SAAS;YACf;UACF;UACAgB,cAAc,CAACe,KAAI,GAAID,GAAG;QAC5B,CAAC,CAAC;MACJ,OAAO;QACLd,cAAc,CAACe,KAAI,GAAI;UAACC,EAAE,EAAE,CAAC;UAAE/B,IAAI,EAAE;QAAI,CAAC;MAC5C;MACA,IAAI4B,IAAG,IAAKA,IAAI,CAACG,EAAE,EAAE;QACnBN,QAAO,GAAInC,GAAG,CAACsC,IAAI,CAAC;QACpB,IAAIA,IAAI,CAACI,KAAK,EAAE;UACdhB,UAAU,CAACK,KAAI,GAAI,CAAC;YAACrB,IAAI,EAAE4B,IAAI,CAAC5B,IAAI;YAAEiB,GAAG,EAAEW,IAAI,CAACI;UAAK,CAAC;QACxD;MACF;MACAP,QAAQ,CAACK,KAAK,CAACvB,GAAE,GAAIA,GAAE,IAAK,CAAC;MAC7BM,eAAe,CAACiB,KAAK,CAACG,IAAI,CAACR,QAAQ,CAACK,KAAK,CAACvB,GAAG,CAAC;IAChD;IACAoB,IAAI,CAACzB,KAAK,CAACC,IAAI,EAAED,KAAK,CAACK,GAAG;IAC1BhB,KAAK,CAAC,MAAMW,KAAK,CAACC,IAAI,EAAG+B,EAAE,IAAK;MAC9BC,OAAO,CAACC,GAAG,CAAC,YAAY;MACxBD,OAAO,CAACC,GAAG,CAACF,EAAE;MACdP,IAAI,CAACO,EAAE,EAAEA,EAAE,CAAC3B,GAAG;MACfkB,QAAO,GAAInC,GAAG,CAAC4C,EAAE;IACnB,CAAC;IACD,MAAMG,YAAW,GAAIA,CAAA,KAAM;MACzB3C,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC4C,IAAI,CAAC,UAAUC,QAAQ,EAAE;QAC5C,IAAIA,QAAQ,EAAE;UACZzB,eAAe,CAACgB,KAAI,GAAIrC,MAAM,CAAC8C,QAAQ,CAAC;QAC1C;MACF,CAAC,CAAC;IACJ;IACAF,YAAY,EAAE;IACd,MAAMG,oBAAmB,GAAIA,CAAA,KAAM;MACjC,IAAIf,QAAQ,CAACK,KAAK,CAACjB,eAAc,IAAKY,QAAQ,CAACK,KAAK,CAACjB,eAAe,CAAC4B,MAAK,GAAI,CAAC,EAAE;QAC/E,IAAIV,EAAC,GAAIlB,eAAe,CAACiB,KAAK,CAACjB,eAAe,CAACiB,KAAK,CAACW,MAAK,GAAI,CAAC,CAAC;QAChE,IAAIV,EAAC,KAAMN,QAAQ,CAACK,KAAK,CAACC,EAAE,EAAE;UAC5BhC,KAAK,CAAC,aAAa;UACnB;QACF;QACA0B,QAAQ,CAACK,KAAK,CAACvB,GAAE,GAAIwB,EAAE;MACzB;IACF;IACA,MAAMW,MAAK,GAAIA,CAAA,KAAM;MACnBxC,KAAK,CAACS,UAAS,IAAKT,KAAK,CAACS,UAAU,EAAC;IACvC;IACA,MAAMgC,eAAc,GAAKd,GAAG,IAAK;MAC/BJ,QAAQ,CAACK,KAAK,CAACE,KAAI,GAAIH,GAAG,CAAC1B,IAAI;IACjC;IACA,MAAMyC,cAAa,GAAIA,CAAA,KAAM;MAC3B,IAAI,CAACnB,QAAQ,CAACK,KAAK,CAACE,KAAK,EAAE;QACzB;MACF;MACAP,QAAQ,CAACK,KAAK,CAACE,KAAI,GAAI,EAAE;MACzBhB,UAAU,CAACc,KAAK,CAACT,KAAI,GAAI,EAAE;IAC7B;IACA,MAAMwB,WAAU,GAAIvD,GAAG,CAAC,IAAI;IAC5B,MAAMwD,MAAK,GAAIA,CAAA,KAAM;MACnBD,WAAW,CAACf,KAAK,CAACiB,QAAQ,CAACC,KAAI,IAAK;QAClC,IAAI,CAACA,KAAK,EAAE;UACV,OAAO,KAAK;QACd;QACA,IAAI,CAACvB,QAAQ,CAACK,KAAK,CAACvB,GAAE,IAAKkB,QAAQ,CAACK,KAAK,CAACvB,GAAE,KAAM,CAAC,EAAE;UACnDR,KAAK,CAAC,SAAS;UACf,OAAO,KAAK;QACd;QACA,IAAI0B,QAAQ,CAACK,KAAK,CAACC,EAAE,EAAE;UACrBlC,SAAS,CAAC4B,QAAQ,CAACK,KAAK,EAAGD,GAAG,IAAK;YACjC/B,OAAO,CAAC,MAAM;YACdN,MAAM,CAACyC,IAAI,CAAC;cAACgB,IAAI,EAAE,eAAe;cAAEC,KAAK,EAAC;gBAAEnB,EAAE,EAAEF,GAAG,CAAC,IAAI;cAAC;YAAC,CAAC,CAAC;YAC5D3B,KAAK,CAACO,WAAU,IAAKP,KAAK,CAACO,WAAW,CAACoB,GAAG,CAAC,IAAI,CAAC;UAClD,CAAC;QACH,OAAO;UACLjC,OAAO,CAAC6B,QAAQ,CAACK,KAAK,EAAGD,GAAG,IAAK;YAC/B/B,OAAO,CAAC,MAAM;YACdN,MAAM,CAACyC,IAAI,CAAC;cAACgB,IAAI,EAAE,eAAe;cAAEC,KAAK,EAAC;gBAAEnB,EAAE,EAAEF,GAAG,CAAC,IAAI;cAAC;YAAC,CAAC,CAAC;YAC5D3B,KAAK,CAACO,WAAU,IAAKP,KAAK,CAACO,WAAW,CAACoB,GAAG,CAAC,IAAI,CAAC;UAClD,CAAC;QACH;MACF,CAAC,CAAC;IACJ;IACA,OAAO;MACLhB,eAAe;MACfC,eAAe;MACfC,cAAc;MACdU,QAAQ;MACRH,KAAK;MACLN,UAAU;MACV6B,WAAW;MACXR,YAAY;MACZG,oBAAoB;MACpBE,MAAM;MACNC,eAAe;MACfC,cAAc;MACdE;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}