{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, with<PERSON><PERSON><PERSON> as _withKeys, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveDirective as _resolveDirective, openBlock as _openBlock, createBlock as _createBlock, withDirectives as _withDirectives, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-22cecf1c\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"report\"\n};\nconst _hoisted_2 = {\n  class: \"header\"\n};\nconst _hoisted_3 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", {\n  style: {\n    \"vertical-align\": \"middle\"\n  }\n}, \"搜索\", -1 /* HOISTED */));\nconst _hoisted_4 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", {\n  style: {\n    \"vertical-align\": \"middle\"\n  }\n}, \"重置\", -1 /* HOISTED */));\nconst _hoisted_5 = {\n  class: \"report-main\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_cascader = _resolveComponent(\"el-cascader\");\n  const _component_Search = _resolveComponent(\"Search\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_page = _resolveComponent(\"page\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_form, {\n    inline: true,\n    model: $setup.params,\n    class: \"form-inline\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      label: \"课程名称\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"small\",\n        onKeydown: _withKeys($setup.search, [\"enter\"]),\n        class: \"search-input\",\n        modelValue: $setup.params.name,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.params.name = $event),\n        placeholder: \"请输入关键字\"\n      }, {\n        suffix: _withCtx(() => [_createElementVNode(\"i\", {\n          onClick: _cache[0] || (_cache[0] = (...args) => $setup.search && $setup.search(...args)),\n          class: \"el-input__icon el-icon-search search-btn\"\n        })]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onKeydown\", \"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"课程状态\",\n      class: \"select\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_select, {\n        size: \"small\",\n        modelValue: $setup.params.status,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.params.status = $event),\n        onChange: $setup.search\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_option, {\n          label: \"全部\",\n          value: \"\"\n        }), _createVNode(_component_el_option, {\n          label: \"未发布\",\n          value: \"unpublished\"\n        }), _createVNode(_component_el_option, {\n          label: \"已发布\",\n          value: \"published\"\n        })]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"课程分类\",\n      class: \"select\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_cascader, {\n        size: \"small\",\n        modelValue: $setup.selectCidList,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.selectCidList = $event),\n        options: $setup.categoryOptions,\n        props: {\n          checkStrictly: true\n        },\n        onChange: $setup.search,\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"options\", \"onChange\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        size: \"small\",\n        type: \"primary\",\n        onClick: _cache[4] || (_cache[4] = $event => $setup.search())\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, {\n          style: {\n            \"vertical-align\": \"middle\"\n          }\n        }, {\n          default: _withCtx(() => [_createVNode(_component_Search)]),\n          _: 1 /* STABLE */\n        }), _hoisted_3]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[5] || (_cache[5] = $event => $setup.resetParams())\n      }, {\n        default: _withCtx(() => [_hoisted_4]),\n        _: 1 /* STABLE */\n      })]),\n\n      _: 1 /* STABLE */\n    })]),\n\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\"])]), _createElementVNode(\"div\", _hoisted_5, [_withDirectives((_openBlock(), _createBlock(_component_el_table, {\n    data: $setup.dataList\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_table_column, {\n      label: \"序号\",\n      type: \"index\",\n      index: $setup.customIndexFn\n    }, null, 8 /* PROPS */, [\"index\"]), _createVNode(_component_el_table_column, {\n      label: \"名字\",\n      prop: \"member.name\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"真实姓名\",\n      prop: \"member.realname\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"公司\"\n    }, {\n      default: _withCtx(scope => [_createTextVNode(_toDisplayString(scope.row.member && scope.row.member.memberCompanyList && scope.row.member.memberCompanyList[0].name), 1 /* TEXT */)]),\n\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"报名课程数\",\n      prop: \"totalSignLessonQty\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"报名次数\",\n      prop: \"totalSignQty\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"平均报名次数\",\n      prop: \"avgSignQty\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"已完成数量\",\n      prop: \"completedSignQty\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"进行中数量\",\n      prop: \"progressSignQty\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"已取消数量\",\n      prop: \"cancelSignQty\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"学习时长\",\n      prop: \"totalLearnTime\"\n    }, {\n      default: _withCtx(scope => [_createTextVNode(_toDisplayString($options.formatSeconds(scope.row.totalLearnTime)), 1 /* TEXT */)]),\n\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"平均学习时长\",\n      prop: \"avgLearnTime\"\n    }, {\n      default: _withCtx(scope => [_createTextVNode(_toDisplayString($options.formatSeconds(scope.row.avgLearnTime)), 1 /* TEXT */)]),\n\n      _: 1 /* STABLE */\n    })]),\n\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\"])), [[_directive_loading, $setup.loading]]), _createVNode(_component_page, {\n    total: $setup.total,\n    \"size-change\": $setup.sizeChange,\n    \"current-change\": $setup.currentChange,\n    \"page-size\": $setup.params.size\n  }, null, 8 /* PROPS */, [\"total\", \"size-change\", \"current-change\", \"page-size\"])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementVNode", "style", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_createVNode", "_component_el_form", "inline", "model", "$setup", "params", "_component_el_form_item", "label", "_component_el_input", "size", "onKeydown", "_with<PERSON><PERSON><PERSON>", "search", "name", "$event", "placeholder", "suffix", "_withCtx", "onClick", "_cache", "args", "_component_el_select", "status", "onChange", "_component_el_option", "value", "_component_el_cascader", "selectCidList", "options", "categoryOptions", "props", "checkStrictly", "clearable", "_component_el_button", "type", "_component_el_icon", "_component_Search", "_hoisted_3", "resetParams", "_hoisted_4", "_hoisted_5", "_createBlock", "_component_el_table", "data", "dataList", "_component_el_table_column", "index", "customIndexFn", "prop", "default", "scope", "row", "member", "memberCompanyList", "$options", "formatSeconds", "totalLearnTime", "avgLearnTime", "loading", "_component_page", "total", "sizeChange", "currentChange"], "sources": ["D:\\sourcecodeAndDocument\\learning-platform\\admin\\src\\views\\learn\\report\\memberstudy\\index.vue"], "sourcesContent": ["<template>\n  <div class=\"report\">\n    <div class=\"header\">\n      <el-form :inline=\"true\" :model=\"params\" class=\"form-inline\">\n        <el-form-item label=\"课程名称\">\n          <el-input size=\"small\" @keydown.enter=\"search\" class=\"search-input\" v-model=\"params.name\" placeholder=\"请输入关键字\">\n            <template #suffix>\n              <i @click=\"search\" class=\"el-input__icon el-icon-search search-btn\"></i>\n            </template>\n          </el-input>\n        </el-form-item>\n        <el-form-item label=\"课程状态\" class=\"select\">\n          <el-select size=\"small\" v-model=\"params.status\" @change=\"search\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"未发布\" value=\"unpublished\"></el-option>\n            <el-option label=\"已发布\" value=\"published\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"课程分类\" class=\"select\">\n          <el-cascader size=\"small\" v-model=\"selectCidList\" :options=\"categoryOptions\" :props=\"{ checkStrictly: true }\" @change=\"search\" clearable></el-cascader>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" type=\"primary\" @click=\"search()\">\n            <el-icon style=\"vertical-align: middle\">\n              <Search />\n            </el-icon>\n            <span style=\"vertical-align: middle\">搜索</span>\n          </el-button>\n          <el-button size=\"small\" @click=\"resetParams()\">\n            <span style=\"vertical-align: middle\">重置</span>\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"report-main\">\n      <el-table :data=\"dataList\" v-loading=\"loading\">\n        <el-table-column label=\"序号\" type=\"index\" :index=\"customIndexFn\"></el-table-column>\n        <el-table-column label=\"名字\" prop=\"member.name\"></el-table-column>\n        <el-table-column label=\"真实姓名\" prop=\"member.realname\"></el-table-column>\n        <el-table-column label=\"公司\">\n          <template #default=\"scope\">\n            {{scope.row.member && scope.row.member.memberCompanyList && scope.row.member.memberCompanyList[0].name}}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"报名课程数\" prop=\"totalSignLessonQty\"></el-table-column>\n        <el-table-column label=\"报名次数\" prop=\"totalSignQty\"></el-table-column>\n        <el-table-column label=\"平均报名次数\" prop=\"avgSignQty\"></el-table-column>\n        <el-table-column label=\"已完成数量\" prop=\"completedSignQty\"></el-table-column>\n        <el-table-column label=\"进行中数量\" prop=\"progressSignQty\"></el-table-column>\n        <el-table-column label=\"已取消数量\" prop=\"cancelSignQty\"></el-table-column>\n        <el-table-column label=\"学习时长\" prop=\"totalLearnTime\">\n          <template #default=\"scope\">\n            {{formatSeconds(scope.row.totalLearnTime) }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"平均学习时长\" prop=\"avgLearnTime\">\n          <template #default=\"scope\">\n            {{formatSeconds(scope.row.avgLearnTime) }}\n          </template>\n        </el-table-column>\n      </el-table>\n      <page :total=\"total\" :size-change=\"sizeChange\" :current-change=\"currentChange\" :page-size=\"params.size\"/>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {ref} from \"vue\"\nimport Page from \"@/components/Page\";\nimport {Search} from \"@element-plus/icons-vue\";\nimport {findCategoryList, toTree} from \"@/api/learn/category\";\nimport {getMemberStudyReport} from \"@/api/learn/lesson\";\nimport {formatSeconds} from \"../../../../util/dateUtils\";\nexport default {\n  name: \"LearnReportIndex\",\n  methods: {formatSeconds},\n  components: {Search, Page},\n  setup() {\n    const loading = ref(true)\n    const total = ref(0)\n    const dataList = ref([])\n    const c = {\n      current: 1,\n      size: 20\n    }\n    const params = ref(c)\n    const selectCidList = ref([])\n    const categoryOptions = ref([])\n    // 加载分类\n    const loadCategory = () => {\n      findCategoryList(0, true, (res) => {if (res) { categoryOptions.value = toTree(res);}})\n    }\n    loadCategory();\n\n    const loadList = () => {\n      loading.value = true\n      getMemberStudyReport(params.value, res => {\n        dataList.value = res.list\n        total.value = res.total\n        loading.value = false\n      }).catch(() => {\n        loading.value = false\n      })\n    }\n    loadList()\n    const currentChange = (c) => {\n      params.value.current = c;\n      loadList();\n    }\n    const sizeChange = (s) => {\n      params.value.size = s;\n      loadList();\n    }\n    const search = () => {\n      if (selectCidList.value && selectCidList.value.length > 0) {\n        params.value.cid = selectCidList.value[selectCidList.value.length - 1];\n      }\n      params.value.current = 1\n      loadList()\n    }\n    const resetParams = () => {\n      params.value = c\n    }\n    const customIndexFn = (index) => {\n      return (params.value.current - 1) * params.value.size + index + 1;\n    }\n    return {\n      customIndexFn,\n      loading,\n      dataList,\n      selectCidList,\n      categoryOptions,\n      params,\n      total,\n      currentChange,\n      sizeChange,\n      search,\n      resetParams\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.report {\n  margin: 20px;\n  font-size: 12px;\n  .report-main {\n    ::v-deep .el-table {\n      font-size: 12px;\n      .el-table__empty-block {\n        line-height: 400px;\n        .el-table__empty-text {\n          line-height: 400px;\n        }\n      }\n      th, td {\n        padding: 6px 0;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAQ;;EACZA,KAAK,EAAC;AAAQ;gEAwBXC,mBAAA,CAA8C;EAAxCC,KAA8B,EAA9B;IAAA;EAAA;AAA8B,GAAC,IAAE;gEAGvCD,mBAAA,CAA8C;EAAxCC,KAA8B,EAA9B;IAAA;EAAA;AAA8B,GAAC,IAAE;;EAK1CF,KAAK,EAAC;AAAa;;;;;;;;;;;;;;;uBAjC1BG,mBAAA,CA8DM,OA9DNC,UA8DM,GA7DJH,mBAAA,CA+BM,OA/BNI,UA+BM,GA9BJC,YAAA,CA6BUC,kBAAA;IA7BAC,MAAM,EAAE,IAAI;IAAGC,KAAK,EAAEC,MAAA,CAAAC,MAAM;IAAEX,KAAK,EAAC;;sBAC5C,MAMe,CANfM,YAAA,CAMeM,uBAAA;MANDC,KAAK,EAAC;IAAM;wBACxB,MAIW,CAJXP,YAAA,CAIWQ,mBAAA;QAJDC,IAAI,EAAC,OAAO;QAAEC,SAAO,EAAAC,SAAA,CAAQP,MAAA,CAAAQ,MAAM;QAAElB,KAAK,EAAC,cAAc;oBAAUU,MAAA,CAAAC,MAAM,CAACQ,IAAI;mEAAXT,MAAA,CAAAC,MAAM,CAACQ,IAAI,GAAAC,MAAA;QAAEC,WAAW,EAAC;;QACzFC,MAAM,EAAAC,QAAA,CACf,MAAwE,CAAxEtB,mBAAA,CAAwE;UAApEuB,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,MAAA,CAAAQ,MAAA,IAAAR,MAAA,CAAAQ,MAAA,IAAAQ,IAAA,CAAM;UAAE1B,KAAK,EAAC;;;;;QAI/BM,YAAA,CAMeM,uBAAA;MANDC,KAAK,EAAC,MAAM;MAACb,KAAK,EAAC;;wBAC/B,MAIY,CAJZM,YAAA,CAIYqB,oBAAA;QAJDZ,IAAI,EAAC,OAAO;oBAAUL,MAAA,CAAAC,MAAM,CAACiB,MAAM;mEAAblB,MAAA,CAAAC,MAAM,CAACiB,MAAM,GAAAR,MAAA;QAAGS,QAAM,EAAEnB,MAAA,CAAAQ;;0BACvD,MAA2C,CAA3CZ,YAAA,CAA2CwB,oBAAA;UAAhCjB,KAAK,EAAC,IAAI;UAACkB,KAAK,EAAC;YAC5BzB,YAAA,CAAuDwB,oBAAA;UAA5CjB,KAAK,EAAC,KAAK;UAACkB,KAAK,EAAC;YAC7BzB,YAAA,CAAqDwB,oBAAA;UAA1CjB,KAAK,EAAC,KAAK;UAACkB,KAAK,EAAC;;;;;QAGjCzB,YAAA,CAEeM,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACb,KAAK,EAAC;;wBAC/B,MAAuJ,CAAvJM,YAAA,CAAuJ0B,sBAAA;QAA1IjB,IAAI,EAAC,OAAO;oBAAUL,MAAA,CAAAuB,aAAa;mEAAbvB,MAAA,CAAAuB,aAAa,GAAAb,MAAA;QAAGc,OAAO,EAAExB,MAAA,CAAAyB,eAAe;QAAGC,KAAK,EAAE;UAAAC,aAAA;QAAA,CAAuB;QAAGR,QAAM,EAAEnB,MAAA,CAAAQ,MAAM;QAAEoB,SAAS,EAAT;;;QAEjIhC,YAAA,CAUeM,uBAAA;wBATb,MAKY,CALZN,YAAA,CAKYiC,oBAAA;QALDxB,IAAI,EAAC,OAAO;QAACyB,IAAI,EAAC,SAAS;QAAEhB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAL,MAAA,IAAEV,MAAA,CAAAQ,MAAM;;0BACnD,MAEU,CAFVZ,YAAA,CAEUmC,kBAAA;UAFDvC,KAA8B,EAA9B;YAAA;UAAA;QAA8B;4BACrC,MAAU,CAAVI,YAAA,CAAUoC,iBAAA,E;;YAEZC,UAA8C,C;;UAEhDrC,YAAA,CAEYiC,oBAAA;QAFDxB,IAAI,EAAC,OAAO;QAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAL,MAAA,IAAEV,MAAA,CAAAkC,WAAW;;0BACzC,MAA8C,CAA9CC,UAA8C,C;;;;;;;;kCAKtD5C,mBAAA,CA4BM,OA5BN6C,UA4BM,G,+BA3BJC,YAAA,CAyBWC,mBAAA;IAzBAC,IAAI,EAAEvC,MAAA,CAAAwC;EAAQ;sBACvB,MAAkF,CAAlF5C,YAAA,CAAkF6C,0BAAA;MAAjEtC,KAAK,EAAC,IAAI;MAAC2B,IAAI,EAAC,OAAO;MAAEY,KAAK,EAAE1C,MAAA,CAAA2C;wCACjD/C,YAAA,CAAiE6C,0BAAA;MAAhDtC,KAAK,EAAC,IAAI;MAACyC,IAAI,EAAC;QACjChD,YAAA,CAAuE6C,0BAAA;MAAtDtC,KAAK,EAAC,MAAM;MAACyC,IAAI,EAAC;QACnChD,YAAA,CAIkB6C,0BAAA;MAJDtC,KAAK,EAAC;IAAI;MACd0C,OAAO,EAAAhC,QAAA,CAAEiC,KAAK,K,kCACrBA,KAAK,CAACC,GAAG,CAACC,MAAM,IAAIF,KAAK,CAACC,GAAG,CAACC,MAAM,CAACC,iBAAiB,IAAIH,KAAK,CAACC,GAAG,CAACC,MAAM,CAACC,iBAAiB,IAAIxC,IAAI,iB;;;QAG1Gb,YAAA,CAA2E6C,0BAAA;MAA1DtC,KAAK,EAAC,OAAO;MAACyC,IAAI,EAAC;QACpChD,YAAA,CAAoE6C,0BAAA;MAAnDtC,KAAK,EAAC,MAAM;MAACyC,IAAI,EAAC;QACnChD,YAAA,CAAoE6C,0BAAA;MAAnDtC,KAAK,EAAC,QAAQ;MAACyC,IAAI,EAAC;QACrChD,YAAA,CAAyE6C,0BAAA;MAAxDtC,KAAK,EAAC,OAAO;MAACyC,IAAI,EAAC;QACpChD,YAAA,CAAwE6C,0BAAA;MAAvDtC,KAAK,EAAC,OAAO;MAACyC,IAAI,EAAC;QACpChD,YAAA,CAAsE6C,0BAAA;MAArDtC,KAAK,EAAC,OAAO;MAACyC,IAAI,EAAC;QACpChD,YAAA,CAIkB6C,0BAAA;MAJDtC,KAAK,EAAC,MAAM;MAACyC,IAAI,EAAC;;MACtBC,OAAO,EAAAhC,QAAA,CAAEiC,KAAK,K,kCACrBI,QAAA,CAAAC,aAAa,CAACL,KAAK,CAACC,GAAG,CAACK,cAAc,kB;;;QAG5CxD,YAAA,CAIkB6C,0BAAA;MAJDtC,KAAK,EAAC,QAAQ;MAACyC,IAAI,EAAC;;MACxBC,OAAO,EAAAhC,QAAA,CAAEiC,KAAK,K,kCACrBI,QAAA,CAAAC,aAAa,CAACL,KAAK,CAACC,GAAG,CAACM,YAAY,kB;;;;;;sDAtBNrD,MAAA,CAAAsD,OAAO,E,GA0B7C1D,YAAA,CAAyG2D,eAAA;IAAlGC,KAAK,EAAExD,MAAA,CAAAwD,KAAK;IAAG,aAAW,EAAExD,MAAA,CAAAyD,UAAU;IAAG,gBAAc,EAAEzD,MAAA,CAAA0D,aAAa;IAAG,WAAS,EAAE1D,MAAA,CAAAC,MAAM,CAACI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}