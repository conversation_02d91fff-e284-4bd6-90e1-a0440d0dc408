{"ast": null, "code": "export default {\n  name: \"PageIndex\",\n  props: {\n    // 数据总条数\n    total: {\n      type: Number,\n      default: 0\n    },\n    // 每页显示条数\n    size: {\n      type: Number,\n      default: 10\n    },\n    // 当前页码\n    sizeChange: {\n      type: Function\n    },\n    // 分页条数\n    currentChange: {\n      type: Function\n    },\n    pageSize: {\n      type: Number,\n      default: 20\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "total", "type", "Number", "default", "size", "sizeChange", "Function", "currentChange", "pageSize"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/components/Page/index.vue"], "sourcesContent": ["<template>\n  <el-pagination\n    :total=\"total\"\n    :page-size=\"pageSize\"\n    class=\"page-bar\"\n    layout=\"total, prev, pager, next, sizes\"\n    @size-change=\"sizeChange\"\n    @current-change=\"currentChange\"/>\n</template>\n\n<script>\n  export default {\n    name: \"PageIndex\",\n    props: {\n      // 数据总条数\n      total: {\n        type: Number,\n        default: 0\n      },\n      // 每页显示条数\n      size: {\n        type: Number,\n        default: 10\n      },\n      // 当前页码\n      sizeChange: {\n        type: Function\n      },\n      // 分页条数\n      currentChange: {\n        type: Function\n      },\n      pageSize: {\n        type: Number,\n        default: 20\n      }\n    }\n  }\n</script>\n\n<style scoped=\"scope\" lang=\"scss\">\n.page-bar {\n  margin-top: 10px;\n}\n</style>\n"], "mappings": "AAWE,eAAe;EACbA,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE;IACL;IACAC,KAAK,EAAE;MACLC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACD;IACAC,IAAI,EAAE;MACJH,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACD;IACAE,UAAU,EAAE;MACVJ,IAAI,EAAEK;IACR,CAAC;IACD;IACAC,aAAa,EAAE;MACbN,IAAI,EAAEK;IACR,CAAC;IACDE,QAAQ,EAAE;MACRP,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}