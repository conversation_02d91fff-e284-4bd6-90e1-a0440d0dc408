{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { onMounted } from \"vue\";\nimport { useRoute } from \"vue-router\";\nimport router from \"../../router\";\nimport { loading, success } from \"../../util/tipsUtils\";\nimport { workWeChatLogin } from \"../../api/login\";\nimport { setToken } from \"../../util/tokenUtils\";\nexport default {\n  name: \"workWeChatLogin\",\n  setup() {\n    const route = useRoute();\n    onMounted(() => {\n      let loadingObj = loading(\"正在登录...\");\n      const code = route.query.code;\n      const state = route.query.state;\n      const appId = route.query.appid;\n      console.log(code, state, appId);\n      workWeChatLogin({\n        code,\n        state,\n        appId\n      }, res => {\n        success(\"登录成功\");\n        const accessToken = {\n          expiresIn: res.expiresIn,\n          value: res.value\n        };\n        const refreshToken = res.refreshToken;\n        const data = {\n          accessToken: accessToken,\n          refreshToken: refreshToken\n        };\n        // 保存登录信息\n        setToken(data);\n        loadingObj.close();\n        router.push(\"/index\");\n      }).catch(e => {\n        console.log(e);\n        loadingObj.close();\n        router.push(\"/login\");\n      });\n    });\n    return {};\n  }\n};", "map": {"version": 3, "names": ["onMounted", "useRoute", "router", "loading", "success", "workWeChatLogin", "setToken", "name", "setup", "route", "loadingObj", "code", "query", "state", "appId", "appid", "console", "log", "res", "accessToken", "expiresIn", "value", "refreshToken", "data", "close", "push", "catch", "e"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/login/workWeChat.vue"], "sourcesContent": ["<template>\n  <div/>\n</template>\n\n<script>\nimport {onMounted} from \"vue\"\nimport {useRoute} from \"vue-router\"\nimport router from \"../../router\"\nimport {loading, success} from \"../../util/tipsUtils\";\nimport {workWeChatLogin} from \"../../api/login\"\nimport {setToken} from \"../../util/tokenUtils\";\nexport default {\n  name: \"workWeChatLogin\",\n  setup() {\n    const route = useRoute()\n    onMounted(() => {\n      let loadingObj = loading(\"正在登录...\")\n      const code = route.query.code\n      const state = route.query.state\n      const appId = route.query.appid\n      console.log(code, state, appId)\n      workWeChatLogin({ code, state, appId }, (res) => {\n        success(\"登录成功\");\n        const accessToken = { expiresIn: res.expiresIn, value: res.value };\n        const refreshToken = res.refreshToken;\n        const data = { accessToken: accessToken, refreshToken: refreshToken };\n        // 保存登录信息\n        setToken(data)\n        loadingObj.close()\n        router.push(\"/index\")\n      }).catch((e) => {\n        console.log(e)\n        loadingObj.close()\n        router.push(\"/login\")\n      })\n    })\n    return {}\n  }\n}\n</script>\n"], "mappings": ";AAKA,SAAQA,SAAS,QAAO,KAAI;AAC5B,SAAQC,QAAQ,QAAO,YAAW;AAClC,OAAOC,MAAK,MAAO,cAAa;AAChC,SAAQC,OAAO,EAAEC,OAAO,QAAO,sBAAsB;AACrD,SAAQC,eAAe,QAAO,iBAAgB;AAC9C,SAAQC,QAAQ,QAAO,uBAAuB;AAC9C,eAAe;EACbC,IAAI,EAAE,iBAAiB;EACvBC,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIR,QAAQ,EAAC;IACvBD,SAAS,CAAC,MAAM;MACd,IAAIU,UAAS,GAAIP,OAAO,CAAC,SAAS;MAClC,MAAMQ,IAAG,GAAIF,KAAK,CAACG,KAAK,CAACD,IAAG;MAC5B,MAAME,KAAI,GAAIJ,KAAK,CAACG,KAAK,CAACC,KAAI;MAC9B,MAAMC,KAAI,GAAIL,KAAK,CAACG,KAAK,CAACG,KAAI;MAC9BC,OAAO,CAACC,GAAG,CAACN,IAAI,EAAEE,KAAK,EAAEC,KAAK;MAC9BT,eAAe,CAAC;QAAEM,IAAI;QAAEE,KAAK;QAAEC;MAAM,CAAC,EAAGI,GAAG,IAAK;QAC/Cd,OAAO,CAAC,MAAM,CAAC;QACf,MAAMe,WAAU,GAAI;UAAEC,SAAS,EAAEF,GAAG,CAACE,SAAS;UAAEC,KAAK,EAAEH,GAAG,CAACG;QAAM,CAAC;QAClE,MAAMC,YAAW,GAAIJ,GAAG,CAACI,YAAY;QACrC,MAAMC,IAAG,GAAI;UAAEJ,WAAW,EAAEA,WAAW;UAAEG,YAAY,EAAEA;QAAa,CAAC;QACrE;QACAhB,QAAQ,CAACiB,IAAI;QACbb,UAAU,CAACc,KAAK,EAAC;QACjBtB,MAAM,CAACuB,IAAI,CAAC,QAAQ;MACtB,CAAC,CAAC,CAACC,KAAK,CAAEC,CAAC,IAAK;QACdX,OAAO,CAACC,GAAG,CAACU,CAAC;QACbjB,UAAU,CAACc,KAAK,EAAC;QACjBtB,MAAM,CAACuB,IAAI,CAAC,QAAQ;MACtB,CAAC;IACH,CAAC;IACD,OAAO,CAAC;EACV;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}