{"ast": null, "code": "import { get } from \"../../util/requestUtils\";\nexport function getAuthorityList(params, callback) {\n  return get(\"/auth/authorities\", params, callback);\n}\nexport function getAuthorityTree(callback) {\n  return get(\"/auth/authorities/tree\", {}, callback);\n}", "map": {"version": 3, "names": ["get", "getAuthorityList", "params", "callback", "getAuthorityTree"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/api/auth/authority.js"], "sourcesContent": ["import {get} from \"../../util/requestUtils\";\n\nexport function getAuthorityList(params, callback) {\n  return get(\"/auth/authorities\", params, callback)\n}\n\nexport function getAuthorityTree(callback) {\n  return get(\"/auth/authorities/tree\", {}, callback)\n}\n"], "mappings": "AAAA,SAAQA,GAAG,QAAO,yBAAyB;AAE3C,OAAO,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EACjD,OAAOH,GAAG,CAAC,mBAAmB,EAAEE,MAAM,EAAEC,QAAQ,CAAC;AACnD;AAEA,OAAO,SAASC,gBAAgBA,CAACD,QAAQ,EAAE;EACzC,OAAOH,GAAG,CAAC,wBAAwB,EAAE,CAAC,CAAC,EAAEG,QAAQ,CAAC;AACpD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}