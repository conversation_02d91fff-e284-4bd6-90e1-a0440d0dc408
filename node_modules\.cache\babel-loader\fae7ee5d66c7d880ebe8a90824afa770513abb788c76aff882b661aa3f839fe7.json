{"ast": null, "code": "import { ref } from \"vue\";\nimport Page from \"../../../components/Page\";\nimport { getMemberList, sealMember, unsealMember, updateMember, memberPwdReset } from \"@/api/member/index\";\nimport { confirm, success } from \"@/util/tipsUtils\";\nexport default {\n  name: \"MemeberList\",\n  components: {\n    Page\n  },\n  setup() {\n    const showResetPwdDialogFlag = ref(false);\n    const showUserDialogFlag = ref(false);\n    const stateMap = {\n      \"normal\": \"正常\",\n      \"black\": \"黑名单\",\n      \"lock\": \"锁定\",\n      \"deleted\": \"注销\"\n    };\n    const total = ref(0);\n    const memberList = ref([]);\n    const dataLoading = ref(true);\n    const param = ref({\n      current: 1,\n      size: 20,\n      keyword: \"\"\n    });\n    const member = ref({});\n    const loadMemberList = () => {\n      dataLoading.value = true;\n      getMemberList(param.value, res => {\n        dataLoading.value = false;\n        memberList.value = res.list;\n        total.value = res.total;\n      }).catch(() => {\n        dataLoading.value = false;\n      });\n    };\n    loadMemberList();\n    // 页码改变\n    const currentChange = currentPage => {\n      param.value.current = currentPage;\n      loadMemberList();\n    };\n    // 页面显示数量改变\n    const sizeChange = size => {\n      param.value.size = size;\n      loadMemberList();\n    };\n    const search = () => {\n      loadMemberList();\n    };\n    const seal = function (item) {\n      confirm(\"确认禁用该会员【\" + item.name + \"】\", \"禁用\", () => {\n        sealMember({\n          id: item.id\n        }, () => {\n          loadMemberList();\n        });\n      });\n    };\n    const unseal = function (item) {\n      confirm(\"确认解禁该会员【\" + item.name + \"】\", \"解禁\", () => {\n        unsealMember({\n          id: item.id\n        }, () => {\n          loadMemberList();\n        });\n      });\n    };\n    const showUserDialog = function (item) {\n      showUserDialogFlag.value = true;\n      member.value = item;\n    };\n    const hideUserDialog = function () {\n      showUserDialogFlag.value = false;\n    };\n    const submit = function () {\n      member.value.createTime = null;\n      member.value.updateTime = null;\n      updateMember(member.value, () => {\n        success(\"更新成功\");\n        hideUserDialog();\n      });\n    };\n    const memberReset = ref({\n      username: \"\",\n      password: \"\"\n    });\n    const showResetPwdDialog = function (item) {\n      showResetPwdDialogFlag.value = true;\n      memberReset.value.username = item.username;\n    };\n    const hideResetPwdDialog = function () {\n      showResetPwdDialogFlag.value = false;\n    };\n    const resetPwdSubmit = function () {\n      memberPwdReset(memberReset.value, res => {\n        success(\"重置成功\");\n        console.log(\"重置密码\", res);\n        hideResetPwdDialog();\n      });\n    };\n    return {\n      stateMap,\n      param,\n      total,\n      memberList,\n      currentChange,\n      sizeChange,\n      search,\n      dataLoading,\n      seal,\n      unseal,\n      showUserDialogFlag,\n      showUserDialog,\n      hideUserDialog,\n      member,\n      submit,\n      showResetPwdDialogFlag,\n      showResetPwdDialog,\n      hideResetPwdDialog,\n      resetPwdSubmit,\n      memberReset\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "Page", "getMemberList", "sealMember", "unsealMember", "updateMember", "memberPwdReset", "confirm", "success", "name", "components", "setup", "showResetPwdDialogFlag", "showUserDialogFlag", "stateMap", "total", "memberList", "dataLoading", "param", "current", "size", "keyword", "member", "loadMemberList", "value", "res", "list", "catch", "currentChange", "currentPage", "sizeChange", "search", "seal", "item", "id", "unseal", "showUserDialog", "hideUserDialog", "submit", "createTime", "updateTime", "memberReset", "username", "password", "showResetPwdDialog", "hideResetPwdDialog", "resetPwdSubmit", "console", "log"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/member/list/index.vue"], "sourcesContent": ["<template>\n  <div class=\"member-container\">\n    <div class=\"head\">\n      <el-input size=\"small\" v-model=\"param.keyword\" clearable placeholder=\"输入名称搜索\" class=\"custom-input\" @keyup.enter=\"search\">\n        <template #append>\n          <el-button size=\"small\" class=\"custom-btn\" icon=\"el-icon-search\" @click=\"search\">搜索</el-button>\n        </template>\n      </el-input>\n    </div>\n    <el-table v-loading=\"dataLoading\" :data=\"memberList\" size=\"small\" style=\"width: 100%;\">\n      <el-table-column type=\"expand\">\n        <template #default=\"props\">\n          <el-card class=\"box-card\">\n            <template #header>\n              <div>\n                <span>基础信息</span>\n              </div>\n            </template>\n            <div class=\"table-wrapper\">\n              <table class=\"fl-table\">\n                <tbody>\n                  <tr><td>编号</td><td>{{props.row.code}}</td></tr>\n                  <tr><td>姓名</td><td>{{props.row.name}}</td></tr>\n                  <tr><td>性别</td><td>{{props.row.gender}}</td></tr>\n                  <tr><td>出生日期</td><td>{{props.row.birthday}}</td></tr>\n                  <tr><td>人员状态</td><td>{{stateMap[props.row.status]}}</td></tr>\n                  <tr><td>注册时间</td><td>{{props.row.createTime}}</td></tr>\n                  <tr><td>过期时间</td><td>{{props.row.expireTime}}</td></tr>\n                  <tr><td>手机电话</td><td>{{props.row.mobile}}</td></tr>\n                  <tr><td>座机号码</td><td>{{props.row.telephone}}</td></tr>\n                  <tr><td>电子邮箱</td><td>{{props.row.email}}</td></tr>\n                  <tr><td>会员等级</td><td>{{props.row.level && props.row.level.name || \"无\"}}</td></tr>\n                </tbody>\n              </table>\n            </div>\n          </el-card>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"username\" label=\"账号\"/>\n      <el-table-column prop=\"name\" label=\"姓名\"/>\n      <el-table-column prop=\"mobile\" label=\"手机号码\"/>\n      <el-table-column :show-overflow-tooltip=\"true\" prop=\"email\" label=\"邮箱\"/>\n      <el-table-column label=\"会员等级\">\n        <template #default=\"scope\">\n          {{scope.row.level && scope.row.level.name || \"无\"}}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"状态\" align=\"center\">\n        <template #default=\"scope\">\n          {{stateMap[scope.row.status]}}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\">\n        <template #default=\"scope\">\n          <el-button size=\"small\" type=\"text\" @click=\"showUserDialog(scope.row)\">编辑</el-button>\n          <el-button size=\"small\" type=\"text\" style=\"color: red;\" @click=\"seal(scope.row)\" v-if=\"scope.row.status === 'normal'\">禁用</el-button>\n          <el-button size=\"small\" type=\"text\" v-if=\"scope.row.status === 'lock'\" @click=\"unseal(scope.row)\">解禁</el-button>\n          <el-button size=\"small\" type=\"text\" @click=\"showResetPwdDialog(scope.row)\">重置密码</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <!--分页组件-->\n    <page :total=\"total\" @size-change=\"sizeChange\" @current-change=\"currentChange\" :page-size=\"param.size\"/>\n    <el-dialog v-model=\"showResetPwdDialogFlag\" :title=\"'重置密码'\" append-to-body width=\"90%\" :before-close=\"hideResetPwdDialog\">\n      <div style=\"padding: 10px 0;\">\n        <div style=\"margin: 10px;display: inline-block;\">新密码：</div>\n        <div style=\"margin: 10px;display: inline-block;width: 300px;\">\n          <el-input style=\"height: 40px;\" size=\"small\" v-model=\"memberReset.password\" placeholder=\"请输入密码\"></el-input>\n        </div>\n      </div>\n      <template #footer>\n        <div style=\"text-align: center;\">\n          <el-button size=\"small\" @click=\"resetPwdSubmit\">提交</el-button>\n        </div>\n      </template>\n    </el-dialog>\n    <!-- 编辑 -->\n    <el-dialog v-model=\"showUserDialogFlag\" :title=\"'编辑用户'\" append-to-body width=\"90%\" :before-close=\"hideUserDialog\">\n      <el-form :model=\"member\" ref=\"userRef\" class=\"user-form\" label-width=\"150px\">\n        <el-form-item label=\"名字：\" prop=\"name\">\n          <el-input size=\"small\" v-model=\"member.name\" placeholder=\"请输入名字\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"账号：\" prop=\"username\">\n          <el-input size=\"small\" v-model=\"member.username\" placeholder=\"请输入账号\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"工号：\" prop=\"code\">\n          <el-input size=\"small\" v-model=\"member.code\" placeholder=\"请输入工号\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"邮箱：\" prop=\"email\">\n          <el-input size=\"small\" v-model=\"member.email\" placeholder=\"请输入邮箱\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"手机号码：\" prop=\"mobile\">\n          <el-input size=\"small\" v-model=\"member.mobile\" placeholder=\"请输入手机号码\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"出生日期：\" prop=\"birthday\">\n          <el-date-picker style=\"width: 100%;\" size=\"small\" v-model=\"member.birthday\" type=\"date\" placeholder=\"选择出生日期\"></el-date-picker>\n        </el-form-item>\n        <el-form-item label=\"性别：\" prop=\"gender\">\n          <el-radio size=\"small\" v-model=\"member.gender\" label=\"男\">男</el-radio>\n          <el-radio size=\"small\" v-model=\"member.gender\" label=\"女\">女</el-radio>\n        </el-form-item>\n        <el-form-item label=\"办公电话：\" prop=\"telephone\">\n          <el-input size=\"small\" v-model=\"member.telephone\" placeholder=\"请输入电话\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"过期时间：\" prop=\"contractStartDate\">\n          <el-date-picker style=\"width: 100%;\" size=\"small\" v-model=\"member.expireTime\" type=\"date\" placeholder=\"过期时间\" format=\"YYYY-MM-DD HH:mm:ss\" value-format=\"YYYY-MM-DD HH:mm:ss\"></el-date-picker>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div style=\"text-align: center;\">\n          <el-button size=\"small\" @click=\"submit\">提交</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import {ref} from \"vue\"\n  import Page from \"../../../components/Page\"\n  import {getMemberList, sealMember, unsealMember, updateMember, memberPwdReset} from \"@/api/member/index\";\n  import {confirm, success} from \"@/util/tipsUtils\"\n  export default {\n    name: \"MemeberList\",\n    components: {\n      Page\n    },\n    setup() {\n      const showResetPwdDialogFlag = ref(false)\n      const showUserDialogFlag = ref(false)\n      const stateMap = {\"normal\": \"正常\", \"black\": \"黑名单\", \"lock\": \"锁定\", \"deleted\": \"注销\"}\n      const total = ref(0)\n      const memberList = ref([])\n      const dataLoading = ref(true)\n      const param = ref({\n        current: 1,\n        size: 20,\n        keyword: \"\",\n      })\n      const member = ref({})\n      const loadMemberList = () => {\n        dataLoading.value = true\n        getMemberList(param.value, res => {\n          dataLoading.value = false\n          memberList.value = res.list\n          total.value = res.total\n        }).catch(() => {\n          dataLoading.value = false\n        })\n      }\n      loadMemberList();\n      // 页码改变\n      const currentChange = (currentPage) => {\n        param.value.current = currentPage;\n        loadMemberList()\n      }\n      // 页面显示数量改变\n      const sizeChange = (size) => {\n        param.value.size = size;\n        loadMemberList()\n      }\n      const search = () => {\n        loadMemberList()\n      }\n      const seal = function (item) {\n        confirm(\"确认禁用该会员【\"+ item.name +\"】\",  \"禁用\", () => {\n          sealMember({id: item.id}, () => {\n            loadMemberList()\n          })\n        })\n      }\n      const unseal = function (item) {\n        confirm(\"确认解禁该会员【\"+ item.name +\"】\",  \"解禁\", () => {\n          unsealMember({id: item.id}, () => {\n            loadMemberList()\n          })\n        })\n      }\n      const showUserDialog = function (item) {\n        showUserDialogFlag.value = true\n        member.value = item\n      }\n      const hideUserDialog = function () {\n        showUserDialogFlag.value = false\n      }\n      const submit = function () {\n        member.value.createTime = null\n        member.value.updateTime = null\n        updateMember(member.value, () => {\n          success(\"更新成功\")\n          hideUserDialog()\n        })\n      }\n      const memberReset = ref({\n        username: \"\",\n        password: \"\"\n      })\n      const showResetPwdDialog = function (item) {\n        showResetPwdDialogFlag.value = true\n        memberReset.value.username = item.username\n      }\n      const hideResetPwdDialog = function () {\n        showResetPwdDialogFlag.value = false\n      }\n      const resetPwdSubmit = function () {\n        memberPwdReset(memberReset.value, (res) => {\n          success(\"重置成功\")\n          console.log(\"重置密码\", res)\n          hideResetPwdDialog()\n        })\n      }\n      return {\n        stateMap,\n        param,\n        total,\n        memberList,\n        currentChange,\n        sizeChange,\n        search,\n        dataLoading,\n        seal,\n        unseal,\n        showUserDialogFlag,\n        showUserDialog,\n        hideUserDialog,\n        member,\n        submit,\n        showResetPwdDialogFlag,\n        showResetPwdDialog,\n        hideResetPwdDialog,\n        resetPwdSubmit,\n        memberReset\n      }\n    }\n  }\n</script>\n\n<style scoped lang=\"scss\">\n  .member-container {\n    margin: 20px;\n    .head {\n      margin-bottom: 10px;\n      .custom-input {\n        width: 50%;\n        min-width: 300px;\n        max-width: 400px;\n      }\n      .custom-btn {\n        &:hover {\n          color: $--color-primary;\n        }\n      }\n    }\n  }\n  .box-card {\n    max-width: 500px;\n  }\n  .fl-table {\n    border-radius: 5px;\n    font-size: 12px;\n    font-weight: normal;\n    border: none;\n    border-collapse: collapse;\n    width: 100%;\n    background-color: white;\n  }\n  .fl-table td {\n    border: 1px solid #f8f8f8;\n    font-size: 12px;\n    padding: 12px;\n  }\n  .fl-table tr td:nth-child(1) {\n    background: #F8F8F8;\n    width: 30%;\n    min-width: 100px;\n  }\n  .user-form {\n    display: inline-block;\n    .el-form-item {\n      width: 50%;\n      float: left;\n    }\n  }\n</style>\n"], "mappings": "AAsHE,SAAQA,GAAG,QAAO,KAAI;AACtB,OAAOC,IAAG,MAAO,0BAAyB;AAC1C,SAAQC,aAAa,EAAEC,UAAU,EAAEC,YAAY,EAAEC,YAAY,EAAEC,cAAc,QAAO,oBAAoB;AACxG,SAAQC,OAAO,EAAEC,OAAO,QAAO,kBAAiB;AAChD,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE;IACVT;EACF,CAAC;EACDU,KAAKA,CAAA,EAAG;IACN,MAAMC,sBAAqB,GAAIZ,GAAG,CAAC,KAAK;IACxC,MAAMa,kBAAiB,GAAIb,GAAG,CAAC,KAAK;IACpC,MAAMc,QAAO,GAAI;MAAC,QAAQ,EAAE,IAAI;MAAE,OAAO,EAAE,KAAK;MAAE,MAAM,EAAE,IAAI;MAAE,SAAS,EAAE;IAAI;IAC/E,MAAMC,KAAI,GAAIf,GAAG,CAAC,CAAC;IACnB,MAAMgB,UAAS,GAAIhB,GAAG,CAAC,EAAE;IACzB,MAAMiB,WAAU,GAAIjB,GAAG,CAAC,IAAI;IAC5B,MAAMkB,KAAI,GAAIlB,GAAG,CAAC;MAChBmB,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC;IACD,MAAMC,MAAK,GAAItB,GAAG,CAAC,CAAC,CAAC;IACrB,MAAMuB,cAAa,GAAIA,CAAA,KAAM;MAC3BN,WAAW,CAACO,KAAI,GAAI,IAAG;MACvBtB,aAAa,CAACgB,KAAK,CAACM,KAAK,EAAEC,GAAE,IAAK;QAChCR,WAAW,CAACO,KAAI,GAAI,KAAI;QACxBR,UAAU,CAACQ,KAAI,GAAIC,GAAG,CAACC,IAAG;QAC1BX,KAAK,CAACS,KAAI,GAAIC,GAAG,CAACV,KAAI;MACxB,CAAC,CAAC,CAACY,KAAK,CAAC,MAAM;QACbV,WAAW,CAACO,KAAI,GAAI,KAAI;MAC1B,CAAC;IACH;IACAD,cAAc,EAAE;IAChB;IACA,MAAMK,aAAY,GAAKC,WAAW,IAAK;MACrCX,KAAK,CAACM,KAAK,CAACL,OAAM,GAAIU,WAAW;MACjCN,cAAc,EAAC;IACjB;IACA;IACA,MAAMO,UAAS,GAAKV,IAAI,IAAK;MAC3BF,KAAK,CAACM,KAAK,CAACJ,IAAG,GAAIA,IAAI;MACvBG,cAAc,EAAC;IACjB;IACA,MAAMQ,MAAK,GAAIA,CAAA,KAAM;MACnBR,cAAc,EAAC;IACjB;IACA,MAAMS,IAAG,GAAI,SAAAA,CAAUC,IAAI,EAAE;MAC3B1B,OAAO,CAAC,UAAU,GAAE0B,IAAI,CAACxB,IAAG,GAAG,GAAG,EAAG,IAAI,EAAE,MAAM;QAC/CN,UAAU,CAAC;UAAC+B,EAAE,EAAED,IAAI,CAACC;QAAE,CAAC,EAAE,MAAM;UAC9BX,cAAc,EAAC;QACjB,CAAC;MACH,CAAC;IACH;IACA,MAAMY,MAAK,GAAI,SAAAA,CAAUF,IAAI,EAAE;MAC7B1B,OAAO,CAAC,UAAU,GAAE0B,IAAI,CAACxB,IAAG,GAAG,GAAG,EAAG,IAAI,EAAE,MAAM;QAC/CL,YAAY,CAAC;UAAC8B,EAAE,EAAED,IAAI,CAACC;QAAE,CAAC,EAAE,MAAM;UAChCX,cAAc,EAAC;QACjB,CAAC;MACH,CAAC;IACH;IACA,MAAMa,cAAa,GAAI,SAAAA,CAAUH,IAAI,EAAE;MACrCpB,kBAAkB,CAACW,KAAI,GAAI,IAAG;MAC9BF,MAAM,CAACE,KAAI,GAAIS,IAAG;IACpB;IACA,MAAMI,cAAa,GAAI,SAAAA,CAAA,EAAY;MACjCxB,kBAAkB,CAACW,KAAI,GAAI,KAAI;IACjC;IACA,MAAMc,MAAK,GAAI,SAAAA,CAAA,EAAY;MACzBhB,MAAM,CAACE,KAAK,CAACe,UAAS,GAAI,IAAG;MAC7BjB,MAAM,CAACE,KAAK,CAACgB,UAAS,GAAI,IAAG;MAC7BnC,YAAY,CAACiB,MAAM,CAACE,KAAK,EAAE,MAAM;QAC/BhB,OAAO,CAAC,MAAM;QACd6B,cAAc,EAAC;MACjB,CAAC;IACH;IACA,MAAMI,WAAU,GAAIzC,GAAG,CAAC;MACtB0C,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAMC,kBAAiB,GAAI,SAAAA,CAAUX,IAAI,EAAE;MACzCrB,sBAAsB,CAACY,KAAI,GAAI,IAAG;MAClCiB,WAAW,CAACjB,KAAK,CAACkB,QAAO,GAAIT,IAAI,CAACS,QAAO;IAC3C;IACA,MAAMG,kBAAiB,GAAI,SAAAA,CAAA,EAAY;MACrCjC,sBAAsB,CAACY,KAAI,GAAI,KAAI;IACrC;IACA,MAAMsB,cAAa,GAAI,SAAAA,CAAA,EAAY;MACjCxC,cAAc,CAACmC,WAAW,CAACjB,KAAK,EAAGC,GAAG,IAAK;QACzCjB,OAAO,CAAC,MAAM;QACduC,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEvB,GAAG;QACvBoB,kBAAkB,EAAC;MACrB,CAAC;IACH;IACA,OAAO;MACL/B,QAAQ;MACRI,KAAK;MACLH,KAAK;MACLC,UAAU;MACVY,aAAa;MACbE,UAAU;MACVC,MAAM;MACNd,WAAW;MACXe,IAAI;MACJG,MAAM;MACNtB,kBAAkB;MAClBuB,cAAc;MACdC,cAAc;MACdf,MAAM;MACNgB,MAAM;MACN1B,sBAAsB;MACtBgC,kBAAkB;MAClBC,kBAAkB;MAClBC,cAAc;MACdL;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}