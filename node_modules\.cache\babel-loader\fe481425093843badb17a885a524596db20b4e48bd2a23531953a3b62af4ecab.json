{"ast": null, "code": "import { ref } from \"vue\";\nimport Page from \"@/components/Page\";\nimport { gotoCertificateTemplateEdit } from \"@/router/goto\";\nimport { findCertificateTemplateList } from \"@/api/certificate\";\nexport default {\n  name: \"LearnReportSignUpIndex\",\n  components: {\n    Page\n  },\n  setup() {\n    const templateList = ref([]);\n    const params = ref({\n      current: 1,\n      size: 20\n    });\n    const loadList = () => {\n      findCertificateTemplateList(params.value, res => {\n        console.log(res);\n        if (res) {\n          total.value = res.total;\n          templateList.value = res.list;\n        }\n      });\n    };\n    loadList();\n    const total = ref(0);\n    const currentChange = c => {\n      params.value.current = c;\n      loadList();\n    };\n    const sizeChange = s => {\n      params.value.size = s;\n      loadList();\n    };\n    const search = () => {\n      loadList();\n    };\n    return {\n      gotoCertificateTemplateEdit,\n      search,\n      params,\n      total,\n      currentChange,\n      sizeChange,\n      templateList\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "Page", "gotoCertificateTemplateEdit", "findCertificateTemplateList", "name", "components", "setup", "templateList", "params", "current", "size", "loadList", "value", "res", "console", "log", "total", "list", "currentChange", "c", "sizeChange", "s", "search"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/certificate/template/index.vue"], "sourcesContent": ["<template>\n  <div class=\"cert-template-wrap\">\n    <div class=\"cert-template-header\">\n      <el-form :inline=\"true\" :model=\"params\" class=\"form-inline\">\n        <el-form-item label=\"证书名称\">\n          <el-input size=\"small\" @keydown.enter=\"search\" class=\"search-input\" v-model=\"params.keyword\" placeholder=\"请输入关键字\">\n            <template #suffix>\n              <i @click=\"search\" class=\"el-input__icon el-icon-search search-btn\"></i>\n            </template>\n          </el-input>\n        </el-form-item>\n        <el-form-item label=\"状态\" class=\"select\">\n          <el-select size=\"small\" v-model=\"params.status\" @change=\"search\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"启用\" value=\"active\"></el-option>\n            <el-option label=\"禁用\" value=\"inactive\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" type=\"primary\" @click=\"gotoCertificateTemplateEdit()\">\n            <el-icon style=\"vertical-align: middle\">\n              <Plus />\n            </el-icon>\n            <span style=\"vertical-align: middle\">新增</span>\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"cert-template-main\">\n      <el-table :data=\"templateList\">\n        <el-table-column label=\"证书名称\" prop=\"name\">\n          <template #default=\"scope\">\n            <img :src=\"scope.row.desgin\" />\n          </template>\n        </el-table-column>\n        <el-table-column label=\"证书名称\" prop=\"name\"></el-table-column>\n        <el-table-column label=\"证书描述\" prop=\"description\"></el-table-column>\n        <el-table-column label=\"颁发机构\" prop=\"awardingOrganization\"></el-table-column>\n        <el-table-column label=\"颁发人员\" prop=\"awarderName\"></el-table-column>\n        <el-table-column label=\"颁发条件\" prop=\"awardConditions\"></el-table-column>\n        <el-table-column label=\"到期策略\" prop=\"validityPolicy\"></el-table-column>\n        <el-table-column label=\"状态\" prop=\"statusName\"></el-table-column>\n        <el-table-column label=\"操作\">\n          <div class=\"opt-btn-wrap\">\n            <div class=\"opt-btn-item\">\n              <el-button size=\"small\" type=\"primary\">预览</el-button>\n            </div>\n            <div class=\"opt-btn-item\">\n              <el-button size=\"small\" type=\"primary\">编辑</el-button>\n            </div>\n            <div class=\"opt-btn-item\">\n              <el-button size=\"small\" type=\"primary\">关联</el-button>\n            </div>\n            <div class=\"opt-btn-item\">\n              <el-button size=\"small\" type=\"danger\">删除</el-button>\n            </div>\n          </div>\n        </el-table-column>\n      </el-table>\n      <page :total=\"total\" :size-change=\"sizeChange\" :current-change=\"currentChange\" :page-size=\"params.size\"/>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {ref} from \"vue\"\nimport Page from \"@/components/Page\";\nimport {gotoCertificateTemplateEdit} from \"@/router/goto\";\nimport {findCertificateTemplateList} from \"@/api/certificate\";\nexport default {\n  name: \"LearnReportSignUpIndex\",\n  components: {Page},\n  setup() {\n    const templateList = ref([])\n    const params = ref({\n      current: 1,\n      size: 20\n    })\n    const loadList = () => {\n      findCertificateTemplateList(params.value, res => {\n        console.log(res)\n        if (res) {\n          total.value = res.total;\n          templateList.value = res.list;\n        }\n      })\n    }\n    loadList()\n    const total = ref(0)\n    const currentChange = (c) => {\n      params.value.current = c;\n      loadList();\n    }\n    const sizeChange = (s) => {\n      params.value.size = s;\n      loadList();\n    }\n    const search = () => {\n      loadList();\n    }\n    return {\n      gotoCertificateTemplateEdit,\n      search,\n      params,\n      total,\n      currentChange,\n      sizeChange,\n      templateList\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n  .cert-template-wrap {\n    margin: 20px;\n    font-size: 12px;\n    .cert-template-main {\n      ::v-deep .el-table {\n        font-size: 12px;\n        .el-table__empty-block {\n          line-height: 400px;\n          .el-table__empty-text {\n            line-height: 400px;\n          }\n        }\n        th, td {\n          padding: 6px 0;\n        }\n      }\n    }\n    .opt-btn-wrap {\n      //display: flex;\n    }\n    .opt-btn-item {\n      width: 50%;\n      display: inline-block;\n      margin: 2px;\n    }\n  }\n</style>\n"], "mappings": "AAiEA,SAAQA,GAAG,QAAO,KAAI;AACtB,OAAOC,IAAG,MAAO,mBAAmB;AACpC,SAAQC,2BAA2B,QAAO,eAAe;AACzD,SAAQC,2BAA2B,QAAO,mBAAmB;AAC7D,eAAe;EACbC,IAAI,EAAE,wBAAwB;EAC9BC,UAAU,EAAE;IAACJ;EAAI,CAAC;EAClBK,KAAKA,CAAA,EAAG;IACN,MAAMC,YAAW,GAAIP,GAAG,CAAC,EAAE;IAC3B,MAAMQ,MAAK,GAAIR,GAAG,CAAC;MACjBS,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE;IACR,CAAC;IACD,MAAMC,QAAO,GAAIA,CAAA,KAAM;MACrBR,2BAA2B,CAACK,MAAM,CAACI,KAAK,EAAEC,GAAE,IAAK;QAC/CC,OAAO,CAACC,GAAG,CAACF,GAAG;QACf,IAAIA,GAAG,EAAE;UACPG,KAAK,CAACJ,KAAI,GAAIC,GAAG,CAACG,KAAK;UACvBT,YAAY,CAACK,KAAI,GAAIC,GAAG,CAACI,IAAI;QAC/B;MACF,CAAC;IACH;IACAN,QAAQ,EAAC;IACT,MAAMK,KAAI,GAAIhB,GAAG,CAAC,CAAC;IACnB,MAAMkB,aAAY,GAAKC,CAAC,IAAK;MAC3BX,MAAM,CAACI,KAAK,CAACH,OAAM,GAAIU,CAAC;MACxBR,QAAQ,EAAE;IACZ;IACA,MAAMS,UAAS,GAAKC,CAAC,IAAK;MACxBb,MAAM,CAACI,KAAK,CAACF,IAAG,GAAIW,CAAC;MACrBV,QAAQ,EAAE;IACZ;IACA,MAAMW,MAAK,GAAIA,CAAA,KAAM;MACnBX,QAAQ,EAAE;IACZ;IACA,OAAO;MACLT,2BAA2B;MAC3BoB,MAAM;MACNd,MAAM;MACNQ,KAAK;MACLE,aAAa;MACbE,UAAU;MACVb;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}