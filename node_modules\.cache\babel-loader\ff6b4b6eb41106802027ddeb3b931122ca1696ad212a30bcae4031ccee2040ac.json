{"ast": null, "code": "import html2canvas from \"html2canvas\";\nimport jsPDF from \"jspdf\";\nimport { nextTick, ref, computed } from \"vue\";\nimport { formatDate } from \"@/util/dateUtils\";\n// import { getLesson, learnSignUp } from \"@/api/learn/lesson\"\n// import { useRoute } from \"vue-router\"\nexport default {\n  name: \"CertificatePreview\",\n  props: {\n    certificate: {\n      type: Object\n    },\n    download: {\n      type: Boolean\n    }\n  },\n  setup() {\n    const courseName = ref(\"课程示例\"); // 课程名称\n    const userName = ref(\"张三\"); // 课程名称\n    const startTime = ref(formatDate(new Date(), \"yyyy-MM-dd\")); // 开始时间\n    const futureTime = new Date(new Date().getTime() + 90 * 24 * 60 * 60 * 1000).toLocaleString();\n    const endTime = ref(formatDate(futureTime, \"yyyy-MM-dd\")); // 结束时间\n    const awardDate = ref(formatDate(new Date(), \"yyyy年MM月dd日\")); // 颁发时间\n    const courseID = ref(0); // 课程Id\n    const memberId = ref(1); // 用户Id\n    // 设置编号  编号等于  日期 + 课程id  + 用户id这样\n    const code = computed(() => {\n      return \"UZ\" + formatDate(futureTime, \"yyyyMMdd\") + courseID.value + memberId.value;\n    });\n    const downloadCertificate = () => {\n      nextTick(() => {\n        // 使用$nextTick，解决数据还没有渲染到html就先转为图片，此时的图片会是空内容的问题\n        const canvas = document.createElement(\"canvas\"); // 创建一个canvas节点\n        const shareContent = document.getElementById(\"certificate\"); // 需要截图的包裹的（原生的）DOM 对象\n        const width = shareContent.offsetWidth; // 获取dom 宽度\n        const height = shareContent.offsetHeight; // 获取dom 高度\n        const scale = 2; // 定义任意放大倍数 支持小数\n        canvas.getContext(\"2d\").scale(scale, scale); // 获取context,设置scale\n        const rect = shareContent.getBoundingClientRect(); // 获取元素相对于视口的\n        const scrollTop = document.documentElement.scrollTop || document.body.scrollTop; // 获取滚动轴滚动的长度\n        html2canvas(document.getElementById(\"certificate\"), {\n          // 转换为图片\n          x: rect.left + 8,\n          // 绘制的dom元素相对于视口的位置\n          y: rect.top,\n          scrollY: -scrollTop,\n          scale: scale,\n          // 添加的scale 参数\n          width: width,\n          // dom 原始宽度\n          height: height,\n          useCORS: true,\n          // 开启跨域\n          dpi: window.devicePixelRatio * 2\n        }).then(canvas => {\n          const context = canvas.getContext(\"2d\");\n          // 关闭抗锯齿\n          context.mozImageSmoothingEnabled = false;\n          context.msImageSmoothingEnabled = false;\n          context.imageSmoothingEnabled = false;\n          const imgUrl = canvas.toDataURL(\"image/jpeg\");\n          var doc = new jsPDF(\"p\", \"mm\", \"a4\");\n          doc.addImage(imgUrl, \"JPEG\", 0, 0, 210, 297);\n          doc.save(\"my-certificate\" + \".pdf\");\n          // var a = document.createElement(\"a\")\n          // a.download =  \"my-certificate\"\n          // // 设置图片地址\n          // a.href = imgUrl;\n          // a.click();\n        });\n      });\n    };\n\n    return {\n      formatDate,\n      awardDate,\n      downloadCertificate,\n      courseName,\n      userName,\n      startTime,\n      endTime,\n      code\n    };\n  }\n};", "map": {"version": 3, "names": ["html2canvas", "jsPDF", "nextTick", "ref", "computed", "formatDate", "name", "props", "certificate", "type", "Object", "download", "Boolean", "setup", "courseName", "userName", "startTime", "Date", "futureTime", "getTime", "toLocaleString", "endTime", "awardDate", "courseID", "memberId", "code", "value", "downloadCertificate", "canvas", "document", "createElement", "shareContent", "getElementById", "width", "offsetWidth", "height", "offsetHeight", "scale", "getContext", "rect", "getBoundingClientRect", "scrollTop", "documentElement", "body", "x", "left", "y", "top", "scrollY", "useCORS", "dpi", "window", "devicePixelRatio", "then", "context", "mozImageSmoothingEnabled", "msImageSmoothingEnabled", "imageSmoothingEnabled", "imgUrl", "toDataURL", "doc", "addImage", "save"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/certificate/preview/index.vue"], "sourcesContent": ["<template>\n  <div class=\"certificate-coat\">\n    <el-button v-if=\"download\" size=\"small\" @click=\"downloadCertificate\">下载证书</el-button>\n    <div class=\"certificate-box\" id=\"certificate\" ref=\"certificate\" :style=\"certificate.design ? 'background-image:url(' + certificate.design + ')' : ''\">\n      <div class=\"number-box\">\n        <span class=\"number-title\">证书编号：</span>\n        <span class=\"code\">{{certificate.code || code}}</span>\n      </div>\n      <p class=\"training-certificate\">{{certificate.name}}</p>\n      <div class=\"user-name\">{{ certificate.member ? certificate.member.name : userName}}</div>同学\n      <div class=\"des\">于<span>{{ certificate.lessonSignTime ? formatDate(certificate.lessonSignTime, 'yyyy-MM-dd') : startTime}}</span>至<span>{{ certificate.lessonCompleteTime ? formatDate(certificate.lessonCompleteTime, 'yyyy-MM-dd') : endTime}}</span>完成学习课程：</div>\n      <div class=\"course-name\">{{certificate.lesson ? certificate.lesson.name : courseName}}</div>\n      <div class=\"complete-des\">{{certificate.description}}</div>\n      <div class=\"certificate-bottom\">\n        <p class=\"certificate-name\">{{certificate.awardingOrganization}}</p>\n        <p class=\"certificate-date\">{{certificate.awardDate ? formatDate(certificate.awardDate, 'yyyy-MM-dd') : awardDate}}</p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport html2canvas from \"html2canvas\"\nimport jsPDF from \"jspdf\"\nimport { nextTick, ref, computed } from \"vue\"\nimport {formatDate} from \"@/util/dateUtils\";\n// import { getLesson, learnSignUp } from \"@/api/learn/lesson\"\n// import { useRoute } from \"vue-router\"\nexport default {\n  name: \"CertificatePreview\",\n  props: {\n    certificate: {\n      type: Object\n    },\n    download: {\n      type: Boolean\n    }\n  },\n  setup() {\n    const courseName = ref(\"课程示例\") // 课程名称\n    const userName = ref(\"张三\") // 课程名称\n    const startTime = ref(formatDate(new Date(), \"yyyy-MM-dd\")) // 开始时间\n    const futureTime = new Date(new Date().getTime() + 90 * 24 * 60 * 60 * 1000).toLocaleString();\n    const endTime = ref(formatDate(futureTime, \"yyyy-MM-dd\")) // 结束时间\n    const awardDate = ref(formatDate(new Date(), \"yyyy年MM月dd日\")) // 颁发时间\n    const courseID = ref(0) // 课程Id\n    const memberId = ref(1) // 用户Id\n    // 设置编号  编号等于  日期 + 课程id  + 用户id这样\n    const code = computed(() => {\n      return \"UZ\" + formatDate(futureTime, \"yyyyMMdd\") + courseID.value + memberId.value\n    })\n    const downloadCertificate = () => {\n      nextTick(() => { // 使用$nextTick，解决数据还没有渲染到html就先转为图片，此时的图片会是空内容的问题\n        const canvas = document.createElement(\"canvas\") // 创建一个canvas节点\n        const shareContent = document.getElementById(\"certificate\") // 需要截图的包裹的（原生的）DOM 对象\n        const width = shareContent.offsetWidth // 获取dom 宽度\n        const height = shareContent.offsetHeight // 获取dom 高度\n        const scale = 2 // 定义任意放大倍数 支持小数\n        canvas.getContext(\"2d\").scale(scale, scale) // 获取context,设置scale\n        const rect = shareContent.getBoundingClientRect() // 获取元素相对于视口的\n        const scrollTop = document.documentElement.scrollTop || document.body.scrollTop // 获取滚动轴滚动的长度\n        html2canvas(document.getElementById(\"certificate\"), { // 转换为图片\n          x: rect.left + 8, // 绘制的dom元素相对于视口的位置\n          y: rect.top,\n          scrollY: -scrollTop,\n          scale: scale, // 添加的scale 参数\n          width: width, // dom 原始宽度\n          height: height,\n          useCORS: true, // 开启跨域\n          dpi: window.devicePixelRatio * 2\n        }).then(canvas => {\n          const context = canvas.getContext(\"2d\")\n          // 关闭抗锯齿\n          context.mozImageSmoothingEnabled = false\n          context.msImageSmoothingEnabled = false\n          context.imageSmoothingEnabled = false\n          const imgUrl = canvas.toDataURL(\"image/jpeg\");\n          var doc = new jsPDF(\"p\", \"mm\", \"a4\");\n          doc.addImage(imgUrl, \"JPEG\", 0, 0, 210, 297);\n          doc.save(\"my-certificate\" + \".pdf\");\n          // var a = document.createElement(\"a\")\n          // a.download =  \"my-certificate\"\n          // // 设置图片地址\n          // a.href = imgUrl;\n          // a.click();\n        })\n      })\n    }\n    return {\n      formatDate,\n      awardDate,\n      downloadCertificate,\n      courseName,\n      userName,\n      startTime,\n      endTime,\n      code\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.certificate-coat {\n  padding: 20px 0;\n  box-sizing: border-box;\n  .el-button {\n    //float: right;\n  }\n  .certificate-box {\n    width: 800px;\n    height: 1123px;\n    position: relative;\n    margin: 0 auto;\n    padding-top: 216px;\n    box-sizing: border-box;\n    background-image: url(\"../../../assets/lesson/certificate.jpg\");\n    background-repeat: no-repeat;\n    background-size: 800px 1130px;\n    .certificate-name {\n      font-size: 20px;\n      letter-spacing: 3px;\n      text-align: center;\n    }\n    .training-certificate {\n      margin-top: 80px;\n      font-size: 60px;\n      font-weight: bold;\n      color: #bf2d1e;\n      text-align: center;\n      letter-spacing: 10px;\n      text-shadow: 0px 3px 0px #ddd;\n    }\n    .number-box {\n      display: flex;\n      justify-content: end;\n      align-items: center;\n      font-size: 20px;\n      margin-right: 100px;\n      .left-icon, .right-icon {\n        width: 18px;\n        height: 18px;\n      }\n      .number-title {\n        color: #000;\n        margin-left: 15px;\n      }\n      .code {\n        color: #bf2d1e;\n        margin-right: 15px;\n      }\n    }\n    .bottom-icon {\n      display: block;\n      margin: 0 auto;\n      width: 410px;\n      height: 30px;\n      margin-top: 10px;\n    }\n    .user-name {\n      padding: 5px 10px;\n      width: auto;\n      display: inline-block;\n      border-bottom: 1px solid #616161;\n      margin-left: 110px;\n      font-size: 20px;\n      font-weight: 600;\n      letter-spacing: 5px;\n      color: #bf2d1e;\n      margin-top: 66px;\n    }\n    .des {\n      font-size: 20px;\n      color: #616161;\n      text-align: center;\n      margin-top: 48px;\n      span {\n        letter-spacing: 2px;\n      }\n    }\n    .course-name {\n      text-align: center;\n      letter-spacing: 5px;\n      font-size: 30px;\n      font-weight: 600;\n      color: #bf2d1e;\n      margin-top: 20px;\n    }\n    .complete-des {\n      margin-left: 110px;\n      font-size: 20px;\n      color: #616161;\n      margin-top: 26px;\n    }\n  }\n  .certificate-bottom {\n    padding: 5px 10px;\n    width: auto;\n    display: inline-block;\n    margin-right: 110px;\n    font-size: 20px;\n    letter-spacing: 5px;\n    margin-top: 66px;\n    float: right;\n    text-align: center;\n    .certificate-name {\n\n    }\n    .certificate-date {\n      margin-top: 10px;\n    }\n  }\n}\n</style>\n"], "mappings": "AAsBA,OAAOA,WAAU,MAAO,aAAY;AACpC,OAAOC,KAAI,MAAO,OAAM;AACxB,SAASC,QAAQ,EAAEC,GAAG,EAAEC,QAAO,QAAS,KAAI;AAC5C,SAAQC,UAAU,QAAO,kBAAkB;AAC3C;AACA;AACA,eAAe;EACbC,IAAI,EAAE,oBAAoB;EAC1BC,KAAK,EAAE;IACLC,WAAW,EAAE;MACXC,IAAI,EAAEC;IACR,CAAC;IACDC,QAAQ,EAAE;MACRF,IAAI,EAAEG;IACR;EACF,CAAC;EACDC,KAAKA,CAAA,EAAG;IACN,MAAMC,UAAS,GAAIX,GAAG,CAAC,MAAM,GAAE;IAC/B,MAAMY,QAAO,GAAIZ,GAAG,CAAC,IAAI,GAAE;IAC3B,MAAMa,SAAQ,GAAIb,GAAG,CAACE,UAAU,CAAC,IAAIY,IAAI,EAAE,EAAE,YAAY,CAAC,GAAE;IAC5D,MAAMC,UAAS,GAAI,IAAID,IAAI,CAAC,IAAIA,IAAI,EAAE,CAACE,OAAO,EAAC,GAAI,EAAC,GAAI,EAAC,GAAI,EAAC,GAAI,EAAC,GAAI,IAAI,CAAC,CAACC,cAAc,EAAE;IAC7F,MAAMC,OAAM,GAAIlB,GAAG,CAACE,UAAU,CAACa,UAAU,EAAE,YAAY,CAAC,GAAE;IAC1D,MAAMI,SAAQ,GAAInB,GAAG,CAACE,UAAU,CAAC,IAAIY,IAAI,EAAE,EAAE,aAAa,CAAC,GAAE;IAC7D,MAAMM,QAAO,GAAIpB,GAAG,CAAC,CAAC,GAAE;IACxB,MAAMqB,QAAO,GAAIrB,GAAG,CAAC,CAAC,GAAE;IACxB;IACA,MAAMsB,IAAG,GAAIrB,QAAQ,CAAC,MAAM;MAC1B,OAAO,IAAG,GAAIC,UAAU,CAACa,UAAU,EAAE,UAAU,IAAIK,QAAQ,CAACG,KAAI,GAAIF,QAAQ,CAACE,KAAI;IACnF,CAAC;IACD,MAAMC,mBAAkB,GAAIA,CAAA,KAAM;MAChCzB,QAAQ,CAAC,MAAM;QAAE;QACf,MAAM0B,MAAK,GAAIC,QAAQ,CAACC,aAAa,CAAC,QAAQ,GAAE;QAChD,MAAMC,YAAW,GAAIF,QAAQ,CAACG,cAAc,CAAC,aAAa,GAAE;QAC5D,MAAMC,KAAI,GAAIF,YAAY,CAACG,WAAU,EAAE;QACvC,MAAMC,MAAK,GAAIJ,YAAY,CAACK,YAAW,EAAE;QACzC,MAAMC,KAAI,GAAI,GAAE;QAChBT,MAAM,CAACU,UAAU,CAAC,IAAI,CAAC,CAACD,KAAK,CAACA,KAAK,EAAEA,KAAK,GAAE;QAC5C,MAAME,IAAG,GAAIR,YAAY,CAACS,qBAAqB,EAAC,EAAE;QAClD,MAAMC,SAAQ,GAAIZ,QAAQ,CAACa,eAAe,CAACD,SAAQ,IAAKZ,QAAQ,CAACc,IAAI,CAACF,SAAQ,EAAE;QAChFzC,WAAW,CAAC6B,QAAQ,CAACG,cAAc,CAAC,aAAa,CAAC,EAAE;UAAE;UACpDY,CAAC,EAAEL,IAAI,CAACM,IAAG,GAAI,CAAC;UAAE;UAClBC,CAAC,EAAEP,IAAI,CAACQ,GAAG;UACXC,OAAO,EAAE,CAACP,SAAS;UACnBJ,KAAK,EAAEA,KAAK;UAAE;UACdJ,KAAK,EAAEA,KAAK;UAAE;UACdE,MAAM,EAAEA,MAAM;UACdc,OAAO,EAAE,IAAI;UAAE;UACfC,GAAG,EAAEC,MAAM,CAACC,gBAAe,GAAI;QACjC,CAAC,CAAC,CAACC,IAAI,CAACzB,MAAK,IAAK;UAChB,MAAM0B,OAAM,GAAI1B,MAAM,CAACU,UAAU,CAAC,IAAI;UACtC;UACAgB,OAAO,CAACC,wBAAuB,GAAI,KAAI;UACvCD,OAAO,CAACE,uBAAsB,GAAI,KAAI;UACtCF,OAAO,CAACG,qBAAoB,GAAI,KAAI;UACpC,MAAMC,MAAK,GAAI9B,MAAM,CAAC+B,SAAS,CAAC,YAAY,CAAC;UAC7C,IAAIC,GAAE,GAAI,IAAI3D,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;UACpC2D,GAAG,CAACC,QAAQ,CAACH,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;UAC5CE,GAAG,CAACE,IAAI,CAAC,gBAAe,GAAI,MAAM,CAAC;UACnC;UACA;UACA;UACA;UACA;QACF,CAAC;MACH,CAAC;IACH;;IACA,OAAO;MACLzD,UAAU;MACViB,SAAS;MACTK,mBAAmB;MACnBb,UAAU;MACVC,QAAQ;MACRC,SAAS;MACTK,OAAO;MACPI;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}