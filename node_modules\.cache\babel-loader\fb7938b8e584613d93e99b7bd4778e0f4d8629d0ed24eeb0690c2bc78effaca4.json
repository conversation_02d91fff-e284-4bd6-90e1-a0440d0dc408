{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  style: {\n    \"margin\": \"20px\"\n  }\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, \" 概览 \");\n}", "map": {"version": 3, "names": ["style", "_createElementBlock", "_hoisted_1"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/account/index.vue"], "sourcesContent": ["<template>\n  <div style=\"margin: 20px;\">\n    概览\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"AccountIndex\",\n  setup() {\n    return {};\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n</style>\n"], "mappings": ";;EACOA,KAAqB,EAArB;IAAA;EAAA;AAAqB;;uBAA1BC,mBAAA,CAEM,OAFNC,UAEM,EAFqB,MAE3B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}