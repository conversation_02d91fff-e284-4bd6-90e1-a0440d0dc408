{"ast": null, "code": "export default {\n  name: \"CertificatePreview\",\n  props: {\n    certificate: {\n      type: Object\n    },\n    download: {\n      type: Boolean\n    }\n  },\n  methods: {\n    generateCertificate() {\n      const canvas = this.$refs.certificateCanvas;\n      const ctx = canvas.getContext('2d');\n\n      // 绘制背景图\n      const backgroundImage = new Image();\n      backgroundImage.src = this.certificateInfo.backgroundUrl;\n      backgroundImage.onload = () => {\n        ctx.drawImage(backgroundImage, 0, 0, canvas.width, canvas.height);\n\n        // 绘制头像\n        const avatar = new Image();\n        avatar.src = this.certificateInfo.avatarUrl;\n        avatar.onload = () => {\n          ctx.drawImage(avatar, 50, 50, 100, 100); // 头像位置和大小\n\n          // 绘制文字\n          ctx.font = '30px Arial';\n          ctx.fillStyle = 'black';\n          ctx.fillText('证书持有者: ' + this.certificateInfo.name, 200, 100);\n          ctx.font = '24px Arial';\n          ctx.fillText('授予日期: ' + this.certificateInfo.date, 200, 150);\n        };\n      };\n    },\n    downloadCertificate() {\n      const canvas = this.$refs.certificateCanvas;\n      const imageUrl = canvas.toDataURL('image/png'); // 转换为PNG图片\n      const link = document.createElement('a');\n      link.href = imageUrl;\n      link.download = 'certificate.png'; // 下载文件名\n      link.click();\n    }\n  },\n  setup() {\n    return {};\n  }\n};", "map": {"version": 3, "names": ["name", "props", "certificate", "type", "Object", "download", "Boolean", "methods", "generateCertificate", "canvas", "$refs", "certificateCanvas", "ctx", "getContext", "backgroundImage", "Image", "src", "certificateInfo", "backgroundUrl", "onload", "drawImage", "width", "height", "avatar", "avatarUrl", "font", "fillStyle", "fillText", "date", "downloadCertificate", "imageUrl", "toDataURL", "link", "document", "createElement", "href", "click", "setup"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/certificate/preview/index.vue"], "sourcesContent": ["<template>\n  <div class=\"certificate-coat\">\n    <el-button v-if=\"download\" type=\"primary\" size=\"large\" @click=\"downloadCertificate\">下载证书</el-button>\n    <div class=\"certificate-rotate-wrap\">\n      <canvas ref=\"certificateCanvas\" width=\"2894\" height=\"4093\"></canvas>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"CertificatePreview\",\n  props: {\n    certificate: {\n      type: Object\n    },\n    download: {\n      type: Boolean\n    }\n  },\n  methods: {\n    generateCertificate() {\n      const canvas = this.$refs.certificateCanvas;\n      const ctx = canvas.getContext('2d');\n\n      // 绘制背景图\n      const backgroundImage = new Image();\n      backgroundImage.src = this.certificateInfo.backgroundUrl;\n      backgroundImage.onload = () => {\n        ctx.drawImage(backgroundImage, 0, 0, canvas.width, canvas.height);\n\n        // 绘制头像\n        const avatar = new Image();\n        avatar.src = this.certificateInfo.avatarUrl;\n        avatar.onload = () => {\n          ctx.drawImage(avatar, 50, 50, 100, 100); // 头像位置和大小\n\n          // 绘制文字\n          ctx.font = '30px Arial';\n          ctx.fillStyle = 'black';\n          ctx.fillText('证书持有者: ' + this.certificateInfo.name, 200, 100);\n\n          ctx.font = '24px Arial';\n          ctx.fillText('授予日期: ' + this.certificateInfo.date, 200, 150);\n        };\n      };\n    },\n    downloadCertificate() {\n      const canvas = this.$refs.certificateCanvas;\n      const imageUrl = canvas.toDataURL('image/png'); // 转换为PNG图片\n      const link = document.createElement('a');\n      link.href = imageUrl;\n      link.download = 'certificate.png'; // 下载文件名\n      link.click();\n    }\n  },\n  setup() {\n    return {\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.certificate-coat {\n  box-sizing: border-box;\n  .el-button {\n    float: right;\n    font-size: 20px;\n    writing-mode: vertical-rl;\n    text-orientation: upright;\n    height: auto;\n    border-radius: 4px 0 0 4px;\n  }\n  .certificate-box {\n    width: 1123px;\n    height: 794px;\n    position: relative;\n    margin: 0 auto;\n    padding: 0;\n    box-sizing: border-box;\n    background-repeat: no-repeat;\n    background-size: 1123px 794px;\n    .certificate-wrap {\n      padding: 100px;\n      height: 594px;\n      width: 923px;\n    }\n  }\n\n  .certificate-box-nmargin{\n    margin: 0;\n  }\n\n  .certificate-header {\n    margin: 40px 0 20px;\n    .certificate-name {\n      font-size: 60px;\n      font-weight: bold;\n      text-align: center;\n      letter-spacing: 30px;\n      color: #000000;\n      //text-shadow: 0 3px 0 #ddd;\n    }\n  }\n\n  .certificate-main {\n    .certificate-member {\n      width: auto;\n      display: inline-block;\n      font-size: 20px;\n      font-weight: 600;\n      letter-spacing: 5px;\n      margin-top: 66px;\n      color: #000000;\n    }\n    .certificate-content {\n      font-size: 20px;\n      color: #000000;\n      text-indent: 44px;\n      margin: 40px 0;\n      line-height: 60px;\n      span {\n        letter-spacing: 2px;\n      }\n    }\n    .certificate-course-name {\n      text-align: center;\n      letter-spacing: 5px;\n      font-size: 20px;\n      font-weight: 600;\n      margin: 20px 10px 0;\n    }\n    .certificate-complete-desc {\n    }\n  }\n\n  .certificate-bottom {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding-top: 40px;\n    .certificate-code {\n      display: flex;\n      justify-content: end;\n      align-items: center;\n      font-size: 20px;\n      margin-right: 20px;\n      .certificate-code-title {\n        color: #000000;\n      }\n      .certificate-code-main {\n        color: #000000;\n      }\n    }\n    .certificate-org {\n      color: #000000;\n      font-size: 20px;\n      letter-spacing: 5px;\n      text-align: center;\n\n      .certificate-org-name {\n        font-size: 20px;\n        letter-spacing: 3px;\n        text-align: center;\n      }\n      .certificate-date {\n        margin-top: 10px;\n        font-size: 20px;\n      }\n    }\n  }\n}\n.certificate-rotate {\n  transform: rotate(90deg);\n  margin-top: 165px!important;\n  margin-left: -165px!important;\n}\n</style>\n"], "mappings": "AAUA,eAAe;EACbA,IAAI,EAAE,oBAAoB;EAC1BC,KAAK,EAAE;IACLC,WAAW,EAAE;MACXC,IAAI,EAAEC;IACR,CAAC;IACDC,QAAQ,EAAE;MACRF,IAAI,EAAEG;IACR;EACF,CAAC;EACDC,OAAO,EAAE;IACPC,mBAAmBA,CAAA,EAAG;MACpB,MAAMC,MAAK,GAAI,IAAI,CAACC,KAAK,CAACC,iBAAiB;MAC3C,MAAMC,GAAE,GAAIH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;;MAEnC;MACA,MAAMC,eAAc,GAAI,IAAIC,KAAK,EAAE;MACnCD,eAAe,CAACE,GAAE,GAAI,IAAI,CAACC,eAAe,CAACC,aAAa;MACxDJ,eAAe,CAACK,MAAK,GAAI,MAAM;QAC7BP,GAAG,CAACQ,SAAS,CAACN,eAAe,EAAE,CAAC,EAAE,CAAC,EAAEL,MAAM,CAACY,KAAK,EAAEZ,MAAM,CAACa,MAAM,CAAC;;QAEjE;QACA,MAAMC,MAAK,GAAI,IAAIR,KAAK,EAAE;QAC1BQ,MAAM,CAACP,GAAE,GAAI,IAAI,CAACC,eAAe,CAACO,SAAS;QAC3CD,MAAM,CAACJ,MAAK,GAAI,MAAM;UACpBP,GAAG,CAACQ,SAAS,CAACG,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE;;UAEzC;UACAX,GAAG,CAACa,IAAG,GAAI,YAAY;UACvBb,GAAG,CAACc,SAAQ,GAAI,OAAO;UACvBd,GAAG,CAACe,QAAQ,CAAC,SAAQ,GAAI,IAAI,CAACV,eAAe,CAACjB,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;UAE7DY,GAAG,CAACa,IAAG,GAAI,YAAY;UACvBb,GAAG,CAACe,QAAQ,CAAC,QAAO,GAAI,IAAI,CAACV,eAAe,CAACW,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;QAC9D,CAAC;MACH,CAAC;IACH,CAAC;IACDC,mBAAmBA,CAAA,EAAG;MACpB,MAAMpB,MAAK,GAAI,IAAI,CAACC,KAAK,CAACC,iBAAiB;MAC3C,MAAMmB,QAAO,GAAIrB,MAAM,CAACsB,SAAS,CAAC,WAAW,CAAC,EAAE;MAChD,MAAMC,IAAG,GAAIC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAG,GAAIL,QAAQ;MACpBE,IAAI,CAAC3B,QAAO,GAAI,iBAAiB,EAAE;MACnC2B,IAAI,CAACI,KAAK,EAAE;IACd;EACF,CAAC;EACDC,KAAKA,CAAA,EAAG;IACN,OAAO,CACP;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}