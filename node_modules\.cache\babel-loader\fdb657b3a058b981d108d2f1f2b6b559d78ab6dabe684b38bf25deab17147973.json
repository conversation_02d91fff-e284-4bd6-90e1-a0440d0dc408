{"ast": null, "code": "import { ref } from \"vue\";\nimport Page from \"@/components/Page\";\nimport { gotoCertificateTemplateEdit } from \"@/router/goto\";\nimport { findCertificateTemplateList, deleteCertificateTemplate, inactiveCertificateTemplate, activeCertificateTemplate } from \"@/api/certificate\";\nimport { confirm, success } from \"@/util/tipsUtils\";\nimport CertificatePreview from \"@/views/certificate/preview/index.vue\";\nexport default {\n  name: \"LearnReportSignUpIndex\",\n  components: {\n    CertificatePreview,\n    Page\n  },\n  setup() {\n    const dataLoading = ref(true);\n    const templateList = ref([]);\n    const params = ref({\n      current: 1,\n      size: 20,\n      neqStatusList: [\"deleted\"]\n    });\n    const loadList = () => {\n      findCertificateTemplateList(params.value, res => {\n        console.log(res);\n        if (res) {\n          total.value = res.total;\n          templateList.value = res.list;\n        }\n        dataLoading.value = false;\n      }).catch(() => {\n        dataLoading.value = false;\n      });\n    };\n    loadList();\n    const total = ref(0);\n    const currentChange = c => {\n      params.value.current = c;\n      loadList();\n    };\n    const sizeChange = s => {\n      params.value.size = s;\n      loadList();\n    };\n    const search = () => {\n      loadList();\n    };\n    const remove = id => {\n      confirm(\"确认删除该证书模版？\", \"提示\", () => {\n        deleteCertificateTemplate(id, () => {\n          success(\"删除成功\");\n          loadList();\n        });\n      });\n    };\n    const active = id => {\n      confirm(\"确认启用该证书模版？\", \"提示\", () => {\n        activeCertificateTemplate({\n          id: id\n        }, () => {\n          success(\"启用成功\");\n          loadList();\n        });\n      });\n    };\n    const inactive = id => {\n      confirm(\"确认禁用该证书模版？\", \"提示\", () => {\n        inactiveCertificateTemplate({\n          id: id\n        }, () => {\n          success(\"禁用成功\");\n          loadList();\n        });\n      });\n    };\n    const showPreviewViewFlag = ref(false);\n    const showPreview = () => {\n      showPreviewViewFlag.value = true;\n    };\n    const hidePreview = () => {\n      showPreviewViewFlag.value = false;\n    };\n    return {\n      showPreviewViewFlag,\n      showPreview,\n      hidePreview,\n      dataLoading,\n      remove,\n      gotoCertificateTemplateEdit,\n      search,\n      params,\n      total,\n      currentChange,\n      sizeChange,\n      templateList,\n      inactive,\n      active\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "Page", "gotoCertificateTemplateEdit", "findCertificateTemplateList", "deleteCertificateTemplate", "inactiveCertificateTemplate", "activeCertificateTemplate", "confirm", "success", "CertificatePreview", "name", "components", "setup", "dataLoading", "templateList", "params", "current", "size", "neqStatusList", "loadList", "value", "res", "console", "log", "total", "list", "catch", "currentChange", "c", "sizeChange", "s", "search", "remove", "id", "active", "inactive", "showPreviewViewFlag", "showPreview", "hidePreview"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/certificate/template/index.vue"], "sourcesContent": ["<template>\n  <div class=\"cert-template-wrap\">\n    <div class=\"cert-template-header\">\n      <el-form :inline=\"true\" :model=\"params\" class=\"form-inline\">\n        <el-form-item label=\"证书名称\">\n          <el-input size=\"small\" @keydown.enter=\"search\" class=\"search-input\" v-model=\"params.name\" placeholder=\"请输入关键字\">\n            <template #suffix>\n              <i @click=\"search\" class=\"el-input__icon el-icon-search search-btn\"></i>\n            </template>\n          </el-input>\n        </el-form-item>\n        <el-form-item label=\"状态\" class=\"select\">\n          <el-select size=\"small\" v-model=\"params.status\" @change=\"search\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"启用\" value=\"active\"></el-option>\n            <el-option label=\"禁用\" value=\"inactive\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" @click=\"search()\">\n<!--            <el-icon style=\"vertical-align: middle\">-->\n<!--              <Search />-->\n<!--            </el-icon>-->\n            <span style=\"vertical-align: middle\">搜索</span>\n          </el-button>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" type=\"primary\" @click=\"gotoCertificateTemplateEdit()\">\n            <el-icon style=\"vertical-align: middle\">\n              <Plus />\n            </el-icon>\n            <span style=\"vertical-align: middle\">新增</span>\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"cert-template-main\" :loading=\"dataLoading\">\n      <el-table :data=\"templateList\">\n        <el-table-column label=\"序号\" type=\"index\">\n        </el-table-column>\n        <el-table-column label=\"背景图\" prop=\"desgin\">\n          <template #default=\"scope\">\n            <img class=\"desgin\" :src=\"scope.row.design\" />\n          </template>\n        </el-table-column>\n        <el-table-column label=\"证书名称\" prop=\"name\"></el-table-column>\n        <el-table-column label=\"证书描述\" prop=\"description\"></el-table-column>\n        <el-table-column label=\"颁发机构\" prop=\"awardingOrganization\"></el-table-column>\n        <el-table-column label=\"颁发人员\" prop=\"awarderName\"></el-table-column>\n        <el-table-column label=\"颁发条件\" prop=\"awardConditions\"></el-table-column>\n        <el-table-column label=\"到期策略\" prop=\"validityPolicy\"></el-table-column>\n        <el-table-column label=\"状态\" prop=\"statusName\"></el-table-column>\n        <el-table-column label=\"操作\">\n          <template #default=\"scope\">\n            <div class=\"opt-btn-wrap\">\n              <div class=\"opt-btn-item\">\n                <el-button size=\"small\" @click=\"showPreview\">预览</el-button>\n              </div>\n              <div class=\"opt-btn-item\">\n                <el-button size=\"small\" @click=\"gotoCertificateTemplateEdit(scope.row.id)\">编辑</el-button>\n              </div>\n              <div class=\"opt-btn-item\" v-if=\"scope.row.status === 'inactive'\">\n                <el-button size=\"small\" type=\"primary\" @click=\"active(scope.row.id)\">启用</el-button>\n              </div>\n              <div class=\"opt-btn-item\" v-if=\"scope.row.status === 'active'\">\n                <el-button size=\"small\" type=\"warning\" @click=\"inactive(scope.row.id)\">禁用</el-button>\n              </div>\n  <!--            <div class=\"opt-btn-item\">-->\n  <!--              <el-button size=\"small\" type=\"primary\">关联</el-button>-->\n  <!--            </div>-->\n              <div class=\"opt-btn-item\">\n                <el-button size=\"small\" type=\"danger\" @click=\"remove(scope.row.id)\">删除</el-button>\n              </div>\n            </div>\n          </template>\n        </el-table-column>\n      </el-table>\n      <page :total=\"total\" :size-change=\"sizeChange\" :current-change=\"currentChange\" :page-size=\"params.size\"/>\n    </div>\n    <el-dialog style=\"min-width: 840px\" title=\"证书预览\" v-model=\"showPreviewViewFlag\" :before-close=\"hidePreview\">\n      <div>\n        <certificate-preview/>\n      </div>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button size=\"small\" @click=\"hidePreview\">取 消</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {ref} from \"vue\"\nimport Page from \"@/components/Page\";\nimport {gotoCertificateTemplateEdit} from \"@/router/goto\";\nimport {\n  findCertificateTemplateList,\n  deleteCertificateTemplate,\n  inactiveCertificateTemplate,\n  activeCertificateTemplate\n} from \"@/api/certificate\";\nimport {confirm, success} from \"@/util/tipsUtils\";\nimport CertificatePreview from \"@/views/certificate/preview/index.vue\";\nexport default {\n  name: \"LearnReportSignUpIndex\",\n  components: {CertificatePreview, Page},\n  setup() {\n    const dataLoading = ref(true)\n    const templateList = ref([])\n    const params = ref({\n      current: 1,\n      size: 20,\n      neqStatusList: [\"deleted\"]\n    })\n    const loadList = () => {\n      findCertificateTemplateList(params.value, res => {\n        console.log(res)\n        if (res) {\n          total.value = res.total;\n          templateList.value = res.list;\n        }\n        dataLoading.value = false\n      }).catch(() => {\n        dataLoading.value = false\n      })\n    }\n    loadList()\n    const total = ref(0)\n    const currentChange = (c) => {\n      params.value.current = c;\n      loadList();\n    }\n    const sizeChange = (s) => {\n      params.value.size = s;\n      loadList();\n    }\n    const search = () => {\n      loadList();\n    }\n    const remove = (id) => {\n      confirm(\"确认删除该证书模版？\", \"提示\", () => {\n        deleteCertificateTemplate(id, () => {\n          success(\"删除成功\");\n          loadList();\n        })\n      })\n    }\n    const active = (id) => {\n      confirm(\"确认启用该证书模版？\", \"提示\", () => {\n        activeCertificateTemplate({id: id}, () => {\n          success(\"启用成功\");\n          loadList();\n        })\n      })\n    }\n    const inactive = (id) => {\n      confirm(\"确认禁用该证书模版？\", \"提示\", () => {\n        inactiveCertificateTemplate({id: id}, () => {\n          success(\"禁用成功\");\n          loadList();\n        })\n      })\n    }\n\n    const showPreviewViewFlag = ref(false);\n    const showPreview = () => {\n      showPreviewViewFlag.value = true;\n    }\n    const hidePreview = () => {\n      showPreviewViewFlag.value = false;\n    }\n    return {\n      showPreviewViewFlag,\n      showPreview,\n      hidePreview,\n      dataLoading,\n      remove,\n      gotoCertificateTemplateEdit,\n      search,\n      params,\n      total,\n      currentChange,\n      sizeChange,\n      templateList,\n      inactive,\n      active\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n  .cert-template-wrap {\n    margin: 20px;\n    font-size: 12px;\n    .cert-template-main {\n      ::v-deep .el-table {\n        font-size: 12px;\n        .el-table__empty-block {\n          line-height: 400px;\n          .el-table__empty-text {\n            line-height: 400px;\n          }\n        }\n        th, td {\n          padding: 6px 0;\n        }\n      }\n    }\n    .opt-btn-wrap {\n      //display: flex;\n    }\n    .opt-btn-item {\n      width: 50%;\n      display: inline-block;\n      margin: 2px;\n    }\n  }\n  .desgin {\n    width: 116px;\n    height: 76px;\n  }\n</style>\n"], "mappings": "AA6FA,SAAQA,GAAG,QAAO,KAAI;AACtB,OAAOC,IAAG,MAAO,mBAAmB;AACpC,SAAQC,2BAA2B,QAAO,eAAe;AACzD,SACEC,2BAA2B,EAC3BC,yBAAyB,EACzBC,2BAA2B,EAC3BC,yBAAwB,QACnB,mBAAmB;AAC1B,SAAQC,OAAO,EAAEC,OAAO,QAAO,kBAAkB;AACjD,OAAOC,kBAAiB,MAAO,uCAAuC;AACtE,eAAe;EACbC,IAAI,EAAE,wBAAwB;EAC9BC,UAAU,EAAE;IAACF,kBAAkB;IAAER;EAAI,CAAC;EACtCW,KAAKA,CAAA,EAAG;IACN,MAAMC,WAAU,GAAIb,GAAG,CAAC,IAAI;IAC5B,MAAMc,YAAW,GAAId,GAAG,CAAC,EAAE;IAC3B,MAAMe,MAAK,GAAIf,GAAG,CAAC;MACjBgB,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,aAAa,EAAE,CAAC,SAAS;IAC3B,CAAC;IACD,MAAMC,QAAO,GAAIA,CAAA,KAAM;MACrBhB,2BAA2B,CAACY,MAAM,CAACK,KAAK,EAAEC,GAAE,IAAK;QAC/CC,OAAO,CAACC,GAAG,CAACF,GAAG;QACf,IAAIA,GAAG,EAAE;UACPG,KAAK,CAACJ,KAAI,GAAIC,GAAG,CAACG,KAAK;UACvBV,YAAY,CAACM,KAAI,GAAIC,GAAG,CAACI,IAAI;QAC/B;QACAZ,WAAW,CAACO,KAAI,GAAI,KAAI;MAC1B,CAAC,CAAC,CAACM,KAAK,CAAC,MAAM;QACbb,WAAW,CAACO,KAAI,GAAI,KAAI;MAC1B,CAAC;IACH;IACAD,QAAQ,EAAC;IACT,MAAMK,KAAI,GAAIxB,GAAG,CAAC,CAAC;IACnB,MAAM2B,aAAY,GAAKC,CAAC,IAAK;MAC3Bb,MAAM,CAACK,KAAK,CAACJ,OAAM,GAAIY,CAAC;MACxBT,QAAQ,EAAE;IACZ;IACA,MAAMU,UAAS,GAAKC,CAAC,IAAK;MACxBf,MAAM,CAACK,KAAK,CAACH,IAAG,GAAIa,CAAC;MACrBX,QAAQ,EAAE;IACZ;IACA,MAAMY,MAAK,GAAIA,CAAA,KAAM;MACnBZ,QAAQ,EAAE;IACZ;IACA,MAAMa,MAAK,GAAKC,EAAE,IAAK;MACrB1B,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM;QAChCH,yBAAyB,CAAC6B,EAAE,EAAE,MAAM;UAClCzB,OAAO,CAAC,MAAM,CAAC;UACfW,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC;IACH;IACA,MAAMe,MAAK,GAAKD,EAAE,IAAK;MACrB1B,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM;QAChCD,yBAAyB,CAAC;UAAC2B,EAAE,EAAEA;QAAE,CAAC,EAAE,MAAM;UACxCzB,OAAO,CAAC,MAAM,CAAC;UACfW,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC;IACH;IACA,MAAMgB,QAAO,GAAKF,EAAE,IAAK;MACvB1B,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM;QAChCF,2BAA2B,CAAC;UAAC4B,EAAE,EAAEA;QAAE,CAAC,EAAE,MAAM;UAC1CzB,OAAO,CAAC,MAAM,CAAC;UACfW,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC;IACH;IAEA,MAAMiB,mBAAkB,GAAIpC,GAAG,CAAC,KAAK,CAAC;IACtC,MAAMqC,WAAU,GAAIA,CAAA,KAAM;MACxBD,mBAAmB,CAAChB,KAAI,GAAI,IAAI;IAClC;IACA,MAAMkB,WAAU,GAAIA,CAAA,KAAM;MACxBF,mBAAmB,CAAChB,KAAI,GAAI,KAAK;IACnC;IACA,OAAO;MACLgB,mBAAmB;MACnBC,WAAW;MACXC,WAAW;MACXzB,WAAW;MACXmB,MAAM;MACN9B,2BAA2B;MAC3B6B,MAAM;MACNhB,MAAM;MACNS,KAAK;MACLG,aAAa;MACbE,UAAU;MACVf,YAAY;MACZqB,QAAQ;MACRD;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}