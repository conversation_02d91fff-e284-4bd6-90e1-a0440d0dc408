{"ast": null, "code": "// Any plugins you want to use has to be imported\n// Detail plugins list see https://www.tinymce.com/docs/plugins/\n// Custom builds see https://www.tinymce.com/download/custom-builds/\nconst plugins = [\"advlist anchor autolink autosave code codesample colorpicker colorpicker contextmenu directionality emoticons fullscreen hr image imagetools insertdatetime link lists media nonbreaking noneditable pagebreak paste preview print save searchreplace spellchecker tabfocus table template textcolor textpattern visualblocks visualchars wordcount\"];\nexport default plugins;", "map": {"version": 3, "names": ["plugins"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/components/Tinymce/plugins.js"], "sourcesContent": ["// Any plugins you want to use has to be imported\n// Detail plugins list see https://www.tinymce.com/docs/plugins/\n// Custom builds see https://www.tinymce.com/download/custom-builds/\nconst plugins = [\"advlist anchor autolink autosave code codesample colorpicker colorpicker contextmenu directionality emoticons fullscreen hr image imagetools insertdatetime link lists media nonbreaking noneditable pagebreak paste preview print save searchreplace spellchecker tabfocus table template textcolor textpattern visualblocks visualchars wordcount\"]\nexport default plugins\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,OAAO,GAAG,CAAC,qVAAqV,CAAC;AACvW,eAAeA,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}