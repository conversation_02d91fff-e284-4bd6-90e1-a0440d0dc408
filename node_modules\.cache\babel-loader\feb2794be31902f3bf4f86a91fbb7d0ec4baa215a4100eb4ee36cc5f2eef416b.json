{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, resolveDynamicComponent as _resolveDynamicComponent, openBlock as _openBlock, createBlock as _createBlock, Transition as _Transition } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_layout_header = _resolveComponent(\"layout-header\");\n  const _component_el_header = _resolveComponent(\"el-header\");\n  const _component_router_view = _resolveComponent(\"router-view\");\n  const _component_el_main = _resolveComponent(\"el-main\");\n  const _component_el_container = _resolveComponent(\"el-container\");\n  return _openBlock(), _createBlock(_component_el_container, null, {\n    default: _withCtx(() => [_createVNode(_component_el_header, {\n      class: \"layout-header\",\n      height: \"50\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_layout_header)]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_main, {\n      class: \"layout-main\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_router_view, null, {\n        default: _withCtx(({\n          Component\n        }) => [_createVNode(_Transition, null, {\n          default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(Component)))]),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */)]),\n\n        _: 1 /* STABLE */\n      })]),\n\n      _: 1 /* STABLE */\n    })]),\n\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["_createBlock", "_component_el_container", "_createVNode", "_component_el_header", "class", "height", "_component_layout_header", "_component_el_main", "_component_router_view", "Component", "_Transition", "_resolveDynamicComponent"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/components/LayoutNotAside.vue"], "sourcesContent": ["<template>\n  <el-container>\n    <el-header class=\"layout-header\" height=\"50\">\n      <layout-header/>\n    </el-header>\n    <el-main class=\"layout-main\">\n      <router-view v-slot=\"{ Component }\">\n        <transition>\n          <component :is=\"Component\"/>\n        </transition>\n      </router-view>\n    </el-main>\n  </el-container>\n</template>\n\n<script>\nimport LayoutHeader from \"./LayoutHeader.vue\";\nimport CustomHeader from \"./Header.vue\";\nimport CustomFooter from \"./Footer.vue\";\nimport CustomAside from \"./Aside.vue\";\nimport store from \"../store\";\n\nexport default {\n  name: \"LayoutFirst\",\n  components: {\n    LayoutHeader,\n    CustomHeader,\n    CustomFooter,\n    CustomAside\n  },\n  computed: {\n    opened() {\n      return !store.getters.getAsideStatus\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.el-header, .el-footer, .el-main {\n  padding: 0!important;\n}\n.aside {\n  position: fixed;\n  height: 100%;\n  background: #f0f0f0;\n}\n/*隐藏滚动条*/\n.aside::-webkit-scrollbar{\n  display:none;\n}\n.main {\n  min-height: 100%;\n  position: relative;\n  margin-left: 210px;\n  transition: width 0.28s;\n  width: calc(100% - 210px);\n}\n.fixed-header {\n  top: 0;\n  right: 0;\n  z-index: 9;\n  transition: width 0.28s;\n}\n.main.fixed-header {\n  transition: width 0.28s;\n  margin-left: 64px;\n  width: calc(100% - 64px);\n}\n.layout-header {\n  font-size: 12px;\n  position: fixed;\n  z-index: 99;\n  width: 100%;\n}\n.layout-main {\n  margin-top: 50px;\n}\n</style>\n"], "mappings": ";;;;;;;uBACEA,YAAA,CAWeC,uBAAA;sBAVb,MAEY,CAFZC,YAAA,CAEYC,oBAAA;MAFDC,KAAK,EAAC,eAAe;MAACC,MAAM,EAAC;;wBACtC,MAAgB,CAAhBH,YAAA,CAAgBI,wBAAA,E;;QAElBJ,YAAA,CAMUK,kBAAA;MANDH,KAAK,EAAC;IAAa;wBAC1B,MAIc,CAJdF,YAAA,CAIcM,sBAAA;0BAHZ,CAEa;UAHQC;QAAS,OAC9BP,YAAA,CAEaQ,WAAA;4BADX,MAA4B,E,cAA5BV,YAAA,CAA4BW,wBAAA,CAAZF,SAAS,I"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}