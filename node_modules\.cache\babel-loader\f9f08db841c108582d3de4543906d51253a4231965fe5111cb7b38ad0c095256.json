{"ast": null, "code": "export default {\n  name: \"HamburgerIndex\",\n  props: {\n    isActive: {\n      type: Boolean,\n      default: false\n    }\n  },\n  setup(props, context) {\n    const toggleClick = () => {\n      context.emit(\"toggleClick\");\n    };\n    return {\n      toggleClick\n    };\n  }\n};", "map": {"version": 3, "names": ["name", "props", "isActive", "type", "Boolean", "default", "setup", "context", "toggleClick", "emit"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/components/Hamburger/index.vue"], "sourcesContent": ["<template>\n  <div style=\"padding: 0 10px;\" @click=\"toggleClick\">\n    <svg\n      :class=\"{'is-active': isActive}\"\n      class=\"hamburger\"\n      viewBox=\"0 0 1024 1024\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      width=\"64\"\n      height=\"64\">\n      <path d=\"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 0 0 0-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0 0 14.4 7z\"/>\n    </svg>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"HamburgerIndex\",\n  props: {\n    isActive: {\n      type: Boolean,\n      default: false\n    }\n  },\n  setup(props, context) {\n    const toggleClick = () => {\n      context.emit(\"toggleClick\")\n    }\n    return {\n      toggleClick\n    }\n  }\n}\n</script>\n\n<style scoped>\n.hamburger {\n  display: inline-block;\n  vertical-align: middle;\n  width: 14px;\n  height: 14px;\n}\n.hamburger.is-active {\n  transform: rotate(180deg);\n}\n</style>\n"], "mappings": "AAeA,eAAe;EACbA,IAAI,EAAE,gBAAgB;EACtBC,KAAK,EAAE;IACLC,QAAQ,EAAE;MACRC,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,KAAKA,CAACL,KAAK,EAAEM,OAAO,EAAE;IACpB,MAAMC,WAAU,GAAIA,CAAA,KAAM;MACxBD,OAAO,CAACE,IAAI,CAAC,aAAa;IAC5B;IACA,OAAO;MACLD;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}