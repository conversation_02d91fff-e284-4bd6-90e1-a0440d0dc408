{"ast": null, "code": "import { get, put, post } from \"@/util/requestUtils\";\nexport function findList(params, success) {\n  return get(\"/member/group/list\", params, success);\n}\nexport function getGroup(id, success) {\n  return get(\"/member/public-api/group\", {\n    id: id\n  }, success);\n}\nexport function updateGroup(data, success) {\n  return put(\"/member/group\", data, success);\n}\nexport function saveGroup(data, success) {\n  return post(\"/member/group\", data, success);\n}", "map": {"version": 3, "names": ["get", "put", "post", "findList", "params", "success", "getGroup", "id", "updateGroup", "data", "saveGroup"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/api/member/group.js"], "sourcesContent": ["import { get, put, post } from \"@/util/requestUtils\"\n\nexport function findList(params, success) {\n  return get(\"/member/group/list\", params, success)\n}\n\nexport function getGroup(id, success) {\n  return get(\"/member/public-api/group\", {id: id}, success)\n}\n\nexport function updateGroup(data, success) {\n  return put(\"/member/group\", data, success)\n}\n\nexport function saveGroup(data, success) {\n  return post(\"/member/group\", data, success)\n}\n\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,GAAG,EAAEC,IAAI,QAAQ,qBAAqB;AAEpD,OAAO,SAASC,QAAQA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACxC,OAAOL,GAAG,CAAC,oBAAoB,EAAEI,MAAM,EAAEC,OAAO,CAAC;AACnD;AAEA,OAAO,SAASC,QAAQA,CAACC,EAAE,EAAEF,OAAO,EAAE;EACpC,OAAOL,GAAG,CAAC,0BAA0B,EAAE;IAACO,EAAE,EAAEA;EAAE,CAAC,EAAEF,OAAO,CAAC;AAC3D;AAEA,OAAO,SAASG,WAAWA,CAACC,IAAI,EAAEJ,OAAO,EAAE;EACzC,OAAOJ,GAAG,CAAC,eAAe,EAAEQ,IAAI,EAAEJ,OAAO,CAAC;AAC5C;AAEA,OAAO,SAASK,SAASA,CAACD,IAAI,EAAEJ,OAAO,EAAE;EACvC,OAAOH,IAAI,CAAC,eAAe,EAAEO,IAAI,EAAEJ,OAAO,CAAC;AAC7C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}