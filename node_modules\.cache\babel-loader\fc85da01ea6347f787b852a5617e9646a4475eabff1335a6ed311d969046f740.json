{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref } from \"vue\";\nimport { findCategoryList, toTree, getAllParent } from \"@/api/exam/paper/category\";\nimport { saveBaseInfo, updateBaseInfo, getBaseInfo } from \"@/api/exam/paper\";\nimport { useRoute } from \"vue-router\";\nimport { error, success } from \"@/util/tipsUtils\";\nimport router from \"@/router\";\nimport QuestionLib from \"@/views/exam/question-lib\";\nimport * as questionApi from \"@/api/exam/question-lib/question\";\nexport default {\n  name: \"ExamPaperNormalIndex\",\n  components: {\n    QuestionLib\n  },\n  setup() {\n    const route = useRoute();\n    const colors = [\"#99A9BF\", \"#F7BA2A\", \"#FF9900\"];\n    const paper = ref({\n      id: \"\",\n      cidList: [],\n      title: \"\",\n      description: \"\",\n      type: \"normal\",\n      score: 0,\n      limitTime: \"\",\n      passScore: 0,\n      questionDisordered: false,\n      optionDisordered: false,\n      difficulty: 2,\n      questionIdList: []\n    });\n    const paperRules = {\n      title: [{\n        required: true,\n        message: \"请输入题干\",\n        trigger: \"blur\"\n      }],\n      score: [{\n        required: true,\n        message: \"请输入分数\",\n        trigger: \"blur\"\n      }],\n      cidList: [{\n        required: true,\n        message: \"请选择分类\",\n        trigger: \"change\"\n      }],\n      passScore: [{\n        required: true,\n        message: \"请选择合格分数\",\n        trigger: \"change\"\n      }],\n      limitTime: [{\n        required: true,\n        message: \"请输入试卷时间\",\n        trigger: \"blur\"\n      }],\n      questionIdList: [{\n        required: true,\n        message: \"请添加题目\",\n        trigger: \"blur\"\n      }]\n    };\n    const categoryOptions = ref([]);\n    const selectCidList = ref([]);\n    const questionList = ref([]);\n    // 获取分类\n    findCategoryList(0, true, res => {\n      if (res && res.length) {\n        categoryOptions.value = toTree(res);\n        categoryOptions.value.splice(0, 1);\n        if (route.query.id) {\n          // 获取试卷信息\n          getBaseInfo(route.query.id, res => {\n            res.limitTime = res.limitTime / 60;\n            paper.value = res;\n            selectCidList.value = getAllParent(categoryOptions.value, res.cidList);\n            paper.value.cidList = [];\n            for (const valElement of selectCidList.value) {\n              paper.value.cidList.push(valElement[valElement.length - 1]);\n            }\n            paper.value.questionIdList = [];\n            for (const valElement of res.questionList) {\n              paper.value.questionIdList.push(valElement.id);\n              questionList.value.push(valElement);\n            }\n          });\n        }\n      }\n    });\n    // 选择分类\n    const changeCategory = val => {\n      paper.value.cidList = [];\n      for (const valElement of val) {\n        paper.value.cidList.push(valElement[valElement.length - 1]);\n      }\n    };\n    const paperRef = ref();\n    const submitBaseInfo = () => {\n      paperRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        paper.value.limitTime = parseFloat(paper.value.limitTime) * 60;\n        if (paper.value.id) {\n          updateBaseInfo(paper.value, function () {\n            success(\"编辑成功\");\n            router.push({\n              path: \"/exam/paper\"\n            });\n          });\n        } else {\n          saveBaseInfo(paper.value, function () {\n            success(\"新增成功\");\n            router.push({\n              path: \"/exam/paper\"\n            });\n          });\n        }\n      });\n    };\n    const showAddQuestionDialog = ref(false);\n    const showAddQuestion = () => {\n      showAddQuestionDialog.value = true;\n    };\n    const hideAddQuestion = () => {\n      showAddQuestionDialog.value = false;\n    };\n    const selectionChangeCallback = questionIdList => {\n      // 获取题目详情\n      if (!questionIdList || questionIdList.length === 0) {\n        error(\"请选择题目\");\n        return;\n      }\n      for (const questionId of questionIdList) {\n        if (paper.value.questionIdList.indexOf(questionId) > -1) {\n          continue;\n        }\n        paper.value.questionIdList.push(questionId);\n        questionApi.getBaseInfo(questionId, res => {\n          questionList.value.push(res);\n          paper.value.score += res.score;\n        });\n      }\n      success(\"已添加至试题题目列表\");\n      hideAddQuestion();\n    };\n    return {\n      colors,\n      paper,\n      paperRules,\n      categoryOptions,\n      selectCidList,\n      paperRef,\n      changeCategory,\n      submitBaseInfo,\n      questionList,\n      showAddQuestionDialog,\n      showAddQuestion,\n      hideAddQuestion,\n      selectionChangeCallback\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "findCategoryList", "toTree", "getAllParent", "saveBaseInfo", "updateBaseInfo", "getBaseInfo", "useRoute", "error", "success", "router", "QuestionLib", "questionA<PERSON>", "name", "components", "setup", "route", "colors", "paper", "id", "cidList", "title", "description", "type", "score", "limitTime", "passScore", "questionDisordered", "optionDisordered", "difficulty", "questionIdList", "paperRules", "required", "message", "trigger", "categoryOptions", "selectCidList", "questionList", "res", "length", "value", "splice", "query", "valElement", "push", "changeCategory", "val", "paperRef", "submitBaseInfo", "validate", "valid", "parseFloat", "path", "showAddQuestionDialog", "showAddQuestion", "hideAddQuestion", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "questionId", "indexOf"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/exam/paper/normal/index.vue"], "sourcesContent": ["<template>\n  <div class=\"paper-box\">\n    <el-form :model=\"paper\" :rules=\"paperRules\" ref=\"paperRef\" label-width=\"120px\">\n      <el-form-item label=\"分类：\" prop=\"cidList\">\n        <el-cascader size=\"small\" style=\"width: 100%;\"\n                     v-model=\"selectCidList\"\n                     :props=\"{ multiple: true, checkStrictly: true }\"\n                     :options=\"categoryOptions\"\n                     @change=\"changeCategory\">\n        </el-cascader>\n      </el-form-item>\n      <el-form-item label=\"试卷名称：\" prop=\"title\">\n        <el-input size=\"small\" v-model=\"paper.title\" placeholder=\"请输入试卷名称\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"试卷描述：\" prop=\"description\">\n        <el-input size=\"small\" type=\"textarea\" :rows=\"5\" v-model=\"paper.description\" placeholder=\"请输入试卷描述\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"选择题目：\" prop=\"questionIdList\">\n        <el-card size=\"small\" shadow=\"never\">\n          <template #header>\n            <div class=\"clearfix\">\n              <el-button size=\"small\" style=\"padding: 10px;\" type=\"text\" @click=\"showAddQuestion\">添加题目</el-button>\n            </div>\n          </template>\n          <div v-if=\"!(questionList && questionList.length > 0)\">请添加题目</div>\n          <div v-else>\n            <el-table :data=\"questionList\" :show-header=\"false\" :highlight-current-row=\"false\" style=\"width: 100%\">\n              <el-table-column>\n                <template #default=\"scope\">\n                  <div>{{(scope.$index + 1) + '.' + scope.row.title}}</div>\n                  <!--                  <div>-->\n                  <!--                    <ul>-->\n                  <!--                      <li v-for=\"option in JSON.parse(scope.row.options)\" :key=\"option.key\">{{option.key}}.{{option.value}}</li>-->\n                  <!--                    </ul>-->\n                  <!--                  </div>-->\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n        </el-card>\n        <el-dialog title=\"添加题目\" v-model=\"showAddQuestionDialog\" :before-close=\"hideAddQuestion\" width=\"90%\">\n          <question-lib :is-component=\"true\" :hide-component=\"hideAddQuestion\" :selection-change-callback=\"selectionChangeCallback\"/>\n        </el-dialog>\n      </el-form-item>\n      <el-form-item label=\"试卷总分：\">\n        {{paper.score}} 分\n      </el-form-item>\n      <el-form-item label=\"合格分数：\"  prop=\"passScore\">\n        <el-input size=\"small\" v-model=\"paper.passScore\" placeholder=\"请输入试题分数\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"试卷时间：\" prop=\"limitTime\">\n        <el-input size=\"small\" v-model=\"paper.limitTime\" placeholder=\"请输入试卷时间（分）\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"题序打乱：\" prop=\"questionDisordered\">\n        <el-switch id=\"questionDisordered\" v-model=\"paper.questionDisordered\" active-color=\"#415fff\" active-text=\"是\" inactive-text=\"否\"></el-switch>\n      </el-form-item>\n      <el-form-item label=\"选项打乱：\" prop=\"optionDisordered\">\n        <el-switch id=\"optionDisordered\" v-model=\"paper.optionDisordered\" active-color=\"#415fff\" active-text=\"是\" inactive-text=\"否\"></el-switch>\n      </el-form-item>\n      <el-form-item label=\"试卷难度：\" prop=\"difficulty\">\n        <el-rate style=\"line-height: 48px;\" v-model=\"paper.difficulty\" :colors=\"colors\"></el-rate>\n      </el-form-item>\n    </el-form>\n    <el-button size=\"small\" style=\"display:block;margin:50px auto;\" @click=\"submitBaseInfo\">提交</el-button>\n  </div>\n</template>\n<script>\n  import {ref} from \"vue\"\n  import {findCategoryList, toTree, getAllParent} from \"@/api/exam/paper/category\"\n  import {saveBaseInfo, updateBaseInfo, getBaseInfo} from \"@/api/exam/paper\"\n  import {useRoute} from \"vue-router\";\n  import {error, success} from \"@/util/tipsUtils\";\n  import router from \"@/router\";\n  import QuestionLib from \"@/views/exam/question-lib\";\n  import * as questionApi from \"@/api/exam/question-lib/question\";\n\n  export default {\n    name: \"ExamPaperNormalIndex\",\n    components: {\n      QuestionLib\n    },\n    setup() {\n      const route = useRoute()\n      const colors = [\"#99A9BF\", \"#F7BA2A\", \"#FF9900\"]\n      const paper = ref({\n        id: \"\",\n        cidList: [],\n        title: \"\",\n        description: \"\",\n        type: \"normal\",\n        score: 0,\n        limitTime: \"\",\n        passScore: 0,\n        questionDisordered: false,\n        optionDisordered: false,\n        difficulty: 2,\n        questionIdList: []\n      })\n      const paperRules = {\n        title: [{ required: true, message: \"请输入题干\", trigger: \"blur\" }],\n        score: [{ required: true, message: \"请输入分数\", trigger: \"blur\" }],\n        cidList: [{ required: true, message: \"请选择分类\", trigger: \"change\" }],\n        passScore: [{ required: true, message: \"请选择合格分数\", trigger: \"change\" }],\n        limitTime: [{ required: true, message: \"请输入试卷时间\", trigger: \"blur\" }],\n        questionIdList: [{ required: true, message: \"请添加题目\", trigger: \"blur\" }],\n      }\n      const categoryOptions = ref([])\n      const selectCidList = ref([])\n      const questionList = ref([])\n      // 获取分类\n      findCategoryList(0, true, (res) => {\n        if (res && res.length) {\n          categoryOptions.value = toTree(res);\n          categoryOptions.value.splice(0, 1);\n          if (route.query.id) {\n            // 获取试卷信息\n            getBaseInfo(route.query.id, (res) => {\n              res.limitTime = res.limitTime / 60;\n              paper.value = res;\n              selectCidList.value = getAllParent(categoryOptions.value, res.cidList);\n              paper.value.cidList = []\n              for (const valElement of selectCidList.value) {\n                paper.value.cidList.push(valElement[valElement.length - 1])\n              }\n              paper.value.questionIdList = []\n              for (const valElement of res.questionList) {\n                paper.value.questionIdList.push(valElement.id)\n                questionList.value.push(valElement)\n              }\n            })\n          }\n        }\n      })\n      // 选择分类\n      const changeCategory = (val) => {\n        paper.value.cidList = []\n        for (const valElement of val) {\n          paper.value.cidList.push(valElement[valElement.length - 1])\n        }\n      }\n      const paperRef = ref();\n      const submitBaseInfo = () => {\n        paperRef.value.validate((valid) => {\n          if (!valid) { return false }\n          paper.value.limitTime = parseFloat(paper.value.limitTime) * 60;\n          if (paper.value.id) {\n            updateBaseInfo(paper.value, function () {\n              success(\"编辑成功\")\n              router.push({path: \"/exam/paper\"})\n            })\n          } else {\n            saveBaseInfo(paper.value, function () {\n              success(\"新增成功\")\n              router.push({path: \"/exam/paper\"})\n            })\n          }\n        })\n      }\n      const showAddQuestionDialog = ref(false)\n      const showAddQuestion = () => {\n        showAddQuestionDialog.value = true;\n      }\n      const hideAddQuestion = () => {\n        showAddQuestionDialog.value = false;\n      }\n      const selectionChangeCallback = (questionIdList) => {\n        // 获取题目详情\n        if (!questionIdList || questionIdList.length === 0) {\n          error(\"请选择题目\")\n          return;\n        }\n        for (const questionId of questionIdList) {\n          if (paper.value.questionIdList.indexOf(questionId) > -1) {\n            continue;\n          }\n          paper.value.questionIdList.push(questionId)\n          questionApi.getBaseInfo(questionId, (res) => {\n            questionList.value.push(res);\n            paper.value.score += res.score;\n          })\n        }\n        success(\"已添加至试题题目列表\")\n        hideAddQuestion()\n      }\n      return {\n        colors,\n        paper,\n        paperRules,\n        categoryOptions,\n        selectCidList,\n        paperRef,\n        changeCategory,\n        submitBaseInfo,\n        questionList,\n        showAddQuestionDialog,\n        showAddQuestion,\n        hideAddQuestion,\n        selectionChangeCallback\n      }\n    }\n  }\n</script>\n<style scoped lang=\"scss\">\n.paper-box {\n  margin: 20px;\n  .option-delete {\n    margin-left: 20px;\n    cursor: pointer;\n  }\n  .option-delete:hover {\n    color: $--color-primary;\n  }\n  ::v-deep .el-card__header{\n    padding: 0!important;\n  }\n  ::v-deep .el-card .el-table__row:last-child td {\n    border: 0;\n  }\n}\n</style>\n"], "mappings": ";AAmEE,SAAQA,GAAG,QAAO,KAAI;AACtB,SAAQC,gBAAgB,EAAEC,MAAM,EAAEC,YAAY,QAAO,2BAA0B;AAC/E,SAAQC,YAAY,EAAEC,cAAc,EAAEC,WAAW,QAAO,kBAAiB;AACzE,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,KAAK,EAAEC,OAAO,QAAO,kBAAkB;AAC/C,OAAOC,MAAK,MAAO,UAAU;AAC7B,OAAOC,WAAU,MAAO,2BAA2B;AACnD,OAAO,KAAKC,WAAU,MAAO,kCAAkC;AAE/D,eAAe;EACbC,IAAI,EAAE,sBAAsB;EAC5BC,UAAU,EAAE;IACVH;EACF,CAAC;EACDI,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIT,QAAQ,EAAC;IACvB,MAAMU,MAAK,GAAI,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;IAC/C,MAAMC,KAAI,GAAIlB,GAAG,CAAC;MAChBmB,EAAE,EAAE,EAAE;MACNC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,CAAC;MACRC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,CAAC;MACZC,kBAAkB,EAAE,KAAK;MACzBC,gBAAgB,EAAE,KAAK;MACvBC,UAAU,EAAE,CAAC;MACbC,cAAc,EAAE;IAClB,CAAC;IACD,MAAMC,UAAS,GAAI;MACjBV,KAAK,EAAE,CAAC;QAAEW,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9DV,KAAK,EAAE,CAAC;QAAEQ,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9Dd,OAAO,EAAE,CAAC;QAAEY,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC,CAAC;MAClER,SAAS,EAAE,CAAC;QAAEM,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAS,CAAC,CAAC;MACtET,SAAS,EAAE,CAAC;QAAEO,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MACpEJ,cAAc,EAAE,CAAC;QAAEE,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC;IACxE;IACA,MAAMC,eAAc,GAAInC,GAAG,CAAC,EAAE;IAC9B,MAAMoC,aAAY,GAAIpC,GAAG,CAAC,EAAE;IAC5B,MAAMqC,YAAW,GAAIrC,GAAG,CAAC,EAAE;IAC3B;IACAC,gBAAgB,CAAC,CAAC,EAAE,IAAI,EAAGqC,GAAG,IAAK;MACjC,IAAIA,GAAE,IAAKA,GAAG,CAACC,MAAM,EAAE;QACrBJ,eAAe,CAACK,KAAI,GAAItC,MAAM,CAACoC,GAAG,CAAC;QACnCH,eAAe,CAACK,KAAK,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;QAClC,IAAIzB,KAAK,CAAC0B,KAAK,CAACvB,EAAE,EAAE;UAClB;UACAb,WAAW,CAACU,KAAK,CAAC0B,KAAK,CAACvB,EAAE,EAAGmB,GAAG,IAAK;YACnCA,GAAG,CAACb,SAAQ,GAAIa,GAAG,CAACb,SAAQ,GAAI,EAAE;YAClCP,KAAK,CAACsB,KAAI,GAAIF,GAAG;YACjBF,aAAa,CAACI,KAAI,GAAIrC,YAAY,CAACgC,eAAe,CAACK,KAAK,EAAEF,GAAG,CAAClB,OAAO,CAAC;YACtEF,KAAK,CAACsB,KAAK,CAACpB,OAAM,GAAI,EAAC;YACvB,KAAK,MAAMuB,UAAS,IAAKP,aAAa,CAACI,KAAK,EAAE;cAC5CtB,KAAK,CAACsB,KAAK,CAACpB,OAAO,CAACwB,IAAI,CAACD,UAAU,CAACA,UAAU,CAACJ,MAAK,GAAI,CAAC,CAAC;YAC5D;YACArB,KAAK,CAACsB,KAAK,CAACV,cAAa,GAAI,EAAC;YAC9B,KAAK,MAAMa,UAAS,IAAKL,GAAG,CAACD,YAAY,EAAE;cACzCnB,KAAK,CAACsB,KAAK,CAACV,cAAc,CAACc,IAAI,CAACD,UAAU,CAACxB,EAAE;cAC7CkB,YAAY,CAACG,KAAK,CAACI,IAAI,CAACD,UAAU;YACpC;UACF,CAAC;QACH;MACF;IACF,CAAC;IACD;IACA,MAAME,cAAa,GAAKC,GAAG,IAAK;MAC9B5B,KAAK,CAACsB,KAAK,CAACpB,OAAM,GAAI,EAAC;MACvB,KAAK,MAAMuB,UAAS,IAAKG,GAAG,EAAE;QAC5B5B,KAAK,CAACsB,KAAK,CAACpB,OAAO,CAACwB,IAAI,CAACD,UAAU,CAACA,UAAU,CAACJ,MAAK,GAAI,CAAC,CAAC;MAC5D;IACF;IACA,MAAMQ,QAAO,GAAI/C,GAAG,EAAE;IACtB,MAAMgD,cAAa,GAAIA,CAAA,KAAM;MAC3BD,QAAQ,CAACP,KAAK,CAACS,QAAQ,CAAEC,KAAK,IAAK;QACjC,IAAI,CAACA,KAAK,EAAE;UAAE,OAAO,KAAI;QAAE;QAC3BhC,KAAK,CAACsB,KAAK,CAACf,SAAQ,GAAI0B,UAAU,CAACjC,KAAK,CAACsB,KAAK,CAACf,SAAS,IAAI,EAAE;QAC9D,IAAIP,KAAK,CAACsB,KAAK,CAACrB,EAAE,EAAE;UAClBd,cAAc,CAACa,KAAK,CAACsB,KAAK,EAAE,YAAY;YACtC/B,OAAO,CAAC,MAAM;YACdC,MAAM,CAACkC,IAAI,CAAC;cAACQ,IAAI,EAAE;YAAa,CAAC;UACnC,CAAC;QACH,OAAO;UACLhD,YAAY,CAACc,KAAK,CAACsB,KAAK,EAAE,YAAY;YACpC/B,OAAO,CAAC,MAAM;YACdC,MAAM,CAACkC,IAAI,CAAC;cAACQ,IAAI,EAAE;YAAa,CAAC;UACnC,CAAC;QACH;MACF,CAAC;IACH;IACA,MAAMC,qBAAoB,GAAIrD,GAAG,CAAC,KAAK;IACvC,MAAMsD,eAAc,GAAIA,CAAA,KAAM;MAC5BD,qBAAqB,CAACb,KAAI,GAAI,IAAI;IACpC;IACA,MAAMe,eAAc,GAAIA,CAAA,KAAM;MAC5BF,qBAAqB,CAACb,KAAI,GAAI,KAAK;IACrC;IACA,MAAMgB,uBAAsB,GAAK1B,cAAc,IAAK;MAClD;MACA,IAAI,CAACA,cAAa,IAAKA,cAAc,CAACS,MAAK,KAAM,CAAC,EAAE;QAClD/B,KAAK,CAAC,OAAO;QACb;MACF;MACA,KAAK,MAAMiD,UAAS,IAAK3B,cAAc,EAAE;QACvC,IAAIZ,KAAK,CAACsB,KAAK,CAACV,cAAc,CAAC4B,OAAO,CAACD,UAAU,IAAI,CAAC,CAAC,EAAE;UACvD;QACF;QACAvC,KAAK,CAACsB,KAAK,CAACV,cAAc,CAACc,IAAI,CAACa,UAAU;QAC1C7C,WAAW,CAACN,WAAW,CAACmD,UAAU,EAAGnB,GAAG,IAAK;UAC3CD,YAAY,CAACG,KAAK,CAACI,IAAI,CAACN,GAAG,CAAC;UAC5BpB,KAAK,CAACsB,KAAK,CAAChB,KAAI,IAAKc,GAAG,CAACd,KAAK;QAChC,CAAC;MACH;MACAf,OAAO,CAAC,YAAY;MACpB8C,eAAe,EAAC;IAClB;IACA,OAAO;MACLtC,MAAM;MACNC,KAAK;MACLa,UAAU;MACVI,eAAe;MACfC,aAAa;MACbW,QAAQ;MACRF,cAAc;MACdG,cAAc;MACdX,YAAY;MACZgB,qBAAqB;MACrBC,eAAe;MACfC,eAAe;MACfC;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}