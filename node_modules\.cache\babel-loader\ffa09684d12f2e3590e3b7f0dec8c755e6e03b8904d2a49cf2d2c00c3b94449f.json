{"ast": null, "code": "export default {\n  name: \"ResourceIndex\",\n  setup() {\n    return {};\n  }\n};", "map": {"version": 3, "names": ["name", "setup"], "sources": ["D:\\sourcecodeAndDocument\\learning-platform\\admin\\src\\views\\resource\\index.vue"], "sourcesContent": ["<template>\n  <div style=\"margin: 20px;\">\n    概览\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"ResourceIndex\",\n  setup() {\n    return {};\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n</style>\n"], "mappings": "AAOA,eAAe;EACbA,IAAI,EAAE,eAAe;EACrBC,KAAKA,CAAA,EAAG;IACN,OAAO,CAAC,CAAC;EACX;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}