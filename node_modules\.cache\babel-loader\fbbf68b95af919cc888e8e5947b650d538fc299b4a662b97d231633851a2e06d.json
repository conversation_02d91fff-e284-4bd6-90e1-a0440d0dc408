{"ast": null, "code": "import { getRoutes } from \"@/util/authorityUtils\";\nimport store from \"@/store\";\nimport { ref } from \"vue\";\nimport { useRoute } from \"vue-router\";\nimport { Search } from \"@element-plus/icons-vue\";\nexport default {\n  name: \"AsideIndex\",\n  components: {\n    Search\n  },\n  computed: {\n    opened() {\n      return !store.getters.getAsideStatus;\n    }\n  },\n  setup() {\n    const route = useRoute();\n    const routeMap = getRoutes();\n    const routes = ref([]);\n    const routeItemList = route.path.split(\"/\");\n    let key = route.path;\n    if (routeItemList.length > 1) {\n      key = \"/\" + routeItemList[1];\n    }\n    const routeObj = ref(routeMap[key]);\n    if (routeObj.value) {\n      routes.value = routeObj.value.children || [];\n    }\n    // 当前显示菜单\n    const activeMenu = ref(\"\");\n    const changeActiveMenu = function () {\n      const {\n        meta,\n        path\n      } = route;\n      // if set path, the sidebar will highlight the path you set\n      if (meta.activeMenu) {\n        activeMenu.value = meta.activeMenu;\n        return;\n      }\n      if (path === \"/\" || path === \"\") {\n        activeMenu.value = \"\";\n        return;\n      }\n      activeMenu.value = path;\n    };\n    changeActiveMenu();\n    return {\n      routeObj,\n      routes,\n      activeMenu\n    };\n  }\n};", "map": {"version": 3, "names": ["getRoutes", "store", "ref", "useRoute", "Search", "name", "components", "computed", "opened", "getters", "getAsideStatus", "setup", "route", "routeMap", "routes", "routeItemList", "path", "split", "key", "length", "routeObj", "value", "children", "activeMenu", "changeActiveMenu", "meta"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/components/Aside.vue"], "sourcesContent": ["<template>\n  <div v-if=\"routeObj\">\n    <div class=\"aside-title\" v-if=\"!opened && routeObj.meta\">\n      <h2 class=\"el-menu-item menu-title\" style=\"height: 40px; line-height: 46px\">{{routeObj.meta.title}}</h2>\n    </div>\n    <el-menu\n      :default-active=\"activeMenu\"\n      class=\"el-menu-vertical\"\n      :class=\"{'padding-zero': opened}\"\n      :router=\"true\"\n      :collapse=\"opened\"\n      background-color=\"#f0f0f0\"\n      text-color=\"#000000\"\n      active-text-color=\"#221dff\">\n      <template v-for=\"(item, index) in routes\" :key=\"index\">\n        <el-submenu :index=\"item.path\" class=\"sub-menu-item\" v-if=\"!item.hidden && item.children && item.children.length\">\n          <li class=\"el-menu-item el-menu-item-title\" v-if=\"!opened && !item.hidden && item.children && item.children.length\">\n            {{item.meta.title}}\n          </li>\n          <template #title>\n            <el-icon>\n              <component :is=\"item.meta.icon\"/>\n            </el-icon>\n            <span>{{item.meta.title}}</span>\n          </template>\n          <template v-for=\"(subItem, idx) in item.children\" :key=\"index + '-' + idx\">\n            <el-submenu :index=\"subItem.path\" v-if=\"!subItem.hidden && subItem.children && subItem.children.length\" :key=\"index + '-' + idx\">\n              <template #title>\n                <el-icon>\n                  <component :is=\"subItem.meta.icon\"/>\n                </el-icon>\n                <span>{{subItem.meta.title}}</span>\n              </template>\n              <el-menu-item v-for=\"(c, y) in subItem.children\" :index=\"routeObj.path + (item.path ? '/' : '') + item.path + (subItem.path ? '/' : '') + subItem.path + (c.path ? '/' : '') + c.path\" :key=\"index + '-' + idx + '-' + y\" v-show=\"!c.hidden\">\n                <i :class=\"c.meta.icon\"/>\n                <template #title>{{c.meta.title}}</template>\n              </el-menu-item>\n            </el-submenu>\n            <el-menu-item :index=\"routeObj.path + (item.path ? '/' : '') + item.path + (subItem.path ? '/' : '') + subItem.path\" v-else-if=\"!subItem.hidden\">\n              <el-icon>\n                <component :is=\"subItem.meta.icon\"/>\n              </el-icon>\n              <template #title>{{subItem.meta.title}}</template>\n            </el-menu-item>\n          </template>\n        </el-submenu>\n        <el-menu-item :index=\"routeObj.path + (item.path ? '/' : '') + item.path\" v-else-if=\"!item.hidden\">\n          <el-icon v-if=\"item.meta.icon\">\n            <component :is=\"item.meta.icon\"/>\n          </el-icon>\n          <template #title>{{item.meta.title}}</template>\n        </el-menu-item>\n      </template>\n    </el-menu>\n  </div>\n</template>\n<script>\nimport {getRoutes} from \"@/util/authorityUtils\";\nimport store from \"@/store\";\nimport {ref} from \"vue\";\nimport {useRoute} from \"vue-router\";\nimport {Search} from \"@element-plus/icons-vue\";\n\nexport default {\n  name: \"AsideIndex\",\n  components: {Search},\n  computed: {\n    opened() {\n      return !store.getters.getAsideStatus\n    }\n  },\n  setup() {\n    const route = useRoute();\n    const routeMap = getRoutes();\n    const routes = ref([]);\n    const routeItemList = route.path.split(\"/\");\n    let key = route.path;\n    if (routeItemList.length > 1) {\n      key = \"/\" + routeItemList[1]\n    }\n    const routeObj = ref(routeMap[key])\n    if (routeObj.value) {\n      routes.value = routeObj.value.children || []\n    }\n    // 当前显示菜单\n    const activeMenu = ref(\"\")\n    const changeActiveMenu = function() {\n      const { meta, path } = route\n      // if set path, the sidebar will highlight the path you set\n      if (meta.activeMenu) {\n        activeMenu.value = meta.activeMenu;\n        return\n      }\n      if (path === \"/\" || path === \"\") {\n        activeMenu.value = \"\";\n        return\n      }\n      activeMenu.value = path\n    }\n    changeActiveMenu()\n    return {\n      routeObj,\n      routes,\n      activeMenu\n    }\n  }\n}\n</script>\n<style scoped lang=\"scss\">\n.aside-title {\n  position: fixed;\n  z-index: 99;\n  background: #f1f1f1;\n  width: 210px;\n}\n.el-menu-vertical {\n  border: 0;\n  padding: 40px 0;\n  ::v-deep .el-menu-item {\n    font-size: 12px;\n    height: 40px;\n    line-height: 40px;\n    .el-icon-menu {\n      margin: 0 5px 0 0;\n      width: 24px;\n      font-size: 18px;\n    }\n    &:first-child {\n      padding-top: 0;\n      height: 24px;\n    }\n  }\n  ::v-deep .el-submenu__title {\n    font-size: 12px;\n    height: 40px;\n    line-height: 40px;\n  }\n  &:not(.el-menu--collapse) {\n    width: 210px;\n    min-height: 400px;\n  }\n  .sub-menu-item {\n    &:first-child {\n      padding-top: 0;\n      height: 24px;\n    }\n    .el-menu-item-title {\n      display: flex;\n      align-items: flex-start;\n      line-height: 24px;\n      height: 44px;\n      top: 20px;\n      &:hover, &:focus {\n        background: none;\n        cursor: text;\n      }\n    }\n  }\n}\n.padding-zero {\n  padding: 0;\n}\n.menu-title {\n  font-size: 16px;\n  font-weight: 600;\n  cursor: text;\n  &:hover, &:focus {\n    background: none;\n  }\n}\n</style>\n"], "mappings": "AAyDA,SAAQA,SAAS,QAAO,uBAAuB;AAC/C,OAAOC,KAAI,MAAO,SAAS;AAC3B,SAAQC,GAAG,QAAO,KAAK;AACvB,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,MAAM,QAAO,yBAAyB;AAE9C,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,UAAU,EAAE;IAACF;EAAM,CAAC;EACpBG,QAAQ,EAAE;IACRC,MAAMA,CAAA,EAAG;MACP,OAAO,CAACP,KAAK,CAACQ,OAAO,CAACC,cAAa;IACrC;EACF,CAAC;EACDC,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIT,QAAQ,EAAE;IACxB,MAAMU,QAAO,GAAIb,SAAS,EAAE;IAC5B,MAAMc,MAAK,GAAIZ,GAAG,CAAC,EAAE,CAAC;IACtB,MAAMa,aAAY,GAAIH,KAAK,CAACI,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC;IAC3C,IAAIC,GAAE,GAAIN,KAAK,CAACI,IAAI;IACpB,IAAID,aAAa,CAACI,MAAK,GAAI,CAAC,EAAE;MAC5BD,GAAE,GAAI,GAAE,GAAIH,aAAa,CAAC,CAAC;IAC7B;IACA,MAAMK,QAAO,GAAIlB,GAAG,CAACW,QAAQ,CAACK,GAAG,CAAC;IAClC,IAAIE,QAAQ,CAACC,KAAK,EAAE;MAClBP,MAAM,CAACO,KAAI,GAAID,QAAQ,CAACC,KAAK,CAACC,QAAO,IAAK,EAAC;IAC7C;IACA;IACA,MAAMC,UAAS,GAAIrB,GAAG,CAAC,EAAE;IACzB,MAAMsB,gBAAe,GAAI,SAAAA,CAAA,EAAW;MAClC,MAAM;QAAEC,IAAI;QAAET;MAAK,IAAIJ,KAAI;MAC3B;MACA,IAAIa,IAAI,CAACF,UAAU,EAAE;QACnBA,UAAU,CAACF,KAAI,GAAII,IAAI,CAACF,UAAU;QAClC;MACF;MACA,IAAIP,IAAG,KAAM,GAAE,IAAKA,IAAG,KAAM,EAAE,EAAE;QAC/BO,UAAU,CAACF,KAAI,GAAI,EAAE;QACrB;MACF;MACAE,UAAU,CAACF,KAAI,GAAIL,IAAG;IACxB;IACAQ,gBAAgB,EAAC;IACjB,OAAO;MACLJ,QAAQ;MACRN,MAAM;MACNS;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}