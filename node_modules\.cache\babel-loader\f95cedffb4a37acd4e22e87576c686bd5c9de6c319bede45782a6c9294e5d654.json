{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref } from \"vue\";\nimport { findList, updateCompany, saveCompany, findTypeList } from \"@/api/member/company\";\nimport Page from \"../../../components/Page\";\nimport { error, success } from \"@/util/tipsUtils\";\nexport default {\n  name: \"MemberCompany\",\n  components: {\n    Page\n  },\n  props: {\n    cancelCallback: {\n      type: Function,\n      default: () => {}\n    },\n    selectCallback: {\n      type: Function,\n      default: () => {}\n    },\n    isComponent: {\n      type: Boolean,\n      default: false\n    }\n  },\n  setup(props) {\n    const companyTypeList = ref([]);\n    const list = ref([]);\n    const total = ref(0);\n    const dataLoading = ref(true);\n    const searchParam = ref({\n      name: \"\",\n      size: 20,\n      current: 1\n    });\n    // 加载列表\n    const loadList = () => {\n      findTypeList({\n        size: 9999,\n        current: 1,\n        status: 'enable'\n      }, resp => {\n        if (resp.list && resp.list.length) {\n          for (const l of resp.list) {\n            companyTypeList.value.push({\n              label: l.name,\n              value: l.id\n            });\n          }\n        }\n      });\n      dataLoading.value = true;\n      findList(searchParam.value, res => {\n        dataLoading.value = false;\n        if (!res) {\n          return;\n        }\n        list.value = res.list;\n        total.value = res.total;\n      }).catch(() => {\n        dataLoading.value = false;\n      });\n    };\n    loadList();\n    const currentChange = currentPage => {\n      searchParam.value.current = currentPage;\n      loadList();\n    };\n    const sizeChange = s => {\n      searchParam.value.size = s;\n      loadList();\n    };\n    // 搜索\n    const search = () => {\n      loadList();\n    };\n    const memberCompanyRules = {\n      name: [{\n        required: true,\n        message: \"请输入名称\",\n        trigger: \"blur\"\n      }]\n    };\n    const memberCompany = ref({});\n    const memberCompanyRef = ref(null);\n    const showMemberCompanyFormDialog = ref(false);\n    const hideMemberCompanyForm = () => {\n      showMemberCompanyFormDialog.value = false;\n      memberCompany.value = {};\n    };\n    const add = () => {\n      showMemberCompanyFormDialog.value = true;\n    };\n    // 编辑\n    const edit = item => {\n      memberCompany.value = item;\n      showMemberCompanyFormDialog.value = true;\n    };\n    //提交\n    const submitMemberCompany = () => {\n      memberCompanyRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (memberCompany.value.id) {\n          updateCompany(memberCompany.value, () => {\n            success(\"修改成功\");\n            loadList();\n            hideMemberCompanyForm();\n          });\n        } else {\n          saveCompany(memberCompany.value, () => {\n            success(\"新增成功\");\n            loadList();\n            hideMemberCompanyForm();\n          });\n        }\n      });\n    };\n    const multipleSelection = ref([]);\n    const handleSelectionChange = val => {\n      multipleSelection.value = val;\n    };\n    const selectSelectionChange = () => {\n      if (!multipleSelection.value.length) {\n        error(\"请至少选择一个\");\n      }\n      props.selectCallback && props.selectCallback(multipleSelection.value);\n    };\n    return {\n      companyTypeList,\n      handleSelectionChange,\n      selectSelectionChange,\n      list,\n      total,\n      searchParam,\n      search,\n      currentChange,\n      sizeChange,\n      showMemberCompanyFormDialog,\n      add,\n      memberCompany,\n      memberCompanyRef,\n      edit,\n      hideMemberCompanyForm,\n      submitMemberCompany,\n      memberCompanyRules,\n      dataLoading\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "findList", "updateCompany", "saveCompany", "findTypeList", "Page", "error", "success", "name", "components", "props", "cancelCallback", "type", "Function", "default", "selectCallback", "isComponent", "Boolean", "setup", "companyTypeList", "list", "total", "dataLoading", "searchParam", "size", "current", "loadList", "status", "resp", "length", "l", "value", "push", "label", "id", "res", "catch", "currentChange", "currentPage", "sizeChange", "s", "search", "memberCompanyRules", "required", "message", "trigger", "memberCompany", "memberCompanyRef", "showMemberCompanyFormDialog", "hideMemberCompanyForm", "add", "edit", "item", "submitMemberCompany", "validate", "valid", "multipleSelection", "handleSelectionChange", "val", "selectSelectionChange"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/member/company/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"header\">\n      <el-form :inline=\"true\" :model=\"searchParam\" class=\"demo-form-inline\">\n        <el-form-item label=\"\">\n          <el-input size=\"small\" class=\"search-input\" v-model=\"searchParam.name\" placeholder=\"请输入关键字\">\n            <template #append>\n              <el-button size=\"small\" class=\"search-btn\" type=\"primary\" @click=\"search\">搜索</el-button>\n            </template>\n          </el-input>\n        </el-form-item>\n        <el-form-item v-if=\"!isComponent\">\n          <el-button size=\"small\" type=\"primary\" @click=\"add\">创建公司</el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"content\">\n      <div class=\"content-list\">\n        <el-table v-loading=\"dataLoading\" :data=\"list\" size=\"small\" style=\"width: 100%;\" @selection-change=\"handleSelectionChange\">\n          <el-table-column type=\"selection\" width=\"45\" v-if=\"isComponent\"/>\n          <el-table-column label=\"序号\" type=\"index\"/>\n          <el-table-column prop=\"name\" label=\"名称\"/>\n          <el-table-column prop=\"sortOrder\" label=\"排序\"/>\n          <el-table-column prop=\"status\" label=\"状态\">\n            <template #default=\"scope\">\n              {{scope.row.status === 'normal' ? '启用' : '禁用'}}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"50\" v-if=\"!isComponent\">\n            <template #default=\"scope\">\n              <el-button type=\"text\" size=\"small\" @click=\"edit(scope.row)\">编辑</el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n    </div>\n    <page style=\"margin-top: 20px;\" :total=\"total\" :current-change=\"currentChange\" :size-change=\"sizeChange\" :page-size=\"searchParam.size\"></page>\n    <el-dialog title=\"编辑会员公司\" v-model=\"showMemberCompanyFormDialog\" :before-close=\"hideMemberCompanyForm\">\n      <el-form :model=\"memberCompany\" :rules=\"memberCompanyRules\" ref=\"memberCompanyRef\">\n        <el-form-item label=\"名称：\" label-width=\"150px\" prop=\"name\">\n          <el-input size=\"small\" v-model=\"memberCompany.name\" placeholder=\"请输入名称\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"类型：\" label-width=\"150px\" prop=\"companyTypeId\">\n          <el-select\n            v-model=\"memberCompany.companyTypeId\" placeholder=\"请选择公司类型\" style=\"width: 100%\">\n            <el-option\n              v-for=\"item in companyTypeList\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序：\" label-width=\"150px\" prop=\"sortOrder\">\n          <el-input size=\"small\" v-model=\"memberCompany.sortOrder\" placeholder=\"请输入排序，数值越大越靠前\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"状态：\" label-width=\"150px\" prop=\"status\">\n          <el-switch active-color=\"#13ce66\" :active-value=\"'normal'\" :inactive-value=\"'invalid'\"  v-model=\"memberCompany.status\"></el-switch>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button size=\"small\" @click=\"hideMemberCompanyForm\">取 消</el-button>\n          <el-button size=\"small\" type=\"primary\" @click=\"submitMemberCompany\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n    <template v-if=\"isComponent\">\n      <div class=\"dialog-footer\" style=\"text-align: right;margin-top: 30px;\">\n        <el-button size=\"small\" @click=\"cancelCallback\">取 消</el-button>\n        <el-button size=\"small\" type=\"primary\" @click=\"selectSelectionChange\">确 定</el-button>\n      </div>\n    </template>\n  </div>\n</template>\n\n<script>\n  import {ref} from \"vue\"\n  import {findList, updateCompany, saveCompany, findTypeList} from \"@/api/member/company\"\n  import Page from \"../../../components/Page\"\n  import {error, success} from \"@/util/tipsUtils\";\n\n  export default {\n    name: \"MemberCompany\",\n    components: {\n      Page\n    },\n    props: {\n      cancelCallback: {\n        type: Function,\n        default: () => {}\n      },\n      selectCallback: {\n        type: Function,\n        default: () => {}\n      },\n      isComponent: {\n        type: Boolean,\n        default: false\n      }\n    },\n    setup(props) {\n      const companyTypeList = ref([])\n      const list = ref([])\n      const total = ref(0)\n      const dataLoading = ref(true)\n      const searchParam = ref({\n        name: \"\",\n        size: 20,\n        current: 1\n      })\n      // 加载列表\n      const loadList = () => {\n        findTypeList({size: 9999, current: 1, status: 'enable'}, resp => {\n          if (resp.list && resp.list.length) {\n            for (const l of resp.list) {\n              companyTypeList.value.push({label: l.name, value: l.id})\n            }\n          }\n        })\n        dataLoading.value = true\n        findList(searchParam.value, (res) => {\n          dataLoading.value = false\n          if (!res) {return;}\n          list.value = res.list;\n          total.value = res.total;\n        }).catch(() => {\n          dataLoading.value = false\n        })\n      }\n      loadList();\n      const currentChange = (currentPage) => {\n        searchParam.value.current = currentPage;\n        loadList();\n      }\n      const sizeChange = (s) => {\n        searchParam.value.size = s;\n        loadList();\n      }\n      // 搜索\n      const search = () => {\n        loadList();\n      }\n      const memberCompanyRules = {\n        name: [{ required: true, message: \"请输入名称\", trigger: \"blur\" }],\n      }\n      const memberCompany = ref({})\n      const memberCompanyRef = ref(null)\n      const showMemberCompanyFormDialog = ref(false)\n      const hideMemberCompanyForm = () => {\n        showMemberCompanyFormDialog.value = false;\n        memberCompany.value = {}\n      }\n      const add = () => {\n        showMemberCompanyFormDialog.value = true;\n      }\n      // 编辑\n      const edit = (item) => {\n        memberCompany.value = item\n        showMemberCompanyFormDialog.value = true;\n      }\n      //提交\n      const submitMemberCompany = () => {\n        memberCompanyRef.value.validate(valid => {\n          if (!valid) {\n            return false;\n          }\n          if (memberCompany.value.id) {\n            updateCompany(memberCompany.value, () => {\n              success(\"修改成功\")\n              loadList()\n              hideMemberCompanyForm()\n            });\n          } else {\n            saveCompany(memberCompany.value, () => {\n              success(\"新增成功\")\n              loadList()\n              hideMemberCompanyForm()\n            });\n          }\n        })\n      }\n\n      const multipleSelection = ref([])\n      const handleSelectionChange = (val) => {\n        multipleSelection.value = val;\n      }\n      const selectSelectionChange = () => {\n        if (!multipleSelection.value.length) {\n          error(\"请至少选择一个\")\n        }\n        props.selectCallback && props.selectCallback(multipleSelection.value)\n      }\n\n      return {\n        companyTypeList,\n        handleSelectionChange,\n        selectSelectionChange,\n        list,\n        total,\n        searchParam,\n        search,\n        currentChange,\n        sizeChange,\n        showMemberCompanyFormDialog,\n        add,\n        memberCompany,\n        memberCompanyRef,\n        edit,\n        hideMemberCompanyForm,\n        submitMemberCompany,\n        memberCompanyRules,\n        dataLoading,\n      };\n    }\n  };\n</script>\n<style lang=\"scss\">\n  .header {\n    .el-form {\n      .el-form-item {\n        .el-form-item__content {\n          line-height: 28px;\n          .search-btn {\n            &:hover {\n              color: $--color-primary;\n            }\n          }\n        }\n      }\n    }\n  }\n</style>\n<style scoped lang=\"scss\">\n  .app-container {\n    margin: 20px;\n    .content-list {\n      margin: 0;\n      padding: 0;\n      border: 0;\n      font: inherit;\n      vertical-align: baseline;\n    }\n    .search-input {\n      width: 242px;\n    }\n  }\n</style>\n"], "mappings": ";AA6EE,SAAQA,GAAG,QAAO,KAAI;AACtB,SAAQC,QAAQ,EAAEC,aAAa,EAAEC,WAAW,EAAEC,YAAY,QAAO,sBAAqB;AACtF,OAAOC,IAAG,MAAO,0BAAyB;AAC1C,SAAQC,KAAK,EAAEC,OAAO,QAAO,kBAAkB;AAE/C,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,UAAU,EAAE;IACVJ;EACF,CAAC;EACDK,KAAK,EAAE;IACLC,cAAc,EAAE;MACdC,IAAI,EAAEC,QAAQ;MACdC,OAAO,EAAEA,CAAA,KAAM,CAAC;IAClB,CAAC;IACDC,cAAc,EAAE;MACdH,IAAI,EAAEC,QAAQ;MACdC,OAAO,EAAEA,CAAA,KAAM,CAAC;IAClB,CAAC;IACDE,WAAW,EAAE;MACXJ,IAAI,EAAEK,OAAO;MACbH,OAAO,EAAE;IACX;EACF,CAAC;EACDI,KAAKA,CAACR,KAAK,EAAE;IACX,MAAMS,eAAc,GAAInB,GAAG,CAAC,EAAE;IAC9B,MAAMoB,IAAG,GAAIpB,GAAG,CAAC,EAAE;IACnB,MAAMqB,KAAI,GAAIrB,GAAG,CAAC,CAAC;IACnB,MAAMsB,WAAU,GAAItB,GAAG,CAAC,IAAI;IAC5B,MAAMuB,WAAU,GAAIvB,GAAG,CAAC;MACtBQ,IAAI,EAAE,EAAE;MACRgB,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC;IACD;IACA,MAAMC,QAAO,GAAIA,CAAA,KAAM;MACrBtB,YAAY,CAAC;QAACoB,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,CAAC;QAAEE,MAAM,EAAE;MAAQ,CAAC,EAAEC,IAAG,IAAK;QAC/D,IAAIA,IAAI,CAACR,IAAG,IAAKQ,IAAI,CAACR,IAAI,CAACS,MAAM,EAAE;UACjC,KAAK,MAAMC,CAAA,IAAKF,IAAI,CAACR,IAAI,EAAE;YACzBD,eAAe,CAACY,KAAK,CAACC,IAAI,CAAC;cAACC,KAAK,EAAEH,CAAC,CAACtB,IAAI;cAAEuB,KAAK,EAAED,CAAC,CAACI;YAAE,CAAC;UACzD;QACF;MACF,CAAC;MACDZ,WAAW,CAACS,KAAI,GAAI,IAAG;MACvB9B,QAAQ,CAACsB,WAAW,CAACQ,KAAK,EAAGI,GAAG,IAAK;QACnCb,WAAW,CAACS,KAAI,GAAI,KAAI;QACxB,IAAI,CAACI,GAAG,EAAE;UAAC;QAAO;QAClBf,IAAI,CAACW,KAAI,GAAII,GAAG,CAACf,IAAI;QACrBC,KAAK,CAACU,KAAI,GAAII,GAAG,CAACd,KAAK;MACzB,CAAC,CAAC,CAACe,KAAK,CAAC,MAAM;QACbd,WAAW,CAACS,KAAI,GAAI,KAAI;MAC1B,CAAC;IACH;IACAL,QAAQ,EAAE;IACV,MAAMW,aAAY,GAAKC,WAAW,IAAK;MACrCf,WAAW,CAACQ,KAAK,CAACN,OAAM,GAAIa,WAAW;MACvCZ,QAAQ,EAAE;IACZ;IACA,MAAMa,UAAS,GAAKC,CAAC,IAAK;MACxBjB,WAAW,CAACQ,KAAK,CAACP,IAAG,GAAIgB,CAAC;MAC1Bd,QAAQ,EAAE;IACZ;IACA;IACA,MAAMe,MAAK,GAAIA,CAAA,KAAM;MACnBf,QAAQ,EAAE;IACZ;IACA,MAAMgB,kBAAiB,GAAI;MACzBlC,IAAI,EAAE,CAAC;QAAEmC,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC;IAC9D;IACA,MAAMC,aAAY,GAAI9C,GAAG,CAAC,CAAC,CAAC;IAC5B,MAAM+C,gBAAe,GAAI/C,GAAG,CAAC,IAAI;IACjC,MAAMgD,2BAA0B,GAAIhD,GAAG,CAAC,KAAK;IAC7C,MAAMiD,qBAAoB,GAAIA,CAAA,KAAM;MAClCD,2BAA2B,CAACjB,KAAI,GAAI,KAAK;MACzCe,aAAa,CAACf,KAAI,GAAI,CAAC;IACzB;IACA,MAAMmB,GAAE,GAAIA,CAAA,KAAM;MAChBF,2BAA2B,CAACjB,KAAI,GAAI,IAAI;IAC1C;IACA;IACA,MAAMoB,IAAG,GAAKC,IAAI,IAAK;MACrBN,aAAa,CAACf,KAAI,GAAIqB,IAAG;MACzBJ,2BAA2B,CAACjB,KAAI,GAAI,IAAI;IAC1C;IACA;IACA,MAAMsB,mBAAkB,GAAIA,CAAA,KAAM;MAChCN,gBAAgB,CAAChB,KAAK,CAACuB,QAAQ,CAACC,KAAI,IAAK;QACvC,IAAI,CAACA,KAAK,EAAE;UACV,OAAO,KAAK;QACd;QACA,IAAIT,aAAa,CAACf,KAAK,CAACG,EAAE,EAAE;UAC1BhC,aAAa,CAAC4C,aAAa,CAACf,KAAK,EAAE,MAAM;YACvCxB,OAAO,CAAC,MAAM;YACdmB,QAAQ,EAAC;YACTuB,qBAAqB,EAAC;UACxB,CAAC,CAAC;QACJ,OAAO;UACL9C,WAAW,CAAC2C,aAAa,CAACf,KAAK,EAAE,MAAM;YACrCxB,OAAO,CAAC,MAAM;YACdmB,QAAQ,EAAC;YACTuB,qBAAqB,EAAC;UACxB,CAAC,CAAC;QACJ;MACF,CAAC;IACH;IAEA,MAAMO,iBAAgB,GAAIxD,GAAG,CAAC,EAAE;IAChC,MAAMyD,qBAAoB,GAAKC,GAAG,IAAK;MACrCF,iBAAiB,CAACzB,KAAI,GAAI2B,GAAG;IAC/B;IACA,MAAMC,qBAAoB,GAAIA,CAAA,KAAM;MAClC,IAAI,CAACH,iBAAiB,CAACzB,KAAK,CAACF,MAAM,EAAE;QACnCvB,KAAK,CAAC,SAAS;MACjB;MACAI,KAAK,CAACK,cAAa,IAAKL,KAAK,CAACK,cAAc,CAACyC,iBAAiB,CAACzB,KAAK;IACtE;IAEA,OAAO;MACLZ,eAAe;MACfsC,qBAAqB;MACrBE,qBAAqB;MACrBvC,IAAI;MACJC,KAAK;MACLE,WAAW;MACXkB,MAAM;MACNJ,aAAa;MACbE,UAAU;MACVS,2BAA2B;MAC3BE,GAAG;MACHJ,aAAa;MACbC,gBAAgB;MAChBI,IAAI;MACJF,qBAAqB;MACrBI,mBAAmB;MACnBX,kBAAkB;MAClBpB;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}