{"ast": null, "code": "import { resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-d37e24d0\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"app-container\"\n};\nconst _hoisted_2 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_cascader = _resolveComponent(\"el-cascader\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_upload_image = _resolveComponent(\"upload-image\");\n  const _component_el_switch = _resolveComponent(\"el-switch\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"categoryRef\",\n    rules: $setup.rules,\n    model: $setup.category,\n    \"label-width\": \"110px\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      label: \"上级分类\",\n      prop: \"pid\"\n    }, {\n      default: _withCtx(() => [$setup.parentCategory.name ? (_openBlock(), _createBlock(_component_el_input, {\n        key: 0,\n        size: \"small\",\n        type: \"text\",\n        class: \"input-text\",\n        disabled: \"\",\n        modelValue: $setup.parentCategory.name,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.parentCategory.name = $event)\n      }, null, 8, [\"modelValue\"])) : (_openBlock(), _createBlock(_component_el_cascader, {\n        key: 1,\n        class: \"input-text\",\n        props: {\n          checkStrictly: true\n        },\n        modelValue: $setup.selectedPidList,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.selectedPidList = $event),\n        options: $setup.categoryOptions,\n        placeholder: \"请选择上级分类\",\n        onChange: $setup.changeParentCategory\n      }, null, 8, [\"modelValue\", \"options\", \"onChange\"]))]),\n      _: 1\n    }), _createVNode(_component_el_form_item, {\n      label: \"分类名称\",\n      prop: \"name\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"small\",\n        maxlength: \"15\",\n        \"show-word-limit\": \"\",\n        class: \"input-text\",\n        modelValue: $setup.category.name,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.category.name = $event)\n      }, null, 8, [\"modelValue\"])]),\n      _: 1\n    }), _createVNode(_component_el_form_item, {\n      label: \"分类图片\",\n      prop: \"image\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_upload_image, {\n        limit: 1,\n        files: $setup.uploadData.files,\n        \"on-upload-success\": $setup.onUploadSuccess,\n        \"on-upload-remove\": $setup.onUploadRemove,\n        \"upload-url\": $setup.uploadData.url\n      }, null, 8, [\"files\", \"on-upload-success\", \"on-upload-remove\", \"upload-url\"])]),\n      _: 1\n    }), _createVNode(_component_el_form_item, {\n      label: \"排序\",\n      prop: \"sortOrder\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"small\",\n        class: \"input-text\",\n        modelValue: $setup.category.sortOrder,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.category.sortOrder = $event),\n        placeholder: \"数据越大显示越前\"\n      }, null, 8, [\"modelValue\"])]),\n      _: 1\n    }), _createVNode(_component_el_form_item, {\n      label: \"是否显示\",\n      prop: \"isShow\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_switch, {\n        size: \"small\",\n        id: \"isShow\",\n        \"active-color\": \"#13ce66\",\n        modelValue: $setup.category.isShow,\n        \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.category.isShow = $event)\n      }, null, 8, [\"modelValue\"])]),\n      _: 1\n    }), _createVNode(_component_el_form_item, {\n      label: \"是否在首页显示\",\n      prop: \"isShowIndex\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_switch, {\n        size: \"small\",\n        id: \"isShowIndex\",\n        \"active-color\": \"#13ce66\",\n        modelValue: $setup.category.isShowIndex,\n        \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.category.isShowIndex = $event)\n      }, null, 8, [\"modelValue\"])]),\n      _: 1\n    })]),\n    _: 1\n  }, 8, [\"rules\", \"model\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n    size: \"small\",\n    onClick: _cache[6] || (_cache[6] = $event => $setup.cancel())\n  }, {\n    default: _withCtx(() => [_createTextVNode(\"取 消\")]),\n    _: 1\n  }), _createVNode(_component_el_button, {\n    size: \"small\",\n    type: \"primary\",\n    onClick: _cache[7] || (_cache[7] = $event => $setup.submit())\n  }, {\n    default: _withCtx(() => [_createTextVNode(\"确 定\")]),\n    _: 1\n  })])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "rules", "$setup", "model", "category", "_component_el_form_item", "label", "prop", "parentCategory", "name", "_createBlock", "_component_el_input", "size", "type", "disabled", "$event", "_component_el_cascader", "props", "checkStrictly", "selectedPidList", "options", "categoryOptions", "placeholder", "onChange", "changeParentCategory", "maxlength", "_component_upload_image", "limit", "files", "uploadData", "onUploadSuccess", "onUploadRemove", "url", "sortOrder", "_component_el_switch", "id", "isShow", "isShowIndex", "_createElementVNode", "_hoisted_2", "_component_el_button", "onClick", "_cache", "cancel", "submit"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/resource/category/edit.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form ref=\"categoryRef\" :rules=\"rules\" :model=\"category\" label-width=\"110px\">\n      <el-form-item label=\"上级分类\" prop=\"pid\">\n        <el-input size=\"small\" v-if=\"parentCategory.name\" type=\"text\" class=\"input-text\" disabled v-model=\"parentCategory.name\"></el-input>\n        <el-cascader v-else class=\"input-text\" :props=\"{checkStrictly: true}\" v-model=\"selectedPidList\" :options=\"categoryOptions\" placeholder=\"请选择上级分类\" @change=\"changeParentCategory\"></el-cascader>\n      </el-form-item>\n      <el-form-item label=\"分类名称\" prop=\"name\">\n        <el-input size=\"small\" maxlength=\"15\" show-word-limit class=\"input-text\" v-model=\"category.name\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"分类图片\" prop=\"image\">\n        <upload-image :limit=\"1\" :files=\"uploadData.files\" :on-upload-success=\"onUploadSuccess\" :on-upload-remove=\"onUploadRemove\" :upload-url=\"uploadData.url\"></upload-image>\n      </el-form-item>\n      <el-form-item label=\"排序\" prop=\"sortOrder\">\n        <el-input size=\"small\"  class=\"input-text\" v-model=\"category.sortOrder\" placeholder=\"数据越大显示越前\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"是否显示\" prop=\"isShow\">\n        <el-switch size=\"small\"  id=\"isShow\" active-color=\"#13ce66\" v-model=\"category.isShow\"></el-switch>\n      </el-form-item>\n      <el-form-item label=\"是否在首页显示\" prop=\"isShowIndex\">\n        <el-switch size=\"small\"  id=\"isShowIndex\" active-color=\"#13ce66\" v-model=\"category.isShowIndex\"></el-switch>\n      </el-form-item>\n    </el-form>\n    <div class=\"dialog-footer\">\n      <el-button size=\"small\"  @click=\"cancel()\">取 消</el-button>\n      <el-button size=\"small\"  type=\"primary\" @click=\"submit()\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\n  import {ref, watch} from \"vue\"\n  import router from \"../../../router\"\n  import {findCategoryList, toTree, getCategory, saveCategory, updateCategory} from \"../../../api/resource/category\"\n  import uploadImage from \"../../../components/Uplaod/index\";\n  import {success, error} from \"../../../util/tipsUtils\";\n  export default {\n    name: \"ResourceCategoryEdit\",\n    components: {\n      uploadImage\n    },\n    props: {\n      data: {\n        type: Object,\n        required: true\n      },\n      pid: {\n        type: Number,\n        required: true\n      },\n      editSuccess: {\n        type: Function\n      },\n      editCancel: {\n        type: Function\n      }\n    },\n    setup(props) {\n      let selectedPidList = ref([])\n      const categoryOptions = ref([])\n      const parentCategory = ref({})\n      const uploadData = {\n        url: process.env.VUE_APP_BASE_API + \"/oss/resource/category/image\",\n        files: []\n      }\n      const rules = {\n        pid: [{ required: true, message: \"请选择上级分类\", trigger: \"blur\" }],\n        name: [{ required: true, message: \"请输入分类名称\", trigger: \"blur\" }],\n        picture: [{ required: true, message: \"请上传分类图片\", trigger: \"blur\" }]\n      }\n      let category = ref({\n        pid: 0,\n        name: \"\",\n        image: \"\",\n        sortOrder: 1,\n        isShow: true,\n        isShowIndex: true\n      })\n      const init = (item, pid) => {\n        if (pid) {\n          getCategory(pid, res => {\n            if (!res) {\n              error(\"没有找到该分类\")\n              return;\n            }\n            parentCategory.value = res;\n          });\n        } else {\n          parentCategory.value = {id: 0, name: \"全部\"};\n        }\n        if (item && item.id) {\n          category = ref(item);\n          if (item.image) {\n            uploadData.files = [{name: item.name, url: item.image}]\n          }\n        }\n        category.value.pid = pid || 0;\n        selectedPidList.value.push(category.value.pid);\n      }\n      init(props.data, props.pid)\n      watch(() => props.data, (nv) => {\n        console.log(\"监听到data改变了\")\n        console.log(nv)\n        init(nv, nv.pid)\n        category = ref(nv)\n      })\n      const loadCategory = () => {\n        findCategoryList(0, true).then(function (response) {\n          if (response) {\n            categoryOptions.value = toTree(response);\n          }\n        });\n      }\n      loadCategory();\n      const changeParentCategory = () => {\n        if (category.value.selectedPidList && category.value.selectedPidList.length > 0) {\n          let id = selectedPidList.value[selectedPidList.value.length - 1];\n          if (id === category.value.id) {\n            error(\"不能选择自己为上级分类\")\n            return;\n          }\n          category.value.pid = id;\n        }\n      }\n      const cancel = () => {\n        props.editCancel && props.editCancel()\n      }\n      const onUploadSuccess = (res) => {\n        category.value.image = res.data;\n      }\n      const onUploadRemove = () => {\n        if (!category.value.image) {\n          return;\n        }\n        category.value.image = \"\";\n        uploadData.value.files = [];\n      }\n      const categoryRef = ref(null)\n      const submit = () => {\n        categoryRef.value.validate(valid => {\n          if (!valid) {\n            return false;\n          }\n          if (!category.value.pid && category.value.pid !== 0) {\n            error(\"请选择上级分类\")\n            return false;\n          }\n          if (category.value.id) {\n            updateCategory(category.value, (res) => {\n              success(\"编辑成功\")\n              router.push({path: \"/resource/category\", query:{ id: res[\"id\"]}});\n              props.editSuccess && props.editSuccess(res[\"id\"])\n            })\n          } else {\n            saveCategory(category.value, (res) => {\n              success(\"新增成功\")\n              router.push({path: \"/resource/category\", query:{ id: res[\"id\"]}});\n              props.editSuccess && props.editSuccess(res[\"id\"])\n            })\n          }\n        });\n      }\n      return {\n        selectedPidList,\n        categoryOptions,\n        parentCategory,\n        category,\n        rules,\n        uploadData,\n        categoryRef,\n        loadCategory,\n        changeParentCategory,\n        cancel,\n        onUploadSuccess,\n        onUploadRemove,\n        submit\n      }\n    }\n  }\n</script>\n<style scoped lang=\"scss\">\n.dialog-footer {\n  text-align: center;\n}\n.input-text {\n  width: 80%;\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAe;;EAsBnBA,KAAK,EAAC;AAAe;;;;;;;;;uBAtB5BC,mBAAA,CA0BM,OA1BNC,UA0BM,GAzBJC,YAAA,CAoBUC,kBAAA;IApBDC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAEC,MAAA,CAAAD,KAAK;IAAGE,KAAK,EAAED,MAAA,CAAAE,QAAQ;IAAE,aAAW,EAAC;;sBACtE,MAGe,CAHfN,YAAA,CAGeO,uBAAA;MAHDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAAmI,CAAtGL,MAAA,CAAAM,cAAc,CAACC,IAAI,I,cAAhDC,YAAA,CAAmIC,mBAAA;;QAAzHC,IAAI,EAAC,OAAO;QAA4BC,IAAI,EAAC,MAAM;QAAClB,KAAK,EAAC,YAAY;QAACmB,QAAQ,EAAR,EAAQ;oBAAUZ,MAAA,CAAAM,cAAc,CAACC,IAAI;mEAAnBP,MAAA,CAAAM,cAAc,CAACC,IAAI,GAAAM,MAAA;oDACtHL,YAAA,CAA8LM,sBAAA;;QAA1KrB,KAAK,EAAC,YAAY;QAAEsB,KAAK,EAAE;UAAAC,aAAA;QAAA,CAAqB;oBAAWhB,MAAA,CAAAiB,eAAe;mEAAfjB,MAAA,CAAAiB,eAAe,GAAAJ,MAAA;QAAGK,OAAO,EAAElB,MAAA,CAAAmB,eAAe;QAAEC,WAAW,EAAC,SAAS;QAAEC,QAAM,EAAErB,MAAA,CAAAsB;;;QAE5J1B,YAAA,CAEeO,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAA4G,CAA5GT,YAAA,CAA4Ga,mBAAA;QAAlGC,IAAI,EAAC,OAAO;QAACa,SAAS,EAAC,IAAI;QAAC,iBAAe,EAAf,EAAe;QAAC9B,KAAK,EAAC,YAAY;oBAAUO,MAAA,CAAAE,QAAQ,CAACK,IAAI;mEAAbP,MAAA,CAAAE,QAAQ,CAACK,IAAI,GAAAM,MAAA;;;QAEjGjB,YAAA,CAEeO,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAAuK,CAAvKT,YAAA,CAAuK4B,uBAAA;QAAxJC,KAAK,EAAE,CAAC;QAAGC,KAAK,EAAE1B,MAAA,CAAA2B,UAAU,CAACD,KAAK;QAAG,mBAAiB,EAAE1B,MAAA,CAAA4B,eAAe;QAAG,kBAAgB,EAAE5B,MAAA,CAAA6B,cAAc;QAAG,YAAU,EAAE7B,MAAA,CAAA2B,UAAU,CAACG;;;QAErJlC,YAAA,CAEeO,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC5B,MAA0G,CAA1GT,YAAA,CAA0Ga,mBAAA;QAAhGC,IAAI,EAAC,OAAO;QAAEjB,KAAK,EAAC,YAAY;oBAAUO,MAAA,CAAAE,QAAQ,CAAC6B,SAAS;mEAAlB/B,MAAA,CAAAE,QAAQ,CAAC6B,SAAS,GAAAlB,MAAA;QAAEO,WAAW,EAAC;;;QAEtFxB,YAAA,CAEeO,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAAkG,CAAlGT,YAAA,CAAkGoC,oBAAA;QAAvFtB,IAAI,EAAC,OAAO;QAAEuB,EAAE,EAAC,QAAQ;QAAC,cAAY,EAAC,SAAS;oBAAUjC,MAAA,CAAAE,QAAQ,CAACgC,MAAM;mEAAflC,MAAA,CAAAE,QAAQ,CAACgC,MAAM,GAAArB,MAAA;;;QAEtFjB,YAAA,CAEeO,uBAAA;MAFDC,KAAK,EAAC,SAAS;MAACC,IAAI,EAAC;;wBACjC,MAA4G,CAA5GT,YAAA,CAA4GoC,oBAAA;QAAjGtB,IAAI,EAAC,OAAO;QAAEuB,EAAE,EAAC,aAAa;QAAC,cAAY,EAAC,SAAS;oBAAUjC,MAAA,CAAAE,QAAQ,CAACiC,WAAW;mEAApBnC,MAAA,CAAAE,QAAQ,CAACiC,WAAW,GAAAtB,MAAA;;;;;6BAGlGuB,mBAAA,CAGM,OAHNC,UAGM,GAFJzC,YAAA,CAA0D0C,oBAAA;IAA/C5B,IAAI,EAAC,OAAO;IAAG6B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA3B,MAAA,IAAEb,MAAA,CAAAyC,MAAM;;sBAAI,MAAG,C,iBAAH,KAAG,E;;MAC9C7C,YAAA,CAAyE0C,oBAAA;IAA9D5B,IAAI,EAAC,OAAO;IAAEC,IAAI,EAAC,SAAS;IAAE4B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA3B,MAAA,IAAEb,MAAA,CAAA0C,MAAM;;sBAAI,MAAG,C,iBAAH,KAAG,E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}