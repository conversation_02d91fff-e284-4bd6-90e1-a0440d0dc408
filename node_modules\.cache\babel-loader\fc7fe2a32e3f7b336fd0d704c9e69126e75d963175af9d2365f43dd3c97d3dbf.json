{"ast": null, "code": "import { createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-01da8729\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"certificate-coat\"\n};\nconst _hoisted_2 = {\n  class: \"certificate-wrap\"\n};\nconst _hoisted_3 = {\n  class: \"certificate-header\"\n};\nconst _hoisted_4 = {\n  class: \"certificate-name\"\n};\nconst _hoisted_5 = {\n  class: \"certificate-main\"\n};\nconst _hoisted_6 = {\n  class: \"certificate-member\"\n};\nconst _hoisted_7 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", {\n  style: {\n    \"font-weight\": \"normal\"\n  }\n}, \"同学：\", -1 /* HOISTED */));\nconst _hoisted_8 = {\n  class: \"certificate-content\"\n};\nconst _hoisted_9 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", null, \"于 \", -1 /* HOISTED */));\nconst _hoisted_10 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", null, \" 至 \", -1 /* HOISTED */));\nconst _hoisted_11 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", null, \" 参加 \", -1 /* HOISTED */));\nconst _hoisted_12 = {\n  class: \"certificate-course-name\"\n};\nconst _hoisted_13 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", null, \"培训，已完成课程学习。\", -1 /* HOISTED */));\nconst _hoisted_14 = {\n  class: \"certificate-complete-desc\"\n};\nconst _hoisted_15 = {\n  class: \"certificate-bottom\"\n};\nconst _hoisted_16 = {\n  class: \"certificate-code\"\n};\nconst _hoisted_17 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", {\n  class: \"certificate-code-title\"\n}, \"证书编号：\", -1 /* HOISTED */));\nconst _hoisted_18 = {\n  class: \"certificate-code-main\"\n};\nconst _hoisted_19 = {\n  class: \"certificate-org\"\n};\nconst _hoisted_20 = {\n  class: \"certificate-org-name\"\n};\nconst _hoisted_21 = {\n  class: \"certificate-date\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [$props.download ? (_openBlock(), _createBlock(_component_el_button, {\n    key: 0,\n    size: \"small\",\n    onClick: $setup.downloadCertificate\n  }, {\n    default: _withCtx(() => [_createTextVNode(\"下载证书\")]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", {\n    class: _normalizeClass([{\n      'certificate-box-nmargin': $props.download\n    }, \"certificate-box\"]),\n    id: \"certificate\",\n    ref: \"certificate\",\n    style: _normalizeStyle($setup.backgroundStyle)\n  }, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, _toDisplayString($props.certificate.name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"span\", null, _toDisplayString($props.certificate.member ? $props.certificate.member.name : $setup.userName), 1 /* TEXT */), _hoisted_7]), _createElementVNode(\"div\", _hoisted_8, [_hoisted_9, _createElementVNode(\"span\", null, _toDisplayString($props.certificate.lessonSignTime ? $setup.formatDate($props.certificate.lessonSignTime, 'yyyy年MM月dd日') : $setup.startTime), 1 /* TEXT */), _hoisted_10, _createElementVNode(\"span\", null, _toDisplayString($props.certificate.lessonCompleteTime ? $setup.formatDate($props.certificate.lessonCompleteTime, 'yyyy年MM月dd日') : $setup.endTime), 1 /* TEXT */), _hoisted_11, _createElementVNode(\"span\", _hoisted_12, _toDisplayString($props.certificate.lesson ? $props.certificate.lesson.name : $setup.courseName), 1 /* TEXT */), _hoisted_13, _createElementVNode(\"span\", _hoisted_14, _toDisplayString($props.certificate.description || '经考核，成绩合格，特发此证。'), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, [_hoisted_17, _createElementVNode(\"span\", _hoisted_18, _toDisplayString($props.certificate.code || $setup.code), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"p\", _hoisted_20, _toDisplayString($props.certificate.awardingOrganization), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_21, _toDisplayString($props.certificate.awardDate ? $setup.formatDate($props.certificate.awardDate, 'yyyy年MM月dd日') : $setup.awardDate), 1 /* TEXT */)])])])], 6 /* CLASS, STYLE */)]);\n}", "map": {"version": 3, "names": ["class", "_createElementVNode", "style", "_createElementBlock", "_hoisted_1", "$props", "download", "_createBlock", "_component_el_button", "size", "onClick", "$setup", "downloadCertificate", "_normalizeClass", "id", "ref", "_normalizeStyle", "backgroundStyle", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "certificate", "name", "_hoisted_5", "_hoisted_6", "member", "userName", "_hoisted_7", "_hoisted_8", "_hoisted_9", "lessonSignTime", "formatDate", "startTime", "_hoisted_10", "lessonCompleteTime", "endTime", "_hoisted_11", "_hoisted_12", "lesson", "courseName", "_hoisted_13", "_hoisted_14", "description", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "code", "_hoisted_19", "_hoisted_20", "awardingOrganization", "_hoisted_21", "awardDate"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/certificate/preview/index.vue"], "sourcesContent": ["<template>\n  <div class=\"certificate-coat\">\n    <el-button v-if=\"download\" size=\"small\" @click=\"downloadCertificate\">下载证书</el-button>\n    <div :class=\"{'certificate-box-nmargin': download}\" class=\"certificate-box\" id=\"certificate\" ref=\"certificate\" :style=\"backgroundStyle\">\n      <div class=\"certificate-wrap\">\n        <div class=\"certificate-header\">\n          <div class=\"certificate-name\">{{certificate.name}}</div>\n        </div>\n        <div class=\"certificate-main\">\n          <div class=\"certificate-member\">\n            <span>{{ certificate.member ? certificate.member.name : userName}}</span>\n            <span style=\"font-weight: normal;\">同学：</span>\n          </div>\n          <div class=\"certificate-content\">\n            <span>于 </span>\n            <span>{{ certificate.lessonSignTime ? formatDate(certificate.lessonSignTime, 'yyyy年MM月dd日') : startTime}}</span>\n            <span> 至 </span>\n            <span>{{ certificate.lessonCompleteTime ? formatDate(certificate.lessonCompleteTime, 'yyyy年MM月dd日') : endTime}}</span>\n            <span> 参加 </span>\n            <span class=\"certificate-course-name\">{{certificate.lesson ? certificate.lesson.name : courseName}}</span>\n            <span>培训，已完成课程学习。</span>\n            <span class=\"certificate-complete-desc\">{{certificate.description || '经考核，成绩合格，特发此证。'}}</span>\n          </div>\n        </div>\n        <div class=\"certificate-bottom\">\n          <div class=\"certificate-code\">\n            <span class=\"certificate-code-title\">证书编号：</span>\n            <span class=\"certificate-code-main\">{{certificate.code || code}}</span>\n          </div>\n          <div class=\"certificate-org\">\n            <p class=\"certificate-org-name\">{{certificate.awardingOrganization}}</p>\n            <p class=\"certificate-date\">{{certificate.awardDate ? formatDate(certificate.awardDate, 'yyyy年MM月dd日') : awardDate}}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport html2canvas from \"html2canvas\"\nimport jsPDF from \"jspdf\"\nimport {nextTick, ref} from \"vue\"\nimport {formatDate} from \"@/util/dateUtils\";\nimport {toBase64} from \"@/api/oss\";\nexport default {\n  name: \"CertificatePreview\",\n  props: {\n    certificate: {\n      type: Object\n    },\n    download: {\n      type: Boolean\n    }\n  },\n  setup(props) {\n    const courseName = ref(\"课程示例\") // 课程名称\n    const userName = ref(\"张三\") // 课程名称\n    const startTime = ref(formatDate(new Date(), \"yyyy年MM月dd日\")) // 开始时间\n    const futureTime = new Date(new Date().getTime() + 90 * 24 * 60 * 60 * 1000).toLocaleString();\n    const endTime = ref(formatDate(futureTime, \"yyyy年MM月dd日\")) // 结束时间\n    const awardDate = ref(formatDate(new Date(), \"yyyy年MM月dd日\")) // 颁发时间\n    // 设置编号  编号等于  日期 + 课程id  + 用户id这样\n    const code = \"UZ\" + formatDate(futureTime, \"yyyyMMddHHmmssSSS\")\n\n    const backgroundStyle = ref('background-image: url(' + props.certificate.design + ');');\n    toBase64(props.certificate.design, res => {\n      if (res) {\n        backgroundStyle.value = 'background-image: url(' + res + ')';\n      }\n    })\n\n    const downloadCertificate = () => {\n      nextTick(() => { // 使用$nextTick，解决数据还没有渲染到html就先转为图片，此时的图片会是空内容的问题\n        const canvas = document.createElement(\"canvas\") // 创建一个canvas节点\n        const shareContent = document.getElementById(\"certificate\") // 需要截图的包裹的（原生的）DOM 对象\n        const width = shareContent.offsetWidth // 获取dom 宽度\n        const height = shareContent.offsetHeight // 获取dom 高度\n        const scale = 2 // 定义任意放大倍数 支持小数\n        canvas.getContext(\"2d\").scale(scale, scale) // 获取context,设置scale\n        const rect = shareContent.getBoundingClientRect() // 获取元素相对于视口的\n        const scrollTop = document.documentElement.scrollTop || document.body.scrollTop // 获取滚动轴滚动的长度\n        html2canvas(document.getElementById(\"certificate\"), { // 转换为图片\n          x: rect.left + 8, // 绘制的dom元素相对于视口的位置\n          y: rect.top,\n          scrollY: -scrollTop,\n          scale: scale, // 添加的scale 参数\n          width: width, // dom 原始宽度\n          height: height,\n          useCORS: true, // 开启跨域\n          dpi: window.devicePixelRatio * 2\n        }).then(canvas => {\n          const context = canvas.getContext(\"2d\")\n          // 关闭抗锯齿\n          context.mozImageSmoothingEnabled = false\n          context.msImageSmoothingEnabled = false\n          context.imageSmoothingEnabled = false\n          const imageData = canvas.toDataURL(\"image/png\");\n          var pdf = new jsPDF(\"p\", \"mm\", \"a4\");\n          pdf.addImage(imageData, \"PNG\", 0, 0, 212, 297);\n          pdf.save(props.certificate.name + \".pdf\");\n\n          // var a = document.createElement(\"a\")\n          // a.download =  \"my-certificate\"\n          // // 设置图片地址\n          // a.href = imgUrl;\n          // a.click();\n        })\n      })\n    }\n    return {\n      backgroundStyle,\n      formatDate,\n      awardDate,\n      downloadCertificate,\n      courseName,\n      userName,\n      startTime,\n      endTime,\n      code\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.certificate-coat {\n  box-sizing: border-box;\n\n  .el-button {\n    float: right;\n  }\n\n  .certificate-box {\n    width: 1123px;\n    height: 794px;\n    position: relative;\n    margin: 0 auto;\n    padding: 0;\n    box-sizing: border-box;\n    background-repeat: no-repeat;\n    background-size: 1123px 794px;\n\n    .certificate-wrap {\n      padding: 100px;\n      height: 594px;\n      width: 923px;\n    }\n  }\n\n  .certificate-box-nmargin {\n    margin: 0;\n  }\n\n  .certificate-header {\n    margin: 20px 0;\n\n    .certificate-name {\n      font-size: 60px;\n      font-weight: bold;\n      text-align: center;\n      letter-spacing: 30px;\n      text-shadow: 0 3px 0 #ddd;\n    }\n  }\n\n  .certificate-main {\n    .certificate-member {\n      width: auto;\n      display: inline-block;\n      font-size: 20px;\n      font-weight: 600;\n      letter-spacing: 5px;\n      margin-top: 66px;\n    }\n\n    .certificate-content {\n      font-size: 20px;\n      color: #000000;\n      text-indent: 44px;\n      margin: 40px 0;\n      line-height: 60px;\n\n      span {\n        letter-spacing: 2px;\n      }\n    }\n\n    .certificate-course-name {\n      text-align: center;\n      letter-spacing: 5px;\n      font-size: 20px;\n      font-weight: 600;\n      margin: 20px 10px 0;\n    }\n\n    .certificate-complete-desc {\n    }\n  }\n\n  .certificate-bottom {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding-top: 40px;\n\n    .certificate-code {\n      display: flex;\n      justify-content: end;\n      align-items: center;\n      font-size: 20px;\n      margin-right: 20px;\n\n      .certificate-code-title {\n        color: #000;\n      }\n\n      .certificate-code-main {\n        color: #000;\n      }\n    }\n\n    .certificate-org {\n      font-size: 20px;\n      letter-spacing: 5px;\n      text-align: center;\n\n      .certificate-org-name {\n        font-size: 20px;\n        letter-spacing: 3px;\n        text-align: center;\n      }\n\n      .certificate-date {\n        margin-top: 10px;\n        font-size: 20px;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAkB;;EAGpBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAkB;;EAE1BA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAoB;gEAE7BC,mBAAA,CAA6C;EAAvCC,KAA4B,EAA5B;IAAA;EAAA;AAA4B,GAAC,KAAG;;EAEnCF,KAAK,EAAC;AAAqB;gEAC9BC,mBAAA,CAAe,cAAT,IAAE;iEAERA,mBAAA,CAAgB,cAAV,KAAG;iEAETA,mBAAA,CAAiB,cAAX,MAAI;;EACJD,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAAwB,cAAlB,aAAW;;EACXD,KAAK,EAAC;AAA2B;;EAGtCA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAkB;iEAC3BC,mBAAA,CAAiD;EAA3CD,KAAK,EAAC;AAAwB,GAAC,OAAK;;EACpCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAAiB;;EACvBA,KAAK,EAAC;AAAsB;;EAC5BA,KAAK,EAAC;AAAkB;;;uBA9BrCG,mBAAA,CAmCM,OAnCNC,UAmCM,GAlCaC,MAAA,CAAAC,QAAQ,I,cAAzBC,YAAA,CAAqFC,oBAAA;;IAA1DC,IAAI,EAAC,OAAO;IAAEC,OAAK,EAAEC,MAAA,CAAAC;;sBAAqB,MAAI,C,iBAAJ,MAAI,E;;uEACzEX,mBAAA,CAgCM;IAhCAD,KAAK,EAAAa,eAAA;MAAA,2BAA8BR,MAAA,CAAAC;IAAQ,GAAS,iBAAiB;IAACQ,EAAE,EAAC,aAAa;IAACC,GAAG,EAAC,aAAa;IAAEb,KAAK,EAAAc,eAAA,CAAEL,MAAA,CAAAM,eAAe;MACpIhB,mBAAA,CA8BM,OA9BNiB,UA8BM,GA7BJjB,mBAAA,CAEM,OAFNkB,UAEM,GADJlB,mBAAA,CAAwD,OAAxDmB,UAAwD,EAAAC,gBAAA,CAAxBhB,MAAA,CAAAiB,WAAW,CAACC,IAAI,iB,GAElDtB,mBAAA,CAeM,OAfNuB,UAeM,GAdJvB,mBAAA,CAGM,OAHNwB,UAGM,GAFJxB,mBAAA,CAAyE,cAAAoB,gBAAA,CAAhEhB,MAAA,CAAAiB,WAAW,CAACI,MAAM,GAAGrB,MAAA,CAAAiB,WAAW,CAACI,MAAM,CAACH,IAAI,GAAGZ,MAAA,CAAAgB,QAAQ,kBAChEC,UAA6C,C,GAE/C3B,mBAAA,CASM,OATN4B,UASM,GARJC,UAAe,EACf7B,mBAAA,CAAgH,cAAAoB,gBAAA,CAAvGhB,MAAA,CAAAiB,WAAW,CAACS,cAAc,GAAGpB,MAAA,CAAAqB,UAAU,CAAC3B,MAAA,CAAAiB,WAAW,CAACS,cAAc,mBAAmBpB,MAAA,CAAAsB,SAAS,kBACvGC,WAAgB,EAChBjC,mBAAA,CAAsH,cAAAoB,gBAAA,CAA7GhB,MAAA,CAAAiB,WAAW,CAACa,kBAAkB,GAAGxB,MAAA,CAAAqB,UAAU,CAAC3B,MAAA,CAAAiB,WAAW,CAACa,kBAAkB,mBAAmBxB,MAAA,CAAAyB,OAAO,kBAC7GC,WAAiB,EACjBpC,mBAAA,CAA0G,QAA1GqC,WAA0G,EAAAjB,gBAAA,CAAlEhB,MAAA,CAAAiB,WAAW,CAACiB,MAAM,GAAGlC,MAAA,CAAAiB,WAAW,CAACiB,MAAM,CAAChB,IAAI,GAAGZ,MAAA,CAAA6B,UAAU,kBACjGC,WAAwB,EACxBxC,mBAAA,CAA8F,QAA9FyC,WAA8F,EAAArB,gBAAA,CAApDhB,MAAA,CAAAiB,WAAW,CAACqB,WAAW,qC,KAGrE1C,mBAAA,CASM,OATN2C,WASM,GARJ3C,mBAAA,CAGM,OAHN4C,WAGM,GAFJC,WAAiD,EACjD7C,mBAAA,CAAuE,QAAvE8C,WAAuE,EAAA1B,gBAAA,CAAjChB,MAAA,CAAAiB,WAAW,CAAC0B,IAAI,IAAIrC,MAAA,CAAAqC,IAAI,iB,GAEhE/C,mBAAA,CAGM,OAHNgD,WAGM,GAFJhD,mBAAA,CAAwE,KAAxEiD,WAAwE,EAAA7B,gBAAA,CAAtChB,MAAA,CAAAiB,WAAW,CAAC6B,oBAAoB,kBAClElD,mBAAA,CAAwH,KAAxHmD,WAAwH,EAAA/B,gBAAA,CAA1FhB,MAAA,CAAAiB,WAAW,CAAC+B,SAAS,GAAG1C,MAAA,CAAAqB,UAAU,CAAC3B,MAAA,CAAAiB,WAAW,CAAC+B,SAAS,mBAAmB1C,MAAA,CAAA0C,SAAS,iB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}