{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createCommentVNode as _createCommentVNode, withModifiers as _withModifiers, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-b50ef614\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  key: 0,\n  class: \"no-redirect\"\n};\nconst _hoisted_2 = [\"onClick\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_breadcrumb_item = _resolveComponent(\"el-breadcrumb-item\");\n  const _component_el_breadcrumb = _resolveComponent(\"el-breadcrumb\");\n  return _openBlock(), _createBlock(_component_el_breadcrumb, {\n    \"separator-class\": \"el-icon-arrow-right\"\n  }, {\n    default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.levelList, (item, index) => {\n      return _openBlock(), _createBlock(_component_el_breadcrumb_item, {\n        key: item.path\n      }, {\n        default: _withCtx(() => [index === $data.levelList.length - 1 ? (_openBlock(), _createElementBlock(\"span\", _hoisted_1, _toDisplayString(item.meta.title), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"a\", {\n          key: 1,\n          onClick: _withModifiers($event => $options.handleLink(item), [\"prevent\"])\n        }, _toDisplayString(item.meta.title), 9 /* TEXT, PROPS */, _hoisted_2))]),\n        _: 2 /* DYNAMIC */\n      }, 1024 /* DYNAMIC_SLOTS */);\n    }), 128 /* KEYED_FRAGMENT */))]),\n\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_el_breadcrumb", "_createElementBlock", "_Fragment", "_renderList", "$data", "levelList", "item", "index", "_component_el_breadcrumb_item", "key", "path", "length", "_hoisted_1", "_toDisplayString", "meta", "title", "onClick", "_withModifiers", "$event", "$options", "handleLink", "_hoisted_2"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/components/Breadcrumb/index.vue"], "sourcesContent": ["<template>\n  <el-breadcrumb separator-class=\"el-icon-arrow-right\">\n    <el-breadcrumb-item v-for=\"(item, index) in levelList\" :key=\"item.path\">\n      <span v-if=\"index === levelList.length - 1\" class=\"no-redirect\">{{item.meta.title}}</span>\n      <a v-else @click.prevent=\"handleLink(item)\">{{item.meta.title}}</a>\n    </el-breadcrumb-item>\n  </el-breadcrumb>\n</template>\n\n<script>\nimport * as pathToRegexp from \"path-to-regexp\"\nimport router from \"../../router\";\n\nexport default {\n  name: \"BreadcrumbIndex\",\n  data() {\n    return {\n      levelList: null\n    }\n  },\n  watch: {\n    $route() {\n      this.getBreadcrumb()\n    }\n  },\n  created() {\n    this.getBreadcrumb()\n  },\n  methods: {\n    getBreadcrumb() {\n      // only show routes with meta.title\n      let matched = this.$route.matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)\n      // const first = matched[0]\n      // if (!this.isIndex(first)) {\n      //   matched = [{ path: \"/index\", meta: { title: \"总览\" }}].concat(matched)\n      // }\n      this.levelList = matched\n      // this.levelList = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)\n    },\n    isIndex(route) {\n      const name = route && route.name\n      if (!name) {\n        return false\n      }\n      return name.trim().toLocaleLowerCase() === \"Index\".toLocaleLowerCase()\n    },\n    pathCompile(path) {\n      // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561\n      const { params } = this.$route\n      const toPath = pathToRegexp.compile(path)\n      return toPath(params)\n    },\n    handleLink(item) {\n      const { redirect, path } = item\n      console.log(redirect)\n      console.log(path)\n      if (redirect) {\n        router.push(redirect)\n        return\n      }\n      console.log(this.pathCompile(path))\n      router.push(this.pathCompile(path))\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.el-breadcrumb {\n  display: inline-block;\n  font-size: 12px;\n  line-height: 40px;\n  margin-left: 8px;\n}\n</style>\n"], "mappings": ";;;;EAGkDA,KAAK,EAAC;;;;;;uBAFtDC,YAAA,CAKgBC,wBAAA;IALD,iBAAe,EAAC;EAAqB;sBAC9B,MAAkC,E,kBAAtDC,mBAAA,CAGqBC,SAAA,QAAAC,WAAA,CAHuBC,KAAA,CAAAC,SAAS,GAAzBC,IAAI,EAAEC,KAAK;2BAAvCR,YAAA,CAGqBS,6BAAA;QAHmCC,GAAG,EAAEH,IAAI,CAACI;;0BAChE,MAA0F,CAA9EH,KAAK,KAAKH,KAAA,CAAAC,SAAS,CAACM,MAAM,Q,cAAtCV,mBAAA,CAA0F,QAA1FW,UAA0F,EAAAC,gBAAA,CAAxBP,IAAI,CAACQ,IAAI,CAACC,KAAK,qB,cACjFd,mBAAA,CAAmE;;UAAxDe,OAAK,EAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,UAAU,CAACd,IAAI;4BAAKA,IAAI,CAACQ,IAAI,CAACC,KAAK,wBAAAM,UAAA,G"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}