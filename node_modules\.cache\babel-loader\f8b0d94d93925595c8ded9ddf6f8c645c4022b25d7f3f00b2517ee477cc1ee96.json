{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport Page from \"@/components/Page\";\nimport { ref } from \"vue\";\nimport { findList } from \"@/api/learn/order\";\nimport { error, info } from \"@/util/tipsUtils\";\nimport { formatDate } from \"@/util/dateUtils\";\nexport default {\n  name: \"OrderList\",\n  components: {\n    Page\n  },\n  setup(props) {\n    const list = ref([]);\n    const total = ref(0);\n    const dataLoading = ref(true);\n    const selectCidList = ref([]);\n    const categoryOptions = ref([]);\n    const lessonIdList = ref([]);\n    const searchParam = ref({\n      keyword: \"\",\n      status: \"\",\n      startTime: \"\",\n      endTime: \"\",\n      size: 20,\n      current: 1\n    });\n    const statusMap = {\n      unpublished: \"未发布\",\n      published: \"已发布\",\n      deleted: \"已删除\"\n    };\n    const paymentChannelMap = {\n      wechat_pay: \"微信支付\",\n      alipay: \"支付宝\"\n    };\n    const orderStatusMap = {\n      waiting_payment: \"待支付\",\n      cancelled: \"已取消\",\n      closed: \"已关闭\",\n      waiting_delivery: \"待发货\",\n      shipped: \"已发货\",\n      some_shipped: \"部分已发货\"\n    };\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true;\n      findList(searchParam.value, res => {\n        dataLoading.value = false;\n        if (!res) {\n          return;\n        }\n        list.value = res.list;\n        total.value = res.total;\n      }).catch(() => {\n        dataLoading.value = false;\n      });\n    };\n    loadList();\n    // 搜索\n    const search = () => {\n      if (selectCidList.value && selectCidList.value.length > 0) {\n        searchParam.value.cid = selectCidList.value[selectCidList.value.length - 1];\n      }\n      loadList();\n    };\n    // 选择时间\n    const changeStartTime = val => {\n      if (val) {\n        searchParam.value.startTime = formatDate(val);\n      } else {\n        searchParam.value.startTime = val;\n      }\n      search();\n    };\n    // 选择时间\n    const changeEndTime = val => {\n      if (val) {\n        searchParam.value.endTime = formatDate(val);\n      } else {\n        searchParam.value.endTime = val;\n      }\n      search();\n    };\n    // 选择列表项\n    const selectItem = val => {\n      lessonIdList.value = [];\n      if (val && val.length > 0) {\n        for (const valElement of val) {\n          lessonIdList.value.push(valElement.id);\n        }\n      }\n    };\n    const currentChange = currentPage => {\n      searchParam.value.current = currentPage;\n      loadList();\n    };\n    const sizeChange = s => {\n      searchParam.value.size = s;\n      loadList();\n    };\n    // 查看评论\n    const selectTopic = ref({});\n    const drawer = ref(false);\n    const drawerClose = done => {\n      drawer.value = false;\n      done();\n    };\n    const commentView = item => {\n      drawer.value = true;\n      selectTopic.value = item;\n    };\n    const multipleSelection = ref([]);\n    const handleSelectionChange = val => {\n      multipleSelection.value = val;\n    };\n    const selectSelectionChange = () => {\n      if (!multipleSelection.value.length) {\n        error(\"请选择专题\");\n      }\n      props.selectCallback && props.selectCallback(multipleSelection.value);\n    };\n    return {\n      list,\n      total,\n      searchParam,\n      selectCidList,\n      categoryOptions,\n      lessonIdList,\n      search,\n      selectItem,\n      currentChange,\n      sizeChange,\n      dataLoading,\n      statusMap,\n      commentView,\n      selectTopic,\n      drawer,\n      drawerClose,\n      info,\n      handleSelectionChange,\n      selectSelectionChange,\n      paymentChannelMap,\n      orderStatusMap,\n      changeStartTime,\n      changeEndTime\n    };\n  }\n};", "map": {"version": 3, "names": ["Page", "ref", "findList", "error", "info", "formatDate", "name", "components", "setup", "props", "list", "total", "dataLoading", "selectCidList", "categoryOptions", "lessonIdList", "searchParam", "keyword", "status", "startTime", "endTime", "size", "current", "statusMap", "unpublished", "published", "deleted", "paymentChannelMap", "wechat_pay", "alipay", "orderStatusMap", "waiting_payment", "cancelled", "closed", "waiting_delivery", "shipped", "some_shipped", "loadList", "value", "res", "catch", "search", "length", "cid", "changeStartTime", "val", "changeEndTime", "selectItem", "valElement", "push", "id", "currentChange", "currentPage", "sizeChange", "s", "selectTopic", "drawer", "drawerClose", "done", "commentView", "item", "multipleSelection", "handleSelectionChange", "selectSelectionChange", "selectCallback"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/learn/order/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"header\">\n      <el-form :inline=\"true\" :model=\"searchParam\" class=\"form-inline\">\n        <el-form-item label=\"\">\n          <el-input class=\"search-input\" v-model=\"searchParam.keyword\" placeholder=\"请输入关键字\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"状态\" class=\"select\">\n          <el-select v-model=\"searchParam.status\" @change=\"search\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option v-for=\"(item, k) in orderStatusMap\" :label=\"item\" :value=\"k\" :key=\"k\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"日期\" class=\"select\">\n          <div style=\"display: flex;\">\n            <el-date-picker\n              v-model=\"searchParam.startTime\"\n              type=\"datetime\"\n              placeholder=\"订单开始时间\"\n              class=\"input-text\"\n              :default-time=\"new Date(2000, 0, 1, 0, 0, 0)\"\n              @change=\"changeStartTime\"\n              style=\"width: 100%;\"></el-date-picker>\n            <el-date-picker\n              v-model=\"searchParam.endTime\"\n              type=\"datetime\"\n              placeholder=\"订单结束时间\"\n              class=\"input-text\"\n              :default-time=\"new Date(2000, 0, 1, 22, 0, 0)\"\n              @change=\"changeEndTime\"\n              style=\"width: 100%;\"></el-date-picker>\n          </div>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"search\">搜索</el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"content\">\n      <div class=\"order-table-header\">\n        <div class=\"title-box width65 padding-10-0\">\n          <div class=\"image\"></div>\n          <div class=\"title\">商品信息</div>\n          <div class=\"width15\">单价</div>\n          <div class=\"width15\">数量</div>\n        </div>\n        <div class=\"width15 padding-10-0\">实付款</div>\n        <div class=\"width10 padding-10-0\">交易状态</div>\n        <div class=\"width10 padding-10-0\">操作</div>\n      </div>\n      <div class=\"order-table-list\" v-loading=\"dataLoading\">\n        <el-empty style=\"background-color: #FFFFFF;\" v-if=\"!(list && list.length)\"></el-empty>\n        <div v-else class=\"order-item\" v-for=\"item in list\" :key=\"item.id\">\n          <div class=\"order-header\">\n            <div class=\"member-info\" v-if=\"item.member\">\n              <img class=\"member-info-image\" :src=\"item.member.avatar\" />\n              <span>{{item.member.name}}</span>\n            </div>\n            <div class=\"order-no\">订单号：{{item.no}}</div>\n            <div class=\"create-time\">\n              下单时间：{{item.createTime}}\n            </div>\n          </div>\n          <div class=\"order-main\">\n            <div class=\"commodity-list width65\">\n              <div class=\"commodity-item\" v-for=\"c in item.itemList\" :key=\"c.id\">\n                <div class=\"image\">\n                  <el-image :src=\"c.image\">\n                    <template #error>\n                      <div class=\"image-slot\">\n                        <i class=\"el-icon-picture-outline\"></i>\n                      </div>\n                    </template>\n                  </el-image>\n                </div>\n                <div class=\"title-box\">\n                  <div class=\"title\">{{c.title}}</div>\n                </div>\n                <div class=\"price width15\">\n                  <div class=\"del\">￥{{c.price || 0}}</div>\n                  <div>￥{{c.paymentAmount || 0}}</div>\n                </div>\n                <div class=\"num width15\">{{c.quantity || 0}}</div>\n              </div>\n            </div>\n            <div class=\"real-price width15 padding-10-0\">\n              <div>￥{{item.paymentAmount || 0.00}}</div>\n              <div>(含运费：￥{{item.freightAmount || 0.00}})</div>\n              <div v-if=\"item.payment\">{{paymentChannelMap[item.payment.channel]}}</div>\n            </div>\n            <div class=\"order-status width10 padding-10-0\" :class=\"item.status\">{{orderStatusMap[item.status]}}</div>\n<!--            <div class=\"opt width10 padding-10-0\">详情</div>-->\n          </div>\n        </div>\n      </div>\n    </div>\n    <page :total=\"total\" :current-change=\"currentChange\" :size-change=\"sizeChange\" :page-size=\"searchParam.size\"></page>\n  </div>\n</template>\n\n<script>\nimport Page from \"@/components/Page\"\nimport {ref} from \"vue\"\nimport {findList} from \"@/api/learn/order\"\nimport {error, info} from \"@/util/tipsUtils\";\nimport {formatDate} from \"@/util/dateUtils\";\n\nexport default {\n  name: \"OrderList\",\n  components: {\n    Page\n  },\n  setup(props) {\n    const list = ref([])\n    const total = ref(0)\n    const dataLoading = ref(true)\n    const selectCidList = ref([])\n    const categoryOptions = ref([])\n    const lessonIdList = ref([])\n    const searchParam = ref({\n      keyword: \"\",\n      status: \"\",\n      startTime: \"\",\n      endTime: \"\",\n      size: 20,\n      current: 1\n    })\n    const statusMap = {\n      unpublished: \"未发布\",\n      published: \"已发布\",\n      deleted: \"已删除\"\n    }\n    const paymentChannelMap = {\n      wechat_pay: \"微信支付\",\n      alipay: \"支付宝\"\n    }\n    const orderStatusMap = {\n      waiting_payment: \"待支付\",\n      cancelled: \"已取消\",\n      closed: \"已关闭\",\n      waiting_delivery: \"待发货\",\n      shipped: \"已发货\",\n      some_shipped: \"部分已发货\"\n    }\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true\n      findList(searchParam.value, (res) => {\n        dataLoading.value = false\n        if (!res) {return;}\n        list.value = res.list;\n        total.value = res.total;\n      }).catch(() => {\n        dataLoading.value = false\n      })\n    }\n    loadList();\n    // 搜索\n    const search = () => {\n      if (selectCidList.value && selectCidList.value.length > 0) {\n        searchParam.value.cid = selectCidList.value[selectCidList.value.length - 1];\n      }\n      loadList();\n    }\n    // 选择时间\n    const changeStartTime = (val) => {\n      if (val) {\n        searchParam.value.startTime = formatDate(val)\n      } else {\n        searchParam.value.startTime = val\n      }\n      search()\n    }\n    // 选择时间\n    const changeEndTime = (val) => {\n      if (val) {\n        searchParam.value.endTime = formatDate(val)\n      } else {\n        searchParam.value.endTime = val\n      }\n      search()\n    }\n    // 选择列表项\n    const selectItem = (val) => {\n      lessonIdList.value = [];\n      if (val && val.length > 0) {\n        for (const valElement of val) {\n          lessonIdList.value.push(valElement.id);\n        }\n      }\n    }\n    const currentChange = (currentPage) => {\n      searchParam.value.current = currentPage;\n      loadList();\n    }\n    const sizeChange = (s) => {\n      searchParam.value.size = s;\n      loadList();\n    }\n    // 查看评论\n    const selectTopic = ref({})\n    const drawer = ref(false)\n    const drawerClose = (done) => {\n      drawer.value = false\n      done()\n    }\n    const commentView = (item) => {\n      drawer.value = true\n      selectTopic.value = item\n    }\n    const multipleSelection = ref([])\n    const handleSelectionChange = (val) => {\n      multipleSelection.value = val;\n    }\n    const selectSelectionChange = () => {\n      if (!multipleSelection.value.length) {\n        error(\"请选择专题\")\n      }\n      props.selectCallback && props.selectCallback(multipleSelection.value)\n    }\n    return {\n      list,\n      total,\n      searchParam,\n      selectCidList,\n      categoryOptions,\n      lessonIdList,\n      search,\n      selectItem,\n      currentChange,\n      sizeChange,\n      dataLoading,\n      statusMap,\n      commentView,\n      selectTopic,\n      drawer,\n      drawerClose,\n      info,\n      handleSelectionChange,\n      selectSelectionChange,\n      paymentChannelMap,\n      orderStatusMap,\n      changeStartTime,\n      changeEndTime\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.app-container {\n  margin: 20px;\n  .header {\n    .form-inline {\n      .search-input {\n        width: 242px;\n        ::v-deep .el-input__inner {\n          height: 34px;\n          line-height: 34px;\n          border-color: #f3f5f8;\n          &:focus, &:hover {\n            border-color: #f3f5f8;\n          }\n        }\n        ::v-deep .el-input__icon {\n          height: 34px;\n          line-height: 34px;\n          cursor: pointer;\n          &:hover {\n            color: $--color-primary;\n          }\n        }\n      }\n      .select {\n        ::v-deep .el-form-item__label {\n          font-size: 12px;\n        }\n        ::v-deep .el-input__inner {\n          height: 34px;\n          line-height: 34px;\n          border-color: #f3f5f8;\n        }\n      }\n      ::v-deep .el-form-item {\n        margin-bottom: 20px;\n      }\n    }\n  }\n  .content {\n    .order-table-header {\n      display: flex;\n      font-size: 12px;\n      text-align: center;\n      background-color: #ffffff;\n      line-height: 30px;\n      .width65 {\n        width: 65%;\n      }\n      .width15 {\n        width: 15%;\n      }\n      .width10 {\n        width: 10%;\n      }\n      .title-box {\n        display: flex;\n        .title {\n          width: 50%;\n        }\n      }\n      .image {\n        width: 80px;\n        //height: 45px;\n        margin-left: 20px;\n      }\n    }\n    .order-table-list{\n      .width10 {\n        width: 10%;\n      }\n      .width15 {\n        width: 15%;\n      }\n      .width65 {\n        width: 65%;\n      }\n      .padding-10-0 {\n        padding: 10px 0;\n      }\n      .order-item {\n        background-color: #FFFFFF;\n        font-size: 12px;\n        margin-bottom: 10px;\n        .order-header {\n          display: flex;\n          align-items: center;\n          line-height: 28px;\n          background-color: #f0f0f0;\n          .member-info {\n            margin: 5px;\n            display: flex;\n            .member-info-image {\n              margin-right: 5px;\n              width: 24px;\n              height: 24px;\n              border-radius: 50%;\n            }\n          }\n          .create-time {\n            margin-left: 20px;\n            font-weight: 500;\n          }\n          .order-no {\n            margin-left: 20px;\n            color: #222;\n          }\n        }\n        .order-main {\n          display: flex;\n          text-align: center;\n          line-height: 20px;\n          .commodity-list{\n            border-right: 1px solid #f0f0f0;\n            .commodity-item {\n              text-align: left;\n              display: flex;\n              border-bottom: 1px solid #f0f0f0;\n              padding: 10px 0;\n              &:last-child {\n                border: 0;\n              }\n              .image {\n                width: 80px;\n                height: 45px;\n                margin-left: 20px;\n                ::v-deep .el-image {\n                  width: 100%;\n                  height: 100%;\n                }\n              }\n              .title-box {\n                width: 50%;\n                padding: 2px 10px;\n              }\n              .price {\n                width: 15%;\n                text-align: center;\n              }\n              .del {\n                color: #9c9c9c;\n                text-decoration: line-through;\n              }\n              .num {\n                width: 15%;\n                text-align: center;\n              }\n            }\n          }\n          .real-price {\n            border-right: 1px solid #f0f0f0;\n          }\n          .order-status {\n            border-right: 1px solid #f0f0f0;\n          }\n          .waiting_payment {\n            color: #ffcf3d;\n          }\n          .cancelled {\n            color: #a2a19e;\n          }\n          .closed {\n            color: #a2a19e;\n          }\n          .waiting_delivery {\n            color: #be3838;\n          }\n          .shipped {\n            color: $--color-primary;\n          }\n          .some_shipped {\n\n          }\n          .opt {}\n        }\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";AAqGA,OAAOA,IAAG,MAAO,mBAAkB;AACnC,SAAQC,GAAG,QAAO,KAAI;AACtB,SAAQC,QAAQ,QAAO,mBAAkB;AACzC,SAAQC,KAAK,EAAEC,IAAI,QAAO,kBAAkB;AAC5C,SAAQC,UAAU,QAAO,kBAAkB;AAE3C,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAE;IACVP;EACF,CAAC;EACDQ,KAAKA,CAACC,KAAK,EAAE;IACX,MAAMC,IAAG,GAAIT,GAAG,CAAC,EAAE;IACnB,MAAMU,KAAI,GAAIV,GAAG,CAAC,CAAC;IACnB,MAAMW,WAAU,GAAIX,GAAG,CAAC,IAAI;IAC5B,MAAMY,aAAY,GAAIZ,GAAG,CAAC,EAAE;IAC5B,MAAMa,eAAc,GAAIb,GAAG,CAAC,EAAE;IAC9B,MAAMc,YAAW,GAAId,GAAG,CAAC,EAAE;IAC3B,MAAMe,WAAU,GAAIf,GAAG,CAAC;MACtBgB,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC;IACD,MAAMC,SAAQ,GAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE;IACX;IACA,MAAMC,iBAAgB,GAAI;MACxBC,UAAU,EAAE,MAAM;MAClBC,MAAM,EAAE;IACV;IACA,MAAMC,cAAa,GAAI;MACrBC,eAAe,EAAE,KAAK;MACtBC,SAAS,EAAE,KAAK;MAChBC,MAAM,EAAE,KAAK;MACbC,gBAAgB,EAAE,KAAK;MACvBC,OAAO,EAAE,KAAK;MACdC,YAAY,EAAE;IAChB;IACA;IACA,MAAMC,QAAO,GAAIA,CAAA,KAAM;MACrBzB,WAAW,CAAC0B,KAAI,GAAI,IAAG;MACvBpC,QAAQ,CAACc,WAAW,CAACsB,KAAK,EAAGC,GAAG,IAAK;QACnC3B,WAAW,CAAC0B,KAAI,GAAI,KAAI;QACxB,IAAI,CAACC,GAAG,EAAE;UAAC;QAAO;QAClB7B,IAAI,CAAC4B,KAAI,GAAIC,GAAG,CAAC7B,IAAI;QACrBC,KAAK,CAAC2B,KAAI,GAAIC,GAAG,CAAC5B,KAAK;MACzB,CAAC,CAAC,CAAC6B,KAAK,CAAC,MAAM;QACb5B,WAAW,CAAC0B,KAAI,GAAI,KAAI;MAC1B,CAAC;IACH;IACAD,QAAQ,EAAE;IACV;IACA,MAAMI,MAAK,GAAIA,CAAA,KAAM;MACnB,IAAI5B,aAAa,CAACyB,KAAI,IAAKzB,aAAa,CAACyB,KAAK,CAACI,MAAK,GAAI,CAAC,EAAE;QACzD1B,WAAW,CAACsB,KAAK,CAACK,GAAE,GAAI9B,aAAa,CAACyB,KAAK,CAACzB,aAAa,CAACyB,KAAK,CAACI,MAAK,GAAI,CAAC,CAAC;MAC7E;MACAL,QAAQ,EAAE;IACZ;IACA;IACA,MAAMO,eAAc,GAAKC,GAAG,IAAK;MAC/B,IAAIA,GAAG,EAAE;QACP7B,WAAW,CAACsB,KAAK,CAACnB,SAAQ,GAAId,UAAU,CAACwC,GAAG;MAC9C,OAAO;QACL7B,WAAW,CAACsB,KAAK,CAACnB,SAAQ,GAAI0B,GAAE;MAClC;MACAJ,MAAM,EAAC;IACT;IACA;IACA,MAAMK,aAAY,GAAKD,GAAG,IAAK;MAC7B,IAAIA,GAAG,EAAE;QACP7B,WAAW,CAACsB,KAAK,CAAClB,OAAM,GAAIf,UAAU,CAACwC,GAAG;MAC5C,OAAO;QACL7B,WAAW,CAACsB,KAAK,CAAClB,OAAM,GAAIyB,GAAE;MAChC;MACAJ,MAAM,EAAC;IACT;IACA;IACA,MAAMM,UAAS,GAAKF,GAAG,IAAK;MAC1B9B,YAAY,CAACuB,KAAI,GAAI,EAAE;MACvB,IAAIO,GAAE,IAAKA,GAAG,CAACH,MAAK,GAAI,CAAC,EAAE;QACzB,KAAK,MAAMM,UAAS,IAAKH,GAAG,EAAE;UAC5B9B,YAAY,CAACuB,KAAK,CAACW,IAAI,CAACD,UAAU,CAACE,EAAE,CAAC;QACxC;MACF;IACF;IACA,MAAMC,aAAY,GAAKC,WAAW,IAAK;MACrCpC,WAAW,CAACsB,KAAK,CAAChB,OAAM,GAAI8B,WAAW;MACvCf,QAAQ,EAAE;IACZ;IACA,MAAMgB,UAAS,GAAKC,CAAC,IAAK;MACxBtC,WAAW,CAACsB,KAAK,CAACjB,IAAG,GAAIiC,CAAC;MAC1BjB,QAAQ,EAAE;IACZ;IACA;IACA,MAAMkB,WAAU,GAAItD,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAMuD,MAAK,GAAIvD,GAAG,CAAC,KAAK;IACxB,MAAMwD,WAAU,GAAKC,IAAI,IAAK;MAC5BF,MAAM,CAAClB,KAAI,GAAI,KAAI;MACnBoB,IAAI,EAAC;IACP;IACA,MAAMC,WAAU,GAAKC,IAAI,IAAK;MAC5BJ,MAAM,CAAClB,KAAI,GAAI,IAAG;MAClBiB,WAAW,CAACjB,KAAI,GAAIsB,IAAG;IACzB;IACA,MAAMC,iBAAgB,GAAI5D,GAAG,CAAC,EAAE;IAChC,MAAM6D,qBAAoB,GAAKjB,GAAG,IAAK;MACrCgB,iBAAiB,CAACvB,KAAI,GAAIO,GAAG;IAC/B;IACA,MAAMkB,qBAAoB,GAAIA,CAAA,KAAM;MAClC,IAAI,CAACF,iBAAiB,CAACvB,KAAK,CAACI,MAAM,EAAE;QACnCvC,KAAK,CAAC,OAAO;MACf;MACAM,KAAK,CAACuD,cAAa,IAAKvD,KAAK,CAACuD,cAAc,CAACH,iBAAiB,CAACvB,KAAK;IACtE;IACA,OAAO;MACL5B,IAAI;MACJC,KAAK;MACLK,WAAW;MACXH,aAAa;MACbC,eAAe;MACfC,YAAY;MACZ0B,MAAM;MACNM,UAAU;MACVI,aAAa;MACbE,UAAU;MACVzC,WAAW;MACXW,SAAS;MACToC,WAAW;MACXJ,WAAW;MACXC,MAAM;MACNC,WAAW;MACXrD,IAAI;MACJ0D,qBAAqB;MACrBC,qBAAqB;MACrBpC,iBAAiB;MACjBG,cAAc;MACdc,eAAe;MACfE;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}