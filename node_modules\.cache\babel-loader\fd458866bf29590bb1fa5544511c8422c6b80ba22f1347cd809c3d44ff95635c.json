{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, watch } from \"vue\";\nimport router from \"../../../router\";\nimport { findCategoryList, toTree, getCategory, saveCategory, updateCategory } from \"../../../api/circle/category\";\nimport uploadImage from \"../../../components/Uplaod/index\";\nimport { success, error } from \"../../../util/tipsUtils\";\nexport default {\n  name: \"CircleCategoryEdit\",\n  components: {\n    uploadImage\n  },\n  props: {\n    data: {\n      type: Object,\n      required: true\n    },\n    pid: {\n      type: Number,\n      required: true\n    },\n    editSuccess: {\n      type: Function\n    },\n    editCancel: {\n      type: Function\n    }\n  },\n  setup(props) {\n    let selectedPidList = ref([]);\n    const categoryOptions = ref([]);\n    const parentCategory = ref({});\n    const uploadData = {\n      url: process.env.VUE_APP_BASE_API + \"/oss/circle/category/image\",\n      files: []\n    };\n    const rules = {\n      pid: [{\n        required: true,\n        message: \"请选择上级分类\",\n        trigger: \"blur\"\n      }],\n      name: [{\n        required: true,\n        message: \"请输入分类名称\",\n        trigger: \"blur\"\n      }],\n      picture: [{\n        required: true,\n        message: \"请上传分类图片\",\n        trigger: \"blur\"\n      }]\n    };\n    let category = ref({\n      pid: 0,\n      name: \"\",\n      image: \"\",\n      sortOrder: 1,\n      isShow: true,\n      isShowIndex: true\n    });\n    const init = (item, pid) => {\n      if (pid) {\n        getCategory(pid, res => {\n          if (!res) {\n            error(\"没有找到该分类\");\n            return;\n          }\n          parentCategory.value = {\n            \"id\": res.id,\n            \"name\": res.name,\n            \"sortOrder\": res.sortOrder,\n            \"isShow\": res.isShow,\n            \"isShowIndex\": res.isShowIndex,\n            \"image\": res.image,\n            \"pid\": res.pid\n          };\n        });\n      } else {\n        parentCategory.value = {\n          id: 0,\n          name: \"全部\"\n        };\n      }\n      if (item && item.id) {\n        category = ref({\n          \"id\": item.id,\n          \"name\": item.name,\n          \"sortOrder\": item.sortOrder,\n          \"isShow\": item.isShow,\n          \"isShowIndex\": item.isShowIndex,\n          \"image\": item.image,\n          \"pid\": item.pid\n        });\n        if (item.image) {\n          uploadData.files = [{\n            name: item.name,\n            url: item.image\n          }];\n        }\n      }\n      category.value.pid = pid || 0;\n      selectedPidList.value.push(category.value.pid);\n    };\n    init(props.data, props.pid);\n    watch(() => props.data, nv => {\n      console.log(\"监听到data改变了\");\n      console.log(nv);\n      init(nv, nv.pid);\n      category = ref(nv);\n    });\n    const loadCategory = () => {\n      findCategoryList(0, true).then(function (response) {\n        if (response) {\n          categoryOptions.value = toTree(response);\n        }\n      });\n    };\n    loadCategory();\n    const changeParentCategory = () => {\n      if (category.value.selectedPidList && category.value.selectedPidList.length > 0) {\n        let id = selectedPidList.value[selectedPidList.value.length - 1];\n        if (id === category.value.id) {\n          error(\"不能选择自己为上级分类\");\n          return;\n        }\n        category.value.pid = id;\n      }\n    };\n    const cancel = () => {\n      props.editCancel && props.editCancel();\n    };\n    const onUploadSuccess = res => {\n      category.value.image = res.data;\n    };\n    const onUploadRemove = () => {\n      if (!category.value.image) {\n        return;\n      }\n      category.value.image = \"\";\n      uploadData.value.files = [];\n    };\n    const categoryRef = ref(null);\n    const submit = () => {\n      categoryRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (!category.value.pid && category.value.pid !== 0) {\n          error(\"请选择上级分类\");\n          return false;\n        }\n        if (category.value.id) {\n          updateCategory(category.value, res => {\n            success(\"编辑成功\");\n            router.push({\n              path: \"/circle/category\",\n              query: {\n                id: res[\"id\"]\n              }\n            });\n            props.editSuccess && props.editSuccess(res[\"id\"]);\n          });\n        } else {\n          saveCategory(category.value, res => {\n            success(\"新增成功\");\n            router.push({\n              path: \"/circle/category\",\n              query: {\n                id: res[\"id\"]\n              }\n            });\n            props.editSuccess && props.editSuccess(res[\"id\"]);\n          });\n        }\n      });\n    };\n    return {\n      selectedPidList,\n      categoryOptions,\n      parentCategory,\n      category,\n      rules,\n      uploadData,\n      categoryRef,\n      loadCategory,\n      changeParentCategory,\n      cancel,\n      onUploadSuccess,\n      onUploadRemove,\n      submit\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "watch", "router", "findCategoryList", "toTree", "getCategory", "saveCategory", "updateCategory", "uploadImage", "success", "error", "name", "components", "props", "data", "type", "Object", "required", "pid", "Number", "editSuccess", "Function", "editCancel", "setup", "selectedPidList", "categoryOptions", "parentCategory", "uploadData", "url", "process", "env", "VUE_APP_BASE_API", "files", "rules", "message", "trigger", "picture", "category", "image", "sortOrder", "isShow", "isShowIndex", "init", "item", "res", "value", "id", "push", "nv", "console", "log", "loadCategory", "then", "response", "changeParentCategory", "length", "cancel", "onUploadSuccess", "onUploadRemove", "categoryRef", "submit", "validate", "valid", "path", "query"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/circle/category/edit.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form ref=\"categoryRef\" :rules=\"rules\" :model=\"category\" label-width=\"110px\">\n      <el-form-item label=\"上级分类\" prop=\"pid\">\n        <el-input size=\"small\" v-if=\"parentCategory.name\" type=\"text\" class=\"input-text\" disabled v-model=\"parentCategory.name\"></el-input>\n        <el-cascader size=\"small\" v-else class=\"input-text\" :props=\"{checkStrictly: true}\" v-model=\"selectedPidList\" :options=\"categoryOptions\" placeholder=\"请选择上级分类\" @change=\"changeParentCategory\"></el-cascader>\n      </el-form-item>\n      <el-form-item label=\"分类名称\" prop=\"name\">\n        <el-input size=\"small\" maxlength=\"15\" show-word-limit class=\"input-text\" v-model=\"category.name\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"分类图片\" prop=\"image\">\n        <upload-image :limit=\"1\" :files=\"uploadData.files\" :on-upload-success=\"onUploadSuccess\" :on-upload-remove=\"onUploadRemove\" :upload-url=\"uploadData.url\"></upload-image>\n      </el-form-item>\n      <el-form-item label=\"排序\" prop=\"sortOrder\">\n        <el-input size=\"small\" class=\"input-text\" v-model=\"category.sortOrder\" placeholder=\"数据越大显示越前\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"是否显示\" prop=\"isShow\">\n        <el-switch size=\"small\" id=\"isShow\" active-color=\"#13ce66\" v-model=\"category.isShow\"></el-switch>\n      </el-form-item>\n      <el-form-item label=\"是否在首页显示\" prop=\"isShowIndex\">\n        <el-switch size=\"small\" id=\"isShowIndex\" active-color=\"#13ce66\" v-model=\"category.isShowIndex\"></el-switch>\n      </el-form-item>\n    </el-form>\n    <div class=\"dialog-footer\">\n      <el-button size=\"small\" @click=\"cancel()\">取 消</el-button>\n      <el-button size=\"small\" type=\"primary\" @click=\"submit()\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\n  import {ref, watch} from \"vue\"\n  import router from \"../../../router\"\n  import {findCategoryList, toTree, getCategory, saveCategory, updateCategory} from \"../../../api/circle/category\"\n  import uploadImage from \"../../../components/Uplaod/index\";\n  import {success, error} from \"../../../util/tipsUtils\";\n  export default {\n    name: \"CircleCategoryEdit\",\n    components: {\n      uploadImage\n    },\n    props: {\n      data: {\n        type: Object,\n        required: true\n      },\n      pid: {\n        type: Number,\n        required: true\n      },\n      editSuccess: {\n        type: Function\n      },\n      editCancel: {\n        type: Function\n      }\n    },\n    setup(props) {\n      let selectedPidList = ref([])\n      const categoryOptions = ref([])\n      const parentCategory = ref({})\n      const uploadData = {\n        url: process.env.VUE_APP_BASE_API + \"/oss/circle/category/image\",\n        files: []\n      }\n      const rules = {\n        pid: [{ required: true, message: \"请选择上级分类\", trigger: \"blur\" }],\n        name: [{ required: true, message: \"请输入分类名称\", trigger: \"blur\" }],\n        picture: [{ required: true, message: \"请上传分类图片\", trigger: \"blur\" }]\n      }\n      let category = ref({\n        pid: 0,\n        name: \"\",\n        image: \"\",\n        sortOrder: 1,\n        isShow: true,\n        isShowIndex: true\n      })\n      const init = (item, pid) => {\n        if (pid) {\n          getCategory(pid, res => {\n            if (!res) {\n              error(\"没有找到该分类\")\n              return;\n            }\n            parentCategory.value = {\n              \"id\": res.id,\n              \"name\": res.name,\n              \"sortOrder\": res.sortOrder,\n              \"isShow\": res.isShow,\n              \"isShowIndex\": res.isShowIndex,\n              \"image\": res.image,\n              \"pid\": res.pid\n            };\n          });\n        } else {\n          parentCategory.value = {id: 0, name: \"全部\"};\n        }\n        if (item && item.id) {\n          category = ref({\n            \"id\": item.id,\n            \"name\": item.name,\n            \"sortOrder\": item.sortOrder,\n            \"isShow\": item.isShow,\n            \"isShowIndex\": item.isShowIndex,\n            \"image\": item.image,\n            \"pid\": item.pid\n          });\n          if (item.image) {\n            uploadData.files = [{name: item.name, url: item.image}]\n          }\n        }\n        category.value.pid = pid || 0;\n        selectedPidList.value.push(category.value.pid);\n      }\n      init(props.data, props.pid)\n      watch(() => props.data, (nv) => {\n        console.log(\"监听到data改变了\")\n        console.log(nv)\n        init(nv, nv.pid)\n        category = ref(nv)\n      })\n      const loadCategory = () => {\n        findCategoryList(0, true).then(function (response) {\n          if (response) {\n            categoryOptions.value = toTree(response);\n          }\n        });\n      }\n      loadCategory();\n      const changeParentCategory = () => {\n        if (category.value.selectedPidList && category.value.selectedPidList.length > 0) {\n          let id = selectedPidList.value[selectedPidList.value.length - 1];\n          if (id === category.value.id) {\n            error(\"不能选择自己为上级分类\")\n            return;\n          }\n          category.value.pid = id;\n        }\n      }\n      const cancel = () => {\n        props.editCancel && props.editCancel()\n      }\n      const onUploadSuccess = (res) => {\n        category.value.image = res.data;\n      }\n      const onUploadRemove = () => {\n        if (!category.value.image) {\n          return;\n        }\n        category.value.image = \"\";\n        uploadData.value.files = [];\n      }\n      const categoryRef = ref(null)\n      const submit = () => {\n        categoryRef.value.validate(valid => {\n          if (!valid) {\n            return false;\n          }\n          if (!category.value.pid && category.value.pid !== 0) {\n            error(\"请选择上级分类\")\n            return false;\n          }\n          if (category.value.id) {\n            updateCategory(category.value, (res) => {\n              success(\"编辑成功\")\n              router.push({path: \"/circle/category\", query:{ id: res[\"id\"]}});\n              props.editSuccess && props.editSuccess(res[\"id\"])\n            })\n          } else {\n            saveCategory(category.value, (res) => {\n              success(\"新增成功\")\n              router.push({path: \"/circle/category\", query:{ id: res[\"id\"]}});\n              props.editSuccess && props.editSuccess(res[\"id\"])\n            })\n          }\n        });\n      }\n      return {\n        selectedPidList,\n        categoryOptions,\n        parentCategory,\n        category,\n        rules,\n        uploadData,\n        categoryRef,\n        loadCategory,\n        changeParentCategory,\n        cancel,\n        onUploadSuccess,\n        onUploadRemove,\n        submit\n      }\n    }\n  }\n</script>\n<style scoped lang=\"scss\">\n.dialog-footer {\n  text-align: center;\n}\n.input-text {\n  width: 80%;\n}\n</style>\n"], "mappings": ";AA+BE,SAAQA,GAAG,EAAEC,KAAK,QAAO,KAAI;AAC7B,OAAOC,MAAK,MAAO,iBAAgB;AACnC,SAAQC,gBAAgB,EAAEC,MAAM,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc,QAAO,8BAA6B;AAC/G,OAAOC,WAAU,MAAO,kCAAkC;AAC1D,SAAQC,OAAO,EAAEC,KAAK,QAAO,yBAAyB;AACtD,eAAe;EACbC,IAAI,EAAE,oBAAoB;EAC1BC,UAAU,EAAE;IACVJ;EACF,CAAC;EACDK,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,GAAG,EAAE;MACHH,IAAI,EAAEI,MAAM;MACZF,QAAQ,EAAE;IACZ,CAAC;IACDG,WAAW,EAAE;MACXL,IAAI,EAAEM;IACR,CAAC;IACDC,UAAU,EAAE;MACVP,IAAI,EAAEM;IACR;EACF,CAAC;EACDE,KAAKA,CAACV,KAAK,EAAE;IACX,IAAIW,eAAc,GAAIxB,GAAG,CAAC,EAAE;IAC5B,MAAMyB,eAAc,GAAIzB,GAAG,CAAC,EAAE;IAC9B,MAAM0B,cAAa,GAAI1B,GAAG,CAAC,CAAC,CAAC;IAC7B,MAAM2B,UAAS,GAAI;MACjBC,GAAG,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAe,GAAI,4BAA4B;MAChEC,KAAK,EAAE;IACT;IACA,MAAMC,KAAI,GAAI;MACZf,GAAG,EAAE,CAAC;QAAED,QAAQ,EAAE,IAAI;QAAEiB,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9DxB,IAAI,EAAE,CAAC;QAAEM,QAAQ,EAAE,IAAI;QAAEiB,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC/DC,OAAO,EAAE,CAAC;QAAEnB,QAAQ,EAAE,IAAI;QAAEiB,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC;IACnE;IACA,IAAIE,QAAO,GAAIrC,GAAG,CAAC;MACjBkB,GAAG,EAAE,CAAC;MACNP,IAAI,EAAE,EAAE;MACR2B,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,CAAC;MACZC,MAAM,EAAE,IAAI;MACZC,WAAW,EAAE;IACf,CAAC;IACD,MAAMC,IAAG,GAAIA,CAACC,IAAI,EAAEzB,GAAG,KAAK;MAC1B,IAAIA,GAAG,EAAE;QACPb,WAAW,CAACa,GAAG,EAAE0B,GAAE,IAAK;UACtB,IAAI,CAACA,GAAG,EAAE;YACRlC,KAAK,CAAC,SAAS;YACf;UACF;UACAgB,cAAc,CAACmB,KAAI,GAAI;YACrB,IAAI,EAAED,GAAG,CAACE,EAAE;YACZ,MAAM,EAAEF,GAAG,CAACjC,IAAI;YAChB,WAAW,EAAEiC,GAAG,CAACL,SAAS;YAC1B,QAAQ,EAAEK,GAAG,CAACJ,MAAM;YACpB,aAAa,EAAEI,GAAG,CAACH,WAAW;YAC9B,OAAO,EAAEG,GAAG,CAACN,KAAK;YAClB,KAAK,EAAEM,GAAG,CAAC1B;UACb,CAAC;QACH,CAAC,CAAC;MACJ,OAAO;QACLQ,cAAc,CAACmB,KAAI,GAAI;UAACC,EAAE,EAAE,CAAC;UAAEnC,IAAI,EAAE;QAAI,CAAC;MAC5C;MACA,IAAIgC,IAAG,IAAKA,IAAI,CAACG,EAAE,EAAE;QACnBT,QAAO,GAAIrC,GAAG,CAAC;UACb,IAAI,EAAE2C,IAAI,CAACG,EAAE;UACb,MAAM,EAAEH,IAAI,CAAChC,IAAI;UACjB,WAAW,EAAEgC,IAAI,CAACJ,SAAS;UAC3B,QAAQ,EAAEI,IAAI,CAACH,MAAM;UACrB,aAAa,EAAEG,IAAI,CAACF,WAAW;UAC/B,OAAO,EAAEE,IAAI,CAACL,KAAK;UACnB,KAAK,EAAEK,IAAI,CAACzB;QACd,CAAC,CAAC;QACF,IAAIyB,IAAI,CAACL,KAAK,EAAE;UACdX,UAAU,CAACK,KAAI,GAAI,CAAC;YAACrB,IAAI,EAAEgC,IAAI,CAAChC,IAAI;YAAEiB,GAAG,EAAEe,IAAI,CAACL;UAAK,CAAC;QACxD;MACF;MACAD,QAAQ,CAACQ,KAAK,CAAC3B,GAAE,GAAIA,GAAE,IAAK,CAAC;MAC7BM,eAAe,CAACqB,KAAK,CAACE,IAAI,CAACV,QAAQ,CAACQ,KAAK,CAAC3B,GAAG,CAAC;IAChD;IACAwB,IAAI,CAAC7B,KAAK,CAACC,IAAI,EAAED,KAAK,CAACK,GAAG;IAC1BjB,KAAK,CAAC,MAAMY,KAAK,CAACC,IAAI,EAAGkC,EAAE,IAAK;MAC9BC,OAAO,CAACC,GAAG,CAAC,YAAY;MACxBD,OAAO,CAACC,GAAG,CAACF,EAAE;MACdN,IAAI,CAACM,EAAE,EAAEA,EAAE,CAAC9B,GAAG;MACfmB,QAAO,GAAIrC,GAAG,CAACgD,EAAE;IACnB,CAAC;IACD,MAAMG,YAAW,GAAIA,CAAA,KAAM;MACzBhD,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC,CAACiD,IAAI,CAAC,UAAUC,QAAQ,EAAE;QACjD,IAAIA,QAAQ,EAAE;UACZ5B,eAAe,CAACoB,KAAI,GAAIzC,MAAM,CAACiD,QAAQ,CAAC;QAC1C;MACF,CAAC,CAAC;IACJ;IACAF,YAAY,EAAE;IACd,MAAMG,oBAAmB,GAAIA,CAAA,KAAM;MACjC,IAAIjB,QAAQ,CAACQ,KAAK,CAACrB,eAAc,IAAKa,QAAQ,CAACQ,KAAK,CAACrB,eAAe,CAAC+B,MAAK,GAAI,CAAC,EAAE;QAC/E,IAAIT,EAAC,GAAItB,eAAe,CAACqB,KAAK,CAACrB,eAAe,CAACqB,KAAK,CAACU,MAAK,GAAI,CAAC,CAAC;QAChE,IAAIT,EAAC,KAAMT,QAAQ,CAACQ,KAAK,CAACC,EAAE,EAAE;UAC5BpC,KAAK,CAAC,aAAa;UACnB;QACF;QACA2B,QAAQ,CAACQ,KAAK,CAAC3B,GAAE,GAAI4B,EAAE;MACzB;IACF;IACA,MAAMU,MAAK,GAAIA,CAAA,KAAM;MACnB3C,KAAK,CAACS,UAAS,IAAKT,KAAK,CAACS,UAAU,EAAC;IACvC;IACA,MAAMmC,eAAc,GAAKb,GAAG,IAAK;MAC/BP,QAAQ,CAACQ,KAAK,CAACP,KAAI,GAAIM,GAAG,CAAC9B,IAAI;IACjC;IACA,MAAM4C,cAAa,GAAIA,CAAA,KAAM;MAC3B,IAAI,CAACrB,QAAQ,CAACQ,KAAK,CAACP,KAAK,EAAE;QACzB;MACF;MACAD,QAAQ,CAACQ,KAAK,CAACP,KAAI,GAAI,EAAE;MACzBX,UAAU,CAACkB,KAAK,CAACb,KAAI,GAAI,EAAE;IAC7B;IACA,MAAM2B,WAAU,GAAI3D,GAAG,CAAC,IAAI;IAC5B,MAAM4D,MAAK,GAAIA,CAAA,KAAM;MACnBD,WAAW,CAACd,KAAK,CAACgB,QAAQ,CAACC,KAAI,IAAK;QAClC,IAAI,CAACA,KAAK,EAAE;UACV,OAAO,KAAK;QACd;QACA,IAAI,CAACzB,QAAQ,CAACQ,KAAK,CAAC3B,GAAE,IAAKmB,QAAQ,CAACQ,KAAK,CAAC3B,GAAE,KAAM,CAAC,EAAE;UACnDR,KAAK,CAAC,SAAS;UACf,OAAO,KAAK;QACd;QACA,IAAI2B,QAAQ,CAACQ,KAAK,CAACC,EAAE,EAAE;UACrBvC,cAAc,CAAC8B,QAAQ,CAACQ,KAAK,EAAGD,GAAG,IAAK;YACtCnC,OAAO,CAAC,MAAM;YACdP,MAAM,CAAC6C,IAAI,CAAC;cAACgB,IAAI,EAAE,kBAAkB;cAAEC,KAAK,EAAC;gBAAElB,EAAE,EAAEF,GAAG,CAAC,IAAI;cAAC;YAAC,CAAC,CAAC;YAC/D/B,KAAK,CAACO,WAAU,IAAKP,KAAK,CAACO,WAAW,CAACwB,GAAG,CAAC,IAAI,CAAC;UAClD,CAAC;QACH,OAAO;UACLtC,YAAY,CAAC+B,QAAQ,CAACQ,KAAK,EAAGD,GAAG,IAAK;YACpCnC,OAAO,CAAC,MAAM;YACdP,MAAM,CAAC6C,IAAI,CAAC;cAACgB,IAAI,EAAE,kBAAkB;cAAEC,KAAK,EAAC;gBAAElB,EAAE,EAAEF,GAAG,CAAC,IAAI;cAAC;YAAC,CAAC,CAAC;YAC/D/B,KAAK,CAACO,WAAU,IAAKP,KAAK,CAACO,WAAW,CAACwB,GAAG,CAAC,IAAI,CAAC;UAClD,CAAC;QACH;MACF,CAAC,CAAC;IACJ;IACA,OAAO;MACLpB,eAAe;MACfC,eAAe;MACfC,cAAc;MACdW,QAAQ;MACRJ,KAAK;MACLN,UAAU;MACVgC,WAAW;MACXR,YAAY;MACZG,oBAAoB;MACpBE,MAAM;MACNC,eAAe;MACfC,cAAc;MACdE;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}