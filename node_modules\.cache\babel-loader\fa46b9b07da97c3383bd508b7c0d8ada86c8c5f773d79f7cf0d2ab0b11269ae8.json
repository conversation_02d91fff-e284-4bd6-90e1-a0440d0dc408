{"ast": null, "code": "import ElementPlus from \"element-plus\";\nimport locale from \"element-plus/lib/locale/lang/zh-cn\";\nimport \"./element-variables.scss\";\nexport default (app => {\n  app.use(ElementPlus, {\n    locale\n  });\n  return ElementPlus;\n});", "map": {"version": 3, "names": ["ElementPlus", "locale", "app", "use"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/plugins/element/element.js"], "sourcesContent": ["import ElementPlus from \"element-plus\"\nimport locale from \"element-plus/lib/locale/lang/zh-cn\"\nimport \"./element-variables.scss\"\n\nexport default (app) => {\n  app.use(ElementPlus, { locale });\n  return ElementPlus;\n}\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,cAAc;AACtC,OAAOC,MAAM,MAAM,oCAAoC;AACvD,OAAO,0BAA0B;AAEjC,gBAAgBC,GAAG,IAAK;EACtBA,GAAG,CAACC,GAAG,CAACH,WAAW,EAAE;IAAEC;EAAO,CAAC,CAAC;EAChC,OAAOD,WAAW;AACpB,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}