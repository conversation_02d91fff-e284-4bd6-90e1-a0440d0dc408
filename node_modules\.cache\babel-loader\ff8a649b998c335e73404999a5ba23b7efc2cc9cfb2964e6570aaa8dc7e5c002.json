{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref } from \"vue\";\nimport router from \"../../../router\";\nimport { deleteLecturer, findList } from \"@/api/lecturer\";\nimport Page from \"../../../components/Page\";\nimport { confirm, success } from \"@/util/tipsUtils\";\nexport default {\n  name: \"LecturerIndex\",\n  components: {\n    Page\n  },\n  setup() {\n    const statusMap = {\n      \"published\": \"已发布\",\n      \"delete\": \"已删除\"\n    };\n    const list = ref([]);\n    const total = ref(0);\n    const dataLoading = ref(true);\n    const searchParam = ref({\n      keyword: \"\",\n      status: \"\",\n      size: 20,\n      current: 1\n    });\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true;\n      findList(searchParam.value, res => {\n        dataLoading.value = false;\n        if (!res) {\n          return;\n        }\n        list.value = res.list;\n        total.value = res.total;\n      });\n    };\n    loadList();\n    // 搜索\n    const search = () => {\n      loadList();\n    };\n    // 编辑\n    const edit = id => {\n      router.push({\n        path: \"/live/lecturer/edit\",\n        query: {\n          id: id\n        }\n      });\n    };\n    // 编辑\n    const remove = item => {\n      confirm(\"确认删除讲师 \" + item.name + \" 吗？\", \"提示\", () => {\n        deleteLecturer(item.id, () => {\n          success(\"删除成功\");\n          loadList();\n        });\n      }, () => {});\n    };\n    const currentChange = currentPage => {\n      searchParam.value.current = currentPage;\n      loadList();\n    };\n    const sizeChange = s => {\n      searchParam.value.size = s;\n      loadList();\n    };\n    return {\n      list,\n      total,\n      searchParam,\n      search,\n      edit,\n      currentChange,\n      sizeChange,\n      remove,\n      statusMap,\n      dataLoading\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "router", "deleteLecturer", "findList", "Page", "confirm", "success", "name", "components", "setup", "statusMap", "list", "total", "dataLoading", "searchParam", "keyword", "status", "size", "current", "loadList", "value", "res", "search", "edit", "id", "push", "path", "query", "remove", "item", "currentChange", "currentPage", "sizeChange", "s"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/live/lecturer/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"header\">\n      <el-form :inline=\"true\" :model=\"searchParam\" class=\"demo-form-inline\">\n        <el-form-item label=\"\">\n          <el-input size=\"small\" class=\"search-input\" v-model=\"searchParam.keyword\" placeholder=\"请输入关键字\"></el-input>\n          <el-button size=\"small\" class=\"search-btn\" type=\"primary\" @click=\"search\">搜索</el-button>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" type=\"primary\" @click=\"edit()\">\n            <el-icon><Plus /></el-icon>\n            新增\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"content\" v-loading=\"dataLoading\">\n      <div class=\"content-list\">\n        <el-empty v-if=\"!list || !list.length\"/>\n        <div class=\"content-item\" v-for=\"item in list\" :key=\"item.id + ''\">\n          <div class=\"content-item-warp\">\n            <a class=\"image\">\n              <img :src=\"item.image\">\n            </a>\n            <div class=\"article-card-bone\">\n              <div class=\"title-wrap\">\n                <a class=\"title\">{{item.userName}}</a>\n                <span class=\"label create-time\">{{item.createTime}}</span>\n              </div>\n              <div class=\"abstruct\">\n                <div class=\"status\" v-if=\"item.status\">{{statusMap[item.status]}}</div>\n                <div class=\"divider\" v-if=\"item.status && item.jobTitle\"></div>\n                <div class=\"status\" style=\"background: #07c160;color: #fff;\" v-if=\"item.jobTitle\">{{item.jobTitle}}</div>\n                <div class=\"divider\" v-if=\"item.mobile\"></div>\n                <div class=\"status\" style=\"background: green;color: #fff;\" v-if=\"item.mobile\">{{item.mobile}}</div>\n              </div>\n              <div class=\"count-wrapper\">\n                <ul class=\"count\">\n                  <li>直播 {{item.lessonNum || 0}}</li>\n                </ul>\n                <div class=\"article-action-list\">\n                  <span class=\"icon-label\" @click=\"edit(item.id)\">修改</span>\n                  <span class=\"icon-label\" @click=\"remove(item)\">删除</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <page :total=\"total\" :current-change=\"currentChange\" :size-change=\"sizeChange\" :page-size=\"searchParam.size\"></page>\n  </div>\n</template>\n\n<script>\n  import {ref} from \"vue\"\n  import router from \"../../../router\"\n  import {deleteLecturer, findList} from \"@/api/lecturer\"\n  import Page from \"../../../components/Page\"\n  import {confirm, success} from \"@/util/tipsUtils\";\n\n  export default {\n    name: \"LecturerIndex\",\n  components: {\n    Page,\n  },\n  setup() {\n    const statusMap = {\n      \"published\": \"已发布\",\n      \"delete\": \"已删除\"\n    }\n    const list = ref([])\n    const total = ref(0)\n    const dataLoading = ref(true)\n    const searchParam = ref({\n      keyword: \"\",\n      status: \"\",\n      size: 20,\n      current: 1\n    })\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true\n      findList(searchParam.value, (res) => {\n        dataLoading.value = false\n        if (!res) {return;}\n        list.value = res.list;\n        total.value = res.total;\n      })\n    }\n    loadList();\n    // 搜索\n    const search = () => {\n      loadList();\n    }\n    // 编辑\n    const edit = (id) => {\n      router.push({path: \"/live/lecturer/edit\", query: { id : id }})\n    }\n    // 编辑\n    const remove = (item) => {\n      confirm(\"确认删除讲师 \" + item.name + \" 吗？\", \"提示\", () => {\n        deleteLecturer(item.id, () => {\n          success(\"删除成功\")\n          loadList()\n        })\n      }, () => {\n      })\n    }\n    const currentChange = (currentPage) => {\n      searchParam.value.current = currentPage;\n      loadList();\n    }\n    const sizeChange = (s) => {\n      searchParam.value.size = s;\n      loadList();\n    }\n    return {\n      list,\n      total,\n      searchParam,\n      search,\n      edit,\n      currentChange,\n      sizeChange,\n      remove,\n      statusMap,\n      dataLoading,\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n  .app-container {\n    margin: 20px;\n    .content-list {\n      margin: 0;\n      padding: 0;\n      border: 0;\n      font: inherit;\n      vertical-align: baseline;\n      .content-item {\n        padding: 24px 12px;\n        line-height: 1;\n        font-size: 14px;\n        color: #666;\n        border-bottom: 1px solid #e8e8e8;\n        position: relative;\n        background: #ffffff;\n        &:last-child {\n          border-bottom: 0;\n        }\n        .content-item-warp {\n          position: relative;\n          display: flex;\n          .image {\n            width: 108px;\n            min-width: 108px;\n            height: 108px;\n            margin-right: 24px;\n            position: relative;\n            overflow: hidden;\n            border-radius: 4px;\n            border: 1px solid #e8e8e8;\n            cursor: default;\n            img {\n              width: 100%;\n              height: 100%;\n              transition: all .5s ease-out .1s;\n              -o-object-fit: cover;\n              object-fit: cover;\n              -o-object-position: center;\n              object-position: center;\n              &:hover {\n                transform: matrix(1.04,0,0,1.04,0,0);\n                -webkit-backface-visibility: hidden;\n                backface-visibility: hidden;\n              }\n            }\n          }\n          .article-card-bone {\n            width: 100%;\n            display: flex;\n            flex-direction: column;\n            min-width: 0;\n            .title-wrap {\n              display: flex;\n              justify-content: space-between;\n              margin-top: 0;\n              .title {\n                font-size: 16px;\n                overflow: hidden;\n                white-space: nowrap;\n                text-overflow: ellipsis;\n                line-height: 24px;\n                font-weight: 600;\n                display: block;\n                color: #222;\n                cursor: text;\n              }\n              .create-time {\n                color: #999;\n                line-height: 24px;\n                margin-left: 12px;\n                flex-shrink: 0;\n              }\n            }\n            .abstruct {\n              line-height: 20px;\n              margin-top: 20px;\n              height: 20px;\n              display: flex;\n              align-items: flex-end;\n              .status {\n                color: #999;\n                border: none;\n                background-color: #f5f5f5;\n                padding: 0 8px;\n                line-height: 20px;\n                font-size: 12px;\n                border-radius: 2px;\n                white-space: nowrap;\n                display: inline-block;\n                box-sizing: border-box;\n                transition: all .3s;\n                margin-right: 8px;\n              }\n              .article-card .byte-tag-simple {\n                margin-right: 8px;\n              }\n              .divider {\n                width: 1px;\n                height: 12px;\n                margin: 4px 10px 4px 4px;\n                background: #bfbfbf;\n              }\n              .icon {\n                margin-right: 8px;\n                svg {\n                  vertical-align: bottom;\n                  &:focus {\n                    outline: none;\n                  }\n                }\n              }\n            }\n            .count-wrapper {\n              margin-top: 24px;\n              display: flex;\n              justify-content: space-between;\n              .count {\n                line-height: 20px;\n                position: relative;\n                li {\n                  display: inline-block;\n                  margin-right: 24px;\n                  &:after {\n                    content: \"\\ff65\";\n                    font-size: 20px;\n                    margin: 0 8px;\n                    line-height: 0;\n                    position: absolute;\n                    top: 10px;\n                    color: #666;\n                  }\n                  &:last-child:after {\n                    content: \"\"\n                  }\n                }\n              }\n              .article-action-list {\n                display: flex;\n                line-height: 20px;\n                flex: 1 0 auto;\n                justify-content: flex-end;\n                .icon-label {\n                  cursor: pointer;\n                  font-size: 14px;\n                  line-height: 20px;\n                  display: flex;\n                  color: #222;\n                  font-weight: 400;\n                  margin-left: 24px;\n                  &:first-child {\n                    margin-left: 0;\n                  }\n                  &:hover {\n                    color: $--color-primary;\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    .search-input {\n      width: 242px;\n    }\n  }\n</style>\n"], "mappings": ";AAuDE,SAAQA,GAAG,QAAO,KAAI;AACtB,OAAOC,MAAK,MAAO,iBAAgB;AACnC,SAAQC,cAAc,EAAEC,QAAQ,QAAO,gBAAe;AACtD,OAAOC,IAAG,MAAO,0BAAyB;AAC1C,SAAQC,OAAO,EAAEC,OAAO,QAAO,kBAAkB;AAEjD,eAAe;EACbC,IAAI,EAAE,eAAe;EACvBC,UAAU,EAAE;IACVJ;EACF,CAAC;EACDK,KAAKA,CAAA,EAAG;IACN,MAAMC,SAAQ,GAAI;MAChB,WAAW,EAAE,KAAK;MAClB,QAAQ,EAAE;IACZ;IACA,MAAMC,IAAG,GAAIX,GAAG,CAAC,EAAE;IACnB,MAAMY,KAAI,GAAIZ,GAAG,CAAC,CAAC;IACnB,MAAMa,WAAU,GAAIb,GAAG,CAAC,IAAI;IAC5B,MAAMc,WAAU,GAAId,GAAG,CAAC;MACtBe,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC;IACD;IACA,MAAMC,QAAO,GAAIA,CAAA,KAAM;MACrBN,WAAW,CAACO,KAAI,GAAI,IAAG;MACvBjB,QAAQ,CAACW,WAAW,CAACM,KAAK,EAAGC,GAAG,IAAK;QACnCR,WAAW,CAACO,KAAI,GAAI,KAAI;QACxB,IAAI,CAACC,GAAG,EAAE;UAAC;QAAO;QAClBV,IAAI,CAACS,KAAI,GAAIC,GAAG,CAACV,IAAI;QACrBC,KAAK,CAACQ,KAAI,GAAIC,GAAG,CAACT,KAAK;MACzB,CAAC;IACH;IACAO,QAAQ,EAAE;IACV;IACA,MAAMG,MAAK,GAAIA,CAAA,KAAM;MACnBH,QAAQ,EAAE;IACZ;IACA;IACA,MAAMI,IAAG,GAAKC,EAAE,IAAK;MACnBvB,MAAM,CAACwB,IAAI,CAAC;QAACC,IAAI,EAAE,qBAAqB;QAAEC,KAAK,EAAE;UAAEH,EAAC,EAAIA;QAAG;MAAC,CAAC;IAC/D;IACA;IACA,MAAMI,MAAK,GAAKC,IAAI,IAAK;MACvBxB,OAAO,CAAC,SAAQ,GAAIwB,IAAI,CAACtB,IAAG,GAAI,KAAK,EAAE,IAAI,EAAE,MAAM;QACjDL,cAAc,CAAC2B,IAAI,CAACL,EAAE,EAAE,MAAM;UAC5BlB,OAAO,CAAC,MAAM;UACda,QAAQ,EAAC;QACX,CAAC;MACH,CAAC,EAAE,MAAM,CACT,CAAC;IACH;IACA,MAAMW,aAAY,GAAKC,WAAW,IAAK;MACrCjB,WAAW,CAACM,KAAK,CAACF,OAAM,GAAIa,WAAW;MACvCZ,QAAQ,EAAE;IACZ;IACA,MAAMa,UAAS,GAAKC,CAAC,IAAK;MACxBnB,WAAW,CAACM,KAAK,CAACH,IAAG,GAAIgB,CAAC;MAC1Bd,QAAQ,EAAE;IACZ;IACA,OAAO;MACLR,IAAI;MACJC,KAAK;MACLE,WAAW;MACXQ,MAAM;MACNC,IAAI;MACJO,aAAa;MACbE,UAAU;MACVJ,MAAM;MACNlB,SAAS;MACTG;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}