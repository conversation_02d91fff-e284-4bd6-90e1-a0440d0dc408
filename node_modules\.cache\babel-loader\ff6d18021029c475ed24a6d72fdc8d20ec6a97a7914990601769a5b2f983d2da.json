{"ast": null, "code": "import { get, post, put, del } from \"../../util/requestUtils\";\n\n// 新增敏感词\nexport function saveSensitiveWord(data, success) {\n  return post(\"/comment/sensitive-word\", data, success);\n}\n\n// 更新敏感词\nexport function updateSensitiveWord(data, success) {\n  return put(\"/comment/sensitive-word\", data, success);\n}\n\n// 新增敏感词\nexport function removeSensitiveWord(data, success) {\n  return del(\"/comment/sensitive-word\", data, success);\n}\nexport function getSensitiveWordList(param, success) {\n  return get(\"/comment/sensitive-word/list\", param, success);\n}", "map": {"version": 3, "names": ["get", "post", "put", "del", "saveSensitiveWord", "data", "success", "updateSensitiveWord", "removeSensitiveWord", "getSensitiveWordList", "param"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/api/comment/sensitiveWord.js"], "sourcesContent": ["import { get, post, put, del } from \"../../util/requestUtils\"\n\n// 新增敏感词\nexport function saveSensitiveWord(data, success) {\n  return post(\"/comment/sensitive-word\", data, success)\n}\n\n// 更新敏感词\nexport function updateSensitiveWord(data, success) {\n  return put(\"/comment/sensitive-word\", data, success)\n}\n\n// 新增敏感词\nexport function removeSensitiveWord(data, success) {\n  return del(\"/comment/sensitive-word\", data, success)\n}\n\nexport function getSensitiveWordList(param, success) {\n  return get(\"/comment/sensitive-word/list\", param, success)\n}\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAQ,yBAAyB;;AAE7D;AACA,OAAO,SAASC,iBAAiBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC/C,OAAOL,IAAI,CAAC,yBAAyB,EAAEI,IAAI,EAAEC,OAAO,CAAC;AACvD;;AAEA;AACA,OAAO,SAASC,mBAAmBA,CAACF,IAAI,EAAEC,OAAO,EAAE;EACjD,OAAOJ,GAAG,CAAC,yBAAyB,EAAEG,IAAI,EAAEC,OAAO,CAAC;AACtD;;AAEA;AACA,OAAO,SAASE,mBAAmBA,CAACH,IAAI,EAAEC,OAAO,EAAE;EACjD,OAAOH,GAAG,CAAC,yBAAyB,EAAEE,IAAI,EAAEC,OAAO,CAAC;AACtD;AAEA,OAAO,SAASG,oBAAoBA,CAACC,KAAK,EAAEJ,OAAO,EAAE;EACnD,OAAON,GAAG,CAAC,8BAA8B,EAAEU,KAAK,EAAEJ,OAAO,CAAC;AAC5D"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}