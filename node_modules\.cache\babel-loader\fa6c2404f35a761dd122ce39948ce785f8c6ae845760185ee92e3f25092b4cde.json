{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref } from \"vue\";\nimport { useRoute } from \"vue-router\";\nimport router from \"@/router\";\nimport { findCategoryList, toTree, getAllParent } from \"@/api/exam/category\";\nimport { saveBaseInfo, updateBaseInfo, getBaseInfo, publishExam, unPublishExam, saveExamChapter, updateExamChapter, deleteExamChapter, getExamChapterList, saveExamChapterSection, updateExamChapterSection, deleteExamChapterSection } from \"@/api/exam\";\nimport Upload from \"@/components/Uplaod/index\";\nimport TinyMce from \"@/components/Tinymce\";\nimport { success, confirm, error } from \"@/util/tipsUtils\";\nimport * as paperApi from \"@/api/exam/paper\";\nimport PaperList from \"@/views/exam/paper\";\nexport default {\n  name: \"ExamListEditIndex\",\n  components: {\n    PaperList,\n    Upload,\n    TinyMce\n  },\n  setup() {\n    const route = useRoute();\n    const showPaperDialog = ref(false);\n    const paper = ref({});\n    const isUpdate = !!route.query.id;\n    let showStep = ref(\"\");\n    // 基本信息\n    const uploadData = ref({\n      url: process.env.VUE_APP_BASE_API + \"/oss/exam/image\",\n      files: []\n    });\n    const categoryOptions = ref([]);\n    const selectCidList = ref([]);\n    const exam = ref({\n      id: \"\",\n      name: \"\",\n      startTime: \"\",\n      endTime: \"\",\n      image: \"\",\n      cidList: [],\n      phrase: \"\",\n      introduction: \"\"\n    });\n    const examRules = {\n      name: [{\n        required: true,\n        message: \"请输入标题\",\n        trigger: \"blur\"\n      }],\n      startTime: [{\n        required: true,\n        message: \"请选择时间\",\n        trigger: \"change\"\n      }],\n      endTime: [{\n        required: true,\n        message: \"请选择时间\",\n        trigger: \"change\"\n      }],\n      phrase: [{\n        required: true,\n        message: \"请输入简介\",\n        trigger: \"blur\"\n      }],\n      cidList: [{\n        required: true,\n        message: \"请选择分类\",\n        trigger: \"change\"\n      }],\n      introduction: [{\n        required: true,\n        message: \"请输入描述\",\n        trigger: \"blur\"\n      }],\n      image: [{\n        required: true,\n        message: \"请选择海报\",\n        trigger: \"change\"\n      }]\n    };\n    // 加载基本信息\n    const loadBaseInfo = () => {\n      let id = route.query.id;\n      if (!id) {\n        return;\n      }\n      getBaseInfo(id, function (res) {\n        exam.value = res;\n        selectCidList.value = getAllParent(categoryOptions.value, res.cidList);\n        exam.value.cidList = [];\n        uploadData.value.files = [{\n          name: \"海报\",\n          url: exam.value.image\n        }];\n        for (const valElement of selectCidList.value) {\n          exam.value.cidList.push(valElement[valElement.length - 1]);\n        }\n      });\n    };\n    // 获取分类\n    const loadCategory = () => {\n      findCategoryList(0, true, res => {\n        if (res && res.length) {\n          categoryOptions.value = toTree(res);\n          categoryOptions.value.splice(0, 1);\n          loadBaseInfo();\n        }\n      });\n    };\n    // 选择分类\n    const changeCategory = val => {\n      exam.value.cidList = [];\n      for (const valElement of val) {\n        exam.value.cidList.push(valElement[valElement.length - 1]);\n      }\n    };\n    // 选择时间\n    const changeStartTime = val => {\n      exam.value.startTime = val;\n    };\n    // 选择时间\n    const changeEndTime = val => {\n      exam.value.endTime = val;\n    };\n    // 上传图片成功\n    const onUploadImageSuccess = res => {\n      exam.value.image = res.data;\n    };\n    // 删除图片\n    const onUploadImageRemove = () => {\n      exam.value.image = \"\";\n      uploadData.value.files = [];\n    };\n    // 提交基本信息\n    const examRef = ref(null);\n    const submitBaseInfo = () => {\n      examRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (isUpdate) {\n          if (typeof exam.value.startTime === \"string\") {\n            exam.value.startTime = new Date(exam.value.startTime);\n          }\n          if (typeof exam.value.endTime === \"string\") {\n            exam.value.endTime = new Date(exam.value.endTime);\n          }\n          updateBaseInfo(exam.value, function (res) {\n            if (res && res.id) {\n              exam.value.id = res.id;\n              success(\"编辑成功\");\n              let path = route.fullPath;\n              router.push({\n                path,\n                query: {\n                  id: exam.value.id,\n                  step: \"content\"\n                }\n              });\n              showStep.value = \"content\";\n            }\n          });\n        } else {\n          if (typeof exam.value.startTime === \"string\") {\n            exam.value.startTime = new Date(exam.value.startTime);\n          }\n          if (typeof exam.value.endTime === \"string\") {\n            exam.value.endTime = new Date(exam.value.endTime);\n          }\n          saveBaseInfo(exam.value, function (res) {\n            if (res && res.id) {\n              exam.value.id = res.id;\n              success(\"新增成功\");\n              let path = route.fullPath;\n              router.push({\n                path,\n                query: {\n                  id: exam.value.id,\n                  step: \"content\"\n                }\n              });\n              showStep.value = \"content\";\n            }\n          });\n        }\n      });\n    };\n\n    // 内容\n    const contentList = ref([]);\n    const loadContent = () => {\n      if (!(exam.value && exam.value.id)) {\n        return;\n      }\n      getExamChapterList({\n        examId: exam.value.id\n      }, res => {\n        if (res && res.list && res.list.length) {\n          for (const chapter of res.list) {\n            if (chapter.chapterSectionList && chapter.chapterSectionList.length) {\n              for (const section of chapter.chapterSectionList) {\n                paperApi.getBaseInfo(section.paperId, result => {\n                  section.question = result;\n                });\n              }\n            }\n          }\n          contentList.value = res.list;\n        }\n      });\n    };\n    const showChapterDialog = ref(false);\n    const examChapter = ref({\n      id: \"\",\n      examId: \"\",\n      title: \"\",\n      phrase: \"\"\n    });\n    const examChapterRules = ref({\n      title: [{\n        required: true,\n        message: \"请输入标题\",\n        trigger: \"blur\"\n      }],\n      phrase: [{\n        required: true,\n        message: \"请输入简介\",\n        trigger: \"blur\"\n      }]\n    });\n    const showChapter = chapter => {\n      if (chapter && chapter.id) {\n        examChapter.value = chapter;\n      } else {\n        examChapter.value.examId = exam.value.id;\n      }\n      showChapterDialog.value = true;\n    };\n    const hideChapter = () => {\n      showChapterDialog.value = false;\n      examChapter.value.title = \"\";\n      examChapter.value.phrase = \"\";\n    };\n    const deleteChapter = id => {\n      confirm(\"确认删除吗？\", \"提示\", () => {\n        deleteExamChapter({\n          id: id\n        }, () => {\n          success(\"删除成功\");\n          loadContent();\n        });\n      });\n    };\n    const examChapterRef = ref(null);\n    const submitChapter = () => {\n      examChapterRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (examChapter.value.id) {\n          updateExamChapter(examChapter.value, function () {\n            success(\"编辑成功\");\n            hideChapter();\n            loadContent();\n          });\n        } else {\n          saveExamChapter(examChapter.value, function () {\n            success(\"新增成功\");\n            hideChapter();\n            loadContent();\n          });\n        }\n      });\n    };\n    const showChapterSectionDialog = ref(false);\n    const examChapterSection = ref({\n      id: \"\",\n      examChapterId: \"\",\n      title: \"\",\n      paperId: \"\",\n      phrase: \"\"\n    });\n    const examChapterSectionRules = ref({\n      title: [{\n        required: true,\n        message: \"请输入标题\",\n        trigger: \"blur\"\n      }],\n      paperId: [{\n        required: true,\n        message: \"请选择试卷\",\n        trigger: \"blur\"\n      }],\n      phrase: [{\n        required: true,\n        message: \"请输入简介\",\n        trigger: \"blur\"\n      }]\n    });\n    const showChapterSection = (examChapterId, chapterSection) => {\n      showChapterSectionDialog.value = true;\n      if (chapterSection) {\n        examChapterSection.value = chapterSection;\n        paper.value = chapterSection.question;\n      } else {\n        examChapterSection.value.examChapterId = examChapterId;\n      }\n    };\n    const hideChapterSection = () => {\n      showChapterSectionDialog.value = false;\n      examChapterSection.value = {\n        id: \"\",\n        examChapterId: \"\",\n        title: \"\",\n        paperId: \"\",\n        phrase: \"\",\n        totalTime: 0\n      };\n    };\n    const deleteChapterSection = id => {\n      confirm(\"确认删除吗？\", \"提示\", () => {\n        deleteExamChapterSection({\n          id: id\n        }, () => {\n          success(\"删除成功\");\n          loadContent();\n        });\n      });\n    };\n    const examChapterSectionRef = ref(null);\n    const submitChapterSection = () => {\n      examChapterSection.value.paperId = paper.value.id || examChapterSection.value.paperId;\n      examChapterSectionRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (examChapterSection.value.id) {\n          updateExamChapterSection(examChapterSection.value, function () {\n            success(\"编辑成功\");\n            hideChapterSection();\n            loadContent();\n          });\n        } else {\n          saveExamChapterSection(examChapterSection.value, function () {\n            success(\"新增成功\");\n            hideChapterSection();\n            loadContent();\n          });\n        }\n      });\n    };\n    // 发布页面\n    const statusMap = {\n      unpublished: \"草稿箱\",\n      published: \"已发布\",\n      deleted: \"已删除\"\n    };\n    const publish = () => {\n      publishExam({\n        id: exam.value.id\n      }, () => {\n        success(\"发布成功\");\n        exam.value.status = \"published\";\n      });\n    };\n    const unPublish = () => {\n      unPublishExam({\n        id: exam.value.id\n      }, () => {\n        success(\"取消发布成功\");\n        exam.value.status = \"unpublished\";\n      });\n    };\n    // 步骤条\n    const steps = [{\n      key: \"base\",\n      name: \"基础信息\"\n    }, {\n      key: \"content\",\n      name: \"课程章节\"\n    }, {\n      key: \"publish\",\n      name: \"发布\"\n    }];\n    const stepActive = ref(0);\n    const loadStepActiveArray = () => {\n      const stepActiveArray = [];\n      for (let i = 0; i < steps.length; i++) {\n        const step = steps[i];\n        stepActiveArray.push(step.key);\n        if (step.key === showStep.value) {\n          stepActive.value = i;\n          break;\n        }\n      }\n      if (isUpdate) {\n        stepActive.value = steps.length;\n      }\n      return stepActiveArray;\n    };\n    const init = () => {\n      // 初始化加载\n      if (route.query.step) {\n        showStep.value = route.query.step;\n      } else {\n        showStep.value = \"base\";\n      }\n      exam.value.id = route.query.id || \"\";\n      loadCategory();\n      loadContent();\n    };\n    init();\n    // 步骤条点击切换\n    const stepClick = key => {\n      if (!isUpdate && loadStepActiveArray().indexOf(key) < 0) {\n        return;\n      }\n      showStep.value = key;\n      let path = route.fullPath;\n      router.push({\n        path,\n        query: {\n          id: exam.value.id,\n          step: key\n        }\n      });\n    };\n    loadStepActiveArray();\n    const showPaper = () => {\n      showPaperDialog.value = true;\n    };\n    const hidePaper = () => {\n      showPaperDialog.value = false;\n    };\n    const paperSelectionChange = paperIdList => {\n      if (!paperIdList || !paperIdList.length) {\n        error(\"请选择试卷\");\n        return;\n      }\n      paperApi.getBaseInfo(paperIdList[0], res => {\n        paper.value = res;\n      });\n      hidePaper();\n    };\n    // 返回参数与方法\n    return {\n      // 基本信息\n      uploadData,\n      categoryOptions,\n      exam,\n      selectCidList,\n      examRules,\n      examRef,\n      changeCategory,\n      changeStartTime,\n      changeEndTime,\n      onUploadImageSuccess,\n      onUploadImageRemove,\n      submitBaseInfo,\n      // 内容列表\n      contentList,\n      showChapterDialog,\n      examChapter,\n      examChapterRules,\n      showChapterSectionDialog,\n      examChapterSection,\n      examChapterSectionRules,\n      examChapterRef,\n      examChapterSectionRef,\n      showChapter,\n      hideChapter,\n      showChapterSection,\n      hideChapterSection,\n      deleteChapter,\n      deleteChapterSection,\n      submitChapter,\n      submitChapterSection,\n      // 发布页面\n      statusMap,\n      publish,\n      unPublish,\n      // 步骤条\n      steps,\n      stepActive,\n      showStep,\n      stepClick,\n      showPaperDialog,\n      showPaper,\n      hidePaper,\n      paper,\n      paperSelectionChange\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "useRoute", "router", "findCategoryList", "toTree", "getAllParent", "saveBaseInfo", "updateBaseInfo", "getBaseInfo", "publishExam", "unPublishExam", "saveExamChapter", "updateExamChapter", "deleteExamChapter", "getExamChapterList", "saveExamChapterSection", "updateExamChapterSection", "deleteExamChapterSection", "Upload", "TinyMce", "success", "confirm", "error", "paperApi", "PaperList", "name", "components", "setup", "route", "showPaperDialog", "paper", "isUpdate", "query", "id", "showStep", "uploadData", "url", "process", "env", "VUE_APP_BASE_API", "files", "categoryOptions", "selectCidList", "exam", "startTime", "endTime", "image", "cidList", "phrase", "introduction", "examRules", "required", "message", "trigger", "loadBaseInfo", "res", "value", "valElement", "push", "length", "loadCategory", "splice", "changeCategory", "val", "changeStartTime", "changeEndTime", "onUploadImageSuccess", "data", "onUploadImageRemove", "examRef", "submitBaseInfo", "validate", "valid", "Date", "path", "fullPath", "step", "contentList", "loadContent", "examId", "list", "chapter", "chapterSectionList", "section", "paperId", "result", "question", "showChapterDialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title", "examChapterRules", "showChapter", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deleteChapter", "examChapterRef", "submitChapter", "showChapterSectionDialog", "examChapterSection", "exam<PERSON>hapterId", "examChapterSectionRules", "showChapterSection", "chapterSection", "hideChapterSection", "totalTime", "deleteChapterSection", "examChapterSectionRef", "submitChapterSection", "statusMap", "unpublished", "published", "deleted", "publish", "status", "unPublish", "steps", "key", "stepActive", "loadStepActiveArray", "stepActiveArray", "i", "init", "step<PERSON>lick", "indexOf", "showPaper", "hidePaper", "paperSelectionChange", "paperIdList"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/exam/list/edit.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\">\n      <el-col :span=\"20\" style=\"border-right: 1px solid #dddddd;margin-top: 10px;\">\n        <div v-if=\"showStep === 'base'\">\n          <el-form :model=\"exam\" :rules=\"examRules\" ref=\"examRef\" label-width=\"120px\">\n            <el-form-item label=\"名称：\" prop=\"name\">\n              <el-input size=\"mini\" v-model=\"exam.name\" placeholder=\"请输入标题\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"开始时间：\" prop=\"startTime\">\n              <el-date-picker\n                v-model=\"exam.startTime\"\n                type=\"datetime\"\n                placeholder=\"选择开始时间\"\n                class=\"input-text\"\n                :default-time=\"new Date(2000, 0, 1, 0, 0, 0)\"\n                size=\"mini\"\n                @change=\"changeStartTime\"\n                style=\"width: 100%;\"></el-date-picker>\n            </el-form-item>\n            <el-form-item label=\"结束时间：\" prop=\"endTime\">\n              <el-date-picker\n                v-model=\"exam.endTime\"\n                type=\"datetime\"\n                placeholder=\"选择结束时间\"\n                class=\"input-text\"\n                :default-time=\"new Date(2000, 0, 1, 22, 0, 0)\"\n                size=\"mini\"\n                @change=\"changeEndTime\"\n                style=\"width: 100%;\"></el-date-picker>\n            </el-form-item>\n            <el-form-item label=\"分类：\" prop=\"cidList\">\n              <el-cascader style=\"width: 100%;\"\n                           size=\"mini\"\n                           v-model=\"selectCidList\"\n                           :props=\"{ multiple: true, checkStrictly: true }\"\n                           :options=\"categoryOptions\"\n                           @change=\"changeCategory\">\n              </el-cascader>\n            </el-form-item>\n            <el-form-item label=\"简介：\" prop=\"phrase\">\n              <el-input size=\"mini\" v-model=\"exam.phrase\" placeholder=\"请输入简介\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"海报：\" prop=\"image\">\n              <upload\n                :on-upload-success=\"onUploadImageSuccess\"\n                :on-upload-remove=\"onUploadImageRemove\"\n                :files=\"uploadData.files\"\n                :upload-url=\"uploadData.url\"\n                :limit=\"1\"\n                accept=\"image/jpeg,image/gif,image/png\">\n              </upload>\n              <span class=\"upload-image-tips\">图片建议：尺寸 1920 x 1200 像素，大小7M以下</span>\n            </el-form-item>\n            <el-form-item label=\"详情描述：\" prop=\"introduction\">\n              <tiny-mce :height=\"300\" v-model=\"exam.introduction\"></tiny-mce>\n            </el-form-item>\n            <el-button size=\"mini\" style=\"display:block;margin:20px auto;\" @click=\"submitBaseInfo\">提交</el-button>\n          </el-form>\n        </div>\n        <div v-if=\"showStep === 'content'\" style=\"position: relative;min-height: 500px;\">\n          <div><el-button size=\"mini\" @click=\"showChapter()\">新增章节</el-button></div>\n          <div style=\"margin-top: 20px;\">\n            <el-table default-expand-all :data=\"contentList\" :show-header=\"false\" :highlight-current-row=\"true\" style=\"width: 100%\">\n              <el-table-column type=\"expand\">\n                <template #default=\"props\">\n                  <div class=\"tips\">{{props.row.phrase}}</div>\n                  <el-card class=\"box-card\" v-for=\"section in props.row.chapterSectionList\" :key=\"section.title\" style=\"margin-top: 20px;\">\n                    <template #header>\n                      <div class=\"clearfix\" style=\"line-height: 28px;\">\n                        <span>{{section.title}}</span>\n                        <span style=\"float: right;\">\n                          <el-button size=\"mini\" @click=\"showChapterSection(props.row.id, section)\">修改</el-button>\n                          <el-button size=\"mini\" @click=\"deleteChapterSection(props.row.id)\">删除</el-button>\n                        </span>\n                      </div>\n                    </template>\n                    <div class=\"table-wrapper\">\n                      <div class=\"tips\">{{section.phrase}}</div>\n                      <div>{{section.question ? section.question.title : \"\"}}</div>\n                    </div>\n                  </el-card>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"title\" label=\"标题\"></el-table-column>\n              <el-table-column label=\"操作\">\n                <template #default=\"r\">\n                  <span style=\"float: right;\">\n                    <el-button @click=\"showChapterSection(r.row.id)\" size=\"mini\">新增章节内容</el-button>\n                    <el-button @click=\"showChapter(r.row)\" size=\"mini\">修改</el-button>\n                    <el-button @click=\"deleteChapter(r.row.id)\" size=\"mini\">删除</el-button>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n        </div>\n        <div v-if=\"showStep === 'publish'\" style=\"position: relative;height: 500px;\">\n          <div style=\"position:absolute;left:50%;top:40%;-webkit-transform:translateX(-50%);transform:translateX(-50%);\">\n            <p style=\"margin: 0 0 30px 0;\">当前状态：{{statusMap[exam.status]}}</p>\n            <el-button size=\"mini\" type=\"success\" @click=\"publish\" :disabled=\"exam.status === 'published'\">马上发布</el-button>\n            <el-button size=\"mini\" type=\"danger\" @click=\"unPublish\" :disabled=\"exam.status === 'unpublished'\">移入草稿</el-button>\n          </div>\n        </div>\n      </el-col>\n      <el-col :span=\"4\" style=\"position: relative;\">\n        <div style=\"height: 500px;position: absolute;margin: 10px 17%;\">\n          <el-steps direction=\"vertical\" :active=\"stepActive\">\n            <el-step v-for=\"(step) in steps\" :key=\"step.key\" @click=\"stepClick(step.key)\" :class=\"{'step-active': showStep === step.key}\" :title=\"step.name\"></el-step>\n          </el-steps>\n        </div>\n      </el-col>\n    </el-row>\n    <el-dialog title=\"编辑章节\" v-model=\"showChapterDialog\" :before-close=\"hideChapter\">\n      <el-form :model=\"examChapter\" :rules=\"examChapterRules\" ref=\"examChapterRef\">\n        <el-form-item label=\"标题：\" label-width=\"120px\" prop=\"title\">\n          <el-input size=\"mini\" v-model=\"examChapter.title\" placeholder=\"请输入标题\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"简介：\" label-width=\"120px\" prop=\"phrase\">\n          <el-input size=\"mini\" v-model=\"examChapter.phrase\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入简介\"></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button size=\"mini\" @click=\"hideChapter\">取 消</el-button>\n          <el-button size=\"mini\" type=\"primary\" @click=\"submitChapter\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n    <el-dialog title=\"编辑章节内容\" v-model=\"showChapterSectionDialog\" :before-close=\"hideChapterSection\">\n      <el-form :model=\"examChapterSection\" :rules=\"examChapterSectionRules\" ref=\"examChapterSectionRef\">\n        <el-form-item label=\"标题：\" label-width=\"120px\" prop=\"title\">\n          <el-input size=\"mini\" v-model=\"examChapterSection.title\" placeholder=\"请输入标题\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"试卷：\" label-width=\"120px\" prop=\"paperId\">\n          <div>{{paper.title}}</div>\n          <el-button size=\"mini\" @click=\"showPaper\">选择试卷</el-button>\n        </el-form-item>\n        <el-form-item label=\"简介：\" label-width=\"120px\" prop=\"phrase\">\n          <el-input size=\"mini\" v-model=\"examChapterSection.phrase\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入简介\"></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button size=\"mini\" @click=\"hideChapterSection\">取 消</el-button>\n          <el-button size=\"mini\" type=\"primary\" @click=\"submitChapterSection\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n    <el-dialog title=\"选择试卷\" v-model=\"showPaperDialog\" :before-close=\"hidePaper\" width=\"90%\">\n      <paper-list :is-component=\"true\" :hide-component=\"hidePaper\" :selection-change-callback=\"paperSelectionChange\"/>\n    </el-dialog>\n  </div>\n</template>\n<script>\n  import {ref} from \"vue\"\n  import {useRoute} from \"vue-router\"\n  import router from \"@/router\"\n  import {findCategoryList, toTree, getAllParent} from \"@/api/exam/category\"\n  import {saveBaseInfo, updateBaseInfo, getBaseInfo, publishExam, unPublishExam,\n    saveExamChapter, updateExamChapter, deleteExamChapter, getExamChapterList,\n    saveExamChapterSection, updateExamChapterSection, deleteExamChapterSection} from \"@/api/exam\"\n  import Upload from \"@/components/Uplaod/index\"\n  import TinyMce from \"@/components/Tinymce\"\n  import {success, confirm, error} from \"@/util/tipsUtils\";\n  import * as paperApi from \"@/api/exam/paper\";\n  import PaperList from \"@/views/exam/paper\";\n\n  export default {\n    name: \"ExamListEditIndex\",\n    components:{\n      PaperList,\n      Upload,\n      TinyMce\n    },\n    setup() {\n      const route = useRoute()\n      const showPaperDialog = ref(false)\n      const paper = ref({})\n      const isUpdate = !!route.query.id\n      let showStep = ref(\"\")\n      // 基本信息\n      const uploadData = ref({\n        url: process.env.VUE_APP_BASE_API + \"/oss/exam/image\",\n        files: []\n      })\n      const categoryOptions = ref([])\n      const selectCidList = ref([])\n      const exam = ref({\n        id: \"\",\n        name: \"\",\n        startTime: \"\",\n        endTime: \"\",\n        image: \"\",\n        cidList: [],\n        phrase: \"\",\n        introduction: \"\"\n      })\n      const examRules = {\n        name: [{ required: true, message: \"请输入标题\", trigger: \"blur\" }],\n        startTime: [{ required: true, message: \"请选择时间\", trigger: \"change\" }],\n        endTime: [{ required: true, message: \"请选择时间\", trigger: \"change\" }],\n        phrase: [{ required: true, message: \"请输入简介\", trigger: \"blur\" }],\n        cidList: [{ required: true, message: \"请选择分类\", trigger: \"change\" }],\n        introduction: [{ required: true, message: \"请输入描述\", trigger: \"blur\" }],\n        image: [{ required: true, message: \"请选择海报\", trigger: \"change\" }],\n      }\n      // 加载基本信息\n      const loadBaseInfo = () => {\n        let id = route.query.id;\n        if (!id) { return; }\n        getBaseInfo(id, function (res) {\n          exam.value = res;\n          selectCidList.value = getAllParent(categoryOptions.value, res.cidList);\n          exam.value.cidList = []\n          uploadData.value.files = [{name: \"海报\", url: exam.value.image}]\n          for (const valElement of selectCidList.value) {\n            exam.value.cidList.push(valElement[valElement.length - 1])\n          }\n        })\n      }\n      // 获取分类\n      const loadCategory = () => {\n        findCategoryList(0, true, (res) => {\n          if (res && res.length) {\n            categoryOptions.value = toTree(res);\n            categoryOptions.value.splice(0, 1);\n            loadBaseInfo();\n          }\n        })\n      }\n      // 选择分类\n      const changeCategory = (val) => {\n        exam.value.cidList = []\n        for (const valElement of val) {\n          exam.value.cidList.push(valElement[valElement.length - 1])\n        }\n      }\n      // 选择时间\n      const changeStartTime = (val) => {\n        exam.value.startTime = val\n      }\n      // 选择时间\n      const changeEndTime = (val) => {\n        exam.value.endTime = val\n      }\n      // 上传图片成功\n      const onUploadImageSuccess = (res) => {\n        exam.value.image = res.data\n      }\n      // 删除图片\n      const onUploadImageRemove = () => {\n        exam.value.image = \"\"\n        uploadData.value.files = []\n      }\n      // 提交基本信息\n      const examRef = ref(null)\n      const submitBaseInfo = () => {\n        examRef.value.validate((valid) => {\n          if (!valid) { return false }\n          if (isUpdate) {\n            if (typeof exam.value.startTime === \"string\") {\n              exam.value.startTime = new Date(exam.value.startTime)\n            }\n            if (typeof exam.value.endTime === \"string\") {\n              exam.value.endTime = new Date(exam.value.endTime)\n            }\n            updateBaseInfo(exam.value, function (res) {\n              if (res && res.id) {\n                exam.value.id = res.id;\n                success(\"编辑成功\")\n                let path = route.fullPath;\n                router.push({path, query: {id: exam.value.id, step: \"content\"} });\n                showStep.value = \"content\";\n              }\n            })\n          } else {\n            if (typeof exam.value.startTime === \"string\") {\n              exam.value.startTime = new Date(exam.value.startTime)\n            }\n            if (typeof exam.value.endTime === \"string\") {\n              exam.value.endTime = new Date(exam.value.endTime)\n            }\n            saveBaseInfo(exam.value, function (res) {\n              if (res && res.id) {\n                exam.value.id = res.id;\n                success(\"新增成功\")\n                let path = route.fullPath;\n                router.push({path, query: {id: exam.value.id, step: \"content\"} });\n                showStep.value = \"content\";\n              }\n            })\n          }\n        })\n      }\n\n      // 内容\n      const contentList = ref([])\n      const loadContent = () => {\n        if (!(exam.value && exam.value.id)) {\n          return;\n        }\n        getExamChapterList({examId: exam.value.id}, (res) => {\n          if (res && res.list && res.list.length) {\n            for (const chapter of res.list) {\n              if (chapter.chapterSectionList && chapter.chapterSectionList.length) {\n                for (const section of chapter.chapterSectionList) {\n                  paperApi.getBaseInfo(section.paperId, (result) => {\n                    section.question = result\n                  });\n                }\n              }\n            }\n            contentList.value = res.list;\n          }\n        })\n      }\n      const showChapterDialog = ref(false)\n      const examChapter = ref({\n        id: \"\",\n        examId: \"\",\n        title: \"\",\n        phrase: \"\"\n      })\n      const examChapterRules = ref({\n        title: [{ required: true, message: \"请输入标题\", trigger: \"blur\" }],\n        phrase: [{ required: true, message: \"请输入简介\", trigger: \"blur\" }]\n      })\n      const showChapter = (chapter) => {\n        if (chapter && chapter.id) {\n          examChapter.value = chapter;\n        } else {\n          examChapter.value.examId = exam.value.id;\n        }\n        showChapterDialog.value = true;\n      }\n      const hideChapter = () => {\n        showChapterDialog.value = false;\n        examChapter.value.title = \"\"\n        examChapter.value.phrase = \"\"\n      }\n      const deleteChapter = (id) => {\n        confirm(\"确认删除吗？\", \"提示\", () => {\n          deleteExamChapter({id: id}, () => {\n            success(\"删除成功\")\n            loadContent()\n          })\n        })\n      }\n      const examChapterRef = ref(null)\n      const submitChapter = () => {\n        examChapterRef.value.validate((valid) => {\n          if (!valid) { return false }\n          if (examChapter.value.id) {\n            updateExamChapter(examChapter.value, function () {\n              success(\"编辑成功\")\n              hideChapter()\n              loadContent()\n            })\n          } else {\n            saveExamChapter(examChapter.value, function () {\n              success(\"新增成功\")\n              hideChapter()\n              loadContent()\n            })\n          }\n        })\n      }\n      const showChapterSectionDialog = ref(false)\n      const examChapterSection = ref({\n        id: \"\",\n        examChapterId: \"\",\n        title: \"\",\n        paperId: \"\",\n        phrase: \"\"\n      })\n      const examChapterSectionRules = ref({\n        title: [{ required: true, message: \"请输入标题\", trigger: \"blur\" }],\n        paperId: [{ required: true, message: \"请选择试卷\", trigger: \"blur\" }],\n        phrase: [{ required: true, message: \"请输入简介\", trigger: \"blur\" }]\n      })\n      const showChapterSection = (examChapterId, chapterSection) => {\n        showChapterSectionDialog.value = true;\n        if (chapterSection) {\n          examChapterSection.value = chapterSection;\n          paper.value = chapterSection.question;\n        } else {\n          examChapterSection.value.examChapterId = examChapterId\n        }\n      }\n      const hideChapterSection = () => {\n        showChapterSectionDialog.value = false;\n        examChapterSection.value = {id: \"\", examChapterId: \"\", title: \"\", paperId: \"\", phrase: \"\", totalTime: 0}\n      }\n      const deleteChapterSection = (id) => {\n        confirm(\"确认删除吗？\", \"提示\", () => {\n          deleteExamChapterSection({id: id}, () => {\n            success(\"删除成功\")\n            loadContent()\n          })\n        })\n      }\n      const examChapterSectionRef = ref(null)\n      const submitChapterSection = () => {\n        examChapterSection.value.paperId = paper.value.id || examChapterSection.value.paperId;\n        examChapterSectionRef.value.validate((valid) => {\n          if (!valid) { return false }\n          if (examChapterSection.value.id) {\n            updateExamChapterSection(examChapterSection.value, function () {\n              success(\"编辑成功\")\n              hideChapterSection()\n              loadContent()\n            })\n          } else {\n            saveExamChapterSection(examChapterSection.value, function () {\n              success(\"新增成功\")\n              hideChapterSection()\n              loadContent()\n            })\n          }\n        })\n      }\n      // 发布页面\n      const statusMap = {\n        unpublished: \"草稿箱\",\n        published: \"已发布\",\n        deleted: \"已删除\"\n      }\n      const publish = () => {\n        publishExam({id: exam.value.id}, () => {\n          success(\"发布成功\")\n          exam.value.status = \"published\"\n        })\n      }\n      const unPublish = () => {\n        unPublishExam({id: exam.value.id}, () => {\n          success(\"取消发布成功\")\n          exam.value.status = \"unpublished\"\n        })\n      }\n      // 步骤条\n      const steps = [\n        {key: \"base\", name: \"基础信息\"},\n        {key: \"content\", name: \"课程章节\"},\n        {key: \"publish\", name: \"发布\"},\n      ]\n      const stepActive = ref(0)\n      const loadStepActiveArray = () => {\n        const stepActiveArray = [];\n        for (let i = 0; i < steps.length; i++) {\n          const step = steps[i];\n          stepActiveArray.push(step.key);\n          if (step.key === showStep.value) {\n            stepActive.value = i;\n            break;\n          }\n        }\n        if (isUpdate) {\n          stepActive.value = steps.length;\n        }\n        return stepActiveArray;\n      }\n      const init = () => {\n        // 初始化加载\n        if (route.query.step) {\n          showStep.value = route.query.step;\n        } else {\n          showStep.value = \"base\"\n        }\n        exam.value.id = route.query.id || \"\"\n        loadCategory();\n        loadContent();\n      }\n      init()\n      // 步骤条点击切换\n      const stepClick = (key) => {\n        if (!isUpdate && loadStepActiveArray().indexOf(key) < 0) {\n          return;\n        }\n        showStep.value = key;\n        let path = route.fullPath;\n        router.push({path, query: {id: exam.value.id, step: key} });\n      }\n      loadStepActiveArray();\n      const showPaper = () => {\n        showPaperDialog.value = true;\n      }\n      const hidePaper = () => {\n        showPaperDialog.value = false;\n      }\n      const paperSelectionChange = (paperIdList) => {\n        if(!paperIdList || !paperIdList.length) {\n          error(\"请选择试卷\");\n          return;\n        }\n        paperApi.getBaseInfo(paperIdList[0], (res) => {\n          paper.value = res\n        })\n        hidePaper()\n      }\n      // 返回参数与方法\n      return {\n        // 基本信息\n        uploadData,\n        categoryOptions,\n        exam,\n        selectCidList,\n        examRules,\n        examRef,\n        changeCategory,\n        changeStartTime,\n        changeEndTime,\n        onUploadImageSuccess,\n        onUploadImageRemove,\n        submitBaseInfo,\n        // 内容列表\n        contentList,\n        showChapterDialog,\n        examChapter,\n        examChapterRules,\n        showChapterSectionDialog,\n        examChapterSection,\n        examChapterSectionRules,\n        examChapterRef,\n        examChapterSectionRef,\n        showChapter,\n        hideChapter,\n        showChapterSection,\n        hideChapterSection,\n        deleteChapter,\n        deleteChapterSection,\n        submitChapter,\n        submitChapterSection,\n        // 发布页面\n        statusMap,\n        publish,\n        unPublish,\n        // 步骤条\n        steps,\n        stepActive,\n        showStep,\n        stepClick,\n        showPaperDialog,\n        showPaper,\n        hidePaper,\n        paper,\n        paperSelectionChange\n      };\n    }\n  }\n</script>\n<style scoped lang=\"scss\">\n  .app-container {\n    margin: 20px;\n  }\n  .upload-image-tips {\n    font-size: 12px;\n    color: #999999;\n  }\n  .el-form-item {\n    width: 96%;\n  }\n  //>>> .el-input--mini .el-input__inner {\n  //  height: 40px;\n  //}\n  //>>> .el-step.is-vertical .el-step__title{\n  //  cursor:pointer;\n  //}\n  //>>> .el-step.is-vertical .el-step__line {\n  //  width: 1px;\n  //}\n  //>>> .el-step__icon.is-text {\n  //  border-width: 1px;\n  //  cursor:pointer;\n  //}\n  //>>> .el-upload--picture-card, >>> .el-upload-list--picture-card .el-upload-list__item {\n  //  width: 100%;\n  //  height: 62.5%;\n  //}\n  //>>> .step-active .el-step__head.is-finish {\n  //  color: red;\n  //}\n  //.tips {\n  //  font-size: 12px;\n  //  color: #999999;\n  //}\n</style>\n"], "mappings": ";AA2JE,SAAQA,GAAG,QAAO,KAAI;AACtB,SAAQC,QAAQ,QAAO,YAAW;AAClC,OAAOC,MAAK,MAAO,UAAS;AAC5B,SAAQC,gBAAgB,EAAEC,MAAM,EAAEC,YAAY,QAAO,qBAAoB;AACzE,SAAQC,YAAY,EAAEC,cAAc,EAAEC,WAAW,EAAEC,WAAW,EAAEC,aAAa,EAC3EC,eAAe,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,kBAAkB,EACzEC,sBAAsB,EAAEC,wBAAwB,EAAEC,wBAAwB,QAAO,YAAW;AAC9F,OAAOC,MAAK,MAAO,2BAA0B;AAC7C,OAAOC,OAAM,MAAO,sBAAqB;AACzC,SAAQC,OAAO,EAAEC,OAAO,EAAEC,KAAK,QAAO,kBAAkB;AACxD,OAAO,KAAKC,QAAO,MAAO,kBAAkB;AAC5C,OAAOC,SAAQ,MAAO,oBAAoB;AAE1C,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,UAAU,EAAC;IACTF,SAAS;IACTN,MAAM;IACNC;EACF,CAAC;EACDQ,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAI3B,QAAQ,EAAC;IACvB,MAAM4B,eAAc,GAAI7B,GAAG,CAAC,KAAK;IACjC,MAAM8B,KAAI,GAAI9B,GAAG,CAAC,CAAC,CAAC;IACpB,MAAM+B,QAAO,GAAI,CAAC,CAACH,KAAK,CAACI,KAAK,CAACC,EAAC;IAChC,IAAIC,QAAO,GAAIlC,GAAG,CAAC,EAAE;IACrB;IACA,MAAMmC,UAAS,GAAInC,GAAG,CAAC;MACrBoC,GAAG,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAe,GAAI,iBAAiB;MACrDC,KAAK,EAAE;IACT,CAAC;IACD,MAAMC,eAAc,GAAIzC,GAAG,CAAC,EAAE;IAC9B,MAAM0C,aAAY,GAAI1C,GAAG,CAAC,EAAE;IAC5B,MAAM2C,IAAG,GAAI3C,GAAG,CAAC;MACfiC,EAAE,EAAE,EAAE;MACNR,IAAI,EAAE,EAAE;MACRmB,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,YAAY,EAAE;IAChB,CAAC;IACD,MAAMC,SAAQ,GAAI;MAChBzB,IAAI,EAAE,CAAC;QAAE0B,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC7DT,SAAS,EAAE,CAAC;QAAEO,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC,CAAC;MACpER,OAAO,EAAE,CAAC;QAAEM,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC,CAAC;MAClEL,MAAM,EAAE,CAAC;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC/DN,OAAO,EAAE,CAAC;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC,CAAC;MAClEJ,YAAY,EAAE,CAAC;QAAEE,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MACrEP,KAAK,EAAE,CAAC;QAAEK,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC;IACjE;IACA;IACA,MAAMC,YAAW,GAAIA,CAAA,KAAM;MACzB,IAAIrB,EAAC,GAAIL,KAAK,CAACI,KAAK,CAACC,EAAE;MACvB,IAAI,CAACA,EAAE,EAAE;QAAE;MAAQ;MACnBzB,WAAW,CAACyB,EAAE,EAAE,UAAUsB,GAAG,EAAE;QAC7BZ,IAAI,CAACa,KAAI,GAAID,GAAG;QAChBb,aAAa,CAACc,KAAI,GAAInD,YAAY,CAACoC,eAAe,CAACe,KAAK,EAAED,GAAG,CAACR,OAAO,CAAC;QACtEJ,IAAI,CAACa,KAAK,CAACT,OAAM,GAAI,EAAC;QACtBZ,UAAU,CAACqB,KAAK,CAAChB,KAAI,GAAI,CAAC;UAACf,IAAI,EAAE,IAAI;UAAEW,GAAG,EAAEO,IAAI,CAACa,KAAK,CAACV;QAAK,CAAC;QAC7D,KAAK,MAAMW,UAAS,IAAKf,aAAa,CAACc,KAAK,EAAE;UAC5Cb,IAAI,CAACa,KAAK,CAACT,OAAO,CAACW,IAAI,CAACD,UAAU,CAACA,UAAU,CAACE,MAAK,GAAI,CAAC,CAAC;QAC3D;MACF,CAAC;IACH;IACA;IACA,MAAMC,YAAW,GAAIA,CAAA,KAAM;MACzBzD,gBAAgB,CAAC,CAAC,EAAE,IAAI,EAAGoD,GAAG,IAAK;QACjC,IAAIA,GAAE,IAAKA,GAAG,CAACI,MAAM,EAAE;UACrBlB,eAAe,CAACe,KAAI,GAAIpD,MAAM,CAACmD,GAAG,CAAC;UACnCd,eAAe,CAACe,KAAK,CAACK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;UAClCP,YAAY,EAAE;QAChB;MACF,CAAC;IACH;IACA;IACA,MAAMQ,cAAa,GAAKC,GAAG,IAAK;MAC9BpB,IAAI,CAACa,KAAK,CAACT,OAAM,GAAI,EAAC;MACtB,KAAK,MAAMU,UAAS,IAAKM,GAAG,EAAE;QAC5BpB,IAAI,CAACa,KAAK,CAACT,OAAO,CAACW,IAAI,CAACD,UAAU,CAACA,UAAU,CAACE,MAAK,GAAI,CAAC,CAAC;MAC3D;IACF;IACA;IACA,MAAMK,eAAc,GAAKD,GAAG,IAAK;MAC/BpB,IAAI,CAACa,KAAK,CAACZ,SAAQ,GAAImB,GAAE;IAC3B;IACA;IACA,MAAME,aAAY,GAAKF,GAAG,IAAK;MAC7BpB,IAAI,CAACa,KAAK,CAACX,OAAM,GAAIkB,GAAE;IACzB;IACA;IACA,MAAMG,oBAAmB,GAAKX,GAAG,IAAK;MACpCZ,IAAI,CAACa,KAAK,CAACV,KAAI,GAAIS,GAAG,CAACY,IAAG;IAC5B;IACA;IACA,MAAMC,mBAAkB,GAAIA,CAAA,KAAM;MAChCzB,IAAI,CAACa,KAAK,CAACV,KAAI,GAAI,EAAC;MACpBX,UAAU,CAACqB,KAAK,CAAChB,KAAI,GAAI,EAAC;IAC5B;IACA;IACA,MAAM6B,OAAM,GAAIrE,GAAG,CAAC,IAAI;IACxB,MAAMsE,cAAa,GAAIA,CAAA,KAAM;MAC3BD,OAAO,CAACb,KAAK,CAACe,QAAQ,CAAEC,KAAK,IAAK;QAChC,IAAI,CAACA,KAAK,EAAE;UAAE,OAAO,KAAI;QAAE;QAC3B,IAAIzC,QAAQ,EAAE;UACZ,IAAI,OAAOY,IAAI,CAACa,KAAK,CAACZ,SAAQ,KAAM,QAAQ,EAAE;YAC5CD,IAAI,CAACa,KAAK,CAACZ,SAAQ,GAAI,IAAI6B,IAAI,CAAC9B,IAAI,CAACa,KAAK,CAACZ,SAAS;UACtD;UACA,IAAI,OAAOD,IAAI,CAACa,KAAK,CAACX,OAAM,KAAM,QAAQ,EAAE;YAC1CF,IAAI,CAACa,KAAK,CAACX,OAAM,GAAI,IAAI4B,IAAI,CAAC9B,IAAI,CAACa,KAAK,CAACX,OAAO;UAClD;UACAtC,cAAc,CAACoC,IAAI,CAACa,KAAK,EAAE,UAAUD,GAAG,EAAE;YACxC,IAAIA,GAAE,IAAKA,GAAG,CAACtB,EAAE,EAAE;cACjBU,IAAI,CAACa,KAAK,CAACvB,EAAC,GAAIsB,GAAG,CAACtB,EAAE;cACtBb,OAAO,CAAC,MAAM;cACd,IAAIsD,IAAG,GAAI9C,KAAK,CAAC+C,QAAQ;cACzBzE,MAAM,CAACwD,IAAI,CAAC;gBAACgB,IAAI;gBAAE1C,KAAK,EAAE;kBAACC,EAAE,EAAEU,IAAI,CAACa,KAAK,CAACvB,EAAE;kBAAE2C,IAAI,EAAE;gBAAS;cAAE,CAAC,CAAC;cACjE1C,QAAQ,CAACsB,KAAI,GAAI,SAAS;YAC5B;UACF,CAAC;QACH,OAAO;UACL,IAAI,OAAOb,IAAI,CAACa,KAAK,CAACZ,SAAQ,KAAM,QAAQ,EAAE;YAC5CD,IAAI,CAACa,KAAK,CAACZ,SAAQ,GAAI,IAAI6B,IAAI,CAAC9B,IAAI,CAACa,KAAK,CAACZ,SAAS;UACtD;UACA,IAAI,OAAOD,IAAI,CAACa,KAAK,CAACX,OAAM,KAAM,QAAQ,EAAE;YAC1CF,IAAI,CAACa,KAAK,CAACX,OAAM,GAAI,IAAI4B,IAAI,CAAC9B,IAAI,CAACa,KAAK,CAACX,OAAO;UAClD;UACAvC,YAAY,CAACqC,IAAI,CAACa,KAAK,EAAE,UAAUD,GAAG,EAAE;YACtC,IAAIA,GAAE,IAAKA,GAAG,CAACtB,EAAE,EAAE;cACjBU,IAAI,CAACa,KAAK,CAACvB,EAAC,GAAIsB,GAAG,CAACtB,EAAE;cACtBb,OAAO,CAAC,MAAM;cACd,IAAIsD,IAAG,GAAI9C,KAAK,CAAC+C,QAAQ;cACzBzE,MAAM,CAACwD,IAAI,CAAC;gBAACgB,IAAI;gBAAE1C,KAAK,EAAE;kBAACC,EAAE,EAAEU,IAAI,CAACa,KAAK,CAACvB,EAAE;kBAAE2C,IAAI,EAAE;gBAAS;cAAE,CAAC,CAAC;cACjE1C,QAAQ,CAACsB,KAAI,GAAI,SAAS;YAC5B;UACF,CAAC;QACH;MACF,CAAC;IACH;;IAEA;IACA,MAAMqB,WAAU,GAAI7E,GAAG,CAAC,EAAE;IAC1B,MAAM8E,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAI,EAAEnC,IAAI,CAACa,KAAI,IAAKb,IAAI,CAACa,KAAK,CAACvB,EAAE,CAAC,EAAE;QAClC;MACF;MACAnB,kBAAkB,CAAC;QAACiE,MAAM,EAAEpC,IAAI,CAACa,KAAK,CAACvB;MAAE,CAAC,EAAGsB,GAAG,IAAK;QACnD,IAAIA,GAAE,IAAKA,GAAG,CAACyB,IAAG,IAAKzB,GAAG,CAACyB,IAAI,CAACrB,MAAM,EAAE;UACtC,KAAK,MAAMsB,OAAM,IAAK1B,GAAG,CAACyB,IAAI,EAAE;YAC9B,IAAIC,OAAO,CAACC,kBAAiB,IAAKD,OAAO,CAACC,kBAAkB,CAACvB,MAAM,EAAE;cACnE,KAAK,MAAMwB,OAAM,IAAKF,OAAO,CAACC,kBAAkB,EAAE;gBAChD3D,QAAQ,CAACf,WAAW,CAAC2E,OAAO,CAACC,OAAO,EAAGC,MAAM,IAAK;kBAChDF,OAAO,CAACG,QAAO,GAAID,MAAK;gBAC1B,CAAC,CAAC;cACJ;YACF;UACF;UACAR,WAAW,CAACrB,KAAI,GAAID,GAAG,CAACyB,IAAI;QAC9B;MACF,CAAC;IACH;IACA,MAAMO,iBAAgB,GAAIvF,GAAG,CAAC,KAAK;IACnC,MAAMwF,WAAU,GAAIxF,GAAG,CAAC;MACtBiC,EAAE,EAAE,EAAE;MACN8C,MAAM,EAAE,EAAE;MACVU,KAAK,EAAE,EAAE;MACTzC,MAAM,EAAE;IACV,CAAC;IACD,MAAM0C,gBAAe,GAAI1F,GAAG,CAAC;MAC3ByF,KAAK,EAAE,CAAC;QAAEtC,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9DL,MAAM,EAAE,CAAC;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC;IAChE,CAAC;IACD,MAAMsC,WAAU,GAAKV,OAAO,IAAK;MAC/B,IAAIA,OAAM,IAAKA,OAAO,CAAChD,EAAE,EAAE;QACzBuD,WAAW,CAAChC,KAAI,GAAIyB,OAAO;MAC7B,OAAO;QACLO,WAAW,CAAChC,KAAK,CAACuB,MAAK,GAAIpC,IAAI,CAACa,KAAK,CAACvB,EAAE;MAC1C;MACAsD,iBAAiB,CAAC/B,KAAI,GAAI,IAAI;IAChC;IACA,MAAMoC,WAAU,GAAIA,CAAA,KAAM;MACxBL,iBAAiB,CAAC/B,KAAI,GAAI,KAAK;MAC/BgC,WAAW,CAAChC,KAAK,CAACiC,KAAI,GAAI,EAAC;MAC3BD,WAAW,CAAChC,KAAK,CAACR,MAAK,GAAI,EAAC;IAC9B;IACA,MAAM6C,aAAY,GAAK5D,EAAE,IAAK;MAC5BZ,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM;QAC5BR,iBAAiB,CAAC;UAACoB,EAAE,EAAEA;QAAE,CAAC,EAAE,MAAM;UAChCb,OAAO,CAAC,MAAM;UACd0D,WAAW,EAAC;QACd,CAAC;MACH,CAAC;IACH;IACA,MAAMgB,cAAa,GAAI9F,GAAG,CAAC,IAAI;IAC/B,MAAM+F,aAAY,GAAIA,CAAA,KAAM;MAC1BD,cAAc,CAACtC,KAAK,CAACe,QAAQ,CAAEC,KAAK,IAAK;QACvC,IAAI,CAACA,KAAK,EAAE;UAAE,OAAO,KAAI;QAAE;QAC3B,IAAIgB,WAAW,CAAChC,KAAK,CAACvB,EAAE,EAAE;UACxBrB,iBAAiB,CAAC4E,WAAW,CAAChC,KAAK,EAAE,YAAY;YAC/CpC,OAAO,CAAC,MAAM;YACdwE,WAAW,EAAC;YACZd,WAAW,EAAC;UACd,CAAC;QACH,OAAO;UACLnE,eAAe,CAAC6E,WAAW,CAAChC,KAAK,EAAE,YAAY;YAC7CpC,OAAO,CAAC,MAAM;YACdwE,WAAW,EAAC;YACZd,WAAW,EAAC;UACd,CAAC;QACH;MACF,CAAC;IACH;IACA,MAAMkB,wBAAuB,GAAIhG,GAAG,CAAC,KAAK;IAC1C,MAAMiG,kBAAiB,GAAIjG,GAAG,CAAC;MAC7BiC,EAAE,EAAE,EAAE;MACNiE,aAAa,EAAE,EAAE;MACjBT,KAAK,EAAE,EAAE;MACTL,OAAO,EAAE,EAAE;MACXpC,MAAM,EAAE;IACV,CAAC;IACD,MAAMmD,uBAAsB,GAAInG,GAAG,CAAC;MAClCyF,KAAK,EAAE,CAAC;QAAEtC,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9D+B,OAAO,EAAE,CAAC;QAAEjC,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAChEL,MAAM,EAAE,CAAC;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC;IAChE,CAAC;IACD,MAAM+C,kBAAiB,GAAIA,CAACF,aAAa,EAAEG,cAAc,KAAK;MAC5DL,wBAAwB,CAACxC,KAAI,GAAI,IAAI;MACrC,IAAI6C,cAAc,EAAE;QAClBJ,kBAAkB,CAACzC,KAAI,GAAI6C,cAAc;QACzCvE,KAAK,CAAC0B,KAAI,GAAI6C,cAAc,CAACf,QAAQ;MACvC,OAAO;QACLW,kBAAkB,CAACzC,KAAK,CAAC0C,aAAY,GAAIA,aAAY;MACvD;IACF;IACA,MAAMI,kBAAiB,GAAIA,CAAA,KAAM;MAC/BN,wBAAwB,CAACxC,KAAI,GAAI,KAAK;MACtCyC,kBAAkB,CAACzC,KAAI,GAAI;QAACvB,EAAE,EAAE,EAAE;QAAEiE,aAAa,EAAE,EAAE;QAAET,KAAK,EAAE,EAAE;QAAEL,OAAO,EAAE,EAAE;QAAEpC,MAAM,EAAE,EAAE;QAAEuD,SAAS,EAAE;MAAC;IACzG;IACA,MAAMC,oBAAmB,GAAKvE,EAAE,IAAK;MACnCZ,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM;QAC5BJ,wBAAwB,CAAC;UAACgB,EAAE,EAAEA;QAAE,CAAC,EAAE,MAAM;UACvCb,OAAO,CAAC,MAAM;UACd0D,WAAW,EAAC;QACd,CAAC;MACH,CAAC;IACH;IACA,MAAM2B,qBAAoB,GAAIzG,GAAG,CAAC,IAAI;IACtC,MAAM0G,oBAAmB,GAAIA,CAAA,KAAM;MACjCT,kBAAkB,CAACzC,KAAK,CAAC4B,OAAM,GAAItD,KAAK,CAAC0B,KAAK,CAACvB,EAAC,IAAKgE,kBAAkB,CAACzC,KAAK,CAAC4B,OAAO;MACrFqB,qBAAqB,CAACjD,KAAK,CAACe,QAAQ,CAAEC,KAAK,IAAK;QAC9C,IAAI,CAACA,KAAK,EAAE;UAAE,OAAO,KAAI;QAAE;QAC3B,IAAIyB,kBAAkB,CAACzC,KAAK,CAACvB,EAAE,EAAE;UAC/BjB,wBAAwB,CAACiF,kBAAkB,CAACzC,KAAK,EAAE,YAAY;YAC7DpC,OAAO,CAAC,MAAM;YACdkF,kBAAkB,EAAC;YACnBxB,WAAW,EAAC;UACd,CAAC;QACH,OAAO;UACL/D,sBAAsB,CAACkF,kBAAkB,CAACzC,KAAK,EAAE,YAAY;YAC3DpC,OAAO,CAAC,MAAM;YACdkF,kBAAkB,EAAC;YACnBxB,WAAW,EAAC;UACd,CAAC;QACH;MACF,CAAC;IACH;IACA;IACA,MAAM6B,SAAQ,GAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE;IACX;IACA,MAAMC,OAAM,GAAIA,CAAA,KAAM;MACpBtG,WAAW,CAAC;QAACwB,EAAE,EAAEU,IAAI,CAACa,KAAK,CAACvB;MAAE,CAAC,EAAE,MAAM;QACrCb,OAAO,CAAC,MAAM;QACduB,IAAI,CAACa,KAAK,CAACwD,MAAK,GAAI,WAAU;MAChC,CAAC;IACH;IACA,MAAMC,SAAQ,GAAIA,CAAA,KAAM;MACtBvG,aAAa,CAAC;QAACuB,EAAE,EAAEU,IAAI,CAACa,KAAK,CAACvB;MAAE,CAAC,EAAE,MAAM;QACvCb,OAAO,CAAC,QAAQ;QAChBuB,IAAI,CAACa,KAAK,CAACwD,MAAK,GAAI,aAAY;MAClC,CAAC;IACH;IACA;IACA,MAAME,KAAI,GAAI,CACZ;MAACC,GAAG,EAAE,MAAM;MAAE1F,IAAI,EAAE;IAAM,CAAC,EAC3B;MAAC0F,GAAG,EAAE,SAAS;MAAE1F,IAAI,EAAE;IAAM,CAAC,EAC9B;MAAC0F,GAAG,EAAE,SAAS;MAAE1F,IAAI,EAAE;IAAI,CAAC,CAC9B;IACA,MAAM2F,UAAS,GAAIpH,GAAG,CAAC,CAAC;IACxB,MAAMqH,mBAAkB,GAAIA,CAAA,KAAM;MAChC,MAAMC,eAAc,GAAI,EAAE;MAC1B,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIL,KAAK,CAACvD,MAAM,EAAE4D,CAAC,EAAE,EAAE;QACrC,MAAM3C,IAAG,GAAIsC,KAAK,CAACK,CAAC,CAAC;QACrBD,eAAe,CAAC5D,IAAI,CAACkB,IAAI,CAACuC,GAAG,CAAC;QAC9B,IAAIvC,IAAI,CAACuC,GAAE,KAAMjF,QAAQ,CAACsB,KAAK,EAAE;UAC/B4D,UAAU,CAAC5D,KAAI,GAAI+D,CAAC;UACpB;QACF;MACF;MACA,IAAIxF,QAAQ,EAAE;QACZqF,UAAU,CAAC5D,KAAI,GAAI0D,KAAK,CAACvD,MAAM;MACjC;MACA,OAAO2D,eAAe;IACxB;IACA,MAAME,IAAG,GAAIA,CAAA,KAAM;MACjB;MACA,IAAI5F,KAAK,CAACI,KAAK,CAAC4C,IAAI,EAAE;QACpB1C,QAAQ,CAACsB,KAAI,GAAI5B,KAAK,CAACI,KAAK,CAAC4C,IAAI;MACnC,OAAO;QACL1C,QAAQ,CAACsB,KAAI,GAAI,MAAK;MACxB;MACAb,IAAI,CAACa,KAAK,CAACvB,EAAC,GAAIL,KAAK,CAACI,KAAK,CAACC,EAAC,IAAK,EAAC;MACnC2B,YAAY,EAAE;MACdkB,WAAW,EAAE;IACf;IACA0C,IAAI,EAAC;IACL;IACA,MAAMC,SAAQ,GAAKN,GAAG,IAAK;MACzB,IAAI,CAACpF,QAAO,IAAKsF,mBAAmB,EAAE,CAACK,OAAO,CAACP,GAAG,IAAI,CAAC,EAAE;QACvD;MACF;MACAjF,QAAQ,CAACsB,KAAI,GAAI2D,GAAG;MACpB,IAAIzC,IAAG,GAAI9C,KAAK,CAAC+C,QAAQ;MACzBzE,MAAM,CAACwD,IAAI,CAAC;QAACgB,IAAI;QAAE1C,KAAK,EAAE;UAACC,EAAE,EAAEU,IAAI,CAACa,KAAK,CAACvB,EAAE;UAAE2C,IAAI,EAAEuC;QAAG;MAAE,CAAC,CAAC;IAC7D;IACAE,mBAAmB,EAAE;IACrB,MAAMM,SAAQ,GAAIA,CAAA,KAAM;MACtB9F,eAAe,CAAC2B,KAAI,GAAI,IAAI;IAC9B;IACA,MAAMoE,SAAQ,GAAIA,CAAA,KAAM;MACtB/F,eAAe,CAAC2B,KAAI,GAAI,KAAK;IAC/B;IACA,MAAMqE,oBAAmB,GAAKC,WAAW,IAAK;MAC5C,IAAG,CAACA,WAAU,IAAK,CAACA,WAAW,CAACnE,MAAM,EAAE;QACtCrC,KAAK,CAAC,OAAO,CAAC;QACd;MACF;MACAC,QAAQ,CAACf,WAAW,CAACsH,WAAW,CAAC,CAAC,CAAC,EAAGvE,GAAG,IAAK;QAC5CzB,KAAK,CAAC0B,KAAI,GAAID,GAAE;MAClB,CAAC;MACDqE,SAAS,EAAC;IACZ;IACA;IACA,OAAO;MACL;MACAzF,UAAU;MACVM,eAAe;MACfE,IAAI;MACJD,aAAa;MACbQ,SAAS;MACTmB,OAAO;MACPP,cAAc;MACdE,eAAe;MACfC,aAAa;MACbC,oBAAoB;MACpBE,mBAAmB;MACnBE,cAAc;MACd;MACAO,WAAW;MACXU,iBAAiB;MACjBC,WAAW;MACXE,gBAAgB;MAChBM,wBAAwB;MACxBC,kBAAkB;MAClBE,uBAAuB;MACvBL,cAAc;MACdW,qBAAqB;MACrBd,WAAW;MACXC,WAAW;MACXQ,kBAAkB;MAClBE,kBAAkB;MAClBT,aAAa;MACbW,oBAAoB;MACpBT,aAAa;MACbW,oBAAoB;MACpB;MACAC,SAAS;MACTI,OAAO;MACPE,SAAS;MACT;MACAC,KAAK;MACLE,UAAU;MACVlF,QAAQ;MACRuF,SAAS;MACT5F,eAAe;MACf8F,SAAS;MACTC,SAAS;MACT9F,KAAK;MACL+F;IACF,CAAC;EACH;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}