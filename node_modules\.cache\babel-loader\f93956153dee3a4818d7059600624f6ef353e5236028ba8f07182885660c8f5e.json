{"ast": null, "code": "import { ref } from \"vue\";\nimport { getAuthorityList } from \"../../../api/auth/authority\";\nexport default {\n  name: \"AuthAuthorityIndex\",\n  setup() {\n    const param = ref({\n      keyword: \"\"\n    });\n    let authorityList = ref([]);\n    const loadAuthorityList = () => {\n      getAuthorityList(param.value, res => {\n        authorityList.value = res.list;\n      });\n    };\n    loadAuthorityList();\n    return {\n      param,\n      authorityList,\n      expand: true,\n      loadAuthorityList\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "getAuthorityList", "name", "setup", "param", "keyword", "authorityList", "loadAuthorityList", "value", "res", "list", "expand"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/auth/authority/index.vue"], "sourcesContent": ["<template>\n  <div class=\"authority-container\">\n    <div class=\"head\">\n      <el-input size=\"small\" v-model=\"param.keyword\" clearable placeholder=\"输入姓名搜索\" class=\"custom-input\" @keyup.enter=\"loadAuthorityList\">\n        <template #append>\n          <el-button size=\"small\" class=\"custom-btn\" icon=\"el-icon-search\" @click=\"loadAuthorityList\">搜索</el-button>\n        </template>\n      </el-input>\n    </div>\n    <el-table row-key=\"id\" :data=\"authorityList\" default-expand-all :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\" size=\"small\">\n      <el-table-column prop=\"alias\" label=\"别名\" sortable width=\"180\"></el-table-column>\n      <el-table-column prop=\"name\" label=\"名称\" sortable width=\"180\"></el-table-column>\n      <el-table-column prop=\"createTime\" label=\"创建日期\"></el-table-column>\n    </el-table>\n  </div>\n</template>\n\n<script>\nimport {ref} from \"vue\"\nimport {getAuthorityList} from \"../../../api/auth/authority\"\nexport default {\n  name: \"AuthAuthorityIndex\",\n  setup() {\n    const param = ref({keyword: \"\"})\n    let authorityList = ref([])\n    const loadAuthorityList = () => {\n      getAuthorityList(param.value, (res) => {\n        authorityList.value = res.list\n      })\n    }\n    loadAuthorityList()\n    return {\n      param,\n      authorityList,\n      expand: true,\n      loadAuthorityList\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n  .authority-container {\n    padding: 20px;\n    .head {\n      margin-bottom: 10px;\n      .custom-input {\n        width: 50%;\n        min-width: 200px;\n        max-width: 400px;\n      }\n      .custom-btn {\n        &:hover {\n          color: $--color-primary;\n        }\n      }\n    }\n  }\n</style>\n"], "mappings": "AAkBA,SAAQA,GAAG,QAAO,KAAI;AACtB,SAAQC,gBAAgB,QAAO,6BAA4B;AAC3D,eAAe;EACbC,IAAI,EAAE,oBAAoB;EAC1BC,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIJ,GAAG,CAAC;MAACK,OAAO,EAAE;IAAE,CAAC;IAC/B,IAAIC,aAAY,GAAIN,GAAG,CAAC,EAAE;IAC1B,MAAMO,iBAAgB,GAAIA,CAAA,KAAM;MAC9BN,gBAAgB,CAACG,KAAK,CAACI,KAAK,EAAGC,GAAG,IAAK;QACrCH,aAAa,CAACE,KAAI,GAAIC,GAAG,CAACC,IAAG;MAC/B,CAAC;IACH;IACAH,iBAAiB,EAAC;IAClB,OAAO;MACLH,KAAK;MACLE,aAAa;MACbK,MAAM,EAAE,IAAI;MACZJ;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}