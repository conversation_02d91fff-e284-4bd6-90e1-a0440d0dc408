{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, withCtx as _withCtx, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-3bc51ac8\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"content\"\n};\nconst _hoisted_2 = {\n  class: \"interval-box\"\n};\nconst _hoisted_3 = {\n  class: \"image-list\"\n};\nconst _hoisted_4 = {\n  class: \"image-list-header-box\"\n};\nconst _hoisted_5 = [\"onClick\"];\nconst _hoisted_6 = {\n  class: \"choice-image\"\n};\nconst _hoisted_7 = {\n  class: \"choice-title\"\n};\nconst _hoisted_8 = {\n  class: \"choice-link\"\n};\nconst _hoisted_9 = {\n  class: \"submit-btn\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_slider = _resolveComponent(\"el-slider\");\n  const _component_choice_image = _resolveComponent(\"choice-image\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_choice_link = _resolveComponent(\"choice-link\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"span\", null, \"轮播间隔(\" + _toDisplayString($setup.interval) + \"S)\", 1 /* TEXT */), _createVNode(_component_el_slider, {\n    modelValue: $setup.interval,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.interval = $event),\n    min: 2,\n    max: 10\n  }, null, 8 /* PROPS */, [\"modelValue\"])]), _createCommentVNode(\"      <draggable v-model=\\\"carouselList\\\" tag=\\\"ul\\\" class=\\\"image-list\\\" :animation=\\\"200\\\" group=\\\"people\\\" @start=\\\"drag = true\\\" @end=\\\"drag = false\\\">\"), _createElementVNode(\"ul\", _hoisted_3, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.carouselList, (item, index) => {\n    return _openBlock(), _createElementBlock(\"li\", {\n      key: index\n    }, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"span\", null, \"轮播图片 \" + _toDisplayString(index + 1), 1 /* TEXT */), $setup.isDeleteItem ? (_openBlock(), _createElementBlock(\"span\", {\n      key: 0,\n      onClick: $event => $setup.deleteItem(index)\n    }, \"删除\", 8 /* PROPS */, _hoisted_5)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_choice_image, {\n      index: index,\n      item: item,\n      onOnSuccess: $setup.uploadCallback,\n      onOnRemove: $setup.uploadCallback\n    }, null, 8 /* PROPS */, [\"index\", \"item\", \"onOnSuccess\", \"onOnRemove\"])]), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_input, {\n      placeholder: \"请输入标题\",\n      modelValue: item.title,\n      \"onUpdate:modelValue\": $event => item.title = $event\n    }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_choice_link, {\n      index: index,\n      item: item,\n      onChangeLinkType: $setup.changeLinkType,\n      onChangeLink: $setup.changeLink\n    }, null, 8 /* PROPS */, [\"index\", \"item\", \"onChangeLinkType\", \"onChangeLink\"])])]);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\"      </draggable>\"), _createVNode(_component_el_button, {\n    class: \"add-btn\",\n    onClick: $setup.addItem\n  }, {\n    default: _withCtx(() => [_createTextVNode(\"添加图片\")]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"]), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.save\n  }, {\n    default: _withCtx(() => [_createTextVNode(\"保存\")]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_toDisplayString", "$setup", "interval", "_createVNode", "_component_el_slider", "$event", "min", "max", "_createCommentVNode", "_hoisted_3", "_Fragment", "_renderList", "carouselList", "item", "index", "key", "_hoisted_4", "isDeleteItem", "onClick", "deleteItem", "_hoisted_5", "_hoisted_6", "_component_choice_image", "onOnSuccess", "uploadCallback", "onOnRemove", "_hoisted_7", "_component_el_input", "placeholder", "title", "_hoisted_8", "_component_choice_link", "onChangeLinkType", "changeLinkType", "onChangeLink", "changeLink", "_component_el_button", "addItem", "_hoisted_9", "type", "save"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/setting/carousel/index.vue"], "sourcesContent": ["<template>\n  <div>\n    <div class=\"content\">\n      <div class=\"interval-box\">\n        <span>轮播间隔({{interval}}S)</span>\n        <el-slider v-model=\"interval\" :min=\"2\" :max=\"10\"/>\n      </div>\n      <!--      <draggable v-model=\"carouselList\" tag=\"ul\" class=\"image-list\" :animation=\"200\" group=\"people\" @start=\"drag = true\" @end=\"drag = false\">-->\n      <ul class=\"image-list\">\n        <li v-for=\"(item, index) in carouselList\" :key=\"index\">\n          <div class=\"image-list-header-box\">\n            <span>轮播图片 {{index + 1}}</span>\n            <span v-if=\"isDeleteItem\" @click=\"deleteItem(index)\">删除</span>\n          </div>\n          <div class=\"choice-image\">\n            <choice-image :index=\"index\" :item=\"item\" @on-success=\"uploadCallback\" @on-remove=\"uploadCallback\"/>\n          </div>\n          <div class=\"choice-title\">\n            <el-input placeholder=\"请输入标题\" v-model=\"item.title\"/>\n          </div>\n          <div class=\"choice-link\">\n            <choice-link :index=\"index\" :item=\"item\" @change-link-type=\"changeLinkType\" @change-link=\"changeLink\"/>\n          </div>\n        </li>\n      </ul>\n      <!--      </draggable>-->\n      <el-button class=\"add-btn\" @click=\"addItem\">添加图片</el-button>\n      <div class=\"submit-btn\">\n        <el-button type=\"primary\" @click=\"save\">保存</el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {ref, reactive} from \"vue\";\nimport ChoiceImage from \"./choiceImage\";\nimport ChoiceLink from \"./choiceLink\";\nimport {saveCarousel, getCarousel} from \"@/api/setting/carousel\";\nimport {success, error} from \"@/util/tipsUtils\";\nimport {deleteFile} from \"@/api/oss/oss\";\nexport default {\n  name: \"CarouselIndex\",\n  components: {\n    ChoiceImage,\n    ChoiceLink\n  },\n  setup() {\n    const isDeleteItem = ref(true)\n    // 轮播图Item\n    const carouselItem = reactive({\n      // 链接的标题\n      title: \"\",\n      // 图片地址\n      imageUrl: \"\",\n      // 链接类型\n      linkType: \"0\",\n      // 链接\n      link: \"\"\n    })\n    // 轮播秒数间隔\n    const interval = ref(3);\n    // 轮播图列表\n    const carouselList = ref([]);\n    const load = () => {\n      getCarousel({}, (res) => {\n        const carouselJsonStr = res.carouselJson;\n        if (carouselJsonStr) {\n          const carousel = JSON.parse(carouselJsonStr);\n          interval.value = carousel.interval;\n          carouselList.value = carousel.carouselList;\n        } else {\n          carouselList.value.push(carouselItem)\n        }\n      })\n    }\n    load();\n    // 添加图片\n    const addItem = () => {\n      isDeleteItem.value = true\n      carouselList.value.push(carouselItem)\n    }\n    const deletedItem = [];\n    // 删除图片\n    const deleteItem = (index) => {\n      deletedItem.push(carouselList.value[index])\n      carouselList.value.splice(index, 1)\n      isDeleteItem.value = carouselList.value.length > 1\n    }\n    // 图片回传\n    const uploadCallback = (val) => {\n      console.log(val)\n      carouselList.value[val.index].imageUrl = val.link\n      console.log(carouselList.value)\n    }\n    // 链接\n    const changeLink = (index, val) => {\n      console.log(index)\n      console.log(val)\n      carouselList.value[index].link = val\n      console.log(carouselList.value)\n    }\n    // 链接类型\n    const changeLinkType = (index, val) => {\n      carouselList.value[index].linkType = val\n      console.log(carouselList.value)\n    }\n    // 保存\n    const save = () => {\n      for (let i = 0; i < carouselList.value.length; i++) {\n        const item = carouselList.value[i]\n        if (!item.imageUrl) {\n          error(\"请添加图片\"+ (i + 1) +\"的图片\")\n          return\n        }\n        if (item.linkType !== \"0\") {\n          if (item.link === \"\") {\n            error(\"请输入图片\"+ (i + 1) +\"的链接\")\n            return\n          }\n        } else {\n          item.link = \"\"\n        }\n      }\n      const param = {}\n      param.interval = interval.value\n      param.carouselList = carouselList.value\n      saveCarousel({ carouselJson: JSON.stringify(param) }, () => {\n        success(\"保存成功\")\n        // 删除被删除的图片\n        for (let i = 0; i < deletedItem.length; i++) {\n          const item = deletedItem[i];\n          if (item.link && item.link.indexOf(\"http://\") > -1) {\n            deleteFile(item.link);\n          }\n        }\n        setTimeout(() => {location.reload()}, 500)\n      })\n    }\n    return {\n      isDeleteItem,\n      // 轮播图Item\n      carouselItem,\n      // 轮播秒数间隔\n      interval,\n      // 轮播图列表\n      carouselList,\n      load,\n      addItem,\n      deleteItem,\n      uploadCallback,\n      changeLink,\n      changeLinkType,\n      save\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.content {\n  padding: 0 50px;\n  .interval-box {\n    height: 40px;\n    align-items: center;\n    font-size: 14px;\n    margin-top: 20px;\n    display: flex;\n    span {\n      margin-right: 20px;\n    }\n    .el-slider {\n      width: calc(100% - 108px);\n    }\n  }\n  .add-btn {\n    width: 100%;\n  }\n  .image-list {\n    width: 100%;\n    margin-top: 20px;\n    margin-bottom: 20px;\n    li {\n      width: 100%;\n      min-height: 195px;\n      border: 1px solid #dcdfe6;\n      border-radius: 8px;\n      margin-bottom: 20px;\n      .image-list-header-box {\n        height: 30px;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        font-size: 12px;\n        padding: 0 20px;\n        box-sizing: border-box;\n        border-bottom: 1px solid #dcdfe6;\n        span:last-child {\n          font-size: 13px;\n          color: #409eff;\n          cursor: pointer;\n        }\n      }\n      .choice-image {\n        height: 100%;\n      }\n      .choice-title {\n        margin: 0 20px 20px;\n      }\n    }\n  }\n  .submit-btn {\n    text-align: center;\n    margin: 20px 0;\n  }\n}\n.choice-link {\n  margin-bottom: 10px;\n}\n.el-dialog__wrapper ::v-deep .el-dialog__body {\n  padding: 10px 20px;\n}\n</style>\n"], "mappings": ";;;EAESA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAc;;EAKrBA,KAAK,EAAC;AAAY;;EAEbA,KAAK,EAAC;AAAuB;;;EAI7BA,KAAK,EAAC;AAAc;;EAGpBA,KAAK,EAAC;AAAc;;EAGpBA,KAAK,EAAC;AAAa;;EAOvBA,KAAK,EAAC;AAAY;;;;;;;uBA1B3BC,mBAAA,CA8BM,cA7BJC,mBAAA,CA4BM,OA5BNC,UA4BM,GA3BJD,mBAAA,CAGM,OAHNE,UAGM,GAFJF,mBAAA,CAAgC,cAA1B,OAAK,GAAAG,gBAAA,CAAEC,MAAA,CAAAC,QAAQ,IAAE,IAAE,iBACzBC,YAAA,CAAkDC,oBAAA;gBAA9BH,MAAA,CAAAC,QAAQ;+DAARD,MAAA,CAAAC,QAAQ,GAAAG,MAAA;IAAGC,GAAG,EAAE,CAAC;IAAGC,GAAG,EAAE;6CAE/CC,mBAAA,+JAAoJ,EACpJX,mBAAA,CAgBK,MAhBLY,UAgBK,I,kBAfHb,mBAAA,CAcKc,SAAA,QAAAC,WAAA,CAduBV,MAAA,CAAAW,YAAY,GAA5BC,IAAI,EAAEC,KAAK;yBAAvBlB,mBAAA,CAcK;MAdsCmB,GAAG,EAAED;IAAK,IACnDjB,mBAAA,CAGM,OAHNmB,UAGM,GAFJnB,mBAAA,CAA+B,cAAzB,OAAK,GAAAG,gBAAA,CAAEc,KAAK,sBACNb,MAAA,CAAAgB,YAAY,I,cAAxBrB,mBAAA,CAA8D;;MAAnCsB,OAAK,EAAAb,MAAA,IAAEJ,MAAA,CAAAkB,UAAU,CAACL,KAAK;OAAG,IAAE,iBAAAM,UAAA,K,qCAEzDvB,mBAAA,CAEM,OAFNwB,UAEM,GADJlB,YAAA,CAAoGmB,uBAAA;MAArFR,KAAK,EAAEA,KAAK;MAAGD,IAAI,EAAEA,IAAI;MAAGU,WAAU,EAAEtB,MAAA,CAAAuB,cAAc;MAAGC,UAAS,EAAExB,MAAA,CAAAuB;+EAErF3B,mBAAA,CAEM,OAFN6B,UAEM,GADJvB,YAAA,CAAoDwB,mBAAA;MAA1CC,WAAW,EAAC,OAAO;kBAAUf,IAAI,CAACgB,KAAK;uCAAVhB,IAAI,CAACgB,KAAK,GAAAxB;sEAEnDR,mBAAA,CAEM,OAFNiC,UAEM,GADJ3B,YAAA,CAAuG4B,sBAAA;MAAzFjB,KAAK,EAAEA,KAAK;MAAGD,IAAI,EAAEA,IAAI;MAAGmB,gBAAgB,EAAE/B,MAAA,CAAAgC,cAAc;MAAGC,YAAW,EAAEjC,MAAA,CAAAkC;;oCAIhG3B,mBAAA,sBAAyB,EACzBL,YAAA,CAA4DiC,oBAAA;IAAjDzC,KAAK,EAAC,SAAS;IAAEuB,OAAK,EAAEjB,MAAA,CAAAoC;;sBAAS,MAAI,C,iBAAJ,MAAI,E;;kCAChDxC,mBAAA,CAEM,OAFNyC,UAEM,GADJnC,YAAA,CAAsDiC,oBAAA;IAA3CG,IAAI,EAAC,SAAS;IAAErB,OAAK,EAAEjB,MAAA,CAAAuC;;sBAAM,MAAE,C,iBAAF,IAAE,E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}