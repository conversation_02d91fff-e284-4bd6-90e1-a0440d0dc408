{"ast": null, "code": "import { ref } from \"vue\";\nimport { findList, updateTag, saveTag, deleteTag } from \"@/api/member/tag\";\nimport Page from \"../../../components/Page\";\nimport { confirm, error, success } from \"@/util/tipsUtils\";\nexport default {\n  name: \"MemberTag\",\n  components: {\n    Page\n  },\n  props: {\n    cancelCallback: {\n      type: Function,\n      default: () => {}\n    },\n    selectCallback: {\n      type: Function,\n      default: () => {}\n    },\n    isComponent: {\n      type: Boolean,\n      default: false\n    }\n  },\n  setup(props) {\n    const list = ref([]);\n    const total = ref(0);\n    const dataLoading = ref(true);\n    const searchParam = ref({\n      name: \"\",\n      size: 20,\n      current: 1\n    });\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true;\n      findList(searchParam.value, res => {\n        dataLoading.value = false;\n        if (!res) {\n          return;\n        }\n        list.value = res.list;\n        total.value = res.total;\n      }).catch(() => {\n        dataLoading.value = false;\n      });\n    };\n    loadList();\n    const currentChange = currentPage => {\n      searchParam.value.current = currentPage;\n      loadList();\n    };\n    const sizeChange = s => {\n      searchParam.value.size = s;\n      loadList();\n    };\n    // 搜索\n    const search = () => {\n      loadList();\n    };\n    const memberTagRules = {\n      name: [{\n        required: true,\n        message: \"请输入名称\",\n        trigger: \"blur\"\n      }]\n    };\n    const memberTag = ref({});\n    const memberTagRef = ref(null);\n    const showMemberTagFormDialog = ref(false);\n    const hideMemberTagForm = () => {\n      showMemberTagFormDialog.value = false;\n      memberTag.value = {};\n    };\n    const add = () => {\n      showMemberTagFormDialog.value = true;\n    };\n    // 编辑\n    const edit = item => {\n      memberTag.value = item;\n      showMemberTagFormDialog.value = true;\n    };\n    //提交\n    const submitMemberTag = () => {\n      memberTagRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (memberTag.value.id) {\n          updateTag(memberTag.value, () => {\n            success(\"修改成功\");\n            loadList();\n            hideMemberTagForm();\n          });\n        } else {\n          saveTag(memberTag.value, () => {\n            success(\"新增成功\");\n            loadList();\n            hideMemberTagForm();\n          });\n        }\n      });\n    };\n    const multipleSelection = ref([]);\n    const handleSelectionChange = val => {\n      multipleSelection.value = val;\n    };\n    const selectSelectionChange = () => {\n      if (!multipleSelection.value.length) {\n        error(\"请至少选择一个\");\n      }\n      props.selectCallback && props.selectCallback(multipleSelection.value);\n    };\n    const remove = item => {\n      confirm(\"确认永久删除吗？\", \"提示\", () => {\n        deleteTag(item.id, () => {\n          success(\"修改成功\");\n          loadList();\n        });\n      }, () => {});\n    };\n    return {\n      remove,\n      handleSelectionChange,\n      selectSelectionChange,\n      list,\n      total,\n      searchParam,\n      search,\n      currentChange,\n      sizeChange,\n      showMemberTagFormDialog,\n      add,\n      memberTag,\n      memberTagRef,\n      edit,\n      hideMemberTagForm,\n      submitMemberTag,\n      memberTagRules,\n      dataLoading\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "findList", "updateTag", "saveTag", "deleteTag", "Page", "confirm", "error", "success", "name", "components", "props", "cancelCallback", "type", "Function", "default", "selectCallback", "isComponent", "Boolean", "setup", "list", "total", "dataLoading", "searchParam", "size", "current", "loadList", "value", "res", "catch", "currentChange", "currentPage", "sizeChange", "s", "search", "memberTagRules", "required", "message", "trigger", "memberTag", "memberTagRef", "showMemberTagFormDialog", "hideMemberTagForm", "add", "edit", "item", "submitMemberTag", "validate", "valid", "id", "multipleSelection", "handleSelectionChange", "val", "selectSelectionChange", "length", "remove"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/member/tag/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"header\">\n      <el-form :inline=\"true\" :model=\"searchParam\" class=\"demo-form-inline\">\n        <el-form-item label=\"\">\n          <el-input class=\"search-input\" v-model=\"searchParam.name\" placeholder=\"请输入关键字\">\n            <template #append>\n              <el-button class=\"search-btn\" type=\"primary\" @click=\"search\">搜索</el-button>\n            </template>\n          </el-input>\n        </el-form-item>\n        <el-form-item v-if=\"!isComponent\">\n          <el-button type=\"primary\" @click=\"add\">创建标签</el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"content\">\n      <div class=\"content-list\">\n        <el-table v-loading=\"dataLoading\" :data=\"list\" style=\"width: 100%;\" @selection-change=\"handleSelectionChange\">\n          <el-table-column type=\"selection\" width=\"45\" v-if=\"isComponent\"/>\n          <el-table-column label=\"序号\" width=\"70\" type=\"index\"/>\n          <el-table-column prop=\"name\" label=\"名称\"/>\n          <el-table-column prop=\"sortOrder\" label=\"排序\"/>\n          <el-table-column label=\"操作\" width=\"150\" v-if=\"!isComponent\">\n            <template #default=\"scope\">\n              <el-button type=\"text\" @click=\"edit(scope.row)\">编辑</el-button>\n              <el-button type=\"text\" @click=\"remove(scope.row)\">编辑</el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n    </div>\n    <page style=\"margin-top: 20px;\" :total=\"total\" :current-change=\"currentChange\" :size-change=\"sizeChange\" :page-size=\"searchParam.size\"></page>\n    <el-dialog title=\"编辑会员标签\" v-model=\"showMemberTagFormDialog\" :before-close=\"hideMemberTagForm\">\n      <el-form :model=\"memberTag\" :rules=\"memberTagRules\" ref=\"memberTagRef\">\n        <el-form-item label=\"名称：\" label-width=\"150px\" prop=\"name\">\n          <el-input v-model=\"memberTag.name\" placeholder=\"请输入名称\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"排序：\" label-width=\"150px\" prop=\"description\">\n          <el-input v-model=\"memberTag.sortOrder\" placeholder=\"请输入排序，数值越大排序越前\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button @click=\"hideMemberTagForm\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"submitMemberTag\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n    <template v-if=\"isComponent\">\n      <div class=\"dialog-footer\" style=\"text-align: right;margin-top: 30px;\">\n        <el-button @click=\"cancelCallback\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"selectSelectionChange\">确 定</el-button>\n      </div>\n    </template>\n  </div>\n</template>\n\n<script>\n  import {ref} from \"vue\"\n  import {findList, updateTag, saveTag, deleteTag} from \"@/api/member/tag\"\n  import Page from \"../../../components/Page\"\n  import {confirm, error, success} from \"@/util/tipsUtils\";\n\n  export default {\n    name: \"MemberTag\",\n    components: {\n      Page\n    },\n    props: {\n      cancelCallback: {\n        type: Function,\n        default: () => {}\n      },\n      selectCallback: {\n        type: Function,\n        default: () => {}\n      },\n      isComponent: {\n        type: Boolean,\n        default: false\n      }\n    },\n    setup(props) {\n      const list = ref([])\n      const total = ref(0)\n      const dataLoading = ref(true)\n      const searchParam = ref({\n        name: \"\",\n        size: 20,\n        current: 1\n      })\n      // 加载列表\n      const loadList = () => {\n        dataLoading.value = true\n        findList(searchParam.value, (res) => {\n          dataLoading.value = false\n          if (!res) {return;}\n          list.value = res.list;\n          total.value = res.total;\n        }).catch(() => {\n          dataLoading.value = false\n        })\n      }\n      loadList();\n      const currentChange = (currentPage) => {\n        searchParam.value.current = currentPage;\n        loadList();\n      }\n      const sizeChange = (s) => {\n        searchParam.value.size = s;\n        loadList();\n      }\n      // 搜索\n      const search = () => {\n        loadList();\n      }\n      const memberTagRules = {\n        name: [{ required: true, message: \"请输入名称\", trigger: \"blur\" }],\n      }\n      const memberTag = ref({})\n      const memberTagRef = ref(null)\n      const showMemberTagFormDialog = ref(false)\n      const hideMemberTagForm = () => {\n        showMemberTagFormDialog.value = false;\n        memberTag.value = {}\n      }\n      const add = () => {\n        showMemberTagFormDialog.value = true;\n      }\n      // 编辑\n      const edit = (item) => {\n        memberTag.value = item\n        showMemberTagFormDialog.value = true;\n      }\n      //提交\n      const submitMemberTag = () => {\n        memberTagRef.value.validate(valid => {\n          if (!valid) {\n            return false;\n          }\n          if (memberTag.value.id) {\n            updateTag(memberTag.value, () => {\n              success(\"修改成功\")\n              loadList()\n              hideMemberTagForm()\n            });\n          } else {\n            saveTag(memberTag.value, () => {\n              success(\"新增成功\")\n              loadList()\n              hideMemberTagForm()\n            });\n          }\n        })\n      }\n\n      const multipleSelection = ref([])\n      const handleSelectionChange = (val) => {\n        multipleSelection.value = val;\n      }\n      const selectSelectionChange = () => {\n        if (!multipleSelection.value.length) {\n          error(\"请至少选择一个\")\n        }\n        props.selectCallback && props.selectCallback(multipleSelection.value)\n      }\n\n      const remove = (item) => {\n        confirm(\"确认永久删除吗？\", \"提示\", () => {\n          deleteTag(item.id, () => {\n            success(\"修改成功\")\n            loadList()\n          })\n        }, () => {\n        })\n      }\n\n      return {\n        remove,\n        handleSelectionChange,\n        selectSelectionChange,\n        list,\n        total,\n        searchParam,\n        search,\n        currentChange,\n        sizeChange,\n        showMemberTagFormDialog,\n        add,\n        memberTag,\n        memberTagRef,\n        edit,\n        hideMemberTagForm,\n        submitMemberTag,\n        memberTagRules,\n        dataLoading,\n      };\n    }\n  };\n</script>\n<style lang=\"scss\">\n  .header {\n    .el-form {\n      .el-form-item {\n        .el-form-item__content {\n          line-height: 28px;\n          .search-btn {\n            &:hover {\n              color: $--color-primary;\n            }\n          }\n        }\n      }\n    }\n  }\n</style>\n<style scoped lang=\"scss\">\n  .app-container {\n    margin: 20px;\n    .content-list {\n      margin: 0;\n      padding: 0;\n      border: 0;\n      font: inherit;\n      vertical-align: baseline;\n    }\n    .search-input {\n      width: 242px;\n    }\n  }\n</style>\n"], "mappings": "AA2DE,SAAQA,GAAG,QAAO,KAAI;AACtB,SAAQC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,SAAS,QAAO,kBAAiB;AACvE,OAAOC,IAAG,MAAO,0BAAyB;AAC1C,SAAQC,OAAO,EAAEC,KAAK,EAAEC,OAAO,QAAO,kBAAkB;AAExD,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAE;IACVL;EACF,CAAC;EACDM,KAAK,EAAE;IACLC,cAAc,EAAE;MACdC,IAAI,EAAEC,QAAQ;MACdC,OAAO,EAAEA,CAAA,KAAM,CAAC;IAClB,CAAC;IACDC,cAAc,EAAE;MACdH,IAAI,EAAEC,QAAQ;MACdC,OAAO,EAAEA,CAAA,KAAM,CAAC;IAClB,CAAC;IACDE,WAAW,EAAE;MACXJ,IAAI,EAAEK,OAAO;MACbH,OAAO,EAAE;IACX;EACF,CAAC;EACDI,KAAKA,CAACR,KAAK,EAAE;IACX,MAAMS,IAAG,GAAIpB,GAAG,CAAC,EAAE;IACnB,MAAMqB,KAAI,GAAIrB,GAAG,CAAC,CAAC;IACnB,MAAMsB,WAAU,GAAItB,GAAG,CAAC,IAAI;IAC5B,MAAMuB,WAAU,GAAIvB,GAAG,CAAC;MACtBS,IAAI,EAAE,EAAE;MACRe,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC;IACD;IACA,MAAMC,QAAO,GAAIA,CAAA,KAAM;MACrBJ,WAAW,CAACK,KAAI,GAAI,IAAG;MACvB1B,QAAQ,CAACsB,WAAW,CAACI,KAAK,EAAGC,GAAG,IAAK;QACnCN,WAAW,CAACK,KAAI,GAAI,KAAI;QACxB,IAAI,CAACC,GAAG,EAAE;UAAC;QAAO;QAClBR,IAAI,CAACO,KAAI,GAAIC,GAAG,CAACR,IAAI;QACrBC,KAAK,CAACM,KAAI,GAAIC,GAAG,CAACP,KAAK;MACzB,CAAC,CAAC,CAACQ,KAAK,CAAC,MAAM;QACbP,WAAW,CAACK,KAAI,GAAI,KAAI;MAC1B,CAAC;IACH;IACAD,QAAQ,EAAE;IACV,MAAMI,aAAY,GAAKC,WAAW,IAAK;MACrCR,WAAW,CAACI,KAAK,CAACF,OAAM,GAAIM,WAAW;MACvCL,QAAQ,EAAE;IACZ;IACA,MAAMM,UAAS,GAAKC,CAAC,IAAK;MACxBV,WAAW,CAACI,KAAK,CAACH,IAAG,GAAIS,CAAC;MAC1BP,QAAQ,EAAE;IACZ;IACA;IACA,MAAMQ,MAAK,GAAIA,CAAA,KAAM;MACnBR,QAAQ,EAAE;IACZ;IACA,MAAMS,cAAa,GAAI;MACrB1B,IAAI,EAAE,CAAC;QAAE2B,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC;IAC9D;IACA,MAAMC,SAAQ,GAAIvC,GAAG,CAAC,CAAC,CAAC;IACxB,MAAMwC,YAAW,GAAIxC,GAAG,CAAC,IAAI;IAC7B,MAAMyC,uBAAsB,GAAIzC,GAAG,CAAC,KAAK;IACzC,MAAM0C,iBAAgB,GAAIA,CAAA,KAAM;MAC9BD,uBAAuB,CAACd,KAAI,GAAI,KAAK;MACrCY,SAAS,CAACZ,KAAI,GAAI,CAAC;IACrB;IACA,MAAMgB,GAAE,GAAIA,CAAA,KAAM;MAChBF,uBAAuB,CAACd,KAAI,GAAI,IAAI;IACtC;IACA;IACA,MAAMiB,IAAG,GAAKC,IAAI,IAAK;MACrBN,SAAS,CAACZ,KAAI,GAAIkB,IAAG;MACrBJ,uBAAuB,CAACd,KAAI,GAAI,IAAI;IACtC;IACA;IACA,MAAMmB,eAAc,GAAIA,CAAA,KAAM;MAC5BN,YAAY,CAACb,KAAK,CAACoB,QAAQ,CAACC,KAAI,IAAK;QACnC,IAAI,CAACA,KAAK,EAAE;UACV,OAAO,KAAK;QACd;QACA,IAAIT,SAAS,CAACZ,KAAK,CAACsB,EAAE,EAAE;UACtB/C,SAAS,CAACqC,SAAS,CAACZ,KAAK,EAAE,MAAM;YAC/BnB,OAAO,CAAC,MAAM;YACdkB,QAAQ,EAAC;YACTgB,iBAAiB,EAAC;UACpB,CAAC,CAAC;QACJ,OAAO;UACLvC,OAAO,CAACoC,SAAS,CAACZ,KAAK,EAAE,MAAM;YAC7BnB,OAAO,CAAC,MAAM;YACdkB,QAAQ,EAAC;YACTgB,iBAAiB,EAAC;UACpB,CAAC,CAAC;QACJ;MACF,CAAC;IACH;IAEA,MAAMQ,iBAAgB,GAAIlD,GAAG,CAAC,EAAE;IAChC,MAAMmD,qBAAoB,GAAKC,GAAG,IAAK;MACrCF,iBAAiB,CAACvB,KAAI,GAAIyB,GAAG;IAC/B;IACA,MAAMC,qBAAoB,GAAIA,CAAA,KAAM;MAClC,IAAI,CAACH,iBAAiB,CAACvB,KAAK,CAAC2B,MAAM,EAAE;QACnC/C,KAAK,CAAC,SAAS;MACjB;MACAI,KAAK,CAACK,cAAa,IAAKL,KAAK,CAACK,cAAc,CAACkC,iBAAiB,CAACvB,KAAK;IACtE;IAEA,MAAM4B,MAAK,GAAKV,IAAI,IAAK;MACvBvC,OAAO,CAAC,UAAU,EAAE,IAAI,EAAE,MAAM;QAC9BF,SAAS,CAACyC,IAAI,CAACI,EAAE,EAAE,MAAM;UACvBzC,OAAO,CAAC,MAAM;UACdkB,QAAQ,EAAC;QACX,CAAC;MACH,CAAC,EAAE,MAAM,CACT,CAAC;IACH;IAEA,OAAO;MACL6B,MAAM;MACNJ,qBAAqB;MACrBE,qBAAqB;MACrBjC,IAAI;MACJC,KAAK;MACLE,WAAW;MACXW,MAAM;MACNJ,aAAa;MACbE,UAAU;MACVS,uBAAuB;MACvBE,GAAG;MACHJ,SAAS;MACTC,YAAY;MACZI,IAAI;MACJF,iBAAiB;MACjBI,eAAe;MACfX,cAAc;MACdb;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}