{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, resolveDynamicComponent as _resolveDynamicComponent, Transition as _Transition, normalizeClass as _normalizeClass } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_layout_header = _resolveComponent(\"layout-header\");\n  const _component_el_header = _resolveComponent(\"el-header\");\n  const _component_custom_aside = _resolveComponent(\"custom-aside\");\n  const _component_el_aside = _resolveComponent(\"el-aside\");\n  const _component_custom_header = _resolveComponent(\"custom-header\");\n  const _component_router_view = _resolveComponent(\"router-view\");\n  const _component_el_main = _resolveComponent(\"el-main\");\n  const _component_el_container = _resolveComponent(\"el-container\");\n  return _openBlock(), _createBlock(_component_el_container, null, {\n    default: _withCtx(() => [_createVNode(_component_el_header, {\n      class: \"layout-header\",\n      height: \"50\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_layout_header)]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_main, {\n      class: \"layout-main\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_container, null, {\n        default: _withCtx(() => [_createVNode(_component_el_aside, {\n          class: \"aside\",\n          width: \"auto\"\n        }, {\n          default: _withCtx(() => [$setup.loaded ? (_openBlock(), _createBlock(_component_custom_aside, {\n            key: 0\n          })) : _createCommentVNode(\"v-if\", true)]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_container, {\n          class: _normalizeClass([\"main\", {\n            'fixed-header': $options.opened\n          }])\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_header, {\n            height: \"40\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_custom_header)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_main, null, {\n            default: _withCtx(() => [_createVNode(_component_router_view, null, {\n              default: _withCtx(({\n                Component\n              }) => [_createVNode(_Transition, null, {\n                default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(Component)))]),\n                _: 2 /* DYNAMIC */\n              }, 1024 /* DYNAMIC_SLOTS */)]),\n\n              _: 1 /* STABLE */\n            })]),\n\n            _: 1 /* STABLE */\n          })]),\n\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"class\"])]),\n        _: 1 /* STABLE */\n      })]),\n\n      _: 1 /* STABLE */\n    })]),\n\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["_createBlock", "_component_el_container", "_createVNode", "_component_el_header", "class", "height", "_component_layout_header", "_component_el_main", "_component_el_aside", "width", "$setup", "loaded", "_component_custom_aside", "key", "_normalizeClass", "$options", "opened", "_component_custom_header", "_component_router_view", "Component", "_Transition", "_resolveDynamicComponent"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/components/Layout.vue"], "sourcesContent": ["<template>\n  <el-container>\n    <el-header class=\"layout-header\" height=\"50\">\n      <layout-header/>\n    </el-header>\n    <el-main class=\"layout-main\">\n      <el-container>\n        <el-aside class=\"aside\" width=\"auto\">\n          <custom-aside v-if=\"loaded\"/>\n        </el-aside>\n        <el-container class=\"main\" :class=\"{'fixed-header': opened}\">\n          <el-header height=\"40\">\n            <custom-header/>\n          </el-header>\n          <el-main>\n            <router-view v-slot=\"{ Component }\">\n              <transition>\n                <component :is=\"Component\"/>\n              </transition>\n            </router-view>\n          </el-main>\n        </el-container>\n      </el-container>\n    </el-main>\n  </el-container>\n</template>\n\n<script>\nimport LayoutHeader from \"./LayoutHeader.vue\";\nimport CustomHeader from \"./Header.vue\";\nimport CustomAside from \"./Aside.vue\";\nimport store from \"@/store\";\nimport {nextTick, ref, watch} from \"vue\";\nimport {useRoute} from \"vue-router\";\n\nexport default {\n  name: \"LayoutIndex\",\n  components: {\n    LayoutHeader,\n    CustomHeader,\n    CustomAside\n  },\n  computed: {\n    opened() {\n      return !store.getters.getAsideStatus\n    }\n  },\n  setup() {\n    const route = useRoute()\n    const loaded = ref(true)\n    watch(() => route.path, (n, o) => {\n      const ns = n.split(\"/\")\n      const os = o.split(\"/\")\n      if (ns.length === 1 || os.length === 1 || ns[1] !== os[1]) {\n        loaded.value = false\n        nextTick(() => {\n          loaded.value = true\n        })\n      }\n    })\n    return {loaded}\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.el-header, .el-footer, .el-main {\n  padding: 0!important;\n}\n.aside {\n  position: fixed;\n  height: 100%;\n  background: #f0f0f0;\n}\n/*隐藏滚动条*/\n.aside::-webkit-scrollbar{\n  display:none;\n}\n.main {\n  min-height: 100%;\n  position: relative;\n  margin-left: 210px;\n  transition: width 0.28s;\n  width: calc(100% - 210px);\n}\n.fixed-header {\n  top: 0;\n  right: 0;\n  z-index: 9;\n  transition: width 0.28s;\n}\n.main.fixed-header {\n  transition: width 0.28s;\n  margin-left: 64px;\n  width: calc(100% - 64px);\n}\n.layout-header {\n  font-size: 12px;\n  position: fixed;\n  z-index: 99;\n  width: 100%;\n}\n.layout-main {\n  margin-top: 50px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;uBACEA,YAAA,CAuBeC,uBAAA;sBAtBb,MAEY,CAFZC,YAAA,CAEYC,oBAAA;MAFDC,KAAK,EAAC,eAAe;MAACC,MAAM,EAAC;;wBACtC,MAAgB,CAAhBH,YAAA,CAAgBI,wBAAA,E;;QAElBJ,YAAA,CAkBUK,kBAAA;MAlBDH,KAAK,EAAC;IAAa;wBAC1B,MAgBe,CAhBfF,YAAA,CAgBeD,uBAAA;0BAfb,MAEW,CAFXC,YAAA,CAEWM,mBAAA;UAFDJ,KAAK,EAAC,OAAO;UAACK,KAAK,EAAC;;4BAC5B,MAA6B,CAATC,MAAA,CAAAC,MAAM,I,cAA1BX,YAAA,CAA6BY,uBAAA;YAAAC,GAAA;UAAA,M;;YAE/BX,YAAA,CAWeD,uBAAA;UAXDG,KAAK,EAAAU,eAAA,EAAC,MAAM;YAAA,gBAA0BC,QAAA,CAAAC;UAAM;;4BACxD,MAEY,CAFZd,YAAA,CAEYC,oBAAA;YAFDE,MAAM,EAAC;UAAI;8BACpB,MAAgB,CAAhBH,YAAA,CAAgBe,wBAAA,E;;cAElBf,YAAA,CAMUK,kBAAA;8BALR,MAIc,CAJdL,YAAA,CAIcgB,sBAAA;gCAHZ,CAEa;gBAHQC;cAAS,OAC9BjB,YAAA,CAEakB,WAAA;kCADX,MAA4B,E,cAA5BpB,YAAA,CAA4BqB,wBAAA,CAAZF,SAAS,I"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}