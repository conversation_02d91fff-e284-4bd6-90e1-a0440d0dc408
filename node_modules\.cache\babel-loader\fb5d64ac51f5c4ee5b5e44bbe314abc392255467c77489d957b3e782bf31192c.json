{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createBlock as _createBlock, normalizeClass as _normalizeClass, with<PERSON><PERSON>s as _withKeys, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-bafa59f2\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"layout-header-nav-start\"\n};\nconst _hoisted_2 = {\n  class: \"logo\"\n};\nconst _hoisted_3 = [\"href\"];\nconst _hoisted_4 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", {\n  style: {\n    \"font-size\": \"20px\",\n    \"font-weight\": \"700\",\n    \"color\": \"#ffffff\"\n  }\n}, \"培训系统\", -1 /* HOISTED */));\nconst _hoisted_5 = {\n  class: \"layout-header-nav-center\"\n};\nconst _hoisted_6 = [\"onClick\"];\nconst _hoisted_7 = {\n  key: 0\n};\nconst _hoisted_8 = {\n  class: \"el-dropdown-link\"\n};\nconst _hoisted_9 = {\n  class: \"layout-header-nav-end\"\n};\nconst _hoisted_10 = {\n  class: \"right-menu\"\n};\nconst _hoisted_11 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"right-menu-item\"\n}, [/*#__PURE__*/_createElementVNode(\"i\", {\n  class: \"icon el-icon-message\"\n})], -1 /* HOISTED */));\nconst _hoisted_12 = {\n  class: \"avatar-wrapper\"\n};\nconst _hoisted_13 = {\n  style: {\n    \"margin-right\": \"5px\"\n  }\n};\nconst _hoisted_14 = {\n  class: \"function-menu-nav\"\n};\nconst _hoisted_15 = {\n  class: \"function-menu-nav-label\"\n};\nconst _hoisted_16 = {\n  class: \"function-menu-nav-list\"\n};\nconst _hoisted_17 = {\n  class: \"function-menu-nav-list-ul\"\n};\nconst _hoisted_18 = [\"onMouseover\", \"onClick\"];\nconst _hoisted_19 = {\n  class: \"function-menu-content\"\n};\nconst _hoisted_20 = {\n  class: \"function-menu-header\"\n};\nconst _hoisted_21 = {\n  class: \"function-menu-search\"\n};\nconst _hoisted_22 = {\n  class: \"function-menu-tags\"\n};\nconst _hoisted_23 = [\"onClick\"];\nconst _hoisted_24 = {\n  class: \"function-menu-content-main\"\n};\nconst _hoisted_25 = [\"onClick\"];\nconst _hoisted_26 = {\n  class: \"menu-box-content\"\n};\nconst _hoisted_27 = [\"onClick\"];\nconst _hoisted_28 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"right-row\"\n}, [/*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"block\"\n}, [/*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"title\"\n}, \"一站式解决方案\"), /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"desc\"\n}, \"方便易搭建，功能丰富易用\")]), /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"block\"\n}, [/*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"title\"\n}, \"多终端支持\"), /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"desc\"\n}, \"支持电脑、手机、APP、微信、H5、小程序观看\")]), /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"block\"\n}, [/*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"title\"\n}, \"定制化服务\"), /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"desc\"\n}, \"多功能自由组合，量身定制专属企业培训系统\")])], -1 /* HOISTED */));\n\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  const _component_ArrowDown = _resolveComponent(\"ArrowDown\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_dropdown_item = _resolveComponent(\"el-dropdown-item\");\n  const _component_el_dropdown_menu = _resolveComponent(\"el-dropdown-menu\");\n  const _component_el_dropdown = _resolveComponent(\"el-dropdown\");\n  const _component_More = _resolveComponent(\"More\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_drawer = _resolveComponent(\"el-drawer\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createElementVNode(\"div\", {\n    class: _normalizeClass([\"layout-header-nav\", {\n      'absolute': $setup.isShowFunctionMenu\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"a\", {\n    href: $setup.indexPath,\n    title: \"培训系统\"\n  }, [_hoisted_4, _createCommentVNode(\"            <img src=\\\"../assets/logo.svg\\\" alt=\\\"培训系统\\\">\")], 8 /* PROPS */, _hoisted_3)]), _createCommentVNode(\"        <a class=\\\"btn dropdown-item\\\" :class=\\\"{'is-active' : isShowFunctionMenu}\\\" @click=\\\"isShowFunctionMenu = !isShowFunctionMenu\\\">\"), _createCommentVNode(\"          所有功能<i class=\\\"el-icon-arrow-down el-icon&#45;&#45;right\\\"></i>\"), _createCommentVNode(\"        </a>\"), _createVNode(_component_router_link, {\n    class: \"btn\",\n    to: {\n      path: '/'\n    }\n  }, {\n    default: _withCtx(() => [_createTextVNode(\"总览\")]),\n    _: 1 /* STABLE */\n  })]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.functionList, (item, index) => {\n    return _openBlock(), _createElementBlock(\"a\", {\n      class: \"btn\",\n      onClick: $event => $setup.goto(item.path),\n      key: item.path\n    }, [index !== $setup.functionList.length - 1 ? (_openBlock(), _createElementBlock(\"span\", _hoisted_7, _toDisplayString(item.title), 1 /* TEXT */)) : $setup.moreMenu ? (_openBlock(), _createBlock(_component_el_dropdown, {\n      key: 1\n    }, {\n      dropdown: _withCtx(() => [_createVNode(_component_el_dropdown_menu, {\n        class: \"more-function-dropdown\"\n      }, {\n        default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.children, r => {\n          return _openBlock(), _createBlock(_component_el_dropdown_item, {\n            onClick: $event => $setup.goto(r.path),\n            key: r.path\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString(r.title), 1 /* TEXT */)]),\n\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]);\n        }), 128 /* KEYED_FRAGMENT */))]),\n\n        _: 2 /* DYNAMIC */\n      }, 1024 /* DYNAMIC_SLOTS */)]),\n\n      default: _withCtx(() => [_createElementVNode(\"span\", _hoisted_8, [_createTextVNode(_toDisplayString(item.title) + \" \", 1 /* TEXT */), _createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_ArrowDown)]),\n        _: 1 /* STABLE */\n      })])]),\n\n      _: 2 /* DYNAMIC */\n    }, 1024 /* DYNAMIC_SLOTS */)) : _createCommentVNode(\"v-if\", true)], 8 /* PROPS */, _hoisted_6);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_hoisted_11, _createVNode(_component_el_dropdown, {\n    class: \"avatar-container\",\n    trigger: \"click\"\n  }, {\n    dropdown: _withCtx(() => [_createVNode(_component_el_dropdown_menu, {\n      class: \"user-dropdown\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_router_link, {\n        to: \"/account\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_dropdown_item, null, {\n          default: _withCtx(() => [_createTextVNode(\" 账号信息 \")]),\n          _: 1 /* STABLE */\n        })]),\n\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_dropdown_item, {\n        divided: \"\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"span\", {\n          style: {\n            \"display\": \"block\"\n          },\n          onClick: _cache[0] || (_cache[0] = (...args) => $setup.logout && $setup.logout(...args))\n        }, \"退出登录\")]),\n        _: 1 /* STABLE */\n      })]),\n\n      _: 1 /* STABLE */\n    })]),\n\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"span\", _hoisted_13, _toDisplayString($setup.user.name), 1 /* TEXT */), _createVNode(_component_el_icon, {\n      style: {\n        \"vertical-align\": \"middle\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode(_component_More)]),\n      _: 1 /* STABLE */\n    })])]),\n\n    _: 1 /* STABLE */\n  })])])], 2 /* CLASS */), _createVNode(_component_el_drawer, {\n    \"before-close\": $setup.functionMenuDrawerBeforeClose,\n    modelValue: $setup.isShowFunctionMenu,\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.isShowFunctionMenu = $event),\n    withHeader: false,\n    direction: \"ttb\",\n    class: \"function-menu\",\n    \"modal-class\": \"function-menu-modal\",\n    \"destroy-on-close\": \"\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_row, null, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 4,\n        class: \"left-box\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"span\", {\n          onMouseover: _cache[1] || (_cache[1] = $event => $setup.changeNav('')),\n          class: _normalizeClass([\"title\", {\n            'is-active': $setup.navActive === ''\n          }])\n        }, \"所有功能\", 34 /* CLASS, HYDRATE_EVENTS */)]), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"ul\", _hoisted_17, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.routeList, item => {\n          return _openBlock(), _createElementBlock(\"li\", {\n            onMouseover: $event => $setup.changeNav(item.path),\n            onClick: $event => $setup.goto(item.path),\n            class: _normalizeClass([\"function-menu-nav-item\", {\n              'is-active': $setup.navActive === item.path\n            }]),\n            key: item.path\n          }, _toDisplayString(item.title), 43 /* TEXT, CLASS, PROPS, HYDRATE_EVENTS */, _hoisted_18);\n        }), 128 /* KEYED_FRAGMENT */))])])])]),\n\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 20,\n        class: \"right-box\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_row, {\n          class: \"right-box-row\",\n          gutter: 20\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_col, {\n            span: 16\n          }, {\n            default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_el_input, {\n              modelValue: $setup.keyword,\n              \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.keyword = $event),\n              onKeydown: _withKeys($setup.searchFunction, [\"enter\"]),\n              placeholder: \"搜索功能名称\",\n              class: \"function-menu-search-input\"\n            }, {\n              suffix: _withCtx(() => [_createElementVNode(\"i\", {\n                onClick: _cache[2] || (_cache[2] = (...args) => $setup.searchFunction && $setup.searchFunction(...args)),\n                class: \"el-input__icon el-icon-search\"\n              })]),\n              _: 1 /* STABLE */\n            }, 8 /* PROPS */, [\"modelValue\", \"onKeydown\"])]), _createElementVNode(\"div\", _hoisted_22, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.randomRouteList, item => {\n              return _openBlock(), _createElementBlock(\"div\", {\n                class: \"function-menu-tag\",\n                onClick: $event => $setup.goto(item.path),\n                key: item.path\n              }, _toDisplayString(item.title), 9 /* TEXT, PROPS */, _hoisted_23);\n            }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_24, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.functionItemList, (itemList, index) => {\n              return _openBlock(), _createElementBlock(\"ul\", {\n                class: \"function-menu-content-main-ul\",\n                key: index\n              }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(itemList, (item, i) => {\n                return _openBlock(), _createElementBlock(\"li\", {\n                  class: \"menu-box\",\n                  key: i\n                }, [_createElementVNode(\"div\", {\n                  class: \"menu-box-title\",\n                  onClick: $event => $setup.goto(item.path)\n                }, _toDisplayString(item.title), 9 /* TEXT, PROPS */, _hoisted_25), _createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"ul\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.menuList, (m, idx) => {\n                  return _openBlock(), _createElementBlock(\"li\", {\n                    key: idx\n                  }, [_createElementVNode(\"div\", {\n                    class: \"menu-title\",\n                    onClick: $event => $setup.goto(m.path)\n                  }, _toDisplayString(m.title), 9 /* TEXT, PROPS */, _hoisted_27)]);\n                }), 128 /* KEYED_FRAGMENT */))])])]);\n              }), 128 /* KEYED_FRAGMENT */))]);\n            }), 128 /* KEYED_FRAGMENT */))])])]),\n\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_col, {\n            span: 8\n          }, {\n            default: _withCtx(() => [_hoisted_28]),\n            _: 1 /* STABLE */\n          })]),\n\n          _: 1 /* STABLE */\n        })]),\n\n        _: 1 /* STABLE */\n      })]),\n\n      _: 1 /* STABLE */\n    })]),\n\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"before-close\", \"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementVNode", "style", "_createElementBlock", "_normalizeClass", "$setup", "isShowFunctionMenu", "_hoisted_1", "_hoisted_2", "href", "indexPath", "title", "_hoisted_4", "_createCommentVNode", "_createVNode", "_component_router_link", "to", "path", "_hoisted_5", "_Fragment", "_renderList", "functionList", "item", "index", "onClick", "$event", "goto", "key", "length", "_hoisted_7", "_toDisplayString", "moreMenu", "_createBlock", "_component_el_dropdown", "dropdown", "_withCtx", "_component_el_dropdown_menu", "children", "r", "_component_el_dropdown_item", "_hoisted_8", "_component_el_icon", "_component_ArrowDown", "_hoisted_9", "_hoisted_10", "_hoisted_11", "trigger", "divided", "_cache", "args", "logout", "_hoisted_12", "_hoisted_13", "user", "name", "_component_More", "_component_el_drawer", "functionMenuDrawerBeforeClose", "<PERSON><PERSON><PERSON><PERSON>", "direction", "_component_el_row", "_component_el_col", "span", "_hoisted_14", "_hoisted_15", "onMouseover", "changeNav", "navActive", "_hoisted_16", "_hoisted_17", "routeList", "_hoisted_18", "gutter", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_component_el_input", "keyword", "onKeydown", "_with<PERSON><PERSON><PERSON>", "searchFunction", "placeholder", "suffix", "_hoisted_22", "randomRouteList", "_hoisted_23", "_hoisted_24", "functionItemList", "itemList", "i", "_hoisted_25", "_hoisted_26", "menuList", "m", "idx", "_hoisted_27", "_hoisted_28"], "sources": ["D:\\sourcecodeAndDocument\\learning-platform\\admin\\src\\components\\LayoutHeader.vue"], "sourcesContent": ["<template>\n  <div>\n    <div class=\"layout-header-nav\" :class=\"{'absolute': isShowFunctionMenu}\">\n      <div class=\"layout-header-nav-start\">\n        <div class=\"logo\">\n          <a :href=\"indexPath\" title=\"培训系统\">\n            <span style=\"font-size: 20px;font-weight: 700;color: #ffffff;\">培训系统</span>\n<!--            <img src=\"../assets/logo.svg\" alt=\"培训系统\">-->\n          </a>\n        </div>\n        <!--        <a class=\"btn dropdown-item\" :class=\"{'is-active' : isShowFunctionMenu}\" @click=\"isShowFunctionMenu = !isShowFunctionMenu\">-->\n        <!--          所有功能<i class=\"el-icon-arrow-down el-icon&#45;&#45;right\"></i>-->\n        <!--        </a>-->\n        <router-link class=\"btn\" :to=\"{path: '/'}\">总览</router-link>\n      </div>\n      <div class=\"layout-header-nav-center\">\n        <div>\n          <a class=\"btn\" @click=\"goto(item.path)\" v-for=\"(item, index) in functionList\" :key=\"item.path\">\n            <span v-if=\"index !== functionList.length - 1\">{{item.title}}</span>\n            <el-dropdown v-else-if=\"moreMenu\">\n              <span class=\"el-dropdown-link\">\n                {{item.title}}\n                <el-icon><ArrowDown /></el-icon>\n              </span>\n              <template #dropdown>\n                <el-dropdown-menu class=\"more-function-dropdown\">\n                  <el-dropdown-item @click=\"goto(r.path)\" v-for=\"r in item.children\" :key=\"r.path\">{{r.title}}</el-dropdown-item>\n                </el-dropdown-menu>\n              </template>\n            </el-dropdown>\n          </a>\n        </div>\n      </div>\n      <div class=\"layout-header-nav-end\">\n        <div class=\"right-menu\">\n          <div class=\"right-menu-item\">\n            <i class=\"icon el-icon-message\"></i>\n          </div>\n          <el-dropdown class=\"avatar-container\" trigger=\"click\">\n            <div class=\"avatar-wrapper\">\n              <span style=\"margin-right: 5px;\">{{user.name}}</span>\n              <el-icon style=\"vertical-align: middle\">\n                <More />\n              </el-icon>\n            </div>\n            <template #dropdown>\n              <el-dropdown-menu class=\"user-dropdown\">\n                <router-link to=\"/account\">\n                  <el-dropdown-item>\n                    账号信息\n                  </el-dropdown-item>\n                </router-link>\n                <el-dropdown-item divided>\n                  <span style=\"display:block;\" @click=\"logout\">退出登录</span>\n                </el-dropdown-item>\n              </el-dropdown-menu>\n            </template>\n          </el-dropdown>\n        </div>\n      </div>\n    </div>\n    <el-drawer\n      :before-close=\"functionMenuDrawerBeforeClose\"\n      v-model=\"isShowFunctionMenu\"\n      :withHeader=\"false\"\n      direction=\"ttb\"\n      class=\"function-menu\"\n      modal-class=\"function-menu-modal\"\n      destroy-on-close>\n      <el-row>\n        <el-col :span=\"4\" class=\"left-box\">\n          <div class=\"function-menu-nav\">\n            <div class=\"function-menu-nav-label\">\n              <span @mouseover=\"changeNav('')\" class=\"title\" :class=\"{'is-active': navActive === ''}\">所有功能</span>\n            </div>\n            <div class=\"function-menu-nav-list\">\n              <ul class=\"function-menu-nav-list-ul\">\n                <li @mouseover=\"changeNav(item.path)\" @click=\"goto(item.path)\" class=\"function-menu-nav-item\" :class=\"{'is-active': navActive === item.path}\" v-for=\"item in routeList\" :key=\"item.path\">{{item.title}}</li>\n              </ul>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"20\" class=\"right-box\">\n          <el-row class=\"right-box-row\" :gutter=\"20\">\n            <el-col :span=\"16\">\n              <div class=\"function-menu-content\">\n                <div class=\"function-menu-header\">\n                  <div class=\"function-menu-search\">\n                    <el-input v-model=\"keyword\" @keydown.enter=\"searchFunction\" placeholder=\"搜索功能名称\" class=\"function-menu-search-input\">\n                      <template #suffix>\n                        <i @click=\"searchFunction\" class=\"el-input__icon el-icon-search\"></i>\n                      </template>\n                    </el-input>\n                  </div>\n                  <div class=\"function-menu-tags\">\n                    <div class=\"function-menu-tag\" @click=\"goto(item.path)\" v-for=\"item in randomRouteList\" :key=\"item.path\">{{item.title}}</div>\n                  </div>\n                </div>\n                <div class=\"function-menu-content-main\">\n                  <ul class=\"function-menu-content-main-ul\" v-for=\"(itemList, index) in functionItemList\" :key=\"index\">\n                    <li class=\"menu-box\" v-for=\"(item, i) in itemList\" :key=\"i\">\n                      <div class=\"menu-box-title\" @click=\"goto(item.path)\">{{item.title}}</div>\n                      <div class=\"menu-box-content\">\n                        <ul>\n                          <li v-for=\"(m, idx) in item.menuList\" :key=\"idx\">\n                            <div class=\"menu-title\" @click=\"goto(m.path)\">{{m.title}}</div>\n                          </li>\n                        </ul>\n                      </div>\n                    </li>\n                  </ul>\n                </div>\n              </div>\n            </el-col>\n            <el-col :span=\"8\">\n              <div class=\"right-row\">\n                <div class=\"block\">\n                  <div class=\"title\">一站式解决方案</div>\n                  <div class=\"desc\">方便易搭建，功能丰富易用</div>\n                </div>\n                <div class=\"block\">\n                  <div class=\"title\">多终端支持</div>\n                  <div class=\"desc\">支持电脑、手机、APP、微信、H5、小程序观看</div>\n                </div>\n                <div class=\"block\">\n                  <div class=\"title\">定制化服务</div>\n                  <div class=\"desc\">多功能自由组合，量身定制专属企业培训系统</div>\n                </div>\n              </div>\n            </el-col>\n          </el-row>\n        </el-col>\n      </el-row>\n    </el-drawer>\n  </div>\n</template>\n\n<script>\nimport store from \"../store\";\nimport {loginOut} from \"@/api/login\";\nimport {success} from \"@/util/tipsUtils\";\nimport router from \"@/router\";\nimport {getUser} from \"@/util/userUtils\";\nimport {ref} from \"vue\"\nimport {getRouteList} from \"@/util/authorityUtils\";\n\nexport default {\n  name: \"LayoutHeader\",\n  components: {\n  },\n  computed: {\n    opened() {\n      return !store.getters.getAsideStatus\n    }\n  },\n  setup() {\n    const logout = () => {\n      loginOut()\n      success(\"登出成功\")\n      router.push(\"/login\")\n    }\n    const user = getUser()\n    const isShowFunctionMenu = ref(false)\n    const functionMenuDrawerBeforeClose = (done) => {\n      done()\n    }\n    const routeList = getRouteList()\n    // 头部菜单栏\n    const functionList = ref([])\n    const moreMenu = ref(false)\n    if (routeList && routeList.length > 13) {\n      for (let i = 0; i < routeList.length; i++) {\n        if (functionList.value.length === 12) {\n          moreMenu.value = true\n          functionList.value.push({title: \"更多\", path: \"\", children: []});\n        }\n        if (i < 12) {\n         functionList.value.push(routeList[i]);\n        } else {\n          functionList.value[functionList.value.length - 1].children.push(routeList[i])\n        }\n      }\n    } else {\n      functionList.value = routeList\n      functionList.value.push({title: \"更多\", path: \"\", children: []});\n    }\n    function getRandomArrayItem(arr, count) {\n      let shuffled = arr.slice(0), i = arr.length, min = i - count, temp, index;\n      while (i-- > min) {\n        index = Math.floor((i + 1) * Math.random());\n        temp = shuffled[index];\n        shuffled[index] = shuffled[i];\n        shuffled[i] = temp;\n      }\n      return shuffled.slice(min);\n    }\n    const randomRouteList = getRandomArrayItem(routeList, 3);\n    const allFunctionList = []\n    const itemMap = {\"\": allFunctionList}\n    if (routeList && routeList.length) {\n      for (const r of routeList) {\n        if (r.menuList && r.menuList.length) {\n          const itemMapList = [];\n          const rc = [];\n          for (const child of r.menuList) {\n            if (child.menuList && child.menuList.length) {\n              allFunctionList.push(child)\n              itemMapList.push(child)\n            } else {\n              rc.push(child)\n            }\n          }\n          if (rc.length) {\n            const c = {title: r.title, path: r.path, menuList: rc}\n            allFunctionList.push(c)\n            itemMapList.push(c)\n          }\n          itemMap[r.path] = itemMapList\n        }\n      }\n    }\n    const functionItemList = ref([]);\n    const keyword = ref(\"\");\n    const formatItemList = (itemList) => {\n      // 过滤\n      if (keyword.value) {\n        const temps = []\n        for (const item of itemList) {\n          if (item.title.indexOf(keyword.value) > -1) {\n            temps.push(item)\n            continue;\n          }\n          const subItemTemps = []\n          for (const subItem of item.menuList) {\n            if (subItem.title.indexOf(keyword.value) > -1) {\n              subItemTemps.push(subItem);\n            }\n          }\n          if (subItemTemps.length) {\n            item.menuList = subItemTemps\n            temps.push(item)\n          }\n        }\n        itemList = temps\n      }\n      functionItemList.value[0] = [];\n      functionItemList.value[1] = [];\n      functionItemList.value[2] = [];\n      functionItemList.value[3] = [];\n      if (itemList && itemList.length) {\n        const num = functionItemList.value.length;\n        let i = 0\n        for (const r of itemList) {\n          if (i % num === 0) {\n            functionItemList.value[0].push(r)\n          } else if (i % num === 1) {\n            functionItemList.value[1].push(r)\n          } else if (i % num === 2) {\n            functionItemList.value[2].push(r)\n          } else {\n            functionItemList.value[3].push(r)\n          }\n          i++;\n        }\n      }\n    }\n    formatItemList(allFunctionList);\n    const navActive = ref(\"\")\n    const changeNav = (path) => {\n      navActive.value = path\n      formatItemList(itemMap[path])\n    }\n    const searchFunction = () => {\n      formatItemList(itemMap[navActive.value])\n    }\n    const goto = (path) => {\n      if (!path) {\n        return\n      }\n      isShowFunctionMenu.value = false\n      router.push({path: path})\n    }\n    const indexPath = process.env.BASE_URL + \"index\"\n    return {\n      indexPath,\n      moreMenu,\n      user,\n      logout,\n      routeList,\n      functionList,\n      isShowFunctionMenu,\n      functionMenuDrawerBeforeClose,\n      functionItemList,\n      navActive,\n      changeNav,\n      goto,\n      keyword,\n      searchFunction,\n      randomRouteList\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.layout-header-nav {\n  background: #000000;\n  color: #FFFFFF;\n  height: 50px;\n  box-sizing: border-box;\n  width: 100%;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 1px 1px 0 #f5f5f5;\n  box-shadow: none;\n  z-index: 99999;\n  .layout-header-nav-start {\n    flex: 0 0 auto;\n    display: flex;\n    align-items: center;\n    height: 100%;\n    .logo {\n      display: flex;\n      font-size: 0;\n      margin-right: 20px;\n      padding-left: 20px;\n      align-items: center;\n      img {\n        width: 108px;\n        height: 30px;\n      }\n    }\n    .btn {\n      display: inline-flex;\n      flex-shrink: 0;\n      align-items: center;\n      margin: 0 5px;\n      white-space: nowrap;\n      height: 100%;\n      color: #ffffff;\n      &:hover {\n        color: $--color-primary;\n      }\n      ::v-deep .el-dropdown {\n        font-size: 12px;\n        color: #ffffff;\n      }\n    }\n    .dropdown-item {\n      display: inline-flex;\n      align-items: center;\n    }\n    .is-active {\n      color: $--color-primary;\n    }\n  }\n  .layout-header-nav-center {\n    flex: 1 1 600px;\n    justify-content: space-between;\n    display: flex;\n    align-items: center;\n    height: 100%;\n    .btn {\n      display: inline-flex;\n      flex-shrink: 0;\n      align-items: center;\n      margin: 0 10px;\n      white-space: nowrap;\n      height: 100%;\n      color: #ffffff;\n      &:hover {\n        color: $--color-primary;\n      }\n      ::v-deep .el-dropdown {\n        font-size: 12px;\n        color: #ffffff;\n      }\n    }\n  }\n  .layout-header-nav-end {\n    justify-content: flex-end;\n    flex: 0 0 auto;\n    display: flex;\n    align-items: center;\n    height: 100%;\n    .right-menu {\n      float: right;\n      height: 100%;\n      &:focus {\n        outline: none;\n      }\n      .right-menu-item {\n        display: inline-block;\n        padding: 0 8px;\n        height: 100%;\n        font-size: 18px;\n        color: #ffffff;\n        vertical-align: middle;\n        .icon {\n          padding: 15px 0;\n        }\n        &.hover-effect {\n          cursor: pointer;\n          transition: background .3s;\n          &:hover {\n            background: rgba(0, 0, 0, .025)\n          }\n        }\n      }\n      .avatar-container {\n        margin-right: 20px;\n        .avatar-wrapper {\n          position: relative;\n          cursor: pointer;\n          margin: 0;\n          padding: 0 10px;\n          font-size: 12px;\n          color: #ffffff;\n          span {\n            line-height: 50px;\n          }\n          .el-icon-caret-bottom {\n            cursor: pointer;\n            position: absolute;\n            right: -20px;\n            top: 25px;\n            font-size: 12px;\n          }\n        }\n      }\n    }\n  }\n}\n::v-deep .function-menu {\n  margin: 0;\n  padding: 0;\n  left: 0;\n  top: 50px!important;\n  background: #FFFFFF;\n  box-shadow: 0 8px 20px 0 rgb(55 99 170 / 10%);\n  min-height: 600px;\n  .el-row {\n    min-height: 100%;\n    .left-box {\n      .function-menu-nav {\n        padding-top: 20px;\n        .function-menu-nav-label {\n          padding: 10px 0;\n          .title {\n            font-size: 12px;\n            cursor: default;\n            padding-left: 30%;\n            &:hover, &.is-active {\n              color: $--color-primary;\n            }\n          }\n        }\n        .function-menu-nav-list {\n          .function-menu-nav-list-ul {\n            .function-menu-nav-item {\n              font-size: 12px;\n              cursor: pointer;\n              padding: 8px 0 6px 30%;\n              &:hover, &.is-active {\n                color: $--color-primary;\n              }\n            }\n          }\n        }\n      }\n    }\n    .right-box {\n      background: #f3f5f8;\n      .right-box-row{\n        height: 600px;\n        overflow-y: auto;\n      }\n      .function-menu-content {\n        padding-top: 20px;\n        padding-left: 20px;\n        .function-menu-header {\n          margin-bottom: 20px;\n          .function-menu-search {\n            display: inline-block;\n            .function-menu-search-input {\n              width: 300px;\n              height: 34px;\n              line-height: 34px;\n            }\n          }\n          .function-menu-tags {\n            margin-left: 20px;\n            display: inline-block;\n            line-height: 34px;\n            vertical-align: bottom;\n            .function-menu-tag {\n              display: inline-block;\n              background: rgba(205, 216, 229, .5);\n              margin: 0 10px;\n              padding: 0 20px;\n              border-radius: 2px;\n              cursor: pointer;\n              &:hover {\n                color: #FFFFFF;\n                background: $--color-primary;\n              }\n            }\n          }\n        }\n        .function-menu-content-main {\n          display: flex;\n          justify-content: space-between;\n          flex-flow: wrap;\n          .function-menu-content-main-ul {\n            flex: 0.22;\n          }\n          .menu-box {\n            margin-bottom: 20px;\n            .menu-box-title {\n              font-size: 14px;\n              color: #0052d9;\n              font-weight: 500;\n              border-bottom: 1px solid #e6e6e6;\n              padding: 10px 0;\n              cursor: pointer;\n              &:hover {\n                color: $--color-primary;\n              }\n            }\n            .menu-box-content {\n              .menu-title {\n                padding: 6px 0;\n                cursor: pointer;\n                &:hover {\n                  color: $--color-primary;\n                }\n                &:first-child {\n                  padding-top: 12px;\n                }\n              }\n            }\n          }\n        }\n      }\n      .right-row {\n        padding: 20px 20px 0 0;\n        .block {\n          display: block;\n          background-image: linear-gradient(0deg,#fff,#f3f5f8);\n          border: 2px solid #fff;\n          box-shadow: 8px 8px 20px 0 rgb(55 99 170 / 10%), -8px -8px 20px 0 #fff;\n          border-radius: 4px;\n          box-sizing: border-box;\n          flex: 1;\n          height: 92px;\n          padding: 18px;\n          transition: all .3s linear;\n          position: relative;\n          pointer-events: auto;\n          margin-bottom: 20px;\n          .title {\n            margin-bottom: 8px;\n            font-size: 14px;\n            font-weight: 500;\n            color: #000;\n            line-height: 24px;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n          }\n          .desc {\n            font-size: 12px;\n            color: #98a3b7;\n            line-height: 20px;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n          }\n        }\n      }\n    }\n  }\n}\n.absolute {\n  position: absolute;\n  box-shadow: 0 8px 20px 0 rgb(55 99 170 / 10%);\n}\n::v-deep .function-menu-modal {\n  background-color: rgba(0, 0, 0, 0.5);\n}\n.user-dropdown {\n  ::v-deep .el-dropdown-menu__item {\n    font-size: 12px;\n    line-height: 24px;\n    &:hover {\n      background-color: #FFFFFF;\n      color: $--color-primary;\n    }\n  }\n}\n.more-function-dropdown {\n  ::v-deep .el-dropdown-menu__item {\n    font-size: 12px;\n    line-height: 34px;\n    color: #000000;\n    &:hover {\n      background-color: #FFFFFF;\n      color: $--color-primary;\n    }\n  }\n}\n::v-deep .el-input__inner {\n  height: 34px;\n  line-height: 34px;\n  border-color: #f3f5f8;\n  &:focus, &:hover {\n    border-color: #f3f5f8;\n  }\n}\n::v-deep .el-input__icon {\n  line-height: 34px;\n  cursor: pointer;\n  &:hover {\n    color: $--color-primary;\n  }\n}\n.layout-header-nav .layout-header-nav-center .btn[data-v-bafa59f2] .el-dropdown {\n  color: #ffffff;\n}\n:focus-visible {\n  outline: none;\n}\n</style>\n"], "mappings": ";;;EAGWA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAM;;gEAEbC,mBAAA,CAA0E;EAApEC,KAAwD,EAAxD;IAAA;IAAA;IAAA;EAAA;AAAwD,GAAC,MAAI;;EASpEF,KAAK,EAAC;AAA0B;;;;;;EAKvBA,KAAK,EAAC;AAAkB;;EAajCA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAY;iEACrBC,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAiB,I,aAC1BC,mBAAA,CAAoC;EAAjCD,KAAK,EAAC;AAAsB,G;;EAG1BA,KAAK,EAAC;AAAgB;;EACnBE,KAA0B,EAA1B;IAAA;EAAA;AAA0B;;EA+B/BF,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAyB;;EAG/BA,KAAK,EAAC;AAAwB;;EAC7BA,KAAK,EAAC;AAA2B;;;EAShCA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAsB;;EAO5BA,KAAK,EAAC;AAAoB;;;EAI5BA,KAAK,EAAC;AAA4B;;;EAI5BA,KAAK,EAAC;AAAkB;;iEAarCC,mBAAA,CAaM;EAbDD,KAAK,EAAC;AAAW,I,aACpBC,mBAAA,CAGM;EAHDD,KAAK,EAAC;AAAO,I,aAChBC,mBAAA,CAAgC;EAA3BD,KAAK,EAAC;AAAO,GAAC,SAAO,G,aAC1BC,mBAAA,CAAoC;EAA/BD,KAAK,EAAC;AAAM,GAAC,cAAY,E,gBAEhCC,mBAAA,CAGM;EAHDD,KAAK,EAAC;AAAO,I,aAChBC,mBAAA,CAA8B;EAAzBD,KAAK,EAAC;AAAO,GAAC,OAAK,G,aACxBC,mBAAA,CAA+C;EAA1CD,KAAK,EAAC;AAAM,GAAC,yBAAuB,E,gBAE3CC,mBAAA,CAGM;EAHDD,KAAK,EAAC;AAAO,I,aAChBC,mBAAA,CAA8B;EAAzBD,KAAK,EAAC;AAAO,GAAC,OAAK,G,aACxBC,mBAAA,CAA4C;EAAvCD,KAAK,EAAC;AAAM,GAAC,sBAAoB,E;;;;;;;;;;;;;;uBA7HtDG,mBAAA,CAqIM,cApIJF,mBAAA,CA0DM;IA1DDD,KAAK,EAAAI,eAAA,EAAC,mBAAmB;MAAA,YAAsBC,MAAA,CAAAC;IAAkB;MACpEL,mBAAA,CAWM,OAXNM,UAWM,GAVJN,mBAAA,CAKM,OALNO,UAKM,GAJJP,mBAAA,CAGI;IAHAQ,IAAI,EAAEJ,MAAA,CAAAK,SAAS;IAAEC,KAAK,EAAC;MACzBC,UAA0E,EACtFC,mBAAA,6DAA4D,C,gCAGpDA,mBAAA,6IAA0I,EAC1IA,mBAAA,6EAA8E,EAC9EA,mBAAA,gBAAmB,EACnBC,YAAA,CAA2DC,sBAAA;IAA9Cf,KAAK,EAAC,KAAK;IAAEgB,EAAE,EAAE;MAAAC,IAAA;IAAA;;sBAAa,MAAE,C,iBAAF,IAAE,E;;QAE/ChB,mBAAA,CAiBM,OAjBNiB,UAiBM,GAhBJjB,mBAAA,CAeM,e,kBAdJE,mBAAA,CAaIgB,SAAA,QAAAC,WAAA,CAb4Df,MAAA,CAAAgB,YAAY,GAA5BC,IAAI,EAAEC,KAAK;yBAA3DpB,mBAAA,CAaI;MAbDH,KAAK,EAAC,KAAK;MAAEwB,OAAK,EAAAC,MAAA,IAAEpB,MAAA,CAAAqB,IAAI,CAACJ,IAAI,CAACL,IAAI;MAA0CU,GAAG,EAAEL,IAAI,CAACL;QAC3EM,KAAK,KAAKlB,MAAA,CAAAgB,YAAY,CAACO,MAAM,Q,cAAzCzB,mBAAA,CAAoE,QAAA0B,UAAA,EAAAC,gBAAA,CAAnBR,IAAI,CAACX,KAAK,oBACnCN,MAAA,CAAA0B,QAAQ,I,cAAhCC,YAAA,CAUcC,sBAAA;MAAAN,GAAA;IAAA;MALDO,QAAQ,EAAAC,QAAA,CACjB,MAEmB,CAFnBrB,YAAA,CAEmBsB,2BAAA;QAFDpC,KAAK,EAAC;MAAwB;0BACN,MAA0B,E,kBAAlEG,mBAAA,CAA+GgB,SAAA,QAAAC,WAAA,CAA3DE,IAAI,CAACe,QAAQ,EAAlBC,CAAC;+BAAhDN,YAAA,CAA+GO,2BAAA;YAA5Ff,OAAK,EAAAC,MAAA,IAAEpB,MAAA,CAAAqB,IAAI,CAACY,CAAC,CAACrB,IAAI;YAA+BU,GAAG,EAAEW,CAAC,CAACrB;;8BAAM,MAAW,C,kCAATqB,CAAC,CAAC3B,KAAK,iB;;;;;;;;;wBAN9F,MAGO,CAHPV,mBAAA,CAGO,QAHPuC,UAGO,G,kCAFHlB,IAAI,CAACX,KAAK,IAAE,GACd,iBAAAG,YAAA,CAAgC2B,kBAAA;0BAAvB,MAAa,CAAb3B,YAAA,CAAa4B,oBAAA,E;;;;;;sCAWhCzC,mBAAA,CA0BM,OA1BN0C,UA0BM,GAzBJ1C,mBAAA,CAwBM,OAxBN2C,WAwBM,GAvBJC,WAEM,EACN/B,YAAA,CAmBcmB,sBAAA;IAnBDjC,KAAK,EAAC,kBAAkB;IAAC8C,OAAO,EAAC;;IAOjCZ,QAAQ,EAAAC,QAAA,CACjB,MASmB,CATnBrB,YAAA,CASmBsB,2BAAA;MATDpC,KAAK,EAAC;IAAe;wBACrC,MAIc,CAJdc,YAAA,CAIcC,sBAAA;QAJDC,EAAE,EAAC;MAAU;0BACxB,MAEmB,CAFnBF,YAAA,CAEmByB,2BAAA;4BAFD,MAElB,C,iBAFkB,QAElB,E;;;;;UAEFzB,YAAA,CAEmByB,2BAAA;QAFDQ,OAAO,EAAP;MAAO;0BACvB,MAAwD,CAAxD9C,mBAAA,CAAwD;UAAlDC,KAAsB,EAAtB;YAAA;UAAA,CAAsB;UAAEsB,OAAK,EAAAwB,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAE5C,MAAA,CAAA6C,MAAA,IAAA7C,MAAA,CAAA6C,MAAA,IAAAD,IAAA,CAAM;WAAE,MAAI,E;;;;;;;sBAdvD,MAKM,CALNhD,mBAAA,CAKM,OALNkD,WAKM,GAJJlD,mBAAA,CAAqD,QAArDmD,WAAqD,EAAAtB,gBAAA,CAAlBzB,MAAA,CAAAgD,IAAI,CAACC,IAAI,kBAC5CxC,YAAA,CAEU2B,kBAAA;MAFDvC,KAA8B,EAA9B;QAAA;MAAA;IAA8B;wBACrC,MAAQ,CAARY,YAAA,CAAQyC,eAAA,E;;;;;2BAmBpBzC,YAAA,CAwEY0C,oBAAA;IAvET,cAAY,EAAEnD,MAAA,CAAAoD,6BAA6B;gBACnCpD,MAAA,CAAAC,kBAAkB;+DAAlBD,MAAA,CAAAC,kBAAkB,GAAAmB,MAAA;IAC1BiC,UAAU,EAAE,KAAK;IAClBC,SAAS,EAAC,KAAK;IACf3D,KAAK,EAAC,eAAe;IACrB,aAAW,EAAC,qBAAqB;IACjC,kBAAgB,EAAhB;;sBACA,MA+DS,CA/DTc,YAAA,CA+DS8C,iBAAA;wBA9DP,MAWS,CAXT9C,YAAA,CAWS+C,iBAAA;QAXAC,IAAI,EAAE,CAAC;QAAE9D,KAAK,EAAC;;0BACtB,MASM,CATNC,mBAAA,CASM,OATN8D,WASM,GARJ9D,mBAAA,CAEM,OAFN+D,WAEM,GADJ/D,mBAAA,CAAmG;UAA5FgE,WAAS,EAAAjB,MAAA,QAAAA,MAAA,MAAAvB,MAAA,IAAEpB,MAAA,CAAA6D,SAAS;UAAMlE,KAAK,EAAAI,eAAA,EAAC,OAAO;YAAA,aAAuBC,MAAA,CAAA8D,SAAS;UAAA;WAAU,MAAI,kC,GAE9FlE,mBAAA,CAIM,OAJNmE,WAIM,GAHJnE,mBAAA,CAEK,MAFLoE,WAEK,I,kBADHlE,mBAAA,CAA4MgB,SAAA,QAAAC,WAAA,CAA/Cf,MAAA,CAAAiE,SAAS,EAAjBhD,IAAI;+BAAzJnB,mBAAA,CAA4M;YAAvM8D,WAAS,EAAAxC,MAAA,IAAEpB,MAAA,CAAA6D,SAAS,CAAC5C,IAAI,CAACL,IAAI;YAAIO,OAAK,EAAAC,MAAA,IAAEpB,MAAA,CAAAqB,IAAI,CAACJ,IAAI,CAACL,IAAI;YAAGjB,KAAK,EAAAI,eAAA,EAAC,wBAAwB;cAAA,aAAuBC,MAAA,CAAA8D,SAAS,KAAK7C,IAAI,CAACL;YAAI;YAA8BU,GAAG,EAAEL,IAAI,CAACL;8BAAQK,IAAI,CAACX,KAAK,gDAAA4D,WAAA;;;;UAK7MzD,YAAA,CAiDS+C,iBAAA;QAjDAC,IAAI,EAAE,EAAE;QAAE9D,KAAK,EAAC;;0BACvB,MA+CS,CA/CTc,YAAA,CA+CS8C,iBAAA;UA/CD5D,KAAK,EAAC,eAAe;UAAEwE,MAAM,EAAE;;4BACrC,MA6BS,CA7BT1D,YAAA,CA6BS+C,iBAAA;YA7BAC,IAAI,EAAE;UAAE;8BACf,MA2BM,CA3BN7D,mBAAA,CA2BM,OA3BNwE,WA2BM,GA1BJxE,mBAAA,CAWM,OAXNyE,WAWM,GAVJzE,mBAAA,CAMM,OANN0E,WAMM,GALJ7D,YAAA,CAIW8D,mBAAA;0BAJQvE,MAAA,CAAAwE,OAAO;yEAAPxE,MAAA,CAAAwE,OAAO,GAAApD,MAAA;cAAGqD,SAAO,EAAAC,SAAA,CAAQ1E,MAAA,CAAA2E,cAAc;cAAEC,WAAW,EAAC,QAAQ;cAACjF,KAAK,EAAC;;cAC1EkF,MAAM,EAAA/C,QAAA,CACf,MAAqE,CAArElC,mBAAA,CAAqE;gBAAjEuB,OAAK,EAAAwB,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAE5C,MAAA,CAAA2E,cAAA,IAAA3E,MAAA,CAAA2E,cAAA,IAAA/B,IAAA,CAAc;gBAAEjD,KAAK,EAAC;;;8DAIvCC,mBAAA,CAEM,OAFNkF,WAEM,I,kBADJhF,mBAAA,CAA6HgB,SAAA,QAAAC,WAAA,CAAtDf,MAAA,CAAA+E,eAAe,EAAvB9D,IAAI;mCAAnEnB,mBAAA,CAA6H;gBAAxHH,KAAK,EAAC,mBAAmB;gBAAEwB,OAAK,EAAAC,MAAA,IAAEpB,MAAA,CAAAqB,IAAI,CAACJ,IAAI,CAACL,IAAI;gBAAoCU,GAAG,EAAEL,IAAI,CAACL;kCAAQK,IAAI,CAACX,KAAK,wBAAA0E,WAAA;gDAGzHpF,mBAAA,CAaM,OAbNqF,WAaM,I,kBAZJnF,mBAAA,CAWKgB,SAAA,QAAAC,WAAA,CAXiEf,MAAA,CAAAkF,gBAAgB,GAApCC,QAAQ,EAAEjE,KAAK;mCAAjEpB,mBAAA,CAWK;gBAXDH,KAAK,EAAC,+BAA+B;gBAAgD2B,GAAG,EAAEJ;qCAC5FpB,mBAAA,CASKgB,SAAA,QAAAC,WAAA,CAToCoE,QAAQ,GAApBlE,IAAI,EAAEmE,CAAC;qCAApCtF,mBAAA,CASK;kBATDH,KAAK,EAAC,UAAU;kBAAgC2B,GAAG,EAAE8D;oBACvDxF,mBAAA,CAAyE;kBAApED,KAAK,EAAC,gBAAgB;kBAAEwB,OAAK,EAAAC,MAAA,IAAEpB,MAAA,CAAAqB,IAAI,CAACJ,IAAI,CAACL,IAAI;oCAAKK,IAAI,CAACX,KAAK,wBAAA+E,WAAA,GACjEzF,mBAAA,CAMM,OANN0F,WAMM,GALJ1F,mBAAA,CAIK,c,kBAHHE,mBAAA,CAEKgB,SAAA,QAAAC,WAAA,CAFkBE,IAAI,CAACsE,QAAQ,GAAxBC,CAAC,EAAEC,GAAG;uCAAlB3F,mBAAA,CAEK;oBAFkCwB,GAAG,EAAEmE;kBAAG,IAC7C7F,mBAAA,CAA+D;oBAA1DD,KAAK,EAAC,YAAY;oBAAEwB,OAAK,EAAAC,MAAA,IAAEpB,MAAA,CAAAqB,IAAI,CAACmE,CAAC,CAAC5E,IAAI;sCAAK4E,CAAC,CAAClF,KAAK,wBAAAoF,WAAA,E;;;;;;cASvEjF,YAAA,CAeS+C,iBAAA;YAfAC,IAAI,EAAE;UAAC;8BACd,MAaM,CAbNkC,WAaM,C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}