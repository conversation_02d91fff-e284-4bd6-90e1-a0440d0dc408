{"ast": null, "code": "import { del, get } from \"@/util/requestUtils\";\n\n// 删除文件\nexport function deleteFile(path, success) {\n  return del(\"/oss/file\", {\n    path: path\n  }, success);\n}\nexport function toBase64(url, successCallback) {\n  return get(\"/oss/to-base64\", {\n    url: url\n  }, successCallback);\n}", "map": {"version": 3, "names": ["del", "get", "deleteFile", "path", "success", "toBase64", "url", "success<PERSON>allback"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/api/oss/oss.js"], "sourcesContent": ["import { del, get } from \"@/util/requestUtils\"\n\n// 删除文件\nexport function deleteFile(path, success) {\n  return del(\"/oss/file\", { path: path }, success)\n}\n\nexport function toBase64(url, successCallback) {\n  return get(\"/oss/to-base64\", { url: url }, successCallback)\n}\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,GAAG,QAAQ,qBAAqB;;AAE9C;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACxC,OAAOJ,GAAG,CAAC,WAAW,EAAE;IAAEG,IAAI,EAAEA;EAAK,CAAC,EAAEC,OAAO,CAAC;AAClD;AAEA,OAAO,SAASC,QAAQA,CAACC,GAAG,EAAEC,eAAe,EAAE;EAC7C,OAAON,GAAG,CAAC,gBAAgB,EAAE;IAAEK,GAAG,EAAEA;EAAI,CAAC,EAAEC,eAAe,CAAC;AAC7D"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}