{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, reactive } from \"vue\";\nimport ChoiceImage from \"./choiceImage\";\nimport ChoiceLink from \"./choiceLink\";\nimport { saveCarousel, getCarousel } from \"@/api/setting/carousel\";\nimport { success, error } from \"@/util/tipsUtils\";\nimport { deleteFile } from \"@/api/oss/oss\";\nexport default {\n  name: \"CarouselIndex\",\n  components: {\n    ChoiceImage,\n    ChoiceLink\n  },\n  setup() {\n    const isDeleteItem = ref(true);\n    // 轮播图Item\n    const carouselItem = reactive({\n      // 链接的标题\n      title: \"\",\n      // 图片地址\n      imageUrl: \"\",\n      // 链接类型\n      linkType: \"0\",\n      // 链接\n      link: \"\"\n    });\n    // 轮播秒数间隔\n    const interval = ref(3);\n    // 轮播图列表\n    const carouselList = ref([]);\n    const load = () => {\n      getCarousel({}, res => {\n        const carouselJsonStr = res.carouselJson;\n        if (carouselJsonStr) {\n          const carousel = JSON.parse(carouselJsonStr);\n          interval.value = carousel.interval;\n          carouselList.value = carousel.carouselList;\n        } else {\n          carouselList.value.push(carouselItem);\n        }\n      });\n    };\n    load();\n    // 添加图片\n    const addItem = () => {\n      isDeleteItem.value = true;\n      carouselList.value.push(carouselItem);\n    };\n    const deletedItem = [];\n    // 删除图片\n    const deleteItem = index => {\n      deletedItem.push(carouselList.value[index]);\n      carouselList.value.splice(index, 1);\n      isDeleteItem.value = carouselList.value.length > 1;\n    };\n    // 图片回传\n    const uploadCallback = val => {\n      console.log(val);\n      carouselList.value[val.index].imageUrl = val.link;\n      console.log(carouselList.value);\n    };\n    // 链接\n    const changeLink = (index, val) => {\n      console.log(index);\n      console.log(val);\n      carouselList.value[index].link = val;\n      console.log(carouselList.value);\n    };\n    // 链接类型\n    const changeLinkType = (index, val) => {\n      carouselList.value[index].linkType = val;\n      console.log(carouselList.value);\n    };\n    // 保存\n    const save = () => {\n      for (let i = 0; i < carouselList.value.length; i++) {\n        const item = carouselList.value[i];\n        if (!item.imageUrl) {\n          error(\"请添加图片\" + (i + 1) + \"的图片\");\n          return;\n        }\n        if (item.linkType !== \"0\") {\n          if (item.link === \"\") {\n            error(\"请输入图片\" + (i + 1) + \"的链接\");\n            return;\n          }\n        } else {\n          item.link = \"\";\n        }\n      }\n      const param = {};\n      param.interval = interval.value;\n      param.carouselList = carouselList.value;\n      saveCarousel({\n        carouselJson: JSON.stringify(param)\n      }, () => {\n        success(\"保存成功\");\n        // 删除被删除的图片\n        for (let i = 0; i < deletedItem.length; i++) {\n          const item = deletedItem[i];\n          if (item.link && item.link.indexOf(\"http://\") > -1) {\n            deleteFile(item.link);\n          }\n        }\n        setTimeout(() => {\n          location.reload();\n        }, 500);\n      });\n    };\n    return {\n      isDeleteItem,\n      // 轮播图Item\n      carouselItem,\n      // 轮播秒数间隔\n      interval,\n      // 轮播图列表\n      carouselList,\n      load,\n      addItem,\n      deleteItem,\n      uploadCallback,\n      changeLink,\n      changeLinkType,\n      save\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "ChoiceImage", "ChoiceLink", "saveCarousel", "getCarousel", "success", "error", "deleteFile", "name", "components", "setup", "isDeleteItem", "carouselItem", "title", "imageUrl", "linkType", "link", "interval", "carouselList", "load", "res", "carouselJsonStr", "carousel<PERSON><PERSON>", "carousel", "JSON", "parse", "value", "push", "addItem", "deletedItem", "deleteItem", "index", "splice", "length", "uploadCallback", "val", "console", "log", "changeLink", "changeLinkType", "save", "i", "item", "param", "stringify", "indexOf", "setTimeout", "location", "reload"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/setting/carousel/index.vue"], "sourcesContent": ["<template>\n  <div>\n    <div class=\"content\">\n      <div class=\"interval-box\">\n        <span>轮播间隔({{interval}}S)</span>\n        <el-slider v-model=\"interval\" :min=\"2\" :max=\"10\"/>\n      </div>\n      <!--      <draggable v-model=\"carouselList\" tag=\"ul\" class=\"image-list\" :animation=\"200\" group=\"people\" @start=\"drag = true\" @end=\"drag = false\">-->\n      <ul class=\"image-list\">\n        <li v-for=\"(item, index) in carouselList\" :key=\"index\">\n          <div class=\"image-list-header-box\">\n            <span>轮播图片 {{index + 1}}</span>\n            <span v-if=\"isDeleteItem\" @click=\"deleteItem(index)\">删除</span>\n          </div>\n          <div class=\"choice-image\">\n            <choice-image :index=\"index\" :item=\"item\" @on-success=\"uploadCallback\" @on-remove=\"uploadCallback\"/>\n          </div>\n          <div class=\"choice-title\">\n            <el-input placeholder=\"请输入标题\" v-model=\"item.title\"/>\n          </div>\n          <div class=\"choice-link\">\n            <choice-link :index=\"index\" :item=\"item\" @change-link-type=\"changeLinkType\" @change-link=\"changeLink\"/>\n          </div>\n        </li>\n      </ul>\n      <!--      </draggable>-->\n      <el-button class=\"add-btn\" @click=\"addItem\">添加图片</el-button>\n      <div class=\"submit-btn\">\n        <el-button type=\"primary\" @click=\"save\">保存</el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {ref, reactive} from \"vue\";\nimport ChoiceImage from \"./choiceImage\";\nimport ChoiceLink from \"./choiceLink\";\nimport {saveCarousel, getCarousel} from \"@/api/setting/carousel\";\nimport {success, error} from \"@/util/tipsUtils\";\nimport {deleteFile} from \"@/api/oss/oss\";\nexport default {\n  name: \"CarouselIndex\",\n  components: {\n    ChoiceImage,\n    ChoiceLink\n  },\n  setup() {\n    const isDeleteItem = ref(true)\n    // 轮播图Item\n    const carouselItem = reactive({\n      // 链接的标题\n      title: \"\",\n      // 图片地址\n      imageUrl: \"\",\n      // 链接类型\n      linkType: \"0\",\n      // 链接\n      link: \"\"\n    })\n    // 轮播秒数间隔\n    const interval = ref(3);\n    // 轮播图列表\n    const carouselList = ref([]);\n    const load = () => {\n      getCarousel({}, (res) => {\n        const carouselJsonStr = res.carouselJson;\n        if (carouselJsonStr) {\n          const carousel = JSON.parse(carouselJsonStr);\n          interval.value = carousel.interval;\n          carouselList.value = carousel.carouselList;\n        } else {\n          carouselList.value.push(carouselItem)\n        }\n      })\n    }\n    load();\n    // 添加图片\n    const addItem = () => {\n      isDeleteItem.value = true\n      carouselList.value.push(carouselItem)\n    }\n    const deletedItem = [];\n    // 删除图片\n    const deleteItem = (index) => {\n      deletedItem.push(carouselList.value[index])\n      carouselList.value.splice(index, 1)\n      isDeleteItem.value = carouselList.value.length > 1\n    }\n    // 图片回传\n    const uploadCallback = (val) => {\n      console.log(val)\n      carouselList.value[val.index].imageUrl = val.link\n      console.log(carouselList.value)\n    }\n    // 链接\n    const changeLink = (index, val) => {\n      console.log(index)\n      console.log(val)\n      carouselList.value[index].link = val\n      console.log(carouselList.value)\n    }\n    // 链接类型\n    const changeLinkType = (index, val) => {\n      carouselList.value[index].linkType = val\n      console.log(carouselList.value)\n    }\n    // 保存\n    const save = () => {\n      for (let i = 0; i < carouselList.value.length; i++) {\n        const item = carouselList.value[i]\n        if (!item.imageUrl) {\n          error(\"请添加图片\"+ (i + 1) +\"的图片\")\n          return\n        }\n        if (item.linkType !== \"0\") {\n          if (item.link === \"\") {\n            error(\"请输入图片\"+ (i + 1) +\"的链接\")\n            return\n          }\n        } else {\n          item.link = \"\"\n        }\n      }\n      const param = {}\n      param.interval = interval.value\n      param.carouselList = carouselList.value\n      saveCarousel({ carouselJson: JSON.stringify(param) }, () => {\n        success(\"保存成功\")\n        // 删除被删除的图片\n        for (let i = 0; i < deletedItem.length; i++) {\n          const item = deletedItem[i];\n          if (item.link && item.link.indexOf(\"http://\") > -1) {\n            deleteFile(item.link);\n          }\n        }\n        setTimeout(() => {location.reload()}, 500)\n      })\n    }\n    return {\n      isDeleteItem,\n      // 轮播图Item\n      carouselItem,\n      // 轮播秒数间隔\n      interval,\n      // 轮播图列表\n      carouselList,\n      load,\n      addItem,\n      deleteItem,\n      uploadCallback,\n      changeLink,\n      changeLinkType,\n      save\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.content {\n  padding: 0 50px;\n  .interval-box {\n    height: 40px;\n    align-items: center;\n    font-size: 14px;\n    margin-top: 20px;\n    display: flex;\n    span {\n      margin-right: 20px;\n    }\n    .el-slider {\n      width: calc(100% - 108px);\n    }\n  }\n  .add-btn {\n    width: 100%;\n  }\n  .image-list {\n    width: 100%;\n    margin-top: 20px;\n    margin-bottom: 20px;\n    li {\n      width: 100%;\n      min-height: 195px;\n      border: 1px solid #dcdfe6;\n      border-radius: 8px;\n      margin-bottom: 20px;\n      .image-list-header-box {\n        height: 30px;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        font-size: 12px;\n        padding: 0 20px;\n        box-sizing: border-box;\n        border-bottom: 1px solid #dcdfe6;\n        span:last-child {\n          font-size: 13px;\n          color: #409eff;\n          cursor: pointer;\n        }\n      }\n      .choice-image {\n        height: 100%;\n      }\n      .choice-title {\n        margin: 0 20px 20px;\n      }\n    }\n  }\n  .submit-btn {\n    text-align: center;\n    margin: 20px 0;\n  }\n}\n.choice-link {\n  margin-bottom: 10px;\n}\n.el-dialog__wrapper ::v-deep .el-dialog__body {\n  padding: 10px 20px;\n}\n</style>\n"], "mappings": ";AAmCA,SAAQA,GAAG,EAAEC,QAAQ,QAAO,KAAK;AACjC,OAAOC,WAAU,MAAO,eAAe;AACvC,OAAOC,UAAS,MAAO,cAAc;AACrC,SAAQC,YAAY,EAAEC,WAAW,QAAO,wBAAwB;AAChE,SAAQC,OAAO,EAAEC,KAAK,QAAO,kBAAkB;AAC/C,SAAQC,UAAU,QAAO,eAAe;AACxC,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,UAAU,EAAE;IACVR,WAAW;IACXC;EACF,CAAC;EACDQ,KAAKA,CAAA,EAAG;IACN,MAAMC,YAAW,GAAIZ,GAAG,CAAC,IAAI;IAC7B;IACA,MAAMa,YAAW,GAAIZ,QAAQ,CAAC;MAC5B;MACAa,KAAK,EAAE,EAAE;MACT;MACAC,QAAQ,EAAE,EAAE;MACZ;MACAC,QAAQ,EAAE,GAAG;MACb;MACAC,IAAI,EAAE;IACR,CAAC;IACD;IACA,MAAMC,QAAO,GAAIlB,GAAG,CAAC,CAAC,CAAC;IACvB;IACA,MAAMmB,YAAW,GAAInB,GAAG,CAAC,EAAE,CAAC;IAC5B,MAAMoB,IAAG,GAAIA,CAAA,KAAM;MACjBf,WAAW,CAAC,CAAC,CAAC,EAAGgB,GAAG,IAAK;QACvB,MAAMC,eAAc,GAAID,GAAG,CAACE,YAAY;QACxC,IAAID,eAAe,EAAE;UACnB,MAAME,QAAO,GAAIC,IAAI,CAACC,KAAK,CAACJ,eAAe,CAAC;UAC5CJ,QAAQ,CAACS,KAAI,GAAIH,QAAQ,CAACN,QAAQ;UAClCC,YAAY,CAACQ,KAAI,GAAIH,QAAQ,CAACL,YAAY;QAC5C,OAAO;UACLA,YAAY,CAACQ,KAAK,CAACC,IAAI,CAACf,YAAY;QACtC;MACF,CAAC;IACH;IACAO,IAAI,EAAE;IACN;IACA,MAAMS,OAAM,GAAIA,CAAA,KAAM;MACpBjB,YAAY,CAACe,KAAI,GAAI,IAAG;MACxBR,YAAY,CAACQ,KAAK,CAACC,IAAI,CAACf,YAAY;IACtC;IACA,MAAMiB,WAAU,GAAI,EAAE;IACtB;IACA,MAAMC,UAAS,GAAKC,KAAK,IAAK;MAC5BF,WAAW,CAACF,IAAI,CAACT,YAAY,CAACQ,KAAK,CAACK,KAAK,CAAC;MAC1Cb,YAAY,CAACQ,KAAK,CAACM,MAAM,CAACD,KAAK,EAAE,CAAC;MAClCpB,YAAY,CAACe,KAAI,GAAIR,YAAY,CAACQ,KAAK,CAACO,MAAK,GAAI;IACnD;IACA;IACA,MAAMC,cAAa,GAAKC,GAAG,IAAK;MAC9BC,OAAO,CAACC,GAAG,CAACF,GAAG;MACfjB,YAAY,CAACQ,KAAK,CAACS,GAAG,CAACJ,KAAK,CAAC,CAACjB,QAAO,GAAIqB,GAAG,CAACnB,IAAG;MAChDoB,OAAO,CAACC,GAAG,CAACnB,YAAY,CAACQ,KAAK;IAChC;IACA;IACA,MAAMY,UAAS,GAAIA,CAACP,KAAK,EAAEI,GAAG,KAAK;MACjCC,OAAO,CAACC,GAAG,CAACN,KAAK;MACjBK,OAAO,CAACC,GAAG,CAACF,GAAG;MACfjB,YAAY,CAACQ,KAAK,CAACK,KAAK,CAAC,CAACf,IAAG,GAAImB,GAAE;MACnCC,OAAO,CAACC,GAAG,CAACnB,YAAY,CAACQ,KAAK;IAChC;IACA;IACA,MAAMa,cAAa,GAAIA,CAACR,KAAK,EAAEI,GAAG,KAAK;MACrCjB,YAAY,CAACQ,KAAK,CAACK,KAAK,CAAC,CAAChB,QAAO,GAAIoB,GAAE;MACvCC,OAAO,CAACC,GAAG,CAACnB,YAAY,CAACQ,KAAK;IAChC;IACA;IACA,MAAMc,IAAG,GAAIA,CAAA,KAAM;MACjB,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIvB,YAAY,CAACQ,KAAK,CAACO,MAAM,EAAEQ,CAAC,EAAE,EAAE;QAClD,MAAMC,IAAG,GAAIxB,YAAY,CAACQ,KAAK,CAACe,CAAC;QACjC,IAAI,CAACC,IAAI,CAAC5B,QAAQ,EAAE;UAClBR,KAAK,CAAC,OAAO,IAAGmC,CAAA,GAAI,CAAC,IAAG,KAAK;UAC7B;QACF;QACA,IAAIC,IAAI,CAAC3B,QAAO,KAAM,GAAG,EAAE;UACzB,IAAI2B,IAAI,CAAC1B,IAAG,KAAM,EAAE,EAAE;YACpBV,KAAK,CAAC,OAAO,IAAGmC,CAAA,GAAI,CAAC,IAAG,KAAK;YAC7B;UACF;QACF,OAAO;UACLC,IAAI,CAAC1B,IAAG,GAAI,EAAC;QACf;MACF;MACA,MAAM2B,KAAI,GAAI,CAAC;MACfA,KAAK,CAAC1B,QAAO,GAAIA,QAAQ,CAACS,KAAI;MAC9BiB,KAAK,CAACzB,YAAW,GAAIA,YAAY,CAACQ,KAAI;MACtCvB,YAAY,CAAC;QAAEmB,YAAY,EAAEE,IAAI,CAACoB,SAAS,CAACD,KAAK;MAAE,CAAC,EAAE,MAAM;QAC1DtC,OAAO,CAAC,MAAM;QACd;QACA,KAAK,IAAIoC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIZ,WAAW,CAACI,MAAM,EAAEQ,CAAC,EAAE,EAAE;UAC3C,MAAMC,IAAG,GAAIb,WAAW,CAACY,CAAC,CAAC;UAC3B,IAAIC,IAAI,CAAC1B,IAAG,IAAK0B,IAAI,CAAC1B,IAAI,CAAC6B,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC,EAAE;YAClDtC,UAAU,CAACmC,IAAI,CAAC1B,IAAI,CAAC;UACvB;QACF;QACA8B,UAAU,CAAC,MAAM;UAACC,QAAQ,CAACC,MAAM,EAAE;QAAA,CAAC,EAAE,GAAG;MAC3C,CAAC;IACH;IACA,OAAO;MACLrC,YAAY;MACZ;MACAC,YAAY;MACZ;MACAK,QAAQ;MACR;MACAC,YAAY;MACZC,IAAI;MACJS,OAAO;MACPE,UAAU;MACVI,cAAc;MACdI,UAAU;MACVC,cAAc;MACdC;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}