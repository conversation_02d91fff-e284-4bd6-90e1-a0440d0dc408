{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref } from \"vue\";\nimport { useRoute } from \"vue-router\";\nimport router from \"../../../router\";\nimport { saveLecturer, updateLecturer, getLecturer } from \"@/api/lecturer\";\nimport Upload from \"../../../components/Uplaod\";\nimport { error, success } from \"@/util/tipsUtils\";\nimport UserList from \"@/views/organizational/user\";\nexport default {\n  name: \"LecturerEdit\",\n  components: {\n    UserList,\n    Upload\n  },\n  setup() {\n    const route = useRoute();\n    const isUpdate = !!route.query.id;\n    // 基本信息\n    const uploadData = ref({\n      url: process.env.VUE_APP_BASE_API + \"/oss/lecturer/image\",\n      files: []\n    });\n    const lecturer = ref({\n      id: \"\",\n      userId: \"\",\n      image: \"\",\n      mobile: \"\",\n      jobTitle: \"\",\n      description: \"\"\n    });\n    const lecturerRules = {\n      userId: [{\n        required: true,\n        message: \"请选择用户\",\n        trigger: \"change\"\n      }]\n    };\n    // 加载基本信息\n    const load = () => {\n      let id = route.query.id;\n      if (!id) {\n        return;\n      }\n      getLecturer(id, function (res) {\n        lecturer.value = res;\n        if (lecturer.value.image) {\n          uploadData.value.files = [{\n            name: \"头像\",\n            url: lecturer.value.image\n          }];\n        }\n      });\n    };\n    load();\n    // 上传图片成功\n    const onUploadImageSuccess = res => {\n      lecturer.value.image = res.data;\n    };\n    // 删除图片\n    const onUploadImageRemove = () => {\n      lecturer.value.image = \"\";\n      uploadData.value.files = [];\n    };\n    // 提交基本信息\n    const lecturerRef = ref(null);\n    const submitLecturer = () => {\n      lecturerRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (isUpdate) {\n          updateLecturer(lecturer.value, function (res) {\n            if (res && res.id) {\n              lecturer.value.id = res.id;\n              success(\"编辑成功\");\n              router.push({\n                path: \"/live/lecturer/list\"\n              });\n            }\n          });\n        } else {\n          saveLecturer(lecturer.value, function (res) {\n            if (res && res.id) {\n              lecturer.value.id = res.id;\n              success(\"新增成功\");\n              router.push({\n                path: \"/live/lecturer/list\"\n              });\n            }\n          });\n        }\n      });\n    };\n    const showUserSearchDialog = ref(false);\n    const showUserSearch = () => {\n      showUserSearchDialog.value = true;\n    };\n    const hideUserSearch = () => {\n      showUserSearchDialog.value = false;\n    };\n    const submitUser = val => {\n      if (val.length > 1) {\n        error(\"只能选择一个用户\");\n      }\n      console.log(val);\n      lecturer.value.userId = val[0].id;\n      lecturer.value.userName = val[0].name;\n      hideUserSearch();\n    };\n    return {\n      uploadData,\n      lecturer,\n      lecturerRules,\n      lecturerRef,\n      onUploadImageSuccess,\n      onUploadImageRemove,\n      submitLecturer,\n      hideUserSearch,\n      showUserSearch,\n      showUserSearchDialog,\n      submitUser\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "useRoute", "router", "saveLecturer", "updateLecturer", "getLecturer", "Upload", "error", "success", "UserList", "name", "components", "setup", "route", "isUpdate", "query", "id", "uploadData", "url", "process", "env", "VUE_APP_BASE_API", "files", "lecturer", "userId", "image", "mobile", "jobTitle", "description", "lecturerRules", "required", "message", "trigger", "load", "res", "value", "onUploadImageSuccess", "data", "onUploadImageRemove", "lecturer<PERSON><PERSON>", "submitLecturer", "validate", "valid", "push", "path", "showUserSearchDialog", "showUserSearch", "hideUserSearch", "submitUser", "val", "length", "console", "log", "userName"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/live/lecturer/edit.vue"], "sourcesContent": ["<template>\n  <div class=\"lecturer-edit\">\n    <el-form :model=\"lecturer\" :rules=\"lecturerRules\" ref=\"lecturerRef\" label-width=\"120px\">\n      <el-form-item label=\"讲师：\" prop=\"userId\" class=\"name\" @click=\"showUserSearch\">\n        <el-input size=\"small\" v-model=\"lecturer.userName\" placeholder=\"请选择讲师\" readonly></el-input>\n        <el-button size=\"small\">选择</el-button>\n      </el-form-item>\n      <el-form-item label=\"头像：\" prop=\"image\">\n        <upload\n          :on-upload-success=\"onUploadImageSuccess\"\n          :on-upload-remove=\"onUploadImageRemove\"\n          :files=\"uploadData.files\"\n          :upload-url=\"uploadData.url\"\n          :limit=\"1\"\n          accept=\"image/jpeg,image/gif,image/png\">\n        </upload>\n        <span class=\"upload-image-tips\">图片建议：尺寸 1920 x 1200 像素，大小7M以下</span>\n      </el-form-item>\n      <el-form-item label=\"联系电话：\" prop=\"mobile\">\n        <el-input size=\"small\" v-model=\"lecturer.mobile\" placeholder=\"请输入联系电话\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"头衔：\" prop=\"jobTitle\">\n        <el-input size=\"small\" v-model=\"lecturer.jobTitle\" placeholder=\"请输入头衔\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"介绍：\" prop=\"description\">\n        <el-input size=\"small\" type=\"textarea\" :rows=\"10\" v-model=\"lecturer.description\" placeholder=\"请输入介绍\"></el-input>\n      </el-form-item>\n      <el-form-item style=\"text-align: center\">\n        <el-button size=\"small\" @click=\"submitLecturer\">提交</el-button>\n      </el-form-item>\n    </el-form>\n    <el-dialog title=\"搜索用户\" v-model=\"showUserSearchDialog\" :before-close=\"hideUserSearch\" width=\"80%\">\n      <user-list :cancel-callback=\"hideUserSearch\" :submit-callback=\"submitUser\" :is-component=\"true\"/>\n    </el-dialog>\n  </div>\n</template>\n<script>\n  import {ref} from \"vue\"\n  import {useRoute} from \"vue-router\"\n  import router from \"../../../router\"\n  import {saveLecturer, updateLecturer, getLecturer} from \"@/api/lecturer\"\n  import Upload from \"../../../components/Uplaod\"\n  import {error, success} from \"@/util/tipsUtils\";\n  import UserList from \"@/views/organizational/user\";\n\n  export default {\n    name: \"LecturerEdit\",\n    components:{\n      UserList,\n      Upload\n    },\n    setup() {\n      const route = useRoute()\n      const isUpdate = !!route.query.id\n      // 基本信息\n      const uploadData = ref({\n        url: process.env.VUE_APP_BASE_API + \"/oss/lecturer/image\",\n        files: []\n      })\n      const lecturer = ref({\n        id: \"\",\n        userId: \"\",\n        image: \"\",\n        mobile: \"\",\n        jobTitle: \"\",\n        description: \"\"\n      })\n      const lecturerRules = {\n        userId: [{ required: true, message: \"请选择用户\", trigger: \"change\" }],\n      }\n      // 加载基本信息\n      const load = () => {\n        let id = route.query.id;\n        if (!id) { return; }\n        getLecturer(id, function (res) {\n          lecturer.value = res;\n          if (lecturer.value.image) {\n            uploadData.value.files = [{name: \"头像\", url: lecturer.value.image}]\n          }\n        })\n      }\n      load()\n      // 上传图片成功\n      const onUploadImageSuccess = (res) => {\n        lecturer.value.image = res.data\n      }\n      // 删除图片\n      const onUploadImageRemove = () => {\n        lecturer.value.image = \"\"\n        uploadData.value.files = []\n      }\n      // 提交基本信息\n      const lecturerRef = ref(null)\n      const submitLecturer = () => {\n        lecturerRef.value.validate((valid) => {\n          if (!valid) { return false }\n          if (isUpdate) {\n            updateLecturer(lecturer.value, function (res) {\n              if (res && res.id) {\n                lecturer.value.id = res.id;\n                success(\"编辑成功\")\n                router.push({path: \"/live/lecturer/list\"});\n              }\n            })\n          } else {\n            saveLecturer(lecturer.value, function (res) {\n              if (res && res.id) {\n                lecturer.value.id = res.id;\n                success(\"新增成功\")\n                router.push({path: \"/live/lecturer/list\"});\n              }\n            })\n          }\n        })\n      }\n      const showUserSearchDialog = ref(false)\n      const showUserSearch = () => {\n        showUserSearchDialog.value = true\n      }\n      const hideUserSearch = () => {\n        showUserSearchDialog.value = false\n      }\n      const submitUser = (val) => {\n        if (val.length > 1) {\n          error(\"只能选择一个用户\")\n        }\n        console.log(val)\n        lecturer.value.userId = val[0].id\n        lecturer.value.userName = val[0].name\n        hideUserSearch()\n      }\n      return {\n        uploadData,\n        lecturer,\n        lecturerRules,\n        lecturerRef,\n        onUploadImageSuccess,\n        onUploadImageRemove,\n        submitLecturer,\n        hideUserSearch,\n        showUserSearch,\n        showUserSearchDialog,\n        submitUser\n      };\n    }\n  }\n</script>\n<style scoped lang=\"scss\">\n  .lecturer-edit {\n    padding: 20px;\n  }\n  .upload-image-tips {\n    font-size: 12px;\n    color: #999999;\n  }\n  .el-form-item {\n    width: 96%;\n  }\n  .el-tag {\n    margin-right: 10px;\n  }\n  .el-upload--picture-card, .el-upload-list--picture-card .el-upload-list__item {\n    width: 100%;\n    height: 62.5%;\n  }\n  .tips {\n    font-size: 12px;\n    color: #999999;\n  }\n  .name {\n    ::v-deep .el-input{\n      width: calc(100% - 56px);\n    }\n  }\n</style>\n"], "mappings": ";AAqCE,SAAQA,GAAG,QAAO,KAAI;AACtB,SAAQC,QAAQ,QAAO,YAAW;AAClC,OAAOC,MAAK,MAAO,iBAAgB;AACnC,SAAQC,YAAY,EAAEC,cAAc,EAAEC,WAAW,QAAO,gBAAe;AACvE,OAAOC,MAAK,MAAO,4BAA2B;AAC9C,SAAQC,KAAK,EAAEC,OAAO,QAAO,kBAAkB;AAC/C,OAAOC,QAAO,MAAO,6BAA6B;AAElD,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAC;IACTF,QAAQ;IACRH;EACF,CAAC;EACDM,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIZ,QAAQ,EAAC;IACvB,MAAMa,QAAO,GAAI,CAAC,CAACD,KAAK,CAACE,KAAK,CAACC,EAAC;IAChC;IACA,MAAMC,UAAS,GAAIjB,GAAG,CAAC;MACrBkB,GAAG,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAe,GAAI,qBAAqB;MACzDC,KAAK,EAAE;IACT,CAAC;IACD,MAAMC,QAAO,GAAIvB,GAAG,CAAC;MACnBgB,EAAE,EAAE,EAAE;MACNQ,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE;IACf,CAAC;IACD,MAAMC,aAAY,GAAI;MACpBL,MAAM,EAAE,CAAC;QAAEM,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC;IAClE;IACA;IACA,MAAMC,IAAG,GAAIA,CAAA,KAAM;MACjB,IAAIjB,EAAC,GAAIH,KAAK,CAACE,KAAK,CAACC,EAAE;MACvB,IAAI,CAACA,EAAE,EAAE;QAAE;MAAQ;MACnBX,WAAW,CAACW,EAAE,EAAE,UAAUkB,GAAG,EAAE;QAC7BX,QAAQ,CAACY,KAAI,GAAID,GAAG;QACpB,IAAIX,QAAQ,CAACY,KAAK,CAACV,KAAK,EAAE;UACxBR,UAAU,CAACkB,KAAK,CAACb,KAAI,GAAI,CAAC;YAACZ,IAAI,EAAE,IAAI;YAAEQ,GAAG,EAAEK,QAAQ,CAACY,KAAK,CAACV;UAAK,CAAC;QACnE;MACF,CAAC;IACH;IACAQ,IAAI,EAAC;IACL;IACA,MAAMG,oBAAmB,GAAKF,GAAG,IAAK;MACpCX,QAAQ,CAACY,KAAK,CAACV,KAAI,GAAIS,GAAG,CAACG,IAAG;IAChC;IACA;IACA,MAAMC,mBAAkB,GAAIA,CAAA,KAAM;MAChCf,QAAQ,CAACY,KAAK,CAACV,KAAI,GAAI,EAAC;MACxBR,UAAU,CAACkB,KAAK,CAACb,KAAI,GAAI,EAAC;IAC5B;IACA;IACA,MAAMiB,WAAU,GAAIvC,GAAG,CAAC,IAAI;IAC5B,MAAMwC,cAAa,GAAIA,CAAA,KAAM;MAC3BD,WAAW,CAACJ,KAAK,CAACM,QAAQ,CAAEC,KAAK,IAAK;QACpC,IAAI,CAACA,KAAK,EAAE;UAAE,OAAO,KAAI;QAAE;QAC3B,IAAI5B,QAAQ,EAAE;UACZV,cAAc,CAACmB,QAAQ,CAACY,KAAK,EAAE,UAAUD,GAAG,EAAE;YAC5C,IAAIA,GAAE,IAAKA,GAAG,CAAClB,EAAE,EAAE;cACjBO,QAAQ,CAACY,KAAK,CAACnB,EAAC,GAAIkB,GAAG,CAAClB,EAAE;cAC1BR,OAAO,CAAC,MAAM;cACdN,MAAM,CAACyC,IAAI,CAAC;gBAACC,IAAI,EAAE;cAAqB,CAAC,CAAC;YAC5C;UACF,CAAC;QACH,OAAO;UACLzC,YAAY,CAACoB,QAAQ,CAACY,KAAK,EAAE,UAAUD,GAAG,EAAE;YAC1C,IAAIA,GAAE,IAAKA,GAAG,CAAClB,EAAE,EAAE;cACjBO,QAAQ,CAACY,KAAK,CAACnB,EAAC,GAAIkB,GAAG,CAAClB,EAAE;cAC1BR,OAAO,CAAC,MAAM;cACdN,MAAM,CAACyC,IAAI,CAAC;gBAACC,IAAI,EAAE;cAAqB,CAAC,CAAC;YAC5C;UACF,CAAC;QACH;MACF,CAAC;IACH;IACA,MAAMC,oBAAmB,GAAI7C,GAAG,CAAC,KAAK;IACtC,MAAM8C,cAAa,GAAIA,CAAA,KAAM;MAC3BD,oBAAoB,CAACV,KAAI,GAAI,IAAG;IAClC;IACA,MAAMY,cAAa,GAAIA,CAAA,KAAM;MAC3BF,oBAAoB,CAACV,KAAI,GAAI,KAAI;IACnC;IACA,MAAMa,UAAS,GAAKC,GAAG,IAAK;MAC1B,IAAIA,GAAG,CAACC,MAAK,GAAI,CAAC,EAAE;QAClB3C,KAAK,CAAC,UAAU;MAClB;MACA4C,OAAO,CAACC,GAAG,CAACH,GAAG;MACf1B,QAAQ,CAACY,KAAK,CAACX,MAAK,GAAIyB,GAAG,CAAC,CAAC,CAAC,CAACjC,EAAC;MAChCO,QAAQ,CAACY,KAAK,CAACkB,QAAO,GAAIJ,GAAG,CAAC,CAAC,CAAC,CAACvC,IAAG;MACpCqC,cAAc,EAAC;IACjB;IACA,OAAO;MACL9B,UAAU;MACVM,QAAQ;MACRM,aAAa;MACbU,WAAW;MACXH,oBAAoB;MACpBE,mBAAmB;MACnBE,cAAc;MACdO,cAAc;MACdD,cAAc;MACdD,oBAAoB;MACpBG;IACF,CAAC;EACH;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}