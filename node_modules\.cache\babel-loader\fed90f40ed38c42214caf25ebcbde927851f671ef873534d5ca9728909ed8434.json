{"ast": null, "code": "import { nextTick, onMounted, ref, watch } from \"vue\";\nexport default {\n  name: \"CertificatePreview\",\n  props: {\n    certificate: {\n      type: Object\n    },\n    download: {\n      type: Boolean\n    }\n  },\n  setup(props) {\n    const downloadFlag = ref(false);\n    const certificateInfo = ref(props.certificate);\n    const certificateCanvas = ref(null);\n    const canvasContainer = ref(null);\n\n    // 背景图的固定宽高比\n    const backgroundWidth = 2894;\n    const backgroundHeight = 4093;\n\n    // 计算容器的高度\n    const setContainerHeight = () => {\n      const container = canvasContainer.value;\n      const containerWidth = container.clientWidth;\n\n      // 根据背景图的宽高比计算容器的高度\n      const calculatedHeight = containerWidth * (backgroundHeight / backgroundWidth);\n      container.style.height = `${calculatedHeight}px`;\n    };\n\n    // 获取并设置Canvas尺寸\n    const setCanvasSize = () => {\n      const container = canvasContainer.value;\n      const canvas = certificateCanvas.value;\n      const containerWidth = container.clientWidth;\n      const containerHeight = container.clientHeight;\n      canvas.width = containerWidth;\n      canvas.height = containerHeight;\n\n      // 重新生成证书内容\n      generateCertificate(certificateInfo.value);\n    };\n\n    // 加载图片的工具方法，返回一个Promise\n    const loadImage = url => {\n      return new Promise((resolve, reject) => {\n        const img = new Image();\n        img.src = url;\n        img.onload = () => resolve(img);\n        img.onerror = err => reject(err);\n      });\n    };\n\n    // 根据证书对象生成证书\n    const generateCertificate = async certificateInfo => {\n      const canvas = certificateCanvas.value;\n      const ctx = canvas.getContext('2d');\n      try {\n        // 同时加载背景图和头像图\n        const [backgroundImage, avatar] = await Promise.all([loadImage(certificateInfo.design), loadImage(certificateInfo.member.idPhoto || certificateInfo.member.avatar).catch(() => null) // 捕获头像加载失败\n        ]);\n\n        // 计算缩放比例\n        const imageRatio = backgroundImage.width / backgroundImage.height;\n        const canvasRatio = canvas.width / canvas.height;\n        let drawWidth,\n          drawHeight,\n          offsetX = 0,\n          offsetY = 0;\n        if (canvasRatio > imageRatio) {\n          // 如果Canvas宽高比大于背景图宽高比，限制Canvas高度，宽度自适应\n          drawHeight = canvas.height;\n          drawWidth = drawHeight * imageRatio;\n          offsetX = (canvas.width - drawWidth) / 2; // 水平居中\n        } else {\n          // 如果Canvas宽高比小于背景图宽高比，限制Canvas宽度，高度自适应\n          drawWidth = canvas.width;\n          drawHeight = drawWidth / imageRatio;\n          offsetY = (canvas.height - drawHeight) / 2; // 垂直居中\n        }\n\n        // 将背景图绘制到Canvas，背景图根据 Canvas 尺寸缩放并居中\n        ctx.drawImage(backgroundImage, 0, 0, backgroundImage.width, backgroundImage.height, offsetX, offsetY, drawWidth, drawHeight);\n\n        // 绘制背景图\n        // ctx.drawImage(backgroundImage, 0, 0, canvas.width, canvas.height);\n\n        // 如果头像存在且加载成功，则绘制头像\n        if (avatar) {\n          let avatarSize = drawWidth * 0.175; // 头像大小占画布宽度的 17.5%\n          let avatarYSize = drawHeight * 0.165; // 头像大小占画布宽度的 20%\n          let avatarX = drawWidth * 0.66; // 居中\n          let avatarY = drawHeight * 0.382; // 头像位置在画布上方\n          ctx.drawImage(avatar, avatarX, avatarY, avatarSize, avatarYSize); // 头像位置和大小\n          // } else {\n          //   // 头像加载失败或者没有提供，绘制默认的“无头像”占位符\n          //   ctx.fillStyle = 'gray';\n          //   ctx.fillRect(50, 50, 100, 100); // 绘制灰色方块作为占位符\n          //   ctx.fillStyle = 'white';\n          //   ctx.font = '24px Arial';\n          //   ctx.fillText('无头像', 55, 110); // 标注“无头像”\n        }\n\n        // 绘制文字\n        ctx.font = '30px Arial';\n        ctx.fillStyle = \"#000\";\n        ctx.fillText(`${certificateInfo.code}`, drawWidth * 0.17, drawHeight * 0.49);\n        ctx.fillText(`${certificateInfo.member.realname || this.certificateInfo.member.name}`, drawWidth * 0.2, drawHeight * 0.59);\n        ctx.fillText(`${certificateInfo.description}`, drawWidth * 0.42, drawHeight * 0.59);\n        ctx.fillText(`${'2025'}`, drawWidth * 0.56, drawHeight * 0.79);\n        ctx.fillText(`${'20'}`, drawWidth * 0.69, drawHeight * 0.79);\n        ctx.fillText(`${'30'}`, drawWidth * 0.77, drawHeight * 0.79);\n        downloadFlag.value = true;\n      } catch (error) {\n        console.error('图片加载失败', error);\n      }\n    };\n\n    // 在组件挂载后生成证书\n    onMounted(() => {\n      nextTick(() => {\n        setContainerHeight(); // 更新容器高度\n        setCanvasSize(); // 更新Canvas尺寸\n      });\n    });\n\n    // 监听容器尺寸变化\n    watch(canvasContainer, () => {\n      setContainerHeight(); // 更新容器高度\n      setCanvasSize(); // 更新Canvas尺寸\n    });\n\n    // 下载证书\n    const downloadCertificate = () => {\n      const canvas = certificateCanvas.value;\n      const imageUrl = canvas.toDataURL('image/png'); // 转换为PNG图片\n      const link = document.createElement('a');\n      link.href = imageUrl;\n      link.download = 'certificate.png'; // 下载文件名\n      link.click();\n    };\n    return {\n      downloadFlag,\n      canvasContainer,\n      certificateCanvas,\n      downloadCertificate,\n      certificateInfo\n    };\n  }\n};", "map": {"version": 3, "names": ["nextTick", "onMounted", "ref", "watch", "name", "props", "certificate", "type", "Object", "download", "Boolean", "setup", "downloadFlag", "certificateInfo", "certificateCanvas", "canvasContainer", "backgroundWidth", "backgroundHeight", "setContainerHeight", "container", "value", "containerWidth", "clientWidth", "calculatedHeight", "style", "height", "setCanvasSize", "canvas", "containerHeight", "clientHeight", "width", "generateCertificate", "loadImage", "url", "Promise", "resolve", "reject", "img", "Image", "src", "onload", "onerror", "err", "ctx", "getContext", "backgroundImage", "avatar", "all", "design", "member", "idPhoto", "catch", "imageRatio", "canvasRatio", "drawWidth", "drawHeight", "offsetX", "offsetY", "drawImage", "avatarSize", "avatarYSize", "avatarX", "avatarY", "font", "fillStyle", "fillText", "code", "realname", "description", "error", "console", "downloadCertificate", "imageUrl", "toDataURL", "link", "document", "createElement", "href", "click"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/certificate/preview/index.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-button v-if=\"downloadFlag\" type=\"primary\" size=\"large\" @click=\"downloadCertificate\">下载证书</el-button>\n    <div class=\"certificate-coat\">\n      <div class=\"certificate-rotate-wrap\" ref=\"canvasContainer\">\n        <canvas ref=\"certificateCanvas\"></canvas>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {nextTick, onMounted, ref, watch} from \"vue\";\n\nexport default {\n  name: \"CertificatePreview\",\n  props: {\n    certificate: {\n      type: Object\n    },\n    download: {\n      type: Boolean\n    }\n  },\n  setup(props) {\n    const downloadFlag = ref(false)\n    const certificateInfo = ref(props.certificate)\n\n    const certificateCanvas = ref(null);\n    const canvasContainer = ref(null);\n\n    // 背景图的固定宽高比\n    const backgroundWidth = 2894;\n    const backgroundHeight = 4093;\n\n    // 计算容器的高度\n    const setContainerHeight = () => {\n      const container = canvasContainer.value;\n      const containerWidth = container.clientWidth;\n\n      // 根据背景图的宽高比计算容器的高度\n      const calculatedHeight = containerWidth * (backgroundHeight / backgroundWidth);\n\n      container.style.height = `${calculatedHeight}px`;\n    };\n\n    // 获取并设置Canvas尺寸\n    const setCanvasSize = () => {\n      const container = canvasContainer.value;\n      const canvas = certificateCanvas.value;\n\n      const containerWidth = container.clientWidth;\n      const containerHeight = container.clientHeight;\n\n      canvas.width = containerWidth;\n      canvas.height = containerHeight;\n\n      // 重新生成证书内容\n      generateCertificate(certificateInfo.value);\n    };\n\n\n    // 加载图片的工具方法，返回一个Promise\n    const loadImage = (url) => {\n      return new Promise((resolve, reject) => {\n        const img = new Image();\n        img.src = url;\n        img.onload = () => resolve(img);\n        img.onerror = (err) => reject(err);\n      });\n    };\n\n    // 根据证书对象生成证书\n    const generateCertificate = async (certificateInfo) => {\n      const canvas = certificateCanvas.value;\n      const ctx = canvas.getContext('2d');\n\n      try {\n        // 同时加载背景图和头像图\n        const [backgroundImage, avatar] = await Promise.all([\n          loadImage(certificateInfo.design),\n          loadImage(certificateInfo.member.idPhoto || certificateInfo.member.avatar).catch(() => null), // 捕获头像加载失败\n        ]);\n\n        // 计算缩放比例\n        const imageRatio = backgroundImage.width / backgroundImage.height;\n        const canvasRatio = canvas.width / canvas.height;\n\n        let drawWidth, drawHeight, offsetX = 0, offsetY = 0;\n\n        if (canvasRatio > imageRatio) {\n          // 如果Canvas宽高比大于背景图宽高比，限制Canvas高度，宽度自适应\n          drawHeight = canvas.height;\n          drawWidth = drawHeight * imageRatio;\n          offsetX = (canvas.width - drawWidth) / 2;  // 水平居中\n        } else {\n          // 如果Canvas宽高比小于背景图宽高比，限制Canvas宽度，高度自适应\n          drawWidth = canvas.width;\n          drawHeight = drawWidth / imageRatio;\n          offsetY = (canvas.height - drawHeight) / 2;  // 垂直居中\n        }\n\n        // 将背景图绘制到Canvas，背景图根据 Canvas 尺寸缩放并居中\n        ctx.drawImage(backgroundImage, 0, 0, backgroundImage.width, backgroundImage.height, offsetX, offsetY, drawWidth, drawHeight);\n\n\n        // 绘制背景图\n        // ctx.drawImage(backgroundImage, 0, 0, canvas.width, canvas.height);\n\n        // 如果头像存在且加载成功，则绘制头像\n        if (avatar) {\n          let avatarSize = drawWidth * 0.175; // 头像大小占画布宽度的 17.5%\n          let avatarYSize = drawHeight * 0.165; // 头像大小占画布宽度的 20%\n          let avatarX = drawWidth * 0.66; // 居中\n          let avatarY = drawHeight * 0.382; // 头像位置在画布上方\n          ctx.drawImage(avatar, avatarX, avatarY, avatarSize, avatarYSize); // 头像位置和大小\n        // } else {\n        //   // 头像加载失败或者没有提供，绘制默认的“无头像”占位符\n        //   ctx.fillStyle = 'gray';\n        //   ctx.fillRect(50, 50, 100, 100); // 绘制灰色方块作为占位符\n        //   ctx.fillStyle = 'white';\n        //   ctx.font = '24px Arial';\n        //   ctx.fillText('无头像', 55, 110); // 标注“无头像”\n        }\n\n        // 绘制文字\n        ctx.font = '30px Arial';\n        ctx.fillStyle = \"#000\";\n        ctx.fillText(`${certificateInfo.code}`, drawWidth * 0.17, drawHeight * 0.49);\n        ctx.fillText(`${certificateInfo.member.realname || this.certificateInfo.member.name}`, drawWidth * 0.2, drawHeight * 0.59);\n        ctx.fillText(`${certificateInfo.description}`, drawWidth * 0.42, drawHeight * 0.59);\n\n        ctx.fillText(`${'2025'}`, drawWidth * 0.56, drawHeight * 0.79);\n        ctx.fillText(`${'20'}`, drawWidth * 0.69, drawHeight * 0.79);\n        ctx.fillText(`${'30'}`, drawWidth * 0.77, drawHeight * 0.79);\n\n\n        downloadFlag.value = true\n\n      } catch (error) {\n        console.error('图片加载失败', error);\n      }\n    };\n\n    // 在组件挂载后生成证书\n    onMounted(() => {\n      nextTick(() => {\n        setContainerHeight();  // 更新容器高度\n        setCanvasSize();  // 更新Canvas尺寸\n      });\n    });\n\n    // 监听容器尺寸变化\n    watch(canvasContainer, () => {\n      setContainerHeight();  // 更新容器高度\n      setCanvasSize();  // 更新Canvas尺寸\n    });\n\n    // 下载证书\n    const downloadCertificate = () => {\n      const canvas = certificateCanvas.value;\n      const imageUrl = canvas.toDataURL('image/png'); // 转换为PNG图片\n      const link = document.createElement('a');\n      link.href = imageUrl;\n      link.download = 'certificate.png'; // 下载文件名\n      link.click();\n    };\n\n    return {\n      downloadFlag,\n      canvasContainer,\n      certificateCanvas,\n      downloadCertificate,\n      certificateInfo\n    };\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.el-button {\n  float: right;\n  font-size: 20px;\n  //writing-mode: vertical-rl;\n  //text-orientation: upright;\n  //height: auto;\n  border-radius: 4px;\n  margin: 10px;\n}\n.certificate-coat {\n  box-sizing: border-box;\n}\n.certificate-rotate {\n  transform: rotate(90deg);\n  margin-top: 165px!important;\n  margin-left: -165px!important;\n}\n</style>\n"], "mappings": "AAYA,SAAQA,QAAQ,EAAEC,SAAS,EAAEC,GAAG,EAAEC,KAAK,QAAO,KAAK;AAEnD,eAAe;EACbC,IAAI,EAAE,oBAAoB;EAC1BC,KAAK,EAAE;IACLC,WAAW,EAAE;MACXC,IAAI,EAAEC;IACR,CAAC;IACDC,QAAQ,EAAE;MACRF,IAAI,EAAEG;IACR;EACF,CAAC;EACDC,KAAKA,CAACN,KAAK,EAAE;IACX,MAAMO,YAAW,GAAIV,GAAG,CAAC,KAAK;IAC9B,MAAMW,eAAc,GAAIX,GAAG,CAACG,KAAK,CAACC,WAAW;IAE7C,MAAMQ,iBAAgB,GAAIZ,GAAG,CAAC,IAAI,CAAC;IACnC,MAAMa,eAAc,GAAIb,GAAG,CAAC,IAAI,CAAC;;IAEjC;IACA,MAAMc,eAAc,GAAI,IAAI;IAC5B,MAAMC,gBAAe,GAAI,IAAI;;IAE7B;IACA,MAAMC,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,MAAMC,SAAQ,GAAIJ,eAAe,CAACK,KAAK;MACvC,MAAMC,cAAa,GAAIF,SAAS,CAACG,WAAW;;MAE5C;MACA,MAAMC,gBAAe,GAAIF,cAAa,IAAKJ,gBAAe,GAAID,eAAe,CAAC;MAE9EG,SAAS,CAACK,KAAK,CAACC,MAAK,GAAK,GAAEF,gBAAiB,IAAG;IAClD,CAAC;;IAED;IACA,MAAMG,aAAY,GAAIA,CAAA,KAAM;MAC1B,MAAMP,SAAQ,GAAIJ,eAAe,CAACK,KAAK;MACvC,MAAMO,MAAK,GAAIb,iBAAiB,CAACM,KAAK;MAEtC,MAAMC,cAAa,GAAIF,SAAS,CAACG,WAAW;MAC5C,MAAMM,eAAc,GAAIT,SAAS,CAACU,YAAY;MAE9CF,MAAM,CAACG,KAAI,GAAIT,cAAc;MAC7BM,MAAM,CAACF,MAAK,GAAIG,eAAe;;MAE/B;MACAG,mBAAmB,CAAClB,eAAe,CAACO,KAAK,CAAC;IAC5C,CAAC;;IAGD;IACA,MAAMY,SAAQ,GAAKC,GAAG,IAAK;MACzB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,GAAE,GAAI,IAAIC,KAAK,EAAE;QACvBD,GAAG,CAACE,GAAE,GAAIN,GAAG;QACbI,GAAG,CAACG,MAAK,GAAI,MAAML,OAAO,CAACE,GAAG,CAAC;QAC/BA,GAAG,CAACI,OAAM,GAAKC,GAAG,IAAKN,MAAM,CAACM,GAAG,CAAC;MACpC,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMX,mBAAkB,GAAI,MAAOlB,eAAe,IAAK;MACrD,MAAMc,MAAK,GAAIb,iBAAiB,CAACM,KAAK;MACtC,MAAMuB,GAAE,GAAIhB,MAAM,CAACiB,UAAU,CAAC,IAAI,CAAC;MAEnC,IAAI;QACF;QACA,MAAM,CAACC,eAAe,EAAEC,MAAM,IAAI,MAAMZ,OAAO,CAACa,GAAG,CAAC,CAClDf,SAAS,CAACnB,eAAe,CAACmC,MAAM,CAAC,EACjChB,SAAS,CAACnB,eAAe,CAACoC,MAAM,CAACC,OAAM,IAAKrC,eAAe,CAACoC,MAAM,CAACH,MAAM,CAAC,CAACK,KAAK,CAAC,MAAM,IAAI,CAAC,CAAE;QAAA,CAC/F,CAAC;;QAEF;QACA,MAAMC,UAAS,GAAIP,eAAe,CAACf,KAAI,GAAIe,eAAe,CAACpB,MAAM;QACjE,MAAM4B,WAAU,GAAI1B,MAAM,CAACG,KAAI,GAAIH,MAAM,CAACF,MAAM;QAEhD,IAAI6B,SAAS;UAAEC,UAAU;UAAEC,OAAM,GAAI,CAAC;UAAEC,OAAM,GAAI,CAAC;QAEnD,IAAIJ,WAAU,GAAID,UAAU,EAAE;UAC5B;UACAG,UAAS,GAAI5B,MAAM,CAACF,MAAM;UAC1B6B,SAAQ,GAAIC,UAAS,GAAIH,UAAU;UACnCI,OAAM,GAAI,CAAC7B,MAAM,CAACG,KAAI,GAAIwB,SAAS,IAAI,CAAC,EAAG;QAC7C,OAAO;UACL;UACAA,SAAQ,GAAI3B,MAAM,CAACG,KAAK;UACxByB,UAAS,GAAID,SAAQ,GAAIF,UAAU;UACnCK,OAAM,GAAI,CAAC9B,MAAM,CAACF,MAAK,GAAI8B,UAAU,IAAI,CAAC,EAAG;QAC/C;;QAEA;QACAZ,GAAG,CAACe,SAAS,CAACb,eAAe,EAAE,CAAC,EAAE,CAAC,EAAEA,eAAe,CAACf,KAAK,EAAEe,eAAe,CAACpB,MAAM,EAAE+B,OAAO,EAAEC,OAAO,EAAEH,SAAS,EAAEC,UAAU,CAAC;;QAG5H;QACA;;QAEA;QACA,IAAIT,MAAM,EAAE;UACV,IAAIa,UAAS,GAAIL,SAAQ,GAAI,KAAK,EAAE;UACpC,IAAIM,WAAU,GAAIL,UAAS,GAAI,KAAK,EAAE;UACtC,IAAIM,OAAM,GAAIP,SAAQ,GAAI,IAAI,EAAE;UAChC,IAAIQ,OAAM,GAAIP,UAAS,GAAI,KAAK,EAAE;UAClCZ,GAAG,CAACe,SAAS,CAACZ,MAAM,EAAEe,OAAO,EAAEC,OAAO,EAAEH,UAAU,EAAEC,WAAW,CAAC,EAAE;UACpE;UACA;UACA;UACA;UACA;UACA;UACA;QACA;;QAEA;QACAjB,GAAG,CAACoB,IAAG,GAAI,YAAY;QACvBpB,GAAG,CAACqB,SAAQ,GAAI,MAAM;QACtBrB,GAAG,CAACsB,QAAQ,CAAE,GAAEpD,eAAe,CAACqD,IAAK,EAAC,EAAEZ,SAAQ,GAAI,IAAI,EAAEC,UAAS,GAAI,IAAI,CAAC;QAC5EZ,GAAG,CAACsB,QAAQ,CAAE,GAAEpD,eAAe,CAACoC,MAAM,CAACkB,QAAO,IAAK,IAAI,CAACtD,eAAe,CAACoC,MAAM,CAAC7C,IAAK,EAAC,EAAEkD,SAAQ,GAAI,GAAG,EAAEC,UAAS,GAAI,IAAI,CAAC;QAC1HZ,GAAG,CAACsB,QAAQ,CAAE,GAAEpD,eAAe,CAACuD,WAAY,EAAC,EAAEd,SAAQ,GAAI,IAAI,EAAEC,UAAS,GAAI,IAAI,CAAC;QAEnFZ,GAAG,CAACsB,QAAQ,CAAE,GAAE,MAAO,EAAC,EAAEX,SAAQ,GAAI,IAAI,EAAEC,UAAS,GAAI,IAAI,CAAC;QAC9DZ,GAAG,CAACsB,QAAQ,CAAE,GAAE,IAAK,EAAC,EAAEX,SAAQ,GAAI,IAAI,EAAEC,UAAS,GAAI,IAAI,CAAC;QAC5DZ,GAAG,CAACsB,QAAQ,CAAE,GAAE,IAAK,EAAC,EAAEX,SAAQ,GAAI,IAAI,EAAEC,UAAS,GAAI,IAAI,CAAC;QAG5D3C,YAAY,CAACQ,KAAI,GAAI,IAAG;MAE1B,EAAE,OAAOiD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAChC;IACF,CAAC;;IAED;IACApE,SAAS,CAAC,MAAM;MACdD,QAAQ,CAAC,MAAM;QACbkB,kBAAkB,EAAE,EAAG;QACvBQ,aAAa,EAAE,EAAG;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACAvB,KAAK,CAACY,eAAe,EAAE,MAAM;MAC3BG,kBAAkB,EAAE,EAAG;MACvBQ,aAAa,EAAE,EAAG;IACpB,CAAC,CAAC;;IAEF;IACA,MAAM6C,mBAAkB,GAAIA,CAAA,KAAM;MAChC,MAAM5C,MAAK,GAAIb,iBAAiB,CAACM,KAAK;MACtC,MAAMoD,QAAO,GAAI7C,MAAM,CAAC8C,SAAS,CAAC,WAAW,CAAC,EAAE;MAChD,MAAMC,IAAG,GAAIC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAG,GAAIL,QAAQ;MACpBE,IAAI,CAACjE,QAAO,GAAI,iBAAiB,EAAE;MACnCiE,IAAI,CAACI,KAAK,EAAE;IACd,CAAC;IAED,OAAO;MACLlE,YAAY;MACZG,eAAe;MACfD,iBAAiB;MACjByD,mBAAmB;MACnB1D;IACF,CAAC;EACH;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}