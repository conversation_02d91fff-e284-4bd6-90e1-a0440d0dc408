{"ast": null, "code": "import { ref } from \"vue\";\nimport Page from \"@/components/Page\";\nimport { gotoCertificateTemplateEdit } from \"@/router/goto\";\nimport { findCertificateTemplateList, deleteCertificateTemplate, inactiveCertificateTemplate, activeCertificateTemplate } from \"@/api/certificate\";\nimport { confirm, error, success } from \"@/util/tipsUtils\";\nimport CertificatePreview from \"@/views/certificate/preview/index.vue\";\nexport default {\n  name: \"LearnReportSignUpIndex\",\n  components: {\n    CertificatePreview,\n    Page\n  },\n  props: {\n    cancelCallback: {\n      type: Function,\n      default: () => {}\n    },\n    selectCallback: {\n      type: Function,\n      default: () => {}\n    },\n    isComponent: {\n      type: Boolean,\n      default: false\n    }\n  },\n  setup(props) {\n    const dataLoading = ref(true);\n    const templateList = ref([]);\n    const params = ref({\n      current: 1,\n      size: 20,\n      neqStatusList: [\"deleted\"]\n    });\n    const loadList = () => {\n      findCertificateTemplateList(params.value, res => {\n        console.log(res);\n        if (res) {\n          total.value = res.total;\n          templateList.value = res.list;\n        }\n        dataLoading.value = false;\n      }).catch(() => {\n        dataLoading.value = false;\n      });\n    };\n    loadList();\n    const total = ref(0);\n    const currentChange = c => {\n      params.value.current = c;\n      loadList();\n    };\n    const sizeChange = s => {\n      params.value.size = s;\n      loadList();\n    };\n    const search = () => {\n      loadList();\n    };\n    const remove = id => {\n      confirm(\"确认删除该证书模版？\", \"提示\", () => {\n        deleteCertificateTemplate(id, () => {\n          success(\"删除成功\");\n          loadList();\n        });\n      });\n    };\n    const active = id => {\n      confirm(\"确认启用该证书模版？\", \"提示\", () => {\n        activeCertificateTemplate({\n          id: id\n        }, () => {\n          success(\"启用成功\");\n          loadList();\n        });\n      });\n    };\n    const inactive = id => {\n      confirm(\"确认禁用该证书模版？\", \"提示\", () => {\n        inactiveCertificateTemplate({\n          id: id\n        }, () => {\n          success(\"禁用成功\");\n          loadList();\n        });\n      });\n    };\n    const previewCertificate = ref({});\n    const showPreviewViewFlag = ref(false);\n    const showPreview = item => {\n      showPreviewViewFlag.value = true;\n      item.awardDate = item.createTime;\n      item.member = {\n        name: \"张三\",\n        realname: \"张三\"\n      };\n      console.log(\"证书、\", item);\n      previewCertificate.value = item;\n    };\n    const hidePreview = () => {\n      showPreviewViewFlag.value = false;\n    };\n    const multipleSelection = ref([]);\n    const handleSelectionChange = val => {\n      multipleSelection.value = val;\n    };\n    const selectSelectionChange = () => {\n      if (!multipleSelection.value.length) {\n        error(\"请选择证书\");\n      }\n      props.selectCallback && props.selectCallback(multipleSelection.value);\n    };\n    return {\n      handleSelectionChange,\n      selectSelectionChange,\n      previewCertificate,\n      showPreviewViewFlag,\n      showPreview,\n      hidePreview,\n      dataLoading,\n      remove,\n      gotoCertificateTemplateEdit,\n      search,\n      params,\n      total,\n      currentChange,\n      sizeChange,\n      templateList,\n      inactive,\n      active\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "Page", "gotoCertificateTemplateEdit", "findCertificateTemplateList", "deleteCertificateTemplate", "inactiveCertificateTemplate", "activeCertificateTemplate", "confirm", "error", "success", "CertificatePreview", "name", "components", "props", "cancelCallback", "type", "Function", "default", "selectCallback", "isComponent", "Boolean", "setup", "dataLoading", "templateList", "params", "current", "size", "neqStatusList", "loadList", "value", "res", "console", "log", "total", "list", "catch", "currentChange", "c", "sizeChange", "s", "search", "remove", "id", "active", "inactive", "previewCertificate", "showPreviewViewFlag", "showPreview", "item", "awardDate", "createTime", "member", "realname", "hidePreview", "multipleSelection", "handleSelectionChange", "val", "selectSelectionChange", "length"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/certificate/template/index.vue"], "sourcesContent": ["<template>\n  <div class=\"cert-template-wrap\">\n    <div class=\"cert-template-header\">\n      <el-form :inline=\"true\" :model=\"params\" class=\"form-inline\">\n        <el-form-item label=\"证书名称\">\n          <el-input size=\"small\" @keydown.enter=\"search\" class=\"search-input\" v-model=\"params.name\" placeholder=\"请输入关键字\">\n            <template #suffix>\n              <i @click=\"search\" class=\"el-input__icon el-icon-search search-btn\"></i>\n            </template>\n          </el-input>\n        </el-form-item>\n        <el-form-item label=\"状态\" class=\"select\">\n          <el-select size=\"small\" v-model=\"params.status\" @change=\"search\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"启用\" value=\"active\"></el-option>\n            <el-option label=\"禁用\" value=\"inactive\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" @click=\"search()\">\n            <span style=\"vertical-align: middle\">搜索</span>\n          </el-button>\n        </el-form-item>\n        <el-form-item v-if=\"!isComponent\">\n          <el-button size=\"small\" type=\"primary\" @click=\"gotoCertificateTemplateEdit()\">\n            <el-icon style=\"vertical-align: middle\">\n              <Plus />\n            </el-icon>\n            <span style=\"vertical-align: middle\">新增</span>\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"cert-template-main\" :loading=\"dataLoading\">\n      <el-table :data=\"templateList\" @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"45\" v-if=\"isComponent\"/>\n        <el-table-column label=\"序号\" type=\"index\"></el-table-column>\n        <el-table-column label=\"背景图\" prop=\"desgin\">\n          <template #default=\"scope\">\n            <img class=\"desgin\" :src=\"scope.row.design\" />\n          </template>\n        </el-table-column>\n        <el-table-column label=\"证书名称\" prop=\"name\"></el-table-column>\n        <el-table-column label=\"证书描述\" prop=\"description\"></el-table-column>\n        <el-table-column label=\"颁发机构\" prop=\"awardingOrganization\"></el-table-column>\n<!--        <el-table-column label=\"颁发人员\" prop=\"awarderName\"></el-table-column>-->\n<!--        <el-table-column label=\"颁发条件\" prop=\"awardConditions\"></el-table-column>-->\n<!--        <el-table-column label=\"到期策略\" prop=\"validityPolicy\"></el-table-column>-->\n        <el-table-column label=\"状态\" prop=\"statusName\"></el-table-column>\n        <el-table-column label=\"操作\">\n          <template #default=\"scope\">\n            <div class=\"opt-btn-wrap\">\n              <div class=\"opt-btn-item\">\n                <el-button size=\"small\" @click=\"showPreview(scope.row)\">预览</el-button>\n              </div>\n              <div class=\"opt-btn-item\" v-if=\"!isComponent\">\n                <el-button size=\"small\" @click=\"gotoCertificateTemplateEdit(scope.row.id)\">编辑</el-button>\n              </div>\n              <div class=\"opt-btn-item\" v-if=\"!isComponent && scope.row.status === 'inactive'\">\n                <el-button size=\"small\" type=\"primary\" @click=\"active(scope.row.id)\">启用</el-button>\n              </div>\n              <div class=\"opt-btn-item\" v-if=\"!isComponent && scope.row.status === 'active'\">\n                <el-button size=\"small\" type=\"warning\" @click=\"inactive(scope.row.id)\">禁用</el-button>\n              </div>\n  <!--            <div class=\"opt-btn-item\">-->\n  <!--              <el-button size=\"small\" type=\"primary\">关联</el-button>-->\n  <!--            </div>-->\n              <div class=\"opt-btn-item\" v-if=\"!isComponent\">\n                <el-button size=\"small\" type=\"danger\" @click=\"remove(scope.row.id)\">删除</el-button>\n              </div>\n            </div>\n          </template>\n        </el-table-column>\n      </el-table>\n      <page :total=\"total\" :size-change=\"sizeChange\" :current-change=\"currentChange\" :page-size=\"params.size\"/>\n      <template v-if=\"isComponent\">\n        <div class=\"dialog-footer\" style=\"text-align: right;margin-top: 30px;\">\n          <el-button size=\"small\" @click=\"cancelCallback\">取 消</el-button>\n          <el-button size=\"small\" type=\"primary\" @click=\"selectSelectionChange\">确 定</el-button>\n        </div>\n      </template>\n    </div>\n    <el-dialog style=\"min-width: 1163px; min-height: 854px;\" title=\"证书预览\" v-model=\"showPreviewViewFlag\" :before-close=\"hidePreview\" v-if=\"showPreviewViewFlag\">\n      <div>\n        <certificate-preview v-if=\"showPreviewViewFlag\" :download=\"false\" :certificate=\"previewCertificate\" />\n      </div>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button size=\"small\" @click=\"hidePreview\">取 消</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {ref} from \"vue\"\nimport Page from \"@/components/Page\";\nimport {gotoCertificateTemplateEdit} from \"@/router/goto\";\nimport {\n  findCertificateTemplateList,\n  deleteCertificateTemplate,\n  inactiveCertificateTemplate,\n  activeCertificateTemplate\n} from \"@/api/certificate\";\nimport {confirm, error, success} from \"@/util/tipsUtils\";\nimport CertificatePreview from \"@/views/certificate/preview/index.vue\";\nexport default {\n  name: \"LearnReportSignUpIndex\",\n  components: {CertificatePreview, Page},\n  props: {\n    cancelCallback: {\n      type: Function,\n      default: () => {}\n    },\n    selectCallback: {\n      type: Function,\n      default: () => {}\n    },\n    isComponent: {\n      type: Boolean,\n      default: false\n    }\n  },\n  setup(props) {\n    const dataLoading = ref(true)\n    const templateList = ref([])\n    const params = ref({\n      current: 1,\n      size: 20,\n      neqStatusList: [\"deleted\"]\n    })\n    const loadList = () => {\n      findCertificateTemplateList(params.value, res => {\n        console.log(res)\n        if (res) {\n          total.value = res.total;\n          templateList.value = res.list;\n        }\n        dataLoading.value = false\n      }).catch(() => {\n        dataLoading.value = false\n      })\n    }\n    loadList()\n    const total = ref(0)\n    const currentChange = (c) => {\n      params.value.current = c;\n      loadList();\n    }\n    const sizeChange = (s) => {\n      params.value.size = s;\n      loadList();\n    }\n    const search = () => {\n      loadList();\n    }\n    const remove = (id) => {\n      confirm(\"确认删除该证书模版？\", \"提示\", () => {\n        deleteCertificateTemplate(id, () => {\n          success(\"删除成功\");\n          loadList();\n        })\n      })\n    }\n    const active = (id) => {\n      confirm(\"确认启用该证书模版？\", \"提示\", () => {\n        activeCertificateTemplate({id: id}, () => {\n          success(\"启用成功\");\n          loadList();\n        })\n      })\n    }\n    const inactive = (id) => {\n      confirm(\"确认禁用该证书模版？\", \"提示\", () => {\n        inactiveCertificateTemplate({id: id}, () => {\n          success(\"禁用成功\");\n          loadList();\n        })\n      })\n    }\n\n    const previewCertificate = ref({})\n    const showPreviewViewFlag = ref(false);\n    const showPreview = (item) => {\n      showPreviewViewFlag.value = true;\n      item.awardDate = item.createTime\n      item.member = {name: \"张三\", realname: \"张三\"}\n      console.log(\"证书、\", item)\n      previewCertificate.value = item\n    }\n    const hidePreview = () => {\n      showPreviewViewFlag.value = false;\n    }\n\n    const multipleSelection = ref([])\n    const handleSelectionChange = (val) => {\n      multipleSelection.value = val;\n    }\n    const selectSelectionChange = () => {\n      if (!multipleSelection.value.length) {\n        error(\"请选择证书\")\n      }\n      props.selectCallback && props.selectCallback(multipleSelection.value)\n    }\n    return {\n      handleSelectionChange,\n      selectSelectionChange,\n      previewCertificate,\n      showPreviewViewFlag,\n      showPreview,\n      hidePreview,\n      dataLoading,\n      remove,\n      gotoCertificateTemplateEdit,\n      search,\n      params,\n      total,\n      currentChange,\n      sizeChange,\n      templateList,\n      inactive,\n      active\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n  .cert-template-wrap {\n    margin: 20px;\n    font-size: 12px;\n    .cert-template-main {\n      ::v-deep .el-table {\n        font-size: 12px;\n        .el-table__empty-block {\n          line-height: 400px;\n          .el-table__empty-text {\n            line-height: 400px;\n          }\n        }\n        th, td {\n          padding: 6px 0;\n        }\n      }\n    }\n    .opt-btn-wrap {\n      //display: flex;\n    }\n    .opt-btn-item {\n      width: 50%;\n      display: inline-block;\n      margin: 2px;\n    }\n  }\n  .desgin {\n    width: 116px;\n    height: 76px;\n  }\n</style>\n"], "mappings": "AAgGA,SAAQA,GAAG,QAAO,KAAI;AACtB,OAAOC,IAAG,MAAO,mBAAmB;AACpC,SAAQC,2BAA2B,QAAO,eAAe;AACzD,SACEC,2BAA2B,EAC3BC,yBAAyB,EACzBC,2BAA2B,EAC3BC,yBAAwB,QACnB,mBAAmB;AAC1B,SAAQC,OAAO,EAAEC,KAAK,EAAEC,OAAO,QAAO,kBAAkB;AACxD,OAAOC,kBAAiB,MAAO,uCAAuC;AACtE,eAAe;EACbC,IAAI,EAAE,wBAAwB;EAC9BC,UAAU,EAAE;IAACF,kBAAkB;IAAET;EAAI,CAAC;EACtCY,KAAK,EAAE;IACLC,cAAc,EAAE;MACdC,IAAI,EAAEC,QAAQ;MACdC,OAAO,EAAEA,CAAA,KAAM,CAAC;IAClB,CAAC;IACDC,cAAc,EAAE;MACdH,IAAI,EAAEC,QAAQ;MACdC,OAAO,EAAEA,CAAA,KAAM,CAAC;IAClB,CAAC;IACDE,WAAW,EAAE;MACXJ,IAAI,EAAEK,OAAO;MACbH,OAAO,EAAE;IACX;EACF,CAAC;EACDI,KAAKA,CAACR,KAAK,EAAE;IACX,MAAMS,WAAU,GAAItB,GAAG,CAAC,IAAI;IAC5B,MAAMuB,YAAW,GAAIvB,GAAG,CAAC,EAAE;IAC3B,MAAMwB,MAAK,GAAIxB,GAAG,CAAC;MACjByB,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,aAAa,EAAE,CAAC,SAAS;IAC3B,CAAC;IACD,MAAMC,QAAO,GAAIA,CAAA,KAAM;MACrBzB,2BAA2B,CAACqB,MAAM,CAACK,KAAK,EAAEC,GAAE,IAAK;QAC/CC,OAAO,CAACC,GAAG,CAACF,GAAG;QACf,IAAIA,GAAG,EAAE;UACPG,KAAK,CAACJ,KAAI,GAAIC,GAAG,CAACG,KAAK;UACvBV,YAAY,CAACM,KAAI,GAAIC,GAAG,CAACI,IAAI;QAC/B;QACAZ,WAAW,CAACO,KAAI,GAAI,KAAI;MAC1B,CAAC,CAAC,CAACM,KAAK,CAAC,MAAM;QACbb,WAAW,CAACO,KAAI,GAAI,KAAI;MAC1B,CAAC;IACH;IACAD,QAAQ,EAAC;IACT,MAAMK,KAAI,GAAIjC,GAAG,CAAC,CAAC;IACnB,MAAMoC,aAAY,GAAKC,CAAC,IAAK;MAC3Bb,MAAM,CAACK,KAAK,CAACJ,OAAM,GAAIY,CAAC;MACxBT,QAAQ,EAAE;IACZ;IACA,MAAMU,UAAS,GAAKC,CAAC,IAAK;MACxBf,MAAM,CAACK,KAAK,CAACH,IAAG,GAAIa,CAAC;MACrBX,QAAQ,EAAE;IACZ;IACA,MAAMY,MAAK,GAAIA,CAAA,KAAM;MACnBZ,QAAQ,EAAE;IACZ;IACA,MAAMa,MAAK,GAAKC,EAAE,IAAK;MACrBnC,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM;QAChCH,yBAAyB,CAACsC,EAAE,EAAE,MAAM;UAClCjC,OAAO,CAAC,MAAM,CAAC;UACfmB,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC;IACH;IACA,MAAMe,MAAK,GAAKD,EAAE,IAAK;MACrBnC,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM;QAChCD,yBAAyB,CAAC;UAACoC,EAAE,EAAEA;QAAE,CAAC,EAAE,MAAM;UACxCjC,OAAO,CAAC,MAAM,CAAC;UACfmB,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC;IACH;IACA,MAAMgB,QAAO,GAAKF,EAAE,IAAK;MACvBnC,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM;QAChCF,2BAA2B,CAAC;UAACqC,EAAE,EAAEA;QAAE,CAAC,EAAE,MAAM;UAC1CjC,OAAO,CAAC,MAAM,CAAC;UACfmB,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC;IACH;IAEA,MAAMiB,kBAAiB,GAAI7C,GAAG,CAAC,CAAC,CAAC;IACjC,MAAM8C,mBAAkB,GAAI9C,GAAG,CAAC,KAAK,CAAC;IACtC,MAAM+C,WAAU,GAAKC,IAAI,IAAK;MAC5BF,mBAAmB,CAACjB,KAAI,GAAI,IAAI;MAChCmB,IAAI,CAACC,SAAQ,GAAID,IAAI,CAACE,UAAS;MAC/BF,IAAI,CAACG,MAAK,GAAI;QAACxC,IAAI,EAAE,IAAI;QAAEyC,QAAQ,EAAE;MAAI;MACzCrB,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEgB,IAAI;MACvBH,kBAAkB,CAAChB,KAAI,GAAImB,IAAG;IAChC;IACA,MAAMK,WAAU,GAAIA,CAAA,KAAM;MACxBP,mBAAmB,CAACjB,KAAI,GAAI,KAAK;IACnC;IAEA,MAAMyB,iBAAgB,GAAItD,GAAG,CAAC,EAAE;IAChC,MAAMuD,qBAAoB,GAAKC,GAAG,IAAK;MACrCF,iBAAiB,CAACzB,KAAI,GAAI2B,GAAG;IAC/B;IACA,MAAMC,qBAAoB,GAAIA,CAAA,KAAM;MAClC,IAAI,CAACH,iBAAiB,CAACzB,KAAK,CAAC6B,MAAM,EAAE;QACnClD,KAAK,CAAC,OAAO;MACf;MACAK,KAAK,CAACK,cAAa,IAAKL,KAAK,CAACK,cAAc,CAACoC,iBAAiB,CAACzB,KAAK;IACtE;IACA,OAAO;MACL0B,qBAAqB;MACrBE,qBAAqB;MACrBZ,kBAAkB;MAClBC,mBAAmB;MACnBC,WAAW;MACXM,WAAW;MACX/B,WAAW;MACXmB,MAAM;MACNvC,2BAA2B;MAC3BsC,MAAM;MACNhB,MAAM;MACNS,KAAK;MACLG,aAAa;MACbE,UAAU;MACVf,YAAY;MACZqB,QAAQ;MACRD;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}