{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"work-item-box\"\n}, [/*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"item-content\"\n}, [/*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"content-main\"\n}, [/*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"main-title\"\n}, [/*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"title-box two-line\"\n}, [/*#__PURE__*/_createElementVNode(\"span\", {\n  class: \"title-text\"\n}, \"批量报名\")])])])])], -1 /* HOISTED */);\nconst _hoisted_2 = {\n  class: \"topic-list-wrapper\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_member_list = _resolveComponent(\"member-list\");\n  const _component_el_drawer = _resolveComponent(\"el-drawer\");\n  return _openBlock(), _createBlock(_component_el_drawer, {\n    \"custom-class\": \"sign-up-drawer\",\n    modelValue: $setup.dialogModel,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.dialogModel = $event),\n    direction: \"rtl\",\n    \"before-close\": $props.drawerClose,\n    \"destroy-on-close\": \"\"\n  }, {\n    title: _withCtx(() => [_hoisted_1]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_member_list, {\n      \"is-component\": true,\n      \"cancel-callback\": _ctx.signUpCancelSelectMember,\n      \"select-callback\": _ctx.signUpSelectMember\n    }, null, 8 /* PROPS */, [\"cancel-callback\", \"select-callback\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"before-close\"]);\n}", "map": {"version": 3, "names": ["_createElementVNode", "class", "_createBlock", "_component_el_drawer", "$setup", "dialogModel", "$event", "direction", "$props", "drawerClose", "title", "_withCtx", "_hoisted_1", "_hoisted_2", "_createVNode", "_component_member_list", "_ctx", "signUpCancelSelectMember", "signUpSelectMember"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/learn/signup/batch/index.vue"], "sourcesContent": ["<template>\n  <el-drawer custom-class=\"sign-up-drawer\" v-model=\"dialogModel\" direction=\"rtl\" :before-close=\"drawerClose\" destroy-on-close>\n    <template #title>\n      <div class=\"work-item-box\">\n        <div class=\"item-content\">\n          <div class=\"content-main\">\n            <div class=\"main-title\">\n              <div class=\"title-box two-line\">\n                <span class=\"title-text\">批量报名</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </template>\n    <div class=\"topic-list-wrapper\">\n      <member-list :is-component=\"true\" :cancel-callback=\"signUpCancelSelectMember\" :select-callback=\"signUpSelectMember\" />\n    </div>\n  </el-drawer>\n</template>\n\n<script>\nimport {computed, ref} from \"vue\";\nimport {getSignUpList} from \"@/api/learn/lesson\";\nimport MemberList from \"@/views/member/list/index.vue\";\nimport {error} from \"@/util/tipsUtils\";\nimport * as paperApi from \"@/api/exam/paper\";\n\nexport default {\n  name: \"SignupRecordIndex\",\n  components: {\n    MemberList\n  },\n  props: {\n    topic: {\n      type: Object,\n      required: true\n    },\n    showDrawer: {\n      type: Boolean,\n      required: true\n    },\n    drawerClose: {\n      type: Function,\n      required: true\n    }\n  },\n  setup(props, context) {\n    const dialogModel = computed({\n      get() {\n        return props.showDrawer;\n      },\n      set(val) {\n        context.emit('update:showDrawer', val);\n      },\n    });\n    // 查看报名记录\n    const signUpLoading = ref(false)\n    const signUpList = ref([])\n    const signUpTotal = ref(0)\n    const signUpParam = ref({\n      current: 1,\n      size: 20,\n      lessonId: 0\n    })\n    // const loadSignUpList = () => {\n    //   signUpLoading.value = true\n    //   getSignUpList(signUpParam.value, res => {\n    //     signUpList.value = res.list\n    //     signUpTotal.value = res.total\n    //     signUpLoading.value = false\n    //   })\n    // }\n    // const signUpCurrentChange = (currentPage) => {\n    //   signUpParam.value.current = currentPage;\n    //   loadSignUpList();\n    // }\n    // const signUpSizeChange = (s) => {\n    //   signUpParam.value.size = s;\n    //   loadSignUpList();\n    // }\n    // signUpParam.value.current = 1\n    // signUpParam.value.lessonId = ref(props.topic.id)\n    // loadSignUpList()\n    // const signUpStatusMap = {\n    //   \"signed_up\": \"已报名\",\n    //   \"cancel_sign_up\": \"取消报名\",\n    //   \"completed\": \"已完成\"\n    // }\n\n    const hidePaper = () => {\n      showPaperDialog.value = false;\n    }\n    const paperSelectionChange = (paperIdList) => {\n      if(!paperIdList || !paperIdList.length) {\n        error(\"请选择试卷\");\n        return;\n      }\n      paperApi.getBaseInfo(paperIdList[0], (res) => {\n        paper.value = res\n      })\n      hidePaper()\n    }\n\n    return {\n      hidePaper,\n      paperSelectionChange,\n      signUpParam,\n      signUpTotal,\n      signUpList,\n      signUpLoading,\n      // signUpCurrentChange,\n      // signUpSizeChange,\n      // signUpStatusMap,\n      dialogModel\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n\n</style>\n"], "mappings": ";gCAGMA,mBAAA,CAUM;EAVDC,KAAK,EAAC;AAAe,I,aACxBD,mBAAA,CAQM;EARDC,KAAK,EAAC;AAAc,I,aACvBD,mBAAA,CAMM;EANDC,KAAK,EAAC;AAAc,I,aACvBD,mBAAA,CAIM;EAJDC,KAAK,EAAC;AAAY,I,aACrBD,mBAAA,CAEM;EAFDC,KAAK,EAAC;AAAoB,I,aAC7BD,mBAAA,CAAoC;EAA9BC,KAAK,EAAC;AAAY,GAAC,MAAI,E;;EAOpCA,KAAK,EAAC;AAAoB;;;;uBAdjCC,YAAA,CAiBYC,oBAAA;IAjBD,cAAY,EAAC,gBAAgB;gBAAUC,MAAA,CAAAC,WAAW;+DAAXD,MAAA,CAAAC,WAAW,GAAAC,MAAA;IAAEC,SAAS,EAAC,KAAK;IAAE,cAAY,EAAEC,MAAA,CAAAC,WAAW;IAAE,kBAAgB,EAAhB;;IAC9FC,KAAK,EAAAC,QAAA,CACd,MAUM,CAVNC,UAUM,C;sBAER,MAEM,CAFNZ,mBAAA,CAEM,OAFNa,UAEM,GADJC,YAAA,CAAsHC,sBAAA;MAAxG,cAAY,EAAE,IAAI;MAAG,iBAAe,EAAEC,IAAA,CAAAC,wBAAwB;MAAG,iBAAe,EAAED,IAAA,CAAAE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}