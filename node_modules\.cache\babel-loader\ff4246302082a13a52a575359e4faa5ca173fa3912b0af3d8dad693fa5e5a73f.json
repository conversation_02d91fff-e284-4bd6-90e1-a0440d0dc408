{"ast": null, "code": "import { nextTick, onMounted, ref, watch } from \"vue\";\nimport { toBase64 } from \"@/api/oss/oss\";\nexport default {\n  name: \"CertificatePreview\",\n  props: {\n    certificate: {\n      type: Object\n    },\n    download: {\n      type: Boolean\n    }\n  },\n  setup(props) {\n    const downloadFlag = ref(false);\n    const certificateInfo = ref(props.certificate);\n    const certificateCanvas = ref(null);\n    const canvasContainer = ref(null);\n\n    // 背景图的固定宽高比\n    const backgroundWidth = 2894;\n    const backgroundHeight = 4093;\n\n    // 计算容器的高度\n    const setContainerHeight = () => {\n      const container = canvasContainer.value;\n      const containerWidth = container.clientWidth;\n\n      // 根据背景图的宽高比计算容器的高度\n      const calculatedHeight = containerWidth * (backgroundHeight / backgroundWidth);\n      container.style.height = `${calculatedHeight}px`;\n    };\n\n    // 获取并设置Canvas尺寸\n    const setCanvasSize = () => {\n      const container = canvasContainer.value;\n      const canvas = certificateCanvas.value;\n      const containerWidth = container.clientWidth;\n      const containerHeight = container.clientHeight;\n      canvas.width = containerWidth;\n      canvas.height = containerHeight;\n\n      // 重新生成证书内容\n      generateCertificate(certificateInfo.value);\n    };\n\n    // 加载图片的工具方法，返回一个Promise\n    const loadImage = url => {\n      return new Promise((resolve, reject) => {\n        toBase64(url, res => {\n          console.log(\"toBase64\", res);\n          const img = new Image();\n          img.src = url;\n          img.crossOrigin = 'anonymous';\n          img.onload = () => resolve(img);\n          img.onerror = err => reject(err);\n        }).catch(err => reject(err));\n      });\n    };\n\n    // 传入日期字符串 '2025-02-01 00:39:29'\n    const getYear = dateString => {\n      if (!dateString) {\n        return '';\n      }\n      return dateString.split(' ')[0].split('-')[0]; // 拆分获取年份\n    };\n\n    const getMonth = dateString => {\n      if (!dateString) {\n        return '';\n      }\n      return dateString.split(' ')[0].split('-')[1]; // 拆分获取月份\n    };\n\n    const getDay = dateString => {\n      if (!dateString) {\n        return '';\n      }\n      return dateString.split(' ')[0].split('-')[2]; // 拆分获取日期\n    };\n\n    // 根据证书对象生成证书\n    const generateCertificate = async certificateInfo => {\n      const canvas = certificateCanvas.value;\n      const ctx = canvas.getContext('2d');\n      try {\n        // 同时加载背景图和头像图\n        const [backgroundImage, avatar] = await Promise.all([loadImage(certificateInfo.design), loadImage(certificateInfo.member && certificateInfo.member.idPhoto).catch(() => null) // 捕获头像加载失败\n        ]);\n\n        // 计算缩放比例\n        const imageRatio = backgroundImage.width / backgroundImage.height;\n        const canvasRatio = canvas.width / canvas.height;\n        let drawWidth,\n          drawHeight,\n          offsetX = 0,\n          offsetY = 0;\n        if (canvasRatio > imageRatio) {\n          // 如果Canvas宽高比大于背景图宽高比，限制Canvas高度，宽度自适应\n          drawHeight = canvas.height;\n          drawWidth = drawHeight * imageRatio;\n          offsetX = (canvas.width - drawWidth) / 2; // 水平居中\n        } else {\n          // 如果Canvas宽高比小于背景图宽高比，限制Canvas宽度，高度自适应\n          drawWidth = canvas.width;\n          drawHeight = drawWidth / imageRatio;\n          offsetY = (canvas.height - drawHeight) / 2; // 垂直居中\n        }\n\n        // 将背景图绘制到Canvas，背景图根据 Canvas 尺寸缩放并居中\n        ctx.drawImage(backgroundImage, 0, 0, backgroundImage.width, backgroundImage.height, offsetX, offsetY, drawWidth, drawHeight);\n\n        // 绘制背景图\n        // ctx.drawImage(backgroundImage, 0, 0, canvas.width, canvas.height);\n\n        // 如果头像存在且加载成功，则绘制头像\n        if (avatar) {\n          let avatarSize = drawWidth * 0.175; // 头像大小占画布宽度的 17.5%\n          let avatarYSize = drawHeight * 0.165; // 头像大小占画布宽度的 20%\n          let avatarX = drawWidth * 0.66; // 居中\n          let avatarY = drawHeight * 0.382; // 头像位置在画布上方\n          ctx.drawImage(avatar, avatarX, avatarY, avatarSize, avatarYSize); // 头像位置和大小\n          // } else {\n          //   // 头像加载失败或者没有提供，绘制默认的“无头像”占位符\n          //   ctx.fillStyle = 'gray';\n          //   ctx.fillRect(50, 50, 100, 100); // 绘制灰色方块作为占位符\n          //   ctx.fillStyle = 'white';\n          //   ctx.font = '24px Arial';\n          //   ctx.fillText('无头像', 55, 110); // 标注“无头像”\n        }\n\n        // 绘制文字\n        ctx.font = '30px Arial';\n        ctx.fillStyle = \"#000\";\n        ctx.fillText(`${certificateInfo.code}`, drawWidth * 0.17, drawHeight * 0.49);\n        ctx.fillText(`${certificateInfo.member.realname || this.certificateInfo.member.name}`, drawWidth * 0.2, drawHeight * 0.59);\n        ctx.fillText(`${certificateInfo.description}`, drawWidth * 0.42, drawHeight * 0.59);\n        ctx.fillText(`${getYear(certificateInfo.awardDate)}`, drawWidth * 0.56, drawHeight * 0.79);\n        ctx.fillText(`${getMonth(certificateInfo.awardDate)}`, drawWidth * 0.69, drawHeight * 0.79);\n        ctx.fillText(`${getDay(certificateInfo.awardDate)}`, drawWidth * 0.77, drawHeight * 0.79);\n        downloadFlag.value = true;\n      } catch (error) {\n        console.error('图片加载失败', error);\n      }\n    };\n\n    // 在组件挂载后生成证书\n    onMounted(() => {\n      nextTick(() => {\n        setContainerHeight(); // 更新容器高度\n        setCanvasSize(); // 更新Canvas尺寸\n      });\n    });\n\n    // 监听容器尺寸变化\n    watch(canvasContainer, () => {\n      setContainerHeight(); // 更新容器高度\n      setCanvasSize(); // 更新Canvas尺寸\n    });\n\n    // 下载证书\n    const downloadCertificate = () => {\n      const canvas = certificateCanvas.value;\n      const imageUrl = canvas.toDataURL('image/png'); // 转换为PNG图片\n      const link = document.createElement('a');\n      link.href = imageUrl;\n      link.download = 'certificate.png'; // 下载文件名\n      link.click();\n    };\n    return {\n      downloadFlag,\n      canvasContainer,\n      certificateCanvas,\n      downloadCertificate,\n      certificateInfo\n    };\n  }\n};", "map": {"version": 3, "names": ["nextTick", "onMounted", "ref", "watch", "toBase64", "name", "props", "certificate", "type", "Object", "download", "Boolean", "setup", "downloadFlag", "certificateInfo", "certificateCanvas", "canvasContainer", "backgroundWidth", "backgroundHeight", "setContainerHeight", "container", "value", "containerWidth", "clientWidth", "calculatedHeight", "style", "height", "setCanvasSize", "canvas", "containerHeight", "clientHeight", "width", "generateCertificate", "loadImage", "url", "Promise", "resolve", "reject", "res", "console", "log", "img", "Image", "src", "crossOrigin", "onload", "onerror", "err", "catch", "getYear", "dateString", "split", "getMonth", "getDay", "ctx", "getContext", "backgroundImage", "avatar", "all", "design", "member", "idPhoto", "imageRatio", "canvasRatio", "drawWidth", "drawHeight", "offsetX", "offsetY", "drawImage", "avatarSize", "avatarYSize", "avatarX", "avatarY", "font", "fillStyle", "fillText", "code", "realname", "description", "awardDate", "error", "downloadCertificate", "imageUrl", "toDataURL", "link", "document", "createElement", "href", "click"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/certificate/preview/index.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-button v-if=\"downloadFlag\" type=\"primary\" size=\"large\" @click=\"downloadCertificate\">下载证书</el-button>\n    <div class=\"certificate-coat\">\n      <div class=\"certificate-rotate-wrap\" ref=\"canvasContainer\">\n        <canvas ref=\"certificateCanvas\"></canvas>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {nextTick, onMounted, ref, watch} from \"vue\";\nimport {toBase64} from \"@/api/oss/oss\";\n\nexport default {\n  name: \"CertificatePreview\",\n  props: {\n    certificate: {\n      type: Object\n    },\n    download: {\n      type: Boolean\n    }\n  },\n  setup(props) {\n    const downloadFlag = ref(false)\n    const certificateInfo = ref(props.certificate)\n\n    const certificateCanvas = ref(null);\n    const canvasContainer = ref(null);\n\n    // 背景图的固定宽高比\n    const backgroundWidth = 2894;\n    const backgroundHeight = 4093;\n\n    // 计算容器的高度\n    const setContainerHeight = () => {\n      const container = canvasContainer.value;\n      const containerWidth = container.clientWidth;\n\n      // 根据背景图的宽高比计算容器的高度\n      const calculatedHeight = containerWidth * (backgroundHeight / backgroundWidth);\n\n      container.style.height = `${calculatedHeight}px`;\n    };\n\n    // 获取并设置Canvas尺寸\n    const setCanvasSize = () => {\n      const container = canvasContainer.value;\n      const canvas = certificateCanvas.value;\n\n      const containerWidth = container.clientWidth;\n      const containerHeight = container.clientHeight;\n\n      canvas.width = containerWidth;\n      canvas.height = containerHeight;\n\n      // 重新生成证书内容\n      generateCertificate(certificateInfo.value);\n    };\n\n\n    // 加载图片的工具方法，返回一个Promise\n    const loadImage = (url) => {\n      return new Promise((resolve, reject) => {\n        toBase64(url, res => {\n          console.log(\"toBase64\", res)\n          const img = new Image();\n          img.src = url;\n          img.crossOrigin = 'anonymous';\n          img.onload = () => resolve(img);\n          img.onerror = (err) => reject(err);\n        }).catch((err) => reject(err))\n      });\n    };\n\n    // 传入日期字符串 '2025-02-01 00:39:29'\n    const getYear = (dateString) => {\n      if (!dateString) {\n        return ''\n      }\n      return dateString.split(' ')[0].split('-')[0]; // 拆分获取年份\n    }\n\n    const getMonth = (dateString) =>  {\n      if (!dateString) {\n        return ''\n      }\n      return dateString.split(' ')[0].split('-')[1]; // 拆分获取月份\n    }\n\n    const getDay = (dateString) => {\n      if (!dateString) {\n        return ''\n      }\n      return dateString.split(' ')[0].split('-')[2]; // 拆分获取日期\n    }\n\n    // 根据证书对象生成证书\n    const generateCertificate = async (certificateInfo) => {\n      const canvas = certificateCanvas.value;\n      const ctx = canvas.getContext('2d');\n\n      try {\n        // 同时加载背景图和头像图\n        const [backgroundImage, avatar] = await Promise.all([\n          loadImage(certificateInfo.design),\n          loadImage(certificateInfo.member && certificateInfo.member.idPhoto).catch(() => null), // 捕获头像加载失败\n        ]);\n\n        // 计算缩放比例\n        const imageRatio = backgroundImage.width / backgroundImage.height;\n        const canvasRatio = canvas.width / canvas.height;\n\n        let drawWidth, drawHeight, offsetX = 0, offsetY = 0;\n\n        if (canvasRatio > imageRatio) {\n          // 如果Canvas宽高比大于背景图宽高比，限制Canvas高度，宽度自适应\n          drawHeight = canvas.height;\n          drawWidth = drawHeight * imageRatio;\n          offsetX = (canvas.width - drawWidth) / 2;  // 水平居中\n        } else {\n          // 如果Canvas宽高比小于背景图宽高比，限制Canvas宽度，高度自适应\n          drawWidth = canvas.width;\n          drawHeight = drawWidth / imageRatio;\n          offsetY = (canvas.height - drawHeight) / 2;  // 垂直居中\n        }\n\n        // 将背景图绘制到Canvas，背景图根据 Canvas 尺寸缩放并居中\n        ctx.drawImage(backgroundImage, 0, 0, backgroundImage.width, backgroundImage.height, offsetX, offsetY, drawWidth, drawHeight);\n\n\n        // 绘制背景图\n        // ctx.drawImage(backgroundImage, 0, 0, canvas.width, canvas.height);\n\n        // 如果头像存在且加载成功，则绘制头像\n        if (avatar) {\n          let avatarSize = drawWidth * 0.175; // 头像大小占画布宽度的 17.5%\n          let avatarYSize = drawHeight * 0.165; // 头像大小占画布宽度的 20%\n          let avatarX = drawWidth * 0.66; // 居中\n          let avatarY = drawHeight * 0.382; // 头像位置在画布上方\n          ctx.drawImage(avatar, avatarX, avatarY, avatarSize, avatarYSize); // 头像位置和大小\n        // } else {\n        //   // 头像加载失败或者没有提供，绘制默认的“无头像”占位符\n        //   ctx.fillStyle = 'gray';\n        //   ctx.fillRect(50, 50, 100, 100); // 绘制灰色方块作为占位符\n        //   ctx.fillStyle = 'white';\n        //   ctx.font = '24px Arial';\n        //   ctx.fillText('无头像', 55, 110); // 标注“无头像”\n        }\n\n        // 绘制文字\n        ctx.font = '30px Arial';\n        ctx.fillStyle = \"#000\";\n        ctx.fillText(`${certificateInfo.code}`, drawWidth * 0.17, drawHeight * 0.49);\n        ctx.fillText(`${certificateInfo.member.realname || this.certificateInfo.member.name}`, drawWidth * 0.2, drawHeight * 0.59);\n        ctx.fillText(`${certificateInfo.description}`, drawWidth * 0.42, drawHeight * 0.59);\n\n        ctx.fillText(`${getYear(certificateInfo.awardDate)}`, drawWidth * 0.56, drawHeight * 0.79);\n        ctx.fillText(`${getMonth(certificateInfo.awardDate)}`, drawWidth * 0.69, drawHeight * 0.79);\n        ctx.fillText(`${getDay(certificateInfo.awardDate)}`, drawWidth * 0.77, drawHeight * 0.79);\n\n\n        downloadFlag.value = true\n\n      } catch (error) {\n        console.error('图片加载失败', error);\n      }\n    };\n\n    // 在组件挂载后生成证书\n    onMounted(() => {\n      nextTick(() => {\n        setContainerHeight();  // 更新容器高度\n        setCanvasSize();  // 更新Canvas尺寸\n      });\n    });\n\n    // 监听容器尺寸变化\n    watch(canvasContainer, () => {\n      setContainerHeight();  // 更新容器高度\n      setCanvasSize();  // 更新Canvas尺寸\n    });\n\n    // 下载证书\n    const downloadCertificate = () => {\n      const canvas = certificateCanvas.value;\n      const imageUrl = canvas.toDataURL('image/png'); // 转换为PNG图片\n      const link = document.createElement('a');\n      link.href = imageUrl;\n      link.download = 'certificate.png'; // 下载文件名\n      link.click();\n    };\n\n    return {\n      downloadFlag,\n      canvasContainer,\n      certificateCanvas,\n      downloadCertificate,\n      certificateInfo\n    };\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.el-button {\n  font-size: 20px;\n  border-radius: 4px;\n  margin: 10px;\n}\n.certificate-coat {\n  box-sizing: border-box;\n}\n.certificate-rotate {\n  transform: rotate(90deg);\n  margin-top: 165px!important;\n  margin-left: -165px!important;\n}\n</style>\n"], "mappings": "AAYA,SAAQA,QAAQ,EAAEC,SAAS,EAAEC,GAAG,EAAEC,KAAK,QAAO,KAAK;AACnD,SAAQC,QAAQ,QAAO,eAAe;AAEtC,eAAe;EACbC,IAAI,EAAE,oBAAoB;EAC1BC,KAAK,EAAE;IACLC,WAAW,EAAE;MACXC,IAAI,EAAEC;IACR,CAAC;IACDC,QAAQ,EAAE;MACRF,IAAI,EAAEG;IACR;EACF,CAAC;EACDC,KAAKA,CAACN,KAAK,EAAE;IACX,MAAMO,YAAW,GAAIX,GAAG,CAAC,KAAK;IAC9B,MAAMY,eAAc,GAAIZ,GAAG,CAACI,KAAK,CAACC,WAAW;IAE7C,MAAMQ,iBAAgB,GAAIb,GAAG,CAAC,IAAI,CAAC;IACnC,MAAMc,eAAc,GAAId,GAAG,CAAC,IAAI,CAAC;;IAEjC;IACA,MAAMe,eAAc,GAAI,IAAI;IAC5B,MAAMC,gBAAe,GAAI,IAAI;;IAE7B;IACA,MAAMC,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,MAAMC,SAAQ,GAAIJ,eAAe,CAACK,KAAK;MACvC,MAAMC,cAAa,GAAIF,SAAS,CAACG,WAAW;;MAE5C;MACA,MAAMC,gBAAe,GAAIF,cAAa,IAAKJ,gBAAe,GAAID,eAAe,CAAC;MAE9EG,SAAS,CAACK,KAAK,CAACC,MAAK,GAAK,GAAEF,gBAAiB,IAAG;IAClD,CAAC;;IAED;IACA,MAAMG,aAAY,GAAIA,CAAA,KAAM;MAC1B,MAAMP,SAAQ,GAAIJ,eAAe,CAACK,KAAK;MACvC,MAAMO,MAAK,GAAIb,iBAAiB,CAACM,KAAK;MAEtC,MAAMC,cAAa,GAAIF,SAAS,CAACG,WAAW;MAC5C,MAAMM,eAAc,GAAIT,SAAS,CAACU,YAAY;MAE9CF,MAAM,CAACG,KAAI,GAAIT,cAAc;MAC7BM,MAAM,CAACF,MAAK,GAAIG,eAAe;;MAE/B;MACAG,mBAAmB,CAAClB,eAAe,CAACO,KAAK,CAAC;IAC5C,CAAC;;IAGD;IACA,MAAMY,SAAQ,GAAKC,GAAG,IAAK;MACzB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtCjC,QAAQ,CAAC8B,GAAG,EAAEI,GAAE,IAAK;UACnBC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEF,GAAG;UAC3B,MAAMG,GAAE,GAAI,IAAIC,KAAK,EAAE;UACvBD,GAAG,CAACE,GAAE,GAAIT,GAAG;UACbO,GAAG,CAACG,WAAU,GAAI,WAAW;UAC7BH,GAAG,CAACI,MAAK,GAAI,MAAMT,OAAO,CAACK,GAAG,CAAC;UAC/BA,GAAG,CAACK,OAAM,GAAKC,GAAG,IAAKV,MAAM,CAACU,GAAG,CAAC;QACpC,CAAC,CAAC,CAACC,KAAK,CAAED,GAAG,IAAKV,MAAM,CAACU,GAAG,CAAC;MAC/B,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAME,OAAM,GAAKC,UAAU,IAAK;MAC9B,IAAI,CAACA,UAAU,EAAE;QACf,OAAO,EAAC;MACV;MACA,OAAOA,UAAU,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;IACjD;;IAEA,MAAMC,QAAO,GAAKF,UAAU,IAAM;MAChC,IAAI,CAACA,UAAU,EAAE;QACf,OAAO,EAAC;MACV;MACA,OAAOA,UAAU,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;IACjD;;IAEA,MAAME,MAAK,GAAKH,UAAU,IAAK;MAC7B,IAAI,CAACA,UAAU,EAAE;QACf,OAAO,EAAC;MACV;MACA,OAAOA,UAAU,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;IACjD;;IAEA;IACA,MAAMnB,mBAAkB,GAAI,MAAOlB,eAAe,IAAK;MACrD,MAAMc,MAAK,GAAIb,iBAAiB,CAACM,KAAK;MACtC,MAAMiC,GAAE,GAAI1B,MAAM,CAAC2B,UAAU,CAAC,IAAI,CAAC;MAEnC,IAAI;QACF;QACA,MAAM,CAACC,eAAe,EAAEC,MAAM,IAAI,MAAMtB,OAAO,CAACuB,GAAG,CAAC,CAClDzB,SAAS,CAACnB,eAAe,CAAC6C,MAAM,CAAC,EACjC1B,SAAS,CAACnB,eAAe,CAAC8C,MAAK,IAAK9C,eAAe,CAAC8C,MAAM,CAACC,OAAO,CAAC,CAACb,KAAK,CAAC,MAAM,IAAI,CAAC,CAAE;QAAA,CACxF,CAAC;;QAEF;QACA,MAAMc,UAAS,GAAIN,eAAe,CAACzB,KAAI,GAAIyB,eAAe,CAAC9B,MAAM;QACjE,MAAMqC,WAAU,GAAInC,MAAM,CAACG,KAAI,GAAIH,MAAM,CAACF,MAAM;QAEhD,IAAIsC,SAAS;UAAEC,UAAU;UAAEC,OAAM,GAAI,CAAC;UAAEC,OAAM,GAAI,CAAC;QAEnD,IAAIJ,WAAU,GAAID,UAAU,EAAE;UAC5B;UACAG,UAAS,GAAIrC,MAAM,CAACF,MAAM;UAC1BsC,SAAQ,GAAIC,UAAS,GAAIH,UAAU;UACnCI,OAAM,GAAI,CAACtC,MAAM,CAACG,KAAI,GAAIiC,SAAS,IAAI,CAAC,EAAG;QAC7C,OAAO;UACL;UACAA,SAAQ,GAAIpC,MAAM,CAACG,KAAK;UACxBkC,UAAS,GAAID,SAAQ,GAAIF,UAAU;UACnCK,OAAM,GAAI,CAACvC,MAAM,CAACF,MAAK,GAAIuC,UAAU,IAAI,CAAC,EAAG;QAC/C;;QAEA;QACAX,GAAG,CAACc,SAAS,CAACZ,eAAe,EAAE,CAAC,EAAE,CAAC,EAAEA,eAAe,CAACzB,KAAK,EAAEyB,eAAe,CAAC9B,MAAM,EAAEwC,OAAO,EAAEC,OAAO,EAAEH,SAAS,EAAEC,UAAU,CAAC;;QAG5H;QACA;;QAEA;QACA,IAAIR,MAAM,EAAE;UACV,IAAIY,UAAS,GAAIL,SAAQ,GAAI,KAAK,EAAE;UACpC,IAAIM,WAAU,GAAIL,UAAS,GAAI,KAAK,EAAE;UACtC,IAAIM,OAAM,GAAIP,SAAQ,GAAI,IAAI,EAAE;UAChC,IAAIQ,OAAM,GAAIP,UAAS,GAAI,KAAK,EAAE;UAClCX,GAAG,CAACc,SAAS,CAACX,MAAM,EAAEc,OAAO,EAAEC,OAAO,EAAEH,UAAU,EAAEC,WAAW,CAAC,EAAE;UACpE;UACA;UACA;UACA;UACA;UACA;UACA;QACA;;QAEA;QACAhB,GAAG,CAACmB,IAAG,GAAI,YAAY;QACvBnB,GAAG,CAACoB,SAAQ,GAAI,MAAM;QACtBpB,GAAG,CAACqB,QAAQ,CAAE,GAAE7D,eAAe,CAAC8D,IAAK,EAAC,EAAEZ,SAAQ,GAAI,IAAI,EAAEC,UAAS,GAAI,IAAI,CAAC;QAC5EX,GAAG,CAACqB,QAAQ,CAAE,GAAE7D,eAAe,CAAC8C,MAAM,CAACiB,QAAO,IAAK,IAAI,CAAC/D,eAAe,CAAC8C,MAAM,CAACvD,IAAK,EAAC,EAAE2D,SAAQ,GAAI,GAAG,EAAEC,UAAS,GAAI,IAAI,CAAC;QAC1HX,GAAG,CAACqB,QAAQ,CAAE,GAAE7D,eAAe,CAACgE,WAAY,EAAC,EAAEd,SAAQ,GAAI,IAAI,EAAEC,UAAS,GAAI,IAAI,CAAC;QAEnFX,GAAG,CAACqB,QAAQ,CAAE,GAAE1B,OAAO,CAACnC,eAAe,CAACiE,SAAS,CAAE,EAAC,EAAEf,SAAQ,GAAI,IAAI,EAAEC,UAAS,GAAI,IAAI,CAAC;QAC1FX,GAAG,CAACqB,QAAQ,CAAE,GAAEvB,QAAQ,CAACtC,eAAe,CAACiE,SAAS,CAAE,EAAC,EAAEf,SAAQ,GAAI,IAAI,EAAEC,UAAS,GAAI,IAAI,CAAC;QAC3FX,GAAG,CAACqB,QAAQ,CAAE,GAAEtB,MAAM,CAACvC,eAAe,CAACiE,SAAS,CAAE,EAAC,EAAEf,SAAQ,GAAI,IAAI,EAAEC,UAAS,GAAI,IAAI,CAAC;QAGzFpD,YAAY,CAACQ,KAAI,GAAI,IAAG;MAE1B,EAAE,OAAO2D,KAAK,EAAE;QACdzC,OAAO,CAACyC,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAChC;IACF,CAAC;;IAED;IACA/E,SAAS,CAAC,MAAM;MACdD,QAAQ,CAAC,MAAM;QACbmB,kBAAkB,EAAE,EAAG;QACvBQ,aAAa,EAAE,EAAG;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACAxB,KAAK,CAACa,eAAe,EAAE,MAAM;MAC3BG,kBAAkB,EAAE,EAAG;MACvBQ,aAAa,EAAE,EAAG;IACpB,CAAC,CAAC;;IAEF;IACA,MAAMsD,mBAAkB,GAAIA,CAAA,KAAM;MAChC,MAAMrD,MAAK,GAAIb,iBAAiB,CAACM,KAAK;MACtC,MAAM6D,QAAO,GAAItD,MAAM,CAACuD,SAAS,CAAC,WAAW,CAAC,EAAE;MAChD,MAAMC,IAAG,GAAIC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAG,GAAIL,QAAQ;MACpBE,IAAI,CAAC1E,QAAO,GAAI,iBAAiB,EAAE;MACnC0E,IAAI,CAACI,KAAK,EAAE;IACd,CAAC;IAED,OAAO;MACL3E,YAAY;MACZG,eAAe;MACfD,iBAAiB;MACjBkE,mBAAmB;MACnBnE;IACF,CAAC;EACH;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}