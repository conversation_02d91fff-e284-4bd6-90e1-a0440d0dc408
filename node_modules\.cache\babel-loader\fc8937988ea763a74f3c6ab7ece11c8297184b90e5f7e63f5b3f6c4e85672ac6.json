{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport router from \"@/router\";\nimport TinyMce from \"@/components/Tinymce\";\nimport WangEditor from \"@/components/WangEditor/index.vue\";\nimport Upload from \"@/components/Uplaod\";\nimport { ref } from \"vue\";\nimport { useRoute } from \"vue-router\";\nimport { VueDraggableNext } from \"vue-draggable-next\";\nimport { success, confirm, error } from \"@/util/tipsUtils\";\nimport { findCategoryList, toTree, getAllParent } from \"@/api/learn/category\";\nimport { saveBaseInfo, updateBaseInfo, getBaseInfo, publishLesson, unPublishLesson, saveLessonChapter, updateLessonChapter, deleteLessonChapter, getLessonChapterList, updateSortOrder, saveLessonChapterSection, updateLessonChapterSection, deleteLessonChapterSection, saveHomework, updateHomework, getHomework } from \"@/api/learn/lesson\";\nexport default {\n  name: \"LearnLessonEdit\",\n  components: {\n    Upload,\n    WangEditor,\n    draggable: VueDraggableNext\n  },\n  setup() {\n    const route = useRoute();\n    let isUpdate = !!route.query.id;\n    let showStep = ref(\"\");\n    const steps = [{\n      key: \"base\",\n      name: \"课程信息\"\n    }, {\n      key: \"content\",\n      name: \"内容章节\"\n    }, {\n      key: \"homework\",\n      name: \"课后作业\"\n    }, {\n      key: \"publish\",\n      name: \"发布状态\"\n    }];\n    const stepActive = ref(0);\n    const loadStepActiveArray = () => {\n      const stepActiveArray = [];\n      for (let i = 0; i < steps.length; i++) {\n        const step = steps[i];\n        stepActiveArray.push(step.key);\n        if (step.key === showStep.value) {\n          stepActive.value = i;\n          break;\n        }\n      }\n      if (isUpdate) {\n        stepActive.value = steps.length;\n      }\n      return stepActiveArray;\n    };\n    // 基本信息\n    const uploadData = ref({\n      url: process.env.VUE_APP_BASE_API + \"/oss/learn/lesson/image\",\n      files: []\n    });\n    const categoryOptions = ref([]);\n    const selectCidList = ref([]);\n    const lesson = ref({\n      id: \"\",\n      name: \"\",\n      startTime: \"\",\n      endTime: \"\",\n      price: 0,\n      originalPrice: 0,\n      image: \"\",\n      cidList: [],\n      phrase: \"\",\n      introduction: \"\"\n    });\n    const lessonRules = {\n      name: [{\n        required: true,\n        message: \"请输入标题\",\n        trigger: \"blur\"\n      }],\n      startTime: [{\n        required: true,\n        message: \"请选择时间\",\n        trigger: \"change\"\n      }],\n      endTime: [{\n        required: true,\n        message: \"请选择时间\",\n        trigger: \"change\"\n      }],\n      phrase: [{\n        required: true,\n        message: \"请输入简介\",\n        trigger: \"blur\"\n      }],\n      price: [{\n        required: true,\n        message: \"请输入价格\",\n        trigger: \"blur\"\n      }],\n      cidList: [{\n        required: true,\n        message: \"请选择分类\",\n        trigger: \"change\"\n      }],\n      introduction: [{\n        required: true,\n        message: \"请输入描述\",\n        trigger: \"blur\"\n      }],\n      image: [{\n        required: true,\n        message: \"请选择海报\",\n        trigger: \"change\"\n      }]\n    };\n    // 加载基本信息\n    const loadBaseInfo = () => {\n      let id = route.query.id;\n      if (!id) {\n        return;\n      }\n      getBaseInfo(id, function (res) {\n        lesson.value = res;\n        selectCidList.value = getAllParent(categoryOptions.value, res.cidList);\n        lesson.value.cidList = [];\n        uploadData.value.files = [{\n          name: \"海报\",\n          url: lesson.value.image\n        }];\n        for (const valElement of selectCidList.value) {\n          lesson.value.cidList.push(valElement[valElement.length - 1]);\n        }\n      });\n    };\n    // 获取分类\n    const loadCategory = () => {\n      findCategoryList(0, true, res => {\n        if (res && res.length) {\n          categoryOptions.value = toTree(res);\n          loadBaseInfo();\n        }\n      });\n    };\n    // 选择分类\n    const changeCategory = val => {\n      lesson.value.cidList = [];\n      for (const valElement of val) {\n        lesson.value.cidList.push(valElement[valElement.length - 1]);\n      }\n    };\n    // 选择时间\n    const changeStartTime = val => {\n      lesson.value.startTime = val;\n    };\n    // 选择时间\n    const changeEndTime = val => {\n      lesson.value.endTime = val;\n    };\n    // 上传图片成功\n    const onUploadImageSuccess = res => {\n      lesson.value.image = res.data;\n    };\n    // 删除图片\n    const onUploadImageRemove = () => {\n      lesson.value.image = \"\";\n      uploadData.value.files = [];\n    };\n    // 提交基本信息\n    const lessonRef = ref(null);\n    const submitBaseInfo = () => {\n      lessonRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (isUpdate) {\n          if (typeof lesson.value.startTime == \"string\") {\n            lesson.value.startTime = new Date(lesson.value.startTime);\n          }\n          if (typeof lesson.value.endTime == \"string\") {\n            lesson.value.endTime = new Date(lesson.value.endTime);\n          }\n          updateBaseInfo(lesson.value, function (res) {\n            if (res && res.id) {\n              lesson.value = res;\n              success(\"编辑成功\");\n              showStep.value = \"content\";\n              loadStepActiveArray();\n              let path = route.fullPath;\n              router.push({\n                path,\n                query: {\n                  id: lesson.value.id,\n                  step: \"content\"\n                }\n              });\n            }\n          });\n        } else {\n          saveBaseInfo(lesson.value, function (res) {\n            if (res && res.id) {\n              lesson.value = res;\n              success(\"新增成功\");\n              showStep.value = \"content\";\n              loadStepActiveArray();\n              let path = route.fullPath;\n              router.push({\n                path,\n                query: {\n                  id: lesson.value.id,\n                  step: \"content\"\n                }\n              });\n            }\n          });\n        }\n      });\n    };\n\n    // 内容\n    const contentList = ref([]);\n    const showChapterDialog = ref(false);\n    const lessonChapter = ref({\n      id: \"\",\n      lessonId: \"\",\n      title: \"\",\n      phrase: \"\"\n    });\n    const lessonChapterRules = {\n      title: [{\n        required: true,\n        message: \"请输入标题\",\n        trigger: \"blur\"\n      }]\n    };\n    const showChapterSectionDialog = ref(false);\n    const lessonChapterSection = ref({\n      id: \"\",\n      lessonChapterId: \"\",\n      type: \"link\",\n      title: \"\",\n      url: \"\",\n      phrase: \"\",\n      totalTime: 0\n    });\n    const lessonChapterSectionRules = ref({\n      title: [{\n        required: true,\n        message: \"请输入标题\",\n        trigger: \"blur\"\n      }],\n      url: [{\n        required: true,\n        message: \"请输入视频地址\",\n        trigger: \"blur\"\n      }],\n      type: [{\n        required: true,\n        message: \"请选择类型\",\n        trigger: \"change\"\n      }]\n    });\n    const homework = ref({\n      lessonId: \"\",\n      content: \"\",\n      attachment: \"\"\n    });\n    const uploadHomeworkData = ref({\n      url: process.env.VUE_APP_BASE_API + \"/oss/learn/homework/file\",\n      files: []\n    });\n    const loadContent = () => {\n      let id = route.query.id;\n      if (!id) {\n        return;\n      }\n      getLessonChapterList({\n        lessonId: id\n      }, res => {\n        if (res && res.list) {\n          contentList.value = res.list;\n        }\n      });\n      getHomework({\n        lessonId: route.query.id\n      }, res => {\n        homework.value = res;\n        if (homework.value.url) {\n          uploadHomeworkData.value.files = [{\n            name: \"作业附件\",\n            url: homework.value.url\n          }];\n        }\n      });\n    };\n    const showChapter = chapter => {\n      showChapterDialog.value = true;\n      if (chapter && chapter.id) {\n        lessonChapter.value = chapter;\n      } else {\n        lessonChapter.value = {\n          lessonId: lesson.value.id,\n          id: \"\",\n          title: \"\",\n          phrase: \"\"\n        };\n      }\n    };\n    const hideChapter = () => {\n      showChapterDialog.value = false;\n      lessonChapter.value = {\n        id: \"\",\n        lessonId: \"\",\n        title: \"\",\n        phrase: \"\"\n      };\n    };\n    const uploadVideoData = ref({\n      url: process.env.VUE_APP_BASE_API + \"/oss/learn/lesson/video\",\n      files: []\n    });\n    let videoLoaded = false;\n    const showChapterSection = (lessonChapterId, chapterSection) => {\n      showChapterSectionDialog.value = true;\n      if (chapterSection && chapterSection.id) {\n        lessonChapterSection.value = chapterSection;\n        uploadVideoData.value.files = [{\n          name: lessonChapterSection.value.title + \".mp4\",\n          url: lessonChapterSection.value.url\n        }];\n      } else {\n        videoLoaded = false;\n        lessonChapterSection.value = {\n          lessonChapterId: lessonChapterId,\n          id: \"\",\n          title: \"\",\n          url: \"\",\n          phrase: \"\",\n          type: \"link\",\n          totalTime: 0\n        };\n      }\n    };\n    const hideChapterSection = () => {\n      videoLoaded = false;\n      showChapterSectionDialog.value = false;\n      lessonChapterSection.value = {\n        id: \"\",\n        lessonChapterId: \"\",\n        title: \"\",\n        url: \"\",\n        phrase: \"\",\n        type: \"link\",\n        totalTime: 0\n      };\n    };\n    const deleteChapter = id => {\n      confirm(\"确认删除吗？\", \"提示\", () => {\n        deleteLessonChapter({\n          id: id\n        }, () => {\n          success(\"删除成功\");\n          loadContent();\n        });\n      });\n    };\n    const deleteChapterSection = id => {\n      confirm(\"确认删除吗？\", \"提示\", () => {\n        deleteLessonChapterSection({\n          id: id\n        }, () => {\n          success(\"删除成功\");\n          loadContent();\n        });\n      });\n    };\n    const lessonChapterRef = ref(null);\n    const submitChapter = () => {\n      lessonChapterRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (lessonChapter.value.id) {\n          updateLessonChapter(lessonChapter.value, function () {\n            success(\"编辑成功\");\n            hideChapter();\n            loadContent();\n          });\n        } else {\n          saveLessonChapter(lessonChapter.value, function () {\n            success(\"新增成功\");\n            hideChapter();\n            loadContent();\n            stepActive.value = steps.length;\n            isUpdate = true;\n          });\n        }\n      });\n    };\n    const linkVideo = ref(null);\n    const urlBlur = () => {\n      if (lessonChapterSection.value.type === \"link\") {\n        linkVideo.value.addEventListener(\"loadedmetadata\", () => {\n          //时长为秒，小数，182.36\n          lessonChapterSection.value.totalTime = linkVideo.value.duration;\n          videoLoaded = true;\n        });\n      }\n    };\n    const lessonChapterSectionRef = ref(null);\n    const submitChapterSection = () => {\n      if (lessonChapterSection.value.type === \"link\") {\n        if (!lessonChapterSection.value.id && !videoLoaded) {\n          error(\"正在计算视频时长，请稍后再试\");\n        }\n      }\n      lessonChapterSectionRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (lessonChapterSection.value.id) {\n          updateLessonChapterSection(lessonChapterSection.value, function () {\n            success(\"编辑成功\");\n            hideChapterSection();\n            loadContent();\n          });\n        } else {\n          saveLessonChapterSection(lessonChapterSection.value, function () {\n            success(\"新增成功\");\n            hideChapterSection();\n            loadContent();\n          });\n        }\n      });\n    };\n    // 上传视频成功\n    const onUploadVideoSuccess = res => {\n      lessonChapterSection.value.url = res.data;\n      uploadVideoData.value.files = [{\n        name: lessonChapterSection.value.title + \".mp4\",\n        url: res.data\n      }];\n    };\n    // 删除视频\n    const onUploadVideoRemove = () => {\n      lessonChapterSection.value.url = \"\";\n      uploadVideoData.value.files = [];\n    };\n    const onBeforeUploadVideo = file => {\n      let videoUrl = URL.createObjectURL(file);\n      let audioElement = new Audio(videoUrl);\n      audioElement.addEventListener(\"loadedmetadata\", () => {\n        //时长为秒，小数，182.36\n        lessonChapterSection.value.totalTime = audioElement.duration;\n      });\n    };\n    // 拖拽事件\n    const onDraggableChange = () => {\n      console.log(contentList.value);\n      const chapterList = [];\n      for (const content of contentList.value) {\n        const subData = [];\n        if (content.chapterSectionList && content.chapterSectionList.length) {\n          for (const sub of content.chapterSectionList) {\n            subData.push({\n              id: sub.id,\n              list: []\n            });\n          }\n        }\n        chapterList.push({\n          id: content.id,\n          list: subData\n        });\n      }\n      const params = {\n        id: lesson.value.id,\n        list: chapterList\n      };\n      updateSortOrder(params, () => {\n        success(\"排序更新成功\");\n      });\n      console.log(params);\n    };\n    // 作业\n    const homeworkRef = ref(null);\n    const homeworkRules = ref({\n      content: [{\n        required: true,\n        message: \"请输入作业内容\",\n        trigger: \"blur\"\n      }]\n    });\n    // 上传附件成功\n    const onUploadHomeworkAttachmentSuccess = res => {\n      homework.value.attachment = res.data;\n    };\n    // 删除附件成功\n    const onUploadHomeworkAttachmentRemove = () => {\n      homework.value.attachment = \"\";\n      uploadHomeworkData.value.files = [];\n    };\n    const submitHomework = () => {\n      homework.value.lessonId = route.query.id || lesson.value.id;\n      homeworkRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (homework.value.id) {\n          updateHomework(homework.value, () => {\n            success(\"编辑成功\");\n            showStep.value = \"publish\";\n            let path = route.fullPath;\n            router.push({\n              path,\n              query: {\n                id: lesson.value.id,\n                step: \"publish\"\n              }\n            });\n          });\n        } else {\n          saveHomework(homework.value, res => {\n            homework.value = res;\n            success(\"编辑成功\");\n            showStep.value = \"publish\";\n            let path = route.fullPath;\n            router.push({\n              path,\n              query: {\n                id: lesson.value.id,\n                step: \"publish\"\n              }\n            });\n          });\n        }\n      });\n    };\n    // 发布页面\n    const statusMap = {\n      unpublished: \"草稿箱\",\n      published: \"已发布\",\n      deleted: \"已删除\"\n    };\n    const publish = () => {\n      publishLesson({\n        id: lesson.value.id\n      }, () => {\n        success(\"发布成功\");\n        lesson.value.status = \"published\";\n      });\n    };\n    const unPublish = () => {\n      unPublishLesson({\n        id: lesson.value.id\n      }, () => {\n        success(\"取消发布成功\");\n        lesson.value.status = \"unpublished\";\n      });\n    };\n    // 步骤条\n    const init = () => {\n      // 初始化加载\n      if (route.query.step) {\n        showStep.value = route.query.step;\n      } else {\n        showStep.value = \"base\";\n      }\n      lesson.value.id = route.query.id || \"\";\n      loadCategory();\n      loadContent();\n    };\n    init();\n    // 步骤条点击切换\n    const stepClick = key => {\n      if (!isUpdate && loadStepActiveArray().indexOf(key) < 0) {\n        return;\n      }\n      showStep.value = key;\n      let path = route.fullPath;\n      router.push({\n        path,\n        query: {\n          id: lesson.value.id,\n          step: key\n        }\n      });\n    };\n    loadStepActiveArray();\n    // 返回参数与方法\n    return {\n      // 基本信息\n      uploadData,\n      categoryOptions,\n      lesson,\n      selectCidList,\n      lessonRules,\n      lessonRef,\n      changeCategory,\n      changeStartTime,\n      changeEndTime,\n      onUploadImageSuccess,\n      onUploadImageRemove,\n      submitBaseInfo,\n      // 内容列表\n      contentList,\n      showChapterDialog,\n      lessonChapter,\n      lessonChapterRules,\n      showChapterSectionDialog,\n      lessonChapterSection,\n      lessonChapterSectionRules,\n      lessonChapterRef,\n      lessonChapterSectionRef,\n      showChapter,\n      hideChapter,\n      showChapterSection,\n      hideChapterSection,\n      deleteChapter,\n      deleteChapterSection,\n      submitChapter,\n      submitChapterSection,\n      uploadVideoData,\n      linkVideo,\n      urlBlur,\n      onBeforeUploadVideo,\n      onUploadVideoSuccess,\n      onUploadVideoRemove,\n      onDraggableChange,\n      // 作业\n      homework,\n      homeworkRef,\n      homeworkRules,\n      uploadHomeworkData,\n      submitHomework,\n      onUploadHomeworkAttachmentSuccess,\n      onUploadHomeworkAttachmentRemove,\n      // 发布页面\n      statusMap,\n      publish,\n      unPublish,\n      // 步骤条\n      steps,\n      stepActive,\n      showStep,\n      stepClick\n    };\n  }\n};", "map": {"version": 3, "names": ["router", "TinyMce", "WangEditor", "Upload", "ref", "useRoute", "VueDraggableNext", "success", "confirm", "error", "findCategoryList", "toTree", "getAllParent", "saveBaseInfo", "updateBaseInfo", "getBaseInfo", "<PERSON><PERSON><PERSON><PERSON>", "unPublish<PERSON><PERSON><PERSON>", "saveLessonChapter", "updateLessonChapter", "delete<PERSON>esson<PERSON>hapter", "getLessonChapterList", "updateSortOrder", "saveLessonChapterSection", "updateLessonChapterSection", "deleteLessonChapterSection", "saveHomework", "updateHomework", "getHomework", "name", "components", "draggable", "setup", "route", "isUpdate", "query", "id", "showStep", "steps", "key", "stepActive", "loadStepActiveArray", "stepActiveArray", "i", "length", "step", "push", "value", "uploadData", "url", "process", "env", "VUE_APP_BASE_API", "files", "categoryOptions", "selectCidList", "lesson", "startTime", "endTime", "price", "originalPrice", "image", "cidList", "phrase", "introduction", "lessonRules", "required", "message", "trigger", "loadBaseInfo", "res", "valElement", "loadCategory", "changeCategory", "val", "changeStartTime", "changeEndTime", "onUploadImageSuccess", "data", "onUploadImageRemove", "lessonRef", "submitBaseInfo", "validate", "valid", "Date", "path", "fullPath", "contentList", "showChapterDialog", "lessonChapter", "lessonId", "title", "lessonChapterRules", "showChapterSectionDialog", "lessonChapterSection", "lessonChapterId", "type", "totalTime", "lessonChapterSectionRules", "homework", "content", "attachment", "uploadHomeworkData", "loadContent", "list", "showChapter", "chapter", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadVideoData", "videoLoaded", "showChapterSection", "chapterSection", "hideChapterSection", "deleteChapter", "deleteChapterSection", "lessonChapterRef", "submitChapter", "linkVideo", "url<PERSON>lur", "addEventListener", "duration", "lessonChapterSectionRef", "submitChapterSection", "onUploadVideoSuccess", "onUploadVideoRemove", "onBeforeUploadVideo", "file", "videoUrl", "URL", "createObjectURL", "audioElement", "Audio", "onDraggableChange", "console", "log", "chapterList", "subData", "chapterSectionList", "sub", "params", "homeworkRef", "homeworkRules", "onUploadHomeworkAttachmentSuccess", "onUploadHomeworkAttachmentRemove", "submitHomework", "statusMap", "unpublished", "published", "deleted", "publish", "status", "unPublish", "init", "step<PERSON>lick", "indexOf"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/learn/lesson/edit/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row>\n      <el-col :span=\"20\">\n        <div v-if=\"showStep === 'base'\" class=\"base\">\n          <el-form :model=\"lesson\" :rules=\"lessonRules\" ref=\"lessonRef\" label-width=\"120px\">\n            <el-form-item label=\"名称：\" prop=\"name\">\n              <el-input size=\"mini\" v-model=\"lesson.name\" placeholder=\"请输入标题\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"开始时间：\" prop=\"startTime\">\n              <el-date-picker\n                v-model=\"lesson.startTime\"\n                type=\"datetime\"\n                placeholder=\"选择开始时间\"\n                class=\"input-text\"\n                :default-time=\"new Date(2000, 0, 1, 0, 0, 0)\"\n                size=\"mini\"\n                @change=\"changeStartTime\"\n                style=\"width: 100%;\"></el-date-picker>\n            </el-form-item>\n            <el-form-item label=\"结束时间：\" prop=\"endTime\">\n              <el-date-picker\n                v-model=\"lesson.endTime\"\n                type=\"datetime\"\n                placeholder=\"选择结束时间\"\n                class=\"input-text\"\n                :default-time=\"new Date(2000, 0, 1, 22, 0, 0)\"\n                size=\"mini\"\n                @change=\"changeEndTime\"\n                style=\"width: 100%;\"></el-date-picker>\n            </el-form-item>\n            <el-form-item label=\"分类：\" prop=\"cidList\">\n              <el-cascader style=\"width: 100%;\"\n                           size=\"mini\"\n                           v-model=\"selectCidList\"\n                           :props=\"{ multiple: true, checkStrictly: true }\"\n                           :options=\"categoryOptions\"\n                           @change=\"changeCategory\">\n              </el-cascader>\n            </el-form-item>\n            <el-form-item label=\"简介：\" prop=\"phrase\">\n              <el-input size=\"mini\" v-model=\"lesson.phrase\" placeholder=\"请输入简介\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"价格：\" prop=\"price\">\n              <el-input-number class=\"input-number\" v-model=\"lesson.price\" placeholder=\"请输入价格\" :precision=\"2\" :step=\"1\" :min=\"0\"></el-input-number>\n              <el-input-number class=\"input-number\" v-model=\"lesson.originalPrice\" placeholder=\"请输入原价\" :precision=\"2\" :step=\"1\" :min=\"0\"></el-input-number>\n            </el-form-item>\n            <el-form-item label=\"海报：\" prop=\"image\">\n              <upload\n                :class=\"{'no-plus': lesson.image}\"\n                :on-upload-success=\"onUploadImageSuccess\"\n                :on-upload-remove=\"onUploadImageRemove\"\n                :files=\"uploadData.files\"\n                :upload-url=\"uploadData.url\"\n                :limit=\"1\"\n                accept=\"image/jpeg,image/gif,image/png\">\n              </upload>\n              <span class=\"upload-image-tips\">图片建议：尺寸 1920 x 1200 像素，大小7M以下</span>\n            </el-form-item>\n            <el-form-item label=\"详情描述：\" prop=\"introduction\">\n              <tiny-mce :height=\"300\" v-model=\"lesson.introduction\"></tiny-mce>\n            </el-form-item>\n            <div style=\"margin:50px auto;text-align: center;\">\n              <el-button size=\"mini\" @click=\"stepClick('content')\" v-if=\"lesson.id\">下一步</el-button>\n              <el-button size=\"mini\" @click=\"submitBaseInfo\">提交</el-button>\n            </div>\n          </el-form>\n        </div>\n        <div v-if=\"showStep === 'content'\" class=\"content\">\n          <div class=\"content-header\">\n            <el-button size=\"mini\" @click=\"stepClick('base')\">上一步</el-button>\n            <el-button size=\"mini\" @click=\"stepClick('homework')\">下一步</el-button>\n            <el-button size=\"mini\" @click=\"showChapter\">新增章节</el-button>\n          </div>\n          <div style=\"margin-top: 20px;\">\n            <el-table default-expand-all :data=\"contentList\" :show-header=\"false\" :highlight-current-row=\"true\" style=\"width: 100%\">\n              <el-table-column type=\"expand\">\n                <template #default=\"props\">\n                  <div class=\"tips\">{{props.row.phrase}}</div>\n                  <el-card class=\"box-card\" v-for=\"section in props.row.chapterSectionList\" :key=\"section.title\">\n                    <template #header>\n                      <div class=\"clearfix\" style=\"line-height: 28px;\">\n                        <span>{{section.title}}</span>\n                        <span style=\"float: right;\">\n                          <el-button type=\"text\" size=\"mini\" @click=\"section.isPreview = !section.isPreview\">预览</el-button>\n                          <el-button type=\"text\" size=\"mini\" @click=\"showChapterSection(props.row.id, section)\">修改</el-button>\n                          <el-button type=\"text\" size=\"mini\" @click=\"deleteChapterSection(section.id)\">删除</el-button>\n                        </span>\n                      </div>\n                    </template>\n                    <div class=\"table-wrapper\" :class=\"{'show': section.isPreview}\">\n                      <div class=\"tips\">{{section.phrase}}</div>\n                      <div class=\"video-box\">\n                        <video :src=\"section.url\" controls=\"controls\" :style=\"{'margin-top:20px;': !!section.phrase}\"></video>\n                      </div>\n                    </div>\n                  </el-card>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"title\" label=\"标题\"></el-table-column>\n              <el-table-column label=\"操作\">\n                <template #default=\"r\">\n                  <span style=\"float: right;\">\n                    <el-button type=\"text\" @click=\"showChapterSection(r.row.id)\" size=\"mini\">新增章节内容</el-button>\n                    <el-button type=\"text\" @click=\"showChapter(r.row)\" size=\"mini\">修改</el-button>\n                    <el-button type=\"text\" @click=\"deleteChapter(r.row.id)\" size=\"mini\">删除</el-button>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n        </div>\n        <div v-if=\"showStep === 'homework'\" class=\"homework\">\n          <el-form :model=\"homework\" :rules=\"homeworkRules\" ref=\"homeworkRef\" label-width=\"120px\">\n            <el-form-item label=\"作业内容：\" prop=\"content\">\n              <el-input size=\"mini\" type=\"textarea\" v-model=\"homework.content\" :rows=\"20\" placeholder=\"请输入作业内容\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"作业附件：\">\n              <upload\n                list-type=\"text\"\n                :on-upload-success=\"onUploadHomeworkAttachmentSuccess\"\n                :on-upload-remove=\"onUploadHomeworkAttachmentRemove\"\n                :files=\"uploadHomeworkData.files\"\n                :upload-url=\"uploadHomeworkData.url\"\n                :limit=\"1\"\n                accept=\"image/*,video/*,audio/*,application/*\">\n              </upload>\n            </el-form-item>\n            <div style=\"margin:50px auto;text-align: center;\">\n              <el-button size=\"mini\" @click=\"stepClick('content')\">上一步</el-button>\n              <el-button size=\"mini\" @click=\"stepClick('publish')\">下一步</el-button>\n              <el-button size=\"mini\" @click=\"submitHomework\">提交</el-button>\n            </div>\n          </el-form>\n        </div>\n        <div v-if=\"showStep === 'publish'\" class=\"publish\">\n          <div class=\"publish-box\">\n            <div class=\"current-status\">\n              <el-alert :title=\"statusMap[lesson.status]\" effect=\"dark\" type=\"success\" :closable=\"false\" show-icon v-if=\"lesson.status === 'published'\"></el-alert>\n              <el-alert :title=\"statusMap[lesson.status]\" effect=\"dark\" type=\"warning\" :closable=\"false\" show-icon v-else-if=\"lesson.status === 'unpublished'\"> </el-alert>\n              <el-alert :title=\"statusMap[lesson.status]\" effect=\"dark\" type=\"error\" :closable=\"false\" show-icon v-else> </el-alert>\n            </div>\n            <div class=\"btn-list\">\n              <el-button size=\"mini\" @click=\"stepClick('homework')\">上一步</el-button>\n              <el-button size=\"mini\" @click=\"publish\" v-if=\"lesson.status === 'unpublished'\">马上发布</el-button>\n              <el-button size=\"mini\" @click=\"unPublish\" v-if=\"lesson.status === 'published'\">移入草稿</el-button>\n            </div>\n          </div>\n        </div>\n      </el-col>\n      <el-col :span=\"4\" style=\"position: relative;\">\n        <el-affix :offset=\"160\" class=\"affix\">\n          <div class=\"step-list\">\n            <div class=\"title\">\n              步骤导航\n            </div>\n            <el-steps class=\"steps\" finish-status=\"success\" direction=\"vertical\" :active=\"stepActive\">\n              <el-step v-for=\"(step) in steps\" :key=\"step.key\" @click=\"stepClick(step.key)\" :class=\"{'step-active': showStep === step.key}\" :title=\"step.name\"></el-step>\n            </el-steps>\n          </div>\n          <div class=\"draggable\" v-if=\"showStep === 'content'\">\n            <div class=\"title\">\n              章节目录（拖动排序）\n            </div>\n            <draggable class=\"item-list\" v-model=\"contentList\" chosen-class=\"chosen\" force-fallback=\"true\" group=\"item\" animation=\"1000\" @change=\"onDraggableChange\">\n              <transition-group>\n                <div class=\"item\" v-for=\"item in contentList\" :key=\"item.id\">\n                  <div class=\"item-title\">{{item.title}}</div>\n                  <div class=\"sub-item-list\" v-if=\"item.chapterSectionList && item.chapterSectionList.length\">\n                    <draggable v-model=\"item.chapterSectionList\" chosen-class=\"chosen\" force-fallback=\"true\" group=\"sub-item\" animation=\"1000\" @change=\"onDraggableChange\">\n                      <div class=\"sub-item\" v-for=\"subItem in item.chapterSectionList\" :key=\"subItem.id\">{{subItem.title}}</div>\n                    </draggable>\n                  </div>\n                </div>\n              </transition-group>\n            </draggable>\n          </div>\n        </el-affix>\n      </el-col>\n    </el-row>\n    <el-dialog title=\"编辑章节\" v-model=\"showChapterDialog\" :before-close=\"hideChapter\">\n      <el-form :model=\"lessonChapter\" :rules=\"lessonChapterRules\" ref=\"lessonChapterRef\">\n        <el-form-item label=\"标题：\" label-width=\"120px\" prop=\"title\">\n          <el-input size=\"mini\" v-model=\"lessonChapter.title\" placeholder=\"请输入标题\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"简介：\" label-width=\"120px\" prop=\"phrase\">\n          <el-input size=\"mini\" v-model=\"lessonChapter.phrase\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入简介\"></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button size=\"mini\" @click=\"hideChapter\">取 消</el-button>\n          <el-button size=\"mini\" type=\"primary\" @click=\"submitChapter\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n    <el-dialog title=\"编辑章节内容\" v-model=\"showChapterSectionDialog\" :before-close=\"hideChapterSection\">\n      <el-form :model=\"lessonChapterSection\" :rules=\"lessonChapterSectionRules\" ref=\"lessonChapterSectionRef\">\n        <el-form-item label=\"标题：\" label-width=\"120px\" prop=\"title\">\n          <el-input size=\"mini\" v-model=\"lessonChapterSection.title\" placeholder=\"请输入标题\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"视频方式：\" label-width=\"120px\" prop=\"type\">\n          <el-radio v-model=\"lessonChapterSection.type\" label=\"link\">视频链接</el-radio>\n          <el-radio v-model=\"lessonChapterSection.type\" label=\"upload\">视频上传</el-radio>\n        </el-form-item>\n        <el-form-item label=\"视频链接：\" label-width=\"120px\" prop=\"url\" v-if=\"lessonChapterSection.type === 'link'\">\n          <el-input size=\"mini\" @blur=\"urlBlur\" v-model=\"lessonChapterSection.url\" placeholder=\"请输入视频地址\" autocomplete=\"off\"></el-input>\n          <video ref=\"linkVideo\" style=\"display: none;\" :src=\"lessonChapterSection.url\"></video>\n        </el-form-item>\n        <el-form-item label=\"视频上传：\" label-width=\"120px\" prop=\"url\" v-else>\n          <upload\n            :on-before-upload=\"onBeforeUploadVideo\"\n            :on-upload-success=\"onUploadVideoSuccess\"\n            :on-upload-remove=\"onUploadVideoRemove\"\n            :files=\"uploadVideoData.files\"\n            :upload-url=\"uploadVideoData.url\"\n            :limit=\"1\"\n            listType=\"text\"\n            accept=\"audio/mp4,video/mp4\">\n          </upload>\n        </el-form-item>\n        <el-form-item label=\"简介：\" label-width=\"120px\" prop=\"phrase\">\n          <el-input size=\"mini\" v-model=\"lessonChapterSection.phrase\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入简介\"></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button size=\"mini\" @click=\"hideChapterSection\">取 消</el-button>\n          <el-button size=\"mini\" type=\"primary\" @click=\"submitChapterSection\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport router from \"@/router\"\nimport TinyMce from \"@/components/Tinymce\"\nimport WangEditor from \"@/components/WangEditor/index.vue\"\nimport Upload from \"@/components/Uplaod\"\nimport {ref} from \"vue\"\nimport {useRoute} from \"vue-router\"\nimport {VueDraggableNext} from \"vue-draggable-next\"\nimport {success, confirm, error} from \"@/util/tipsUtils\"\nimport {findCategoryList, toTree, getAllParent} from \"@/api/learn/category\"\nimport {saveBaseInfo, updateBaseInfo, getBaseInfo, publishLesson, unPublishLesson,\n    saveLessonChapter, updateLessonChapter, deleteLessonChapter, getLessonChapterList, updateSortOrder,\n    saveLessonChapterSection, updateLessonChapterSection, deleteLessonChapterSection, saveHomework, updateHomework, getHomework} from \"@/api/learn/lesson\"\n\n  export default {\n  name: \"LearnLessonEdit\",\n    components:{\n      Upload,\n      WangEditor,\n      draggable: VueDraggableNext\n    },\n    setup() {\n      const route = useRoute()\n      let isUpdate = !!route.query.id\n      let showStep = ref(\"\")\n      const steps = [\n        {key: \"base\", name: \"课程信息\"},\n        {key: \"content\", name: \"内容章节\"},\n        {key: \"homework\", name: \"课后作业\"},\n        {key: \"publish\", name: \"发布状态\"},\n      ]\n      const stepActive = ref(0)\n      const loadStepActiveArray = () => {\n        const stepActiveArray = [];\n        for (let i = 0; i < steps.length; i++) {\n          const step = steps[i];\n          stepActiveArray.push(step.key);\n          if (step.key === showStep.value) {\n            stepActive.value = i;\n            break;\n          }\n        }\n        if (isUpdate) {\n          stepActive.value = steps.length;\n        }\n        return stepActiveArray;\n      }\n      // 基本信息\n      const uploadData = ref({\n        url: process.env.VUE_APP_BASE_API + \"/oss/learn/lesson/image\",\n        files: []\n      })\n      const categoryOptions = ref([])\n      const selectCidList = ref([])\n      const lesson = ref({\n        id: \"\",\n        name: \"\",\n        startTime: \"\",\n        endTime: \"\",\n        price: 0,\n        originalPrice: 0,\n        image: \"\",\n        cidList: [],\n        phrase: \"\",\n        introduction: \"\"\n      })\n      const lessonRules = {\n        name: [{ required: true, message: \"请输入标题\", trigger: \"blur\" }],\n        startTime: [{ required: true, message: \"请选择时间\", trigger: \"change\" }],\n        endTime: [{ required: true, message: \"请选择时间\", trigger: \"change\" }],\n        phrase: [{ required: true, message: \"请输入简介\", trigger: \"blur\" }],\n        price: [{ required: true, message: \"请输入价格\", trigger: \"blur\" }],\n        cidList: [{ required: true, message: \"请选择分类\", trigger: \"change\" }],\n        introduction: [{ required: true, message: \"请输入描述\", trigger: \"blur\" }],\n        image: [{ required: true, message: \"请选择海报\", trigger: \"change\" }],\n      }\n      // 加载基本信息\n      const loadBaseInfo = () => {\n        let id = route.query.id;\n        if (!id) { return; }\n        getBaseInfo(id, function (res) {\n          lesson.value = res;\n          selectCidList.value = getAllParent(categoryOptions.value, res.cidList);\n          lesson.value.cidList = []\n          uploadData.value.files = [\n            {\n              name: \"海报\",\n              url: lesson.value.image\n            }\n          ]\n          for (const valElement of selectCidList.value) {\n            lesson.value.cidList.push(valElement[valElement.length - 1])\n          }\n        })\n      }\n      // 获取分类\n      const loadCategory = () => {\n        findCategoryList(0, true, (res) => {\n          if (res && res.length) {\n            categoryOptions.value = toTree(res);\n            loadBaseInfo();\n          }\n        })\n      }\n      // 选择分类\n      const changeCategory = (val) => {\n        lesson.value.cidList = []\n        for (const valElement of val) {\n          lesson.value.cidList.push(valElement[valElement.length - 1])\n        }\n      }\n      // 选择时间\n      const changeStartTime = (val) => {\n        lesson.value.startTime = val\n      }\n      // 选择时间\n      const changeEndTime = (val) => {\n        lesson.value.endTime = val\n      }\n      // 上传图片成功\n      const onUploadImageSuccess = (res) => {\n        lesson.value.image = res.data\n      }\n      // 删除图片\n      const onUploadImageRemove = () => {\n        lesson.value.image = \"\"\n        uploadData.value.files = []\n      }\n      // 提交基本信息\n      const lessonRef = ref(null)\n      const submitBaseInfo = () => {\n        lessonRef.value.validate((valid) => {\n          if (!valid) { return false }\n          if (isUpdate) {\n            if(typeof lesson.value.startTime == \"string\") {\n              lesson.value.startTime = new Date(lesson.value.startTime);\n            }\n            if(typeof lesson.value.endTime == \"string\") {\n              lesson.value.endTime = new Date(lesson.value.endTime);\n            }\n            updateBaseInfo(lesson.value, function (res) {\n              if (res && res.id) {\n                lesson.value = res;\n                success(\"编辑成功\")\n                showStep.value = \"content\";\n                loadStepActiveArray()\n                let path = route.fullPath;\n                router.push({path, query: {id: lesson.value.id, step: \"content\"} });\n              }\n            })\n          } else {\n            saveBaseInfo(lesson.value, function (res) {\n              if (res && res.id) {\n                lesson.value = res;\n                success(\"新增成功\")\n                showStep.value = \"content\";\n                loadStepActiveArray()\n                let path = route.fullPath;\n                router.push({path, query: {id: lesson.value.id, step: \"content\"} });\n              }\n            })\n          }\n        })\n      }\n\n      // 内容\n      const contentList = ref([])\n      const showChapterDialog = ref(false)\n      const lessonChapter = ref({\n        id: \"\",\n        lessonId: \"\",\n        title: \"\",\n        phrase: \"\"\n      })\n      const lessonChapterRules = {\n        title: [{ required: true, message: \"请输入标题\", trigger: \"blur\" }],\n      }\n      const showChapterSectionDialog = ref(false)\n      const lessonChapterSection = ref({\n        id: \"\",\n        lessonChapterId: \"\",\n        type: \"link\",\n        title: \"\",\n        url: \"\",\n        phrase: \"\",\n        totalTime: 0\n      })\n      const lessonChapterSectionRules = ref({\n        title: [{ required: true, message: \"请输入标题\", trigger: \"blur\" }],\n        url: [{ required: true, message: \"请输入视频地址\", trigger: \"blur\" }],\n        type: [{ required: true, message: \"请选择类型\", trigger: \"change\" }]\n      })\n      const homework = ref({\n        lessonId: \"\",\n        content: \"\",\n        attachment: \"\",\n      })\n      const uploadHomeworkData = ref({\n        url: process.env.VUE_APP_BASE_API + \"/oss/learn/homework/file\",\n        files: []\n      })\n      const loadContent = () => {\n        let id = route.query.id;\n        if (!id) { return; }\n        getLessonChapterList({lessonId: id}, (res) => {\n          if (res && res.list) {\n            contentList.value = res.list;\n          }\n        })\n        getHomework({lessonId: route.query.id}, (res) => {\n          homework.value = res\n          if (homework.value.url) {\n            uploadHomeworkData.value.files = [\n              {\n                name: \"作业附件\",\n                url: homework.value.url\n              }\n            ]\n          }\n        })\n      }\n      const showChapter = (chapter) => {\n        showChapterDialog.value = true;\n        if (chapter && chapter.id) {\n          lessonChapter.value = chapter;\n        } else {\n          lessonChapter.value = {\n            lessonId: lesson.value.id,\n            id: \"\",\n            title: \"\",\n            phrase: \"\"\n          }\n        }\n      }\n      const hideChapter = () => {\n        showChapterDialog.value = false;\n        lessonChapter.value = {id: \"\", lessonId: \"\", title: \"\", phrase: \"\"}\n      }\n      const uploadVideoData = ref({\n        url: process.env.VUE_APP_BASE_API + \"/oss/learn/lesson/video\",\n        files: []\n      })\n      let videoLoaded = false;\n      const showChapterSection = (lessonChapterId, chapterSection) => {\n        showChapterSectionDialog.value = true;\n        if (chapterSection && chapterSection.id) {\n          lessonChapterSection.value = chapterSection;\n          uploadVideoData.value.files = [\n            {\n              name: lessonChapterSection.value.title + \".mp4\",\n              url: lessonChapterSection.value.url\n            }\n          ]\n        } else {\n          videoLoaded = false\n          lessonChapterSection.value = {\n            lessonChapterId: lessonChapterId,\n            id: \"\",\n            title: \"\",\n            url: \"\",\n            phrase: \"\",\n            type: \"link\",\n            totalTime: 0\n          }\n        }\n      }\n      const hideChapterSection = () => {\n        videoLoaded = false\n        showChapterSectionDialog.value = false;\n        lessonChapterSection.value = {\n          id: \"\",\n          lessonChapterId: \"\",\n          title: \"\",\n          url: \"\",\n          phrase: \"\",\n          type: \"link\",\n          totalTime: 0\n        }\n      }\n      const deleteChapter = (id) => {\n        confirm(\"确认删除吗？\", \"提示\", () => {\n          deleteLessonChapter({id: id}, () => {\n            success(\"删除成功\")\n            loadContent()\n          })\n        })\n      }\n      const deleteChapterSection = (id) => {\n        confirm(\"确认删除吗？\", \"提示\", () => {\n          deleteLessonChapterSection({id: id}, () => {\n            success(\"删除成功\")\n            loadContent()\n          })\n        })\n      }\n      const lessonChapterRef = ref(null)\n      const submitChapter = () => {\n        lessonChapterRef.value.validate((valid) => {\n          if (!valid) { return false }\n          if (lessonChapter.value.id) {\n            updateLessonChapter(lessonChapter.value, function () {\n              success(\"编辑成功\")\n              hideChapter()\n              loadContent()\n            })\n          } else {\n            saveLessonChapter(lessonChapter.value, function () {\n              success(\"新增成功\")\n              hideChapter()\n              loadContent()\n              stepActive.value = steps.length;\n              isUpdate = true;\n            })\n          }\n        })\n      }\n      const linkVideo = ref(null)\n      const urlBlur = () => {\n        if (lessonChapterSection.value.type === \"link\") {\n          linkVideo.value.addEventListener(\"loadedmetadata\", () => {\n            //时长为秒，小数，182.36\n            lessonChapterSection.value.totalTime = linkVideo.value.duration;\n            videoLoaded = true\n          });\n        }\n      }\n      const lessonChapterSectionRef = ref(null)\n      const submitChapterSection = () => {\n        if (lessonChapterSection.value.type === \"link\") {\n          if (!lessonChapterSection.value.id && !videoLoaded) {\n            error(\"正在计算视频时长，请稍后再试\");\n          }\n        }\n        lessonChapterSectionRef.value.validate((valid) => {\n          if (!valid) { return false }\n          if (lessonChapterSection.value.id) {\n            updateLessonChapterSection(lessonChapterSection.value, function () {\n              success(\"编辑成功\")\n              hideChapterSection()\n              loadContent()\n            })\n          } else {\n            saveLessonChapterSection(lessonChapterSection.value, function () {\n              success(\"新增成功\")\n              hideChapterSection()\n              loadContent()\n            })\n          }\n        })\n      }\n      // 上传视频成功\n      const onUploadVideoSuccess = (res) => {\n        lessonChapterSection.value.url = res.data\n        uploadVideoData.value.files = [\n            {\n              name: lessonChapterSection.value.title + \".mp4\",\n              url: res.data\n            }\n        ]\n      }\n      // 删除视频\n      const onUploadVideoRemove = () => {\n        lessonChapterSection.value.url = \"\"\n        uploadVideoData.value.files = []\n      }\n      const onBeforeUploadVideo = (file) => {\n        let videoUrl = URL.createObjectURL(file);\n        let audioElement = new Audio(videoUrl);\n        audioElement.addEventListener(\"loadedmetadata\", () => {\n          //时长为秒，小数，182.36\n          lessonChapterSection.value.totalTime = audioElement.duration;\n        });\n      }\n      // 拖拽事件\n      const onDraggableChange = () => {\n        console.log(contentList.value)\n        const chapterList = []\n        for (const content of contentList.value) {\n          const subData = []\n          if (content.chapterSectionList && content.chapterSectionList.length) {\n            for (const sub of content.chapterSectionList) {\n              subData.push({id: sub.id, list: []})\n            }\n          }\n          chapterList.push({id: content.id, list: subData});\n        }\n        const params = {id: lesson.value.id, list: chapterList}\n        updateSortOrder(params, () => {\n          success(\"排序更新成功\")\n        })\n        console.log(params)\n      }\n      // 作业\n      const homeworkRef = ref(null)\n      const homeworkRules = ref({\n        content: [{ required: true, message: \"请输入作业内容\", trigger: \"blur\" }],\n      })\n      // 上传附件成功\n      const onUploadHomeworkAttachmentSuccess = (res) => {\n        homework.value.attachment = res.data\n      }\n      // 删除附件成功\n      const onUploadHomeworkAttachmentRemove = () => {\n        homework.value.attachment = \"\"\n        uploadHomeworkData.value.files = []\n      }\n      const submitHomework = () => {\n        homework.value.lessonId = route.query.id || lesson.value.id\n        homeworkRef.value.validate((valid) => {\n          if (!valid) {return false}\n          if (homework.value.id) {\n            updateHomework(homework.value, () => {\n              success(\"编辑成功\")\n              showStep.value = \"publish\";\n              let path = route.fullPath;\n              router.push({path, query: {id: lesson.value.id, step: \"publish\"} });\n            })\n          } else {\n            saveHomework(homework.value, (res) => {\n              homework.value = res\n              success(\"编辑成功\")\n              showStep.value = \"publish\";\n              let path = route.fullPath;\n              router.push({path, query: {id: lesson.value.id, step: \"publish\"} });\n            })\n          }\n        })\n      }\n      // 发布页面\n      const statusMap = {\n        unpublished: \"草稿箱\",\n        published: \"已发布\",\n        deleted: \"已删除\"\n      }\n      const publish = () => {\n        publishLesson({id: lesson.value.id}, () => {\n          success(\"发布成功\")\n          lesson.value.status = \"published\"\n        })\n      }\n      const unPublish = () => {\n        unPublishLesson({id: lesson.value.id}, () => {\n          success(\"取消发布成功\")\n          lesson.value.status = \"unpublished\"\n        })\n      }\n      // 步骤条\n      const init = () => {\n        // 初始化加载\n        if (route.query.step) {\n          showStep.value = route.query.step;\n        } else {\n          showStep.value = \"base\"\n        }\n        lesson.value.id = route.query.id || \"\"\n        loadCategory();\n        loadContent();\n      }\n      init()\n      // 步骤条点击切换\n      const stepClick = (key) => {\n        if (!isUpdate && loadStepActiveArray().indexOf(key) < 0) {\n          return;\n        }\n        showStep.value = key;\n        let path = route.fullPath;\n        router.push({path, query: {id: lesson.value.id, step: key} });\n      }\n      loadStepActiveArray();\n      // 返回参数与方法\n      return {\n        // 基本信息\n        uploadData,\n        categoryOptions,\n        lesson,\n        selectCidList,\n        lessonRules,\n        lessonRef,\n        changeCategory,\n        changeStartTime,\n        changeEndTime,\n        onUploadImageSuccess,\n        onUploadImageRemove,\n        submitBaseInfo,\n        // 内容列表\n        contentList,\n        showChapterDialog,\n        lessonChapter,\n        lessonChapterRules,\n        showChapterSectionDialog,\n        lessonChapterSection,\n        lessonChapterSectionRules,\n        lessonChapterRef,\n        lessonChapterSectionRef,\n        showChapter,\n        hideChapter,\n        showChapterSection,\n        hideChapterSection,\n        deleteChapter,\n        deleteChapterSection,\n        submitChapter,\n        submitChapterSection,\n        uploadVideoData,\n        linkVideo,\n        urlBlur,\n        onBeforeUploadVideo,\n        onUploadVideoSuccess,\n        onUploadVideoRemove,\n        onDraggableChange,\n        // 作业\n        homework,\n        homeworkRef,\n        homeworkRules,\n        uploadHomeworkData,\n        submitHomework,\n        onUploadHomeworkAttachmentSuccess,\n        onUploadHomeworkAttachmentRemove,\n        // 发布页面\n        statusMap,\n        publish,\n        unPublish,\n        // 步骤条\n        steps,\n        stepActive,\n        showStep,\n        stepClick,\n      };\n    }\n  }\n</script>\n<style scoped lang=\"scss\">\n  .app-container {\n    margin: 20px;\n    .base {\n      .upload-image-tips {\n        font-size: 12px;\n        color: #999999;\n      }\n      ::v-deep .el-upload--picture-card,\n      ::v-deep .el-upload-list--picture-card .el-upload-list__item {\n        //width: 100%;\n        height: 62.5%;\n        border: none;\n        display: flex;\n        margin: 0;\n        min-height: 146px;\n        justify-content: center;\n        flex-direction: column;\n        max-height: 400px;\n      }\n      .no-plus {\n        ::v-deep .el-upload--picture-card {\n          min-height: inherit;\n          justify-content: inherit;\n          flex-direction: inherit;\n          display: none;\n        }\n        img {\n          max-height: 460px;\n        }\n      }\n      .input-number {\n        margin-right: 20px;\n      }\n    }\n    .content {\n      position: relative;\n      min-height: 500px;\n      .content-header {\n        text-align: right;\n        ::v-deep .el-button {\n          border-color: #f3f5f8;\n        }\n      }\n      .tips {\n        font-size: 12px;\n        color: #999999;\n        padding: 15px 20px;\n      }\n    }\n    .publish {\n      .publish-box {\n        margin: 50px auto;\n        text-align: center;\n        .current-status {\n          margin: 0 auto 20px;\n          width: 180px;\n        }\n        .btn-list{\n          margin: 0 auto;\n          width: 180px;\n          text-align: center;\n        }\n      }\n    }\n  }\n  ::v-deep .el-input__inner, ::v-deep .el-input-number {\n    height: 34px;\n    line-height: 34px;\n    font-size: 12px;\n    border-color: #f3f5f8;\n    //border: none;\n    &:focus, &:hover {\n      border-color: #f3f5f8;\n    }\n    .el-input-number__decrease, .el-input-number__increase {\n      background: #FFFFFF;\n      line-height: 32px;\n      border: none;\n      &:focus, &:hover {\n        border-color: #f3f5f8;\n      }\n    }\n  }\n  ::v-deep .el-textarea__inner {\n    border-color: #f3f5f8;\n    &:focus, &:hover {\n      border-color: #f3f5f8;\n    }\n  }\n  ::v-deep .el-cascader .el-input .el-input__inner:focus {\n    border-color: #f3f5f8;\n  }\n  ::v-deep .el-input__icon {\n    line-height: 34px;\n    cursor: pointer;\n    &:hover {\n      color: $--color-primary;\n    }\n  }\n  ::v-deep .el-form-item__label {\n    font-size: 12px;\n  }\n  ::v-deep .el-table th,\n  ::v-deep .el-table td {\n    padding: 5px 0;\n    font-size: 12px;\n    color: #000000;\n  }\n  ::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td {\n    background-color: #FFFFFF;\n  }\n  ::v-deep .el-table__body tr.current-row > td {\n    background-color: #FFFFFF;\n  }\n  ::v-deep .el-button--text {\n    color: #303133;\n    &:hover {\n      color: $--color-primary;\n    }\n  }\n  ::v-deep .el-cascader:not(.is-disabled):hover .el-input__inner {\n    cursor: pointer;\n    border-color: #f3f5f8;\n  }\n  .box-card {\n    padding: 0 30px 10px;\n    .el-card {\n      box-shadow: none;\n    }\n    ::v-deep .el-card__header {\n      padding: 5px 20px;\n      font-size: 12px;\n    }\n    ::v-deep .el-card__body {\n      padding: 0;\n      .table-wrapper {\n        display: none;\n        .video-box {\n          padding: 0 20px 15px;\n          display: flex;\n          justify-content: center;\n          video {\n            background: #000;\n            width: 320px;\n            height: 240px;\n          }\n        }\n      }\n      .show {\n        display: block;\n      }\n    }\n  }\n  .affix {\n    .step-list {\n      padding: 10px 20px;\n      .title {\n        padding: 0 0 20px 0;\n        font-size: 12px;\n      }\n      .steps {\n        height: 120px;\n        padding-left: 10px;\n        ::v-deep .el-step__title {\n          font-size: 14px;\n        }\n        ::v-deep .el-step__icon {\n          width: 20px;\n          height: 20px;\n        }\n        ::v-deep .el-step.is-vertical .el-step__head {\n          width: 20px;\n        }\n        ::v-deep .el-step.is-vertical .el-step__title{\n          cursor:pointer;\n        }\n        ::v-deep .el-step.is-vertical .el-step__line {\n          width: 1px;\n          left: 10px;\n          top: 2px;\n        }\n        ::v-deep .el-step__icon.is-text {\n          border-width: 1px;\n          cursor:pointer;\n        }\n        ::v-deep .step-active .el-step__head.is-finish {\n          color: red;\n        }\n      }\n    }\n    .draggable {\n      padding: 10px 0 10px 10px;\n      .title {\n        padding: 10px 0 10px;\n        font-size: 12px;\n      }\n      .item-list {\n        padding: 0 0 0 10px;\n        .item {\n          font-size: 12px;\n          line-height: 20px;\n          padding: 5px 0;\n          .sub-item-list {\n            background: #ffffff;\n            padding: 0 10px;\n            border-radius: 4px;\n            margin-top: 5px;\n            .sub-item {\n              line-height: 20px;\n              padding: 5px 0;\n              color: #666666;\n              &:first-child {\n                padding-top: 10px;\n              }\n              &:last-child {\n                padding-bottom: 10px;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  ::v-deep .el-upload--text {\n    font-size: 12px;\n  }\n  ::v-deep .el-affix--fixed {\n    z-index: 98!important;\n  }\n  ::v-deep .el-table__empty-block {\n    line-height: 400px;\n    .el-table__empty-text {\n      line-height: 400px;\n    }\n  }\n</style>\n"], "mappings": ";AA2OA,OAAOA,MAAK,MAAO,UAAS;AAC5B,OAAOC,OAAM,MAAO,sBAAqB;AACzC,OAAOC,UAAS,MAAO,mCAAkC;AACzD,OAAOC,MAAK,MAAO,qBAAoB;AACvC,SAAQC,GAAG,QAAO,KAAI;AACtB,SAAQC,QAAQ,QAAO,YAAW;AAClC,SAAQC,gBAAgB,QAAO,oBAAmB;AAClD,SAAQC,OAAO,EAAEC,OAAO,EAAEC,KAAK,QAAO,kBAAiB;AACvD,SAAQC,gBAAgB,EAAEC,MAAM,EAAEC,YAAY,QAAO,sBAAqB;AAC1E,SAAQC,YAAY,EAAEC,cAAc,EAAEC,WAAW,EAAEC,aAAa,EAAEC,eAAe,EAC7EC,iBAAiB,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,eAAe,EAClGC,wBAAwB,EAAEC,0BAA0B,EAAEC,0BAA0B,EAAEC,YAAY,EAAEC,cAAc,EAAEC,WAAW,QAAO,oBAAmB;AAEvJ,eAAe;EACfC,IAAI,EAAE,iBAAiB;EACrBC,UAAU,EAAC;IACT3B,MAAM;IACND,UAAU;IACV6B,SAAS,EAAEzB;EACb,CAAC;EACD0B,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAI5B,QAAQ,EAAC;IACvB,IAAI6B,QAAO,GAAI,CAAC,CAACD,KAAK,CAACE,KAAK,CAACC,EAAC;IAC9B,IAAIC,QAAO,GAAIjC,GAAG,CAAC,EAAE;IACrB,MAAMkC,KAAI,GAAI,CACZ;MAACC,GAAG,EAAE,MAAM;MAAEV,IAAI,EAAE;IAAM,CAAC,EAC3B;MAACU,GAAG,EAAE,SAAS;MAAEV,IAAI,EAAE;IAAM,CAAC,EAC9B;MAACU,GAAG,EAAE,UAAU;MAAEV,IAAI,EAAE;IAAM,CAAC,EAC/B;MAACU,GAAG,EAAE,SAAS;MAAEV,IAAI,EAAE;IAAM,CAAC,CAChC;IACA,MAAMW,UAAS,GAAIpC,GAAG,CAAC,CAAC;IACxB,MAAMqC,mBAAkB,GAAIA,CAAA,KAAM;MAChC,MAAMC,eAAc,GAAI,EAAE;MAC1B,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIL,KAAK,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;QACrC,MAAME,IAAG,GAAIP,KAAK,CAACK,CAAC,CAAC;QACrBD,eAAe,CAACI,IAAI,CAACD,IAAI,CAACN,GAAG,CAAC;QAC9B,IAAIM,IAAI,CAACN,GAAE,KAAMF,QAAQ,CAACU,KAAK,EAAE;UAC/BP,UAAU,CAACO,KAAI,GAAIJ,CAAC;UACpB;QACF;MACF;MACA,IAAIT,QAAQ,EAAE;QACZM,UAAU,CAACO,KAAI,GAAIT,KAAK,CAACM,MAAM;MACjC;MACA,OAAOF,eAAe;IACxB;IACA;IACA,MAAMM,UAAS,GAAI5C,GAAG,CAAC;MACrB6C,GAAG,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAe,GAAI,yBAAyB;MAC7DC,KAAK,EAAE;IACT,CAAC;IACD,MAAMC,eAAc,GAAIlD,GAAG,CAAC,EAAE;IAC9B,MAAMmD,aAAY,GAAInD,GAAG,CAAC,EAAE;IAC5B,MAAMoD,MAAK,GAAIpD,GAAG,CAAC;MACjBgC,EAAE,EAAE,EAAE;MACNP,IAAI,EAAE,EAAE;MACR4B,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,CAAC;MACRC,aAAa,EAAE,CAAC;MAChBC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,YAAY,EAAE;IAChB,CAAC;IACD,MAAMC,WAAU,GAAI;MAClBpC,IAAI,EAAE,CAAC;QAAEqC,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC7DX,SAAS,EAAE,CAAC;QAAES,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC,CAAC;MACpEV,OAAO,EAAE,CAAC;QAAEQ,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC,CAAC;MAClEL,MAAM,EAAE,CAAC;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC/DT,KAAK,EAAE,CAAC;QAAEO,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9DN,OAAO,EAAE,CAAC;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC,CAAC;MAClEJ,YAAY,EAAE,CAAC;QAAEE,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MACrEP,KAAK,EAAE,CAAC;QAAEK,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC;IACjE;IACA;IACA,MAAMC,YAAW,GAAIA,CAAA,KAAM;MACzB,IAAIjC,EAAC,GAAIH,KAAK,CAACE,KAAK,CAACC,EAAE;MACvB,IAAI,CAACA,EAAE,EAAE;QAAE;MAAQ;MACnBrB,WAAW,CAACqB,EAAE,EAAE,UAAUkC,GAAG,EAAE;QAC7Bd,MAAM,CAACT,KAAI,GAAIuB,GAAG;QAClBf,aAAa,CAACR,KAAI,GAAInC,YAAY,CAAC0C,eAAe,CAACP,KAAK,EAAEuB,GAAG,CAACR,OAAO,CAAC;QACtEN,MAAM,CAACT,KAAK,CAACe,OAAM,GAAI,EAAC;QACxBd,UAAU,CAACD,KAAK,CAACM,KAAI,GAAI,CACvB;UACExB,IAAI,EAAE,IAAI;UACVoB,GAAG,EAAEO,MAAM,CAACT,KAAK,CAACc;QACpB,EACF;QACA,KAAK,MAAMU,UAAS,IAAKhB,aAAa,CAACR,KAAK,EAAE;UAC5CS,MAAM,CAACT,KAAK,CAACe,OAAO,CAAChB,IAAI,CAACyB,UAAU,CAACA,UAAU,CAAC3B,MAAK,GAAI,CAAC,CAAC;QAC7D;MACF,CAAC;IACH;IACA;IACA,MAAM4B,YAAW,GAAIA,CAAA,KAAM;MACzB9D,gBAAgB,CAAC,CAAC,EAAE,IAAI,EAAG4D,GAAG,IAAK;QACjC,IAAIA,GAAE,IAAKA,GAAG,CAAC1B,MAAM,EAAE;UACrBU,eAAe,CAACP,KAAI,GAAIpC,MAAM,CAAC2D,GAAG,CAAC;UACnCD,YAAY,EAAE;QAChB;MACF,CAAC;IACH;IACA;IACA,MAAMI,cAAa,GAAKC,GAAG,IAAK;MAC9BlB,MAAM,CAACT,KAAK,CAACe,OAAM,GAAI,EAAC;MACxB,KAAK,MAAMS,UAAS,IAAKG,GAAG,EAAE;QAC5BlB,MAAM,CAACT,KAAK,CAACe,OAAO,CAAChB,IAAI,CAACyB,UAAU,CAACA,UAAU,CAAC3B,MAAK,GAAI,CAAC,CAAC;MAC7D;IACF;IACA;IACA,MAAM+B,eAAc,GAAKD,GAAG,IAAK;MAC/BlB,MAAM,CAACT,KAAK,CAACU,SAAQ,GAAIiB,GAAE;IAC7B;IACA;IACA,MAAME,aAAY,GAAKF,GAAG,IAAK;MAC7BlB,MAAM,CAACT,KAAK,CAACW,OAAM,GAAIgB,GAAE;IAC3B;IACA;IACA,MAAMG,oBAAmB,GAAKP,GAAG,IAAK;MACpCd,MAAM,CAACT,KAAK,CAACc,KAAI,GAAIS,GAAG,CAACQ,IAAG;IAC9B;IACA;IACA,MAAMC,mBAAkB,GAAIA,CAAA,KAAM;MAChCvB,MAAM,CAACT,KAAK,CAACc,KAAI,GAAI,EAAC;MACtBb,UAAU,CAACD,KAAK,CAACM,KAAI,GAAI,EAAC;IAC5B;IACA;IACA,MAAM2B,SAAQ,GAAI5E,GAAG,CAAC,IAAI;IAC1B,MAAM6E,cAAa,GAAIA,CAAA,KAAM;MAC3BD,SAAS,CAACjC,KAAK,CAACmC,QAAQ,CAAEC,KAAK,IAAK;QAClC,IAAI,CAACA,KAAK,EAAE;UAAE,OAAO,KAAI;QAAE;QAC3B,IAAIjD,QAAQ,EAAE;UACZ,IAAG,OAAOsB,MAAM,CAACT,KAAK,CAACU,SAAQ,IAAK,QAAQ,EAAE;YAC5CD,MAAM,CAACT,KAAK,CAACU,SAAQ,GAAI,IAAI2B,IAAI,CAAC5B,MAAM,CAACT,KAAK,CAACU,SAAS,CAAC;UAC3D;UACA,IAAG,OAAOD,MAAM,CAACT,KAAK,CAACW,OAAM,IAAK,QAAQ,EAAE;YAC1CF,MAAM,CAACT,KAAK,CAACW,OAAM,GAAI,IAAI0B,IAAI,CAAC5B,MAAM,CAACT,KAAK,CAACW,OAAO,CAAC;UACvD;UACA5C,cAAc,CAAC0C,MAAM,CAACT,KAAK,EAAE,UAAUuB,GAAG,EAAE;YAC1C,IAAIA,GAAE,IAAKA,GAAG,CAAClC,EAAE,EAAE;cACjBoB,MAAM,CAACT,KAAI,GAAIuB,GAAG;cAClB/D,OAAO,CAAC,MAAM;cACd8B,QAAQ,CAACU,KAAI,GAAI,SAAS;cAC1BN,mBAAmB,EAAC;cACpB,IAAI4C,IAAG,GAAIpD,KAAK,CAACqD,QAAQ;cACzBtF,MAAM,CAAC8C,IAAI,CAAC;gBAACuC,IAAI;gBAAElD,KAAK,EAAE;kBAACC,EAAE,EAAEoB,MAAM,CAACT,KAAK,CAACX,EAAE;kBAAES,IAAI,EAAE;gBAAS;cAAE,CAAC,CAAC;YACrE;UACF,CAAC;QACH,OAAO;UACLhC,YAAY,CAAC2C,MAAM,CAACT,KAAK,EAAE,UAAUuB,GAAG,EAAE;YACxC,IAAIA,GAAE,IAAKA,GAAG,CAAClC,EAAE,EAAE;cACjBoB,MAAM,CAACT,KAAI,GAAIuB,GAAG;cAClB/D,OAAO,CAAC,MAAM;cACd8B,QAAQ,CAACU,KAAI,GAAI,SAAS;cAC1BN,mBAAmB,EAAC;cACpB,IAAI4C,IAAG,GAAIpD,KAAK,CAACqD,QAAQ;cACzBtF,MAAM,CAAC8C,IAAI,CAAC;gBAACuC,IAAI;gBAAElD,KAAK,EAAE;kBAACC,EAAE,EAAEoB,MAAM,CAACT,KAAK,CAACX,EAAE;kBAAES,IAAI,EAAE;gBAAS;cAAE,CAAC,CAAC;YACrE;UACF,CAAC;QACH;MACF,CAAC;IACH;;IAEA;IACA,MAAM0C,WAAU,GAAInF,GAAG,CAAC,EAAE;IAC1B,MAAMoF,iBAAgB,GAAIpF,GAAG,CAAC,KAAK;IACnC,MAAMqF,aAAY,GAAIrF,GAAG,CAAC;MACxBgC,EAAE,EAAE,EAAE;MACNsD,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACT5B,MAAM,EAAE;IACV,CAAC;IACD,MAAM6B,kBAAiB,GAAI;MACzBD,KAAK,EAAE,CAAC;QAAEzB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC;IAC/D;IACA,MAAMyB,wBAAuB,GAAIzF,GAAG,CAAC,KAAK;IAC1C,MAAM0F,oBAAmB,GAAI1F,GAAG,CAAC;MAC/BgC,EAAE,EAAE,EAAE;MACN2D,eAAe,EAAE,EAAE;MACnBC,IAAI,EAAE,MAAM;MACZL,KAAK,EAAE,EAAE;MACT1C,GAAG,EAAE,EAAE;MACPc,MAAM,EAAE,EAAE;MACVkC,SAAS,EAAE;IACb,CAAC;IACD,MAAMC,yBAAwB,GAAI9F,GAAG,CAAC;MACpCuF,KAAK,EAAE,CAAC;QAAEzB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9DnB,GAAG,EAAE,CAAC;QAAEiB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9D4B,IAAI,EAAE,CAAC;QAAE9B,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC;IAChE,CAAC;IACD,MAAM+B,QAAO,GAAI/F,GAAG,CAAC;MACnBsF,QAAQ,EAAE,EAAE;MACZU,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE;IACd,CAAC;IACD,MAAMC,kBAAiB,GAAIlG,GAAG,CAAC;MAC7B6C,GAAG,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAe,GAAI,0BAA0B;MAC9DC,KAAK,EAAE;IACT,CAAC;IACD,MAAMkD,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAInE,EAAC,GAAIH,KAAK,CAACE,KAAK,CAACC,EAAE;MACvB,IAAI,CAACA,EAAE,EAAE;QAAE;MAAQ;MACnBf,oBAAoB,CAAC;QAACqE,QAAQ,EAAEtD;MAAE,CAAC,EAAGkC,GAAG,IAAK;QAC5C,IAAIA,GAAE,IAAKA,GAAG,CAACkC,IAAI,EAAE;UACnBjB,WAAW,CAACxC,KAAI,GAAIuB,GAAG,CAACkC,IAAI;QAC9B;MACF,CAAC;MACD5E,WAAW,CAAC;QAAC8D,QAAQ,EAAEzD,KAAK,CAACE,KAAK,CAACC;MAAE,CAAC,EAAGkC,GAAG,IAAK;QAC/C6B,QAAQ,CAACpD,KAAI,GAAIuB,GAAE;QACnB,IAAI6B,QAAQ,CAACpD,KAAK,CAACE,GAAG,EAAE;UACtBqD,kBAAkB,CAACvD,KAAK,CAACM,KAAI,GAAI,CAC/B;YACExB,IAAI,EAAE,MAAM;YACZoB,GAAG,EAAEkD,QAAQ,CAACpD,KAAK,CAACE;UACtB,EACF;QACF;MACF,CAAC;IACH;IACA,MAAMwD,WAAU,GAAKC,OAAO,IAAK;MAC/BlB,iBAAiB,CAACzC,KAAI,GAAI,IAAI;MAC9B,IAAI2D,OAAM,IAAKA,OAAO,CAACtE,EAAE,EAAE;QACzBqD,aAAa,CAAC1C,KAAI,GAAI2D,OAAO;MAC/B,OAAO;QACLjB,aAAa,CAAC1C,KAAI,GAAI;UACpB2C,QAAQ,EAAElC,MAAM,CAACT,KAAK,CAACX,EAAE;UACzBA,EAAE,EAAE,EAAE;UACNuD,KAAK,EAAE,EAAE;UACT5B,MAAM,EAAE;QACV;MACF;IACF;IACA,MAAM4C,WAAU,GAAIA,CAAA,KAAM;MACxBnB,iBAAiB,CAACzC,KAAI,GAAI,KAAK;MAC/B0C,aAAa,CAAC1C,KAAI,GAAI;QAACX,EAAE,EAAE,EAAE;QAAEsD,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAE5B,MAAM,EAAE;MAAE;IACpE;IACA,MAAM6C,eAAc,GAAIxG,GAAG,CAAC;MAC1B6C,GAAG,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAe,GAAI,yBAAyB;MAC7DC,KAAK,EAAE;IACT,CAAC;IACD,IAAIwD,WAAU,GAAI,KAAK;IACvB,MAAMC,kBAAiB,GAAIA,CAACf,eAAe,EAAEgB,cAAc,KAAK;MAC9DlB,wBAAwB,CAAC9C,KAAI,GAAI,IAAI;MACrC,IAAIgE,cAAa,IAAKA,cAAc,CAAC3E,EAAE,EAAE;QACvC0D,oBAAoB,CAAC/C,KAAI,GAAIgE,cAAc;QAC3CH,eAAe,CAAC7D,KAAK,CAACM,KAAI,GAAI,CAC5B;UACExB,IAAI,EAAEiE,oBAAoB,CAAC/C,KAAK,CAAC4C,KAAI,GAAI,MAAM;UAC/C1C,GAAG,EAAE6C,oBAAoB,CAAC/C,KAAK,CAACE;QAClC,EACF;MACF,OAAO;QACL4D,WAAU,GAAI,KAAI;QAClBf,oBAAoB,CAAC/C,KAAI,GAAI;UAC3BgD,eAAe,EAAEA,eAAe;UAChC3D,EAAE,EAAE,EAAE;UACNuD,KAAK,EAAE,EAAE;UACT1C,GAAG,EAAE,EAAE;UACPc,MAAM,EAAE,EAAE;UACViC,IAAI,EAAE,MAAM;UACZC,SAAS,EAAE;QACb;MACF;IACF;IACA,MAAMe,kBAAiB,GAAIA,CAAA,KAAM;MAC/BH,WAAU,GAAI,KAAI;MAClBhB,wBAAwB,CAAC9C,KAAI,GAAI,KAAK;MACtC+C,oBAAoB,CAAC/C,KAAI,GAAI;QAC3BX,EAAE,EAAE,EAAE;QACN2D,eAAe,EAAE,EAAE;QACnBJ,KAAK,EAAE,EAAE;QACT1C,GAAG,EAAE,EAAE;QACPc,MAAM,EAAE,EAAE;QACViC,IAAI,EAAE,MAAM;QACZC,SAAS,EAAE;MACb;IACF;IACA,MAAMgB,aAAY,GAAK7E,EAAE,IAAK;MAC5B5B,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM;QAC5BY,mBAAmB,CAAC;UAACgB,EAAE,EAAEA;QAAE,CAAC,EAAE,MAAM;UAClC7B,OAAO,CAAC,MAAM;UACdgG,WAAW,EAAC;QACd,CAAC;MACH,CAAC;IACH;IACA,MAAMW,oBAAmB,GAAK9E,EAAE,IAAK;MACnC5B,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM;QAC5BiB,0BAA0B,CAAC;UAACW,EAAE,EAAEA;QAAE,CAAC,EAAE,MAAM;UACzC7B,OAAO,CAAC,MAAM;UACdgG,WAAW,EAAC;QACd,CAAC;MACH,CAAC;IACH;IACA,MAAMY,gBAAe,GAAI/G,GAAG,CAAC,IAAI;IACjC,MAAMgH,aAAY,GAAIA,CAAA,KAAM;MAC1BD,gBAAgB,CAACpE,KAAK,CAACmC,QAAQ,CAAEC,KAAK,IAAK;QACzC,IAAI,CAACA,KAAK,EAAE;UAAE,OAAO,KAAI;QAAE;QAC3B,IAAIM,aAAa,CAAC1C,KAAK,CAACX,EAAE,EAAE;UAC1BjB,mBAAmB,CAACsE,aAAa,CAAC1C,KAAK,EAAE,YAAY;YACnDxC,OAAO,CAAC,MAAM;YACdoG,WAAW,EAAC;YACZJ,WAAW,EAAC;UACd,CAAC;QACH,OAAO;UACLrF,iBAAiB,CAACuE,aAAa,CAAC1C,KAAK,EAAE,YAAY;YACjDxC,OAAO,CAAC,MAAM;YACdoG,WAAW,EAAC;YACZJ,WAAW,EAAC;YACZ/D,UAAU,CAACO,KAAI,GAAIT,KAAK,CAACM,MAAM;YAC/BV,QAAO,GAAI,IAAI;UACjB,CAAC;QACH;MACF,CAAC;IACH;IACA,MAAMmF,SAAQ,GAAIjH,GAAG,CAAC,IAAI;IAC1B,MAAMkH,OAAM,GAAIA,CAAA,KAAM;MACpB,IAAIxB,oBAAoB,CAAC/C,KAAK,CAACiD,IAAG,KAAM,MAAM,EAAE;QAC9CqB,SAAS,CAACtE,KAAK,CAACwE,gBAAgB,CAAC,gBAAgB,EAAE,MAAM;UACvD;UACAzB,oBAAoB,CAAC/C,KAAK,CAACkD,SAAQ,GAAIoB,SAAS,CAACtE,KAAK,CAACyE,QAAQ;UAC/DX,WAAU,GAAI,IAAG;QACnB,CAAC,CAAC;MACJ;IACF;IACA,MAAMY,uBAAsB,GAAIrH,GAAG,CAAC,IAAI;IACxC,MAAMsH,oBAAmB,GAAIA,CAAA,KAAM;MACjC,IAAI5B,oBAAoB,CAAC/C,KAAK,CAACiD,IAAG,KAAM,MAAM,EAAE;QAC9C,IAAI,CAACF,oBAAoB,CAAC/C,KAAK,CAACX,EAAC,IAAK,CAACyE,WAAW,EAAE;UAClDpG,KAAK,CAAC,gBAAgB,CAAC;QACzB;MACF;MACAgH,uBAAuB,CAAC1E,KAAK,CAACmC,QAAQ,CAAEC,KAAK,IAAK;QAChD,IAAI,CAACA,KAAK,EAAE;UAAE,OAAO,KAAI;QAAE;QAC3B,IAAIW,oBAAoB,CAAC/C,KAAK,CAACX,EAAE,EAAE;UACjCZ,0BAA0B,CAACsE,oBAAoB,CAAC/C,KAAK,EAAE,YAAY;YACjExC,OAAO,CAAC,MAAM;YACdyG,kBAAkB,EAAC;YACnBT,WAAW,EAAC;UACd,CAAC;QACH,OAAO;UACLhF,wBAAwB,CAACuE,oBAAoB,CAAC/C,KAAK,EAAE,YAAY;YAC/DxC,OAAO,CAAC,MAAM;YACdyG,kBAAkB,EAAC;YACnBT,WAAW,EAAC;UACd,CAAC;QACH;MACF,CAAC;IACH;IACA;IACA,MAAMoB,oBAAmB,GAAKrD,GAAG,IAAK;MACpCwB,oBAAoB,CAAC/C,KAAK,CAACE,GAAE,GAAIqB,GAAG,CAACQ,IAAG;MACxC8B,eAAe,CAAC7D,KAAK,CAACM,KAAI,GAAI,CAC1B;QACExB,IAAI,EAAEiE,oBAAoB,CAAC/C,KAAK,CAAC4C,KAAI,GAAI,MAAM;QAC/C1C,GAAG,EAAEqB,GAAG,CAACQ;MACX,EACJ;IACF;IACA;IACA,MAAM8C,mBAAkB,GAAIA,CAAA,KAAM;MAChC9B,oBAAoB,CAAC/C,KAAK,CAACE,GAAE,GAAI,EAAC;MAClC2D,eAAe,CAAC7D,KAAK,CAACM,KAAI,GAAI,EAAC;IACjC;IACA,MAAMwE,mBAAkB,GAAKC,IAAI,IAAK;MACpC,IAAIC,QAAO,GAAIC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;MACxC,IAAII,YAAW,GAAI,IAAIC,KAAK,CAACJ,QAAQ,CAAC;MACtCG,YAAY,CAACX,gBAAgB,CAAC,gBAAgB,EAAE,MAAM;QACpD;QACAzB,oBAAoB,CAAC/C,KAAK,CAACkD,SAAQ,GAAIiC,YAAY,CAACV,QAAQ;MAC9D,CAAC,CAAC;IACJ;IACA;IACA,MAAMY,iBAAgB,GAAIA,CAAA,KAAM;MAC9BC,OAAO,CAACC,GAAG,CAAC/C,WAAW,CAACxC,KAAK;MAC7B,MAAMwF,WAAU,GAAI,EAAC;MACrB,KAAK,MAAMnC,OAAM,IAAKb,WAAW,CAACxC,KAAK,EAAE;QACvC,MAAMyF,OAAM,GAAI,EAAC;QACjB,IAAIpC,OAAO,CAACqC,kBAAiB,IAAKrC,OAAO,CAACqC,kBAAkB,CAAC7F,MAAM,EAAE;UACnE,KAAK,MAAM8F,GAAE,IAAKtC,OAAO,CAACqC,kBAAkB,EAAE;YAC5CD,OAAO,CAAC1F,IAAI,CAAC;cAACV,EAAE,EAAEsG,GAAG,CAACtG,EAAE;cAAEoE,IAAI,EAAE;YAAE,CAAC;UACrC;QACF;QACA+B,WAAW,CAACzF,IAAI,CAAC;UAACV,EAAE,EAAEgE,OAAO,CAAChE,EAAE;UAAEoE,IAAI,EAAEgC;QAAO,CAAC,CAAC;MACnD;MACA,MAAMG,MAAK,GAAI;QAACvG,EAAE,EAAEoB,MAAM,CAACT,KAAK,CAACX,EAAE;QAAEoE,IAAI,EAAE+B;MAAW;MACtDjH,eAAe,CAACqH,MAAM,EAAE,MAAM;QAC5BpI,OAAO,CAAC,QAAQ;MAClB,CAAC;MACD8H,OAAO,CAACC,GAAG,CAACK,MAAM;IACpB;IACA;IACA,MAAMC,WAAU,GAAIxI,GAAG,CAAC,IAAI;IAC5B,MAAMyI,aAAY,GAAIzI,GAAG,CAAC;MACxBgG,OAAO,EAAE,CAAC;QAAElC,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC;IACnE,CAAC;IACD;IACA,MAAM0E,iCAAgC,GAAKxE,GAAG,IAAK;MACjD6B,QAAQ,CAACpD,KAAK,CAACsD,UAAS,GAAI/B,GAAG,CAACQ,IAAG;IACrC;IACA;IACA,MAAMiE,gCAA+B,GAAIA,CAAA,KAAM;MAC7C5C,QAAQ,CAACpD,KAAK,CAACsD,UAAS,GAAI,EAAC;MAC7BC,kBAAkB,CAACvD,KAAK,CAACM,KAAI,GAAI,EAAC;IACpC;IACA,MAAM2F,cAAa,GAAIA,CAAA,KAAM;MAC3B7C,QAAQ,CAACpD,KAAK,CAAC2C,QAAO,GAAIzD,KAAK,CAACE,KAAK,CAACC,EAAC,IAAKoB,MAAM,CAACT,KAAK,CAACX,EAAC;MAC1DwG,WAAW,CAAC7F,KAAK,CAACmC,QAAQ,CAAEC,KAAK,IAAK;QACpC,IAAI,CAACA,KAAK,EAAE;UAAC,OAAO,KAAK;QAAA;QACzB,IAAIgB,QAAQ,CAACpD,KAAK,CAACX,EAAE,EAAE;UACrBT,cAAc,CAACwE,QAAQ,CAACpD,KAAK,EAAE,MAAM;YACnCxC,OAAO,CAAC,MAAM;YACd8B,QAAQ,CAACU,KAAI,GAAI,SAAS;YAC1B,IAAIsC,IAAG,GAAIpD,KAAK,CAACqD,QAAQ;YACzBtF,MAAM,CAAC8C,IAAI,CAAC;cAACuC,IAAI;cAAElD,KAAK,EAAE;gBAACC,EAAE,EAAEoB,MAAM,CAACT,KAAK,CAACX,EAAE;gBAAES,IAAI,EAAE;cAAS;YAAE,CAAC,CAAC;UACrE,CAAC;QACH,OAAO;UACLnB,YAAY,CAACyE,QAAQ,CAACpD,KAAK,EAAGuB,GAAG,IAAK;YACpC6B,QAAQ,CAACpD,KAAI,GAAIuB,GAAE;YACnB/D,OAAO,CAAC,MAAM;YACd8B,QAAQ,CAACU,KAAI,GAAI,SAAS;YAC1B,IAAIsC,IAAG,GAAIpD,KAAK,CAACqD,QAAQ;YACzBtF,MAAM,CAAC8C,IAAI,CAAC;cAACuC,IAAI;cAAElD,KAAK,EAAE;gBAACC,EAAE,EAAEoB,MAAM,CAACT,KAAK,CAACX,EAAE;gBAAES,IAAI,EAAE;cAAS;YAAE,CAAC,CAAC;UACrE,CAAC;QACH;MACF,CAAC;IACH;IACA;IACA,MAAMoG,SAAQ,GAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE;IACX;IACA,MAAMC,OAAM,GAAIA,CAAA,KAAM;MACpBrI,aAAa,CAAC;QAACoB,EAAE,EAAEoB,MAAM,CAACT,KAAK,CAACX;MAAE,CAAC,EAAE,MAAM;QACzC7B,OAAO,CAAC,MAAM;QACdiD,MAAM,CAACT,KAAK,CAACuG,MAAK,GAAI,WAAU;MAClC,CAAC;IACH;IACA,MAAMC,SAAQ,GAAIA,CAAA,KAAM;MACtBtI,eAAe,CAAC;QAACmB,EAAE,EAAEoB,MAAM,CAACT,KAAK,CAACX;MAAE,CAAC,EAAE,MAAM;QAC3C7B,OAAO,CAAC,QAAQ;QAChBiD,MAAM,CAACT,KAAK,CAACuG,MAAK,GAAI,aAAY;MACpC,CAAC;IACH;IACA;IACA,MAAME,IAAG,GAAIA,CAAA,KAAM;MACjB;MACA,IAAIvH,KAAK,CAACE,KAAK,CAACU,IAAI,EAAE;QACpBR,QAAQ,CAACU,KAAI,GAAId,KAAK,CAACE,KAAK,CAACU,IAAI;MACnC,OAAO;QACLR,QAAQ,CAACU,KAAI,GAAI,MAAK;MACxB;MACAS,MAAM,CAACT,KAAK,CAACX,EAAC,GAAIH,KAAK,CAACE,KAAK,CAACC,EAAC,IAAK,EAAC;MACrCoC,YAAY,EAAE;MACd+B,WAAW,EAAE;IACf;IACAiD,IAAI,EAAC;IACL;IACA,MAAMC,SAAQ,GAAKlH,GAAG,IAAK;MACzB,IAAI,CAACL,QAAO,IAAKO,mBAAmB,EAAE,CAACiH,OAAO,CAACnH,GAAG,IAAI,CAAC,EAAE;QACvD;MACF;MACAF,QAAQ,CAACU,KAAI,GAAIR,GAAG;MACpB,IAAI8C,IAAG,GAAIpD,KAAK,CAACqD,QAAQ;MACzBtF,MAAM,CAAC8C,IAAI,CAAC;QAACuC,IAAI;QAAElD,KAAK,EAAE;UAACC,EAAE,EAAEoB,MAAM,CAACT,KAAK,CAACX,EAAE;UAAES,IAAI,EAAEN;QAAG;MAAE,CAAC,CAAC;IAC/D;IACAE,mBAAmB,EAAE;IACrB;IACA,OAAO;MACL;MACAO,UAAU;MACVM,eAAe;MACfE,MAAM;MACND,aAAa;MACbU,WAAW;MACXe,SAAS;MACTP,cAAc;MACdE,eAAe;MACfC,aAAa;MACbC,oBAAoB;MACpBE,mBAAmB;MACnBE,cAAc;MACd;MACAM,WAAW;MACXC,iBAAiB;MACjBC,aAAa;MACbG,kBAAkB;MAClBC,wBAAwB;MACxBC,oBAAoB;MACpBI,yBAAyB;MACzBiB,gBAAgB;MAChBM,uBAAuB;MACvBhB,WAAW;MACXE,WAAW;MACXG,kBAAkB;MAClBE,kBAAkB;MAClBC,aAAa;MACbC,oBAAoB;MACpBE,aAAa;MACbM,oBAAoB;MACpBd,eAAe;MACfS,SAAS;MACTC,OAAO;MACPO,mBAAmB;MACnBF,oBAAoB;MACpBC,mBAAmB;MACnBQ,iBAAiB;MACjB;MACAjC,QAAQ;MACRyC,WAAW;MACXC,aAAa;MACbvC,kBAAkB;MAClB0C,cAAc;MACdF,iCAAiC;MACjCC,gCAAgC;MAChC;MACAE,SAAS;MACTI,OAAO;MACPE,SAAS;MACT;MACAjH,KAAK;MACLE,UAAU;MACVH,QAAQ;MACRoH;IACF,CAAC;EACH;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}