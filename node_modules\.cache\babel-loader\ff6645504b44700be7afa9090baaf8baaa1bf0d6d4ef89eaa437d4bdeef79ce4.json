{"ast": null, "code": "import { createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, createElementVNode as _createElementVNode, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-26602592\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createVNode(_component_el_button, {\n    size: \"small\",\n    type: \"text\",\n    onClick: $setup.to\n  }, {\n    default: _withCtx(() => [_createTextVNode(\"授权\")]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_dialog, {\n    modelValue: $setup.dialog,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.dialog = $event),\n    title: '选择角色',\n    \"append-to-body\": \"\",\n    width: \"90%\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_1, [_createVNode(_component_el_button, {\n      size: \"small\",\n      type: \"text\",\n      onClick: $setup.cancel\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"取消\")]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      size: \"small\",\n      loading: $setup.loading,\n      type: \"primary\",\n      onClick: $setup.submit\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"确认\")]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"loading\", \"onClick\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      ref: \"form\",\n      inline: true,\n      size: \"small\",\n      \"label-width\": \"66px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        style: {\n          \"margin-bottom\": \"0\"\n        },\n        label: \"角色\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          size: \"small\",\n          modelValue: $setup.roleId,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.roleId = $event),\n          style: {\n            \"width\": \"450px\"\n          },\n          placeholder: \"请选择\"\n        }, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.roleList, (item, index) => {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: item.name + index,\n              label: item.name,\n              value: item.id\n            }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))]),\n\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n\n      _: 1 /* STABLE */\n    }, 512 /* NEED_PATCH */)]),\n\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["class", "_createVNode", "_component_el_button", "size", "type", "onClick", "$setup", "to", "_component_el_dialog", "dialog", "$event", "title", "width", "footer", "_withCtx", "_createElementVNode", "_hoisted_1", "cancel", "loading", "submit", "_component_el_form", "ref", "inline", "_component_el_form_item", "style", "label", "_component_el_select", "roleId", "placeholder", "_createElementBlock", "_Fragment", "_renderList", "roleList", "item", "index", "_createBlock", "_component_el_option", "key", "name", "value", "id"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/organizational/user/edit.vue"], "sourcesContent": ["<template>\n  <el-button size=\"small\" type=\"text\" @click=\"to\">授权</el-button>\n  <el-dialog v-model=\"dialog\" :title=\"'选择角色'\" append-to-body width=\"90%\">\n    <el-form ref=\"form\" :inline=\"true\" size=\"small\" label-width=\"66px\">\n      <el-form-item style=\"margin-bottom: 0;\" label=\"角色\">\n        <el-select size=\"small\" v-model=\"roleId\" style=\"width: 450px;\" placeholder=\"请选择\">\n          <el-option v-for=\"(item, index) in roleList\" :key=\"item.name + index\" :label=\"item.name\" :value=\"item.id\"/>\n        </el-select>\n      </el-form-item>\n    </el-form>\n    <template #footer>\n      <div class=\"dialog-footer\">\n        <el-button size=\"small\" type=\"text\" @click=\"cancel\">取消</el-button>\n        <el-button size=\"small\" :loading=\"loading\" type=\"primary\" @click=\"submit\">确认</el-button>\n      </div>\n    </template>\n  </el-dialog>\n</template>\n<script>\nimport {ref} from \"vue\"\nimport {success, warning} from \"../../../util/tipsUtils\";\nimport {getRoleList, getUserRoleList, updateUserRole} from \"../../../api/role\";\nexport default {\n  name: \"UserEdit\",\n  props: {\n    data: {\n      type: Object,\n      required: true\n    }\n  },\n  setup(props) {\n    let dialog = ref(false)\n    let loading = ref(false)\n    let roleList = ref([])\n    let roleId = ref(null)\n    let userId = 0;\n    const form = ref(null)\n    const loadRoleList = (id) => {\n      getRoleList(res => {\n        roleList.value = res || []\n        roleList.value.unshift({\n          name: \"无\",\n          id: -1\n        })\n        getUserRoleList(id, res => {\n          res[0] && (roleId.value = res[0].id)\n        })\n      })\n    }\n    const to = () => {\n      userId = props.data.id\n      loadRoleList(userId)\n      dialog.value = true\n    }\n    const cancel = () => {\n      dialog.value = false\n      form.value.resetFields()\n    }\n    const submit = () => {\n      if (!roleId.value) {\n        warning(\"角色不能为空\")\n        return\n      }\n      loading.value = true\n      const userRoleList = [{ id: roleId.value }]\n      const data = {userId: userId, roleList: userRoleList}\n      updateUserRole(data, () => {\n        success(\"修改成功\")\n        dialog.value = false\n        loading.value = false\n      }).catch(err => {\n        loading.value = false\n        console.log(err.response.data.message)\n      })\n    }\n    return {\n      dialog,\n      loading,\n      roleList,\n      roleId,\n      form,\n      to,\n      cancel,\n      submit\n    }\n  }\n}\n</script>\n\n<style scoped>\n  div{display: inline-block;margin-right: 3px;}\n</style>\n"], "mappings": ";;;EAWWA,KAAK,EAAC;AAAe;;;;;;;;6DAV9BC,YAAA,CAA8DC,oBAAA;IAAnDC,IAAI,EAAC,OAAO;IAACC,IAAI,EAAC,MAAM;IAAEC,OAAK,EAAEC,MAAA,CAAAC;;sBAAI,MAAE,C,iBAAF,IAAE,E;;kCAClDN,YAAA,CAcYO,oBAAA;gBAdQF,MAAA,CAAAG,MAAM;+DAANH,MAAA,CAAAG,MAAM,GAAAC,MAAA;IAAGC,KAAK,EAAE,MAAM;IAAE,gBAAc,EAAd,EAAc;IAACC,KAAK,EAAC;;IAQpDC,MAAM,EAAAC,QAAA,CACf,MAGM,CAHNC,mBAAA,CAGM,OAHNC,UAGM,GAFJf,YAAA,CAAkEC,oBAAA;MAAvDC,IAAI,EAAC,OAAO;MAACC,IAAI,EAAC,MAAM;MAAEC,OAAK,EAAEC,MAAA,CAAAW;;wBAAQ,MAAE,C,iBAAF,IAAE,E;;oCACtDhB,YAAA,CAAwFC,oBAAA;MAA7EC,IAAI,EAAC,OAAO;MAAEe,OAAO,EAAEZ,MAAA,CAAAY,OAAO;MAAEd,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEC,MAAA,CAAAa;;wBAAQ,MAAE,C,iBAAF,IAAE,E;;;sBAVhF,MAMU,CANVlB,YAAA,CAMUmB,kBAAA;MANDC,GAAG,EAAC,MAAM;MAAEC,MAAM,EAAE,IAAI;MAAEnB,IAAI,EAAC,OAAO;MAAC,aAAW,EAAC;;wBAC1D,MAIe,CAJfF,YAAA,CAIesB,uBAAA;QAJDC,KAAyB,EAAzB;UAAA;QAAA,CAAyB;QAACC,KAAK,EAAC;;0BAC5C,MAEY,CAFZxB,YAAA,CAEYyB,oBAAA;UAFDvB,IAAI,EAAC,OAAO;sBAAUG,MAAA,CAAAqB,MAAM;qEAANrB,MAAA,CAAAqB,MAAM,GAAAjB,MAAA;UAAEc,KAAqB,EAArB;YAAA;UAAA,CAAqB;UAACI,WAAW,EAAC;;4BAC9D,MAAiC,E,kBAA5CC,mBAAA,CAA2GC,SAAA,QAAAC,WAAA,CAAxEzB,MAAA,CAAA0B,QAAQ,GAAxBC,IAAI,EAAEC,KAAK;iCAA9BC,YAAA,CAA2GC,oBAAA;cAA7DC,GAAG,EAAEJ,IAAI,CAACK,IAAI,GAAGJ,KAAK;cAAGT,KAAK,EAAEQ,IAAI,CAACK,IAAI;cAAGC,KAAK,EAAEN,IAAI,CAACO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}