{"ast": null, "code": "import { nextTick, ref } from \"vue\";\nimport { getPaper, getRecord } from \"@/api/exam/paper\";\nexport default {\n  name: \"PaperDetail\",\n  props: {\n    examId: {\n      type: Number\n    },\n    signUpId: {\n      type: Number\n    },\n    examChapterSectionId: {\n      type: Number\n    }\n  },\n  setup(props) {\n    const clientHeight = ref(document.documentElement.clientHeight);\n    window.onresize = () => {\n      return (() => {\n        clientHeight.value = document.documentElement.clientHeight;\n      })();\n    };\n    const questionNavList = ref([]);\n    const questionNavBtnList = ref([]);\n    const mainRef = ref(null);\n    const headerRef = ref(null);\n    const position = i => {\n      const anchor = questionNavList.value[i];\n      const scrollTop = anchor.offsetTop - headerRef.value.$el.offsetHeight;\n      nextTick(() => {\n        mainRef.value.$el.scrollTop = scrollTop;\n      });\n    };\n    const paper = ref({});\n    const paperLoading = ref(false);\n    const answerMap = ref({});\n    const answerChangeHandle = (index, item) => {\n      if (item.type === \"subjective\") {\n        if (answerMap.value[item.type + \"_\" + item.id]) {\n          questionNavBtnList.value[index].style.background = \"#415fff\";\n        } else {\n          questionNavBtnList.value[index].style.background = \"#cccccc\";\n        }\n      } else if (item.type === \"multi_choice\") {\n        if (answerMap.value[item.type + \"_\" + item.id].length >= 2) {\n          questionNavBtnList.value[index].style.background = \"#415fff\";\n        } else if (answerMap.value[item.type + \"_\" + item.id].length === 1) {\n          questionNavBtnList.value[index].style.background = \"#fdc90c\";\n        } else {\n          questionNavBtnList.value[index].style.background = \"#cccccc\";\n        }\n      } else if (item.type === \"fill_blank\") {\n        let hasEmpty = false;\n        let hasEmptyCount = 1;\n        for (let i = 0; i < item.blankCount; i++) {\n          if (!answerMap.value[item.type + \"_\" + item.id + \"_\" + (i + 1)]) {\n            hasEmpty = true;\n            hasEmptyCount++;\n          }\n        }\n        if (hasEmpty) {\n          if (hasEmptyCount === item.blankCount) {\n            questionNavBtnList.value[index].style.background = \"#fdc90c\";\n          } else {\n            questionNavBtnList.value[index].style.background = \"#cccccc\";\n          }\n        } else {\n          questionNavBtnList.value[index].style.background = \"#415fff\";\n        }\n      } else {\n        if (answerMap.value[item.type + \"_\" + item.id]) {\n          questionNavBtnList.value[index].style.background = \"#415fff\";\n        }\n      }\n    };\n    const record = ref({});\n    // 加载试卷\n    const loadPaper = () => {\n      paperLoading.value = true;\n      const p = {\n        examId: props.examId,\n        signUpId: props.signUpId,\n        examChapterSectionId: props.examChapterSectionId\n      };\n      getRecord(p, res => {\n        if (!(res && res.id)) {\n          record.value = res;\n          getPaper(props.paperId, res => {\n            if (res.questionList && res.questionList.length) {\n              for (const question of res.questionList) {\n                if (question.type === \"multi_choice\") {\n                  answerMap.value[question.type + \"_\" + question.id] = [];\n                } else if (question.type === \"fill_blank\") {\n                  let thisCount = 0;\n                  question.title.replace(/\\[_\\]/g, function () {\n                    thisCount++;\n                    answerMap.value[question.type + \"_\" + question.id + \"_\" + thisCount] = \"\";\n                    return \"[_]\";\n                  });\n                  question.blankCount = thisCount;\n                } else {\n                  answerMap.value[question.type + \"_\" + question.id] = \"\";\n                }\n              }\n            }\n            paper.value = res;\n            paperLoading.value = false;\n          });\n        } else {\n          record.value = res;\n          answerMap.value = JSON.parse(res.answer);\n          console.log(answerMap.value);\n          paper.value = JSON.parse(res.paper);\n          paperLoading.value = false;\n          nextTick(() => {\n            // 题目导航颜色\n            let i = 0;\n            for (const q of paper.value.questionList) {\n              answerChangeHandle(i, q);\n              i++;\n            }\n          });\n        }\n      });\n    };\n    loadPaper();\n    const formatTitle = item => {\n      if (item.type === \"fill_blank\") {\n        let title = item.title;\n        for (let i = 0; i < item.blankCount; i++) {\n          title = title.replace(\"[_]\", \"[__\" + (i + 1) + \"__]\");\n        }\n        return title;\n      } else {\n        return item.title;\n      }\n    };\n    const colors = [\"#99A9BF\", \"#F7BA2A\", \"#FF9900\"];\n    return {\n      clientHeight,\n      questionNavList,\n      questionNavBtnList,\n      paper,\n      paperLoading,\n      answerMap,\n      position,\n      mainRef,\n      headerRef,\n      answerChangeHandle,\n      formatTitle,\n      colors,\n      record\n    };\n  }\n};", "map": {"version": 3, "names": ["nextTick", "ref", "getPaper", "getRecord", "name", "props", "examId", "type", "Number", "signUpId", "examChapterSectionId", "setup", "clientHeight", "document", "documentElement", "window", "onresize", "value", "questionNavList", "questionNavBtnList", "mainRef", "headerRef", "position", "i", "anchor", "scrollTop", "offsetTop", "$el", "offsetHeight", "paper", "paperLoading", "answerMap", "answerChangeHandle", "index", "item", "id", "style", "background", "length", "hasEmpty", "hasEmptyCount", "blankCount", "record", "loadPaper", "p", "res", "paperId", "questionList", "question", "thisCount", "title", "replace", "JSON", "parse", "answer", "console", "log", "q", "formatTitle", "colors"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/exam/answer/detail/index.vue"], "sourcesContent": ["<template>\n  <div class=\"exam-paper\" v-loading=\"paperLoading\">\n    <el-container :style=\"'height: ' + clientHeight + 'px'\">\n      <el-header class=\"exam-paper-header\" height=\"auto\" ref=\"headerRef\">\n        <div class=\"question-menu-list\" v-if=\"paper.questionList && paper.questionList.length\">\n          <span class=\"question-menu\" v-for=\"(i, index) in paper.questionList.length\" :key=\"i\" @click=\"position(index)\" :ref=\"el => { if (el) questionNavBtnList[index] = el }\">{{i}}</span>\n        </div>\n      </el-header>\n      <el-main ref=\"mainRef\" v-if=\"paper && paper.questionList && paper.questionList.length\">\n        <div class=\"paper-title\">\n          {{paper.title}}\n        </div>\n        <div class=\"paper-base\">\n          <span class=\"paper-base-info\" v-if=\"record && record.id\">状态：{{record.status === 'submitted' ? \"待批改\" : record.status === 'passed' ? \"已通过\" : \"未通过\"}}</span>\n          <span class=\"paper-base-info\">试卷得分：{{record.score}}</span>\n          <span class=\"paper-base-info\">合格分数：{{paper.passScore}}</span>\n          <span class=\"paper-base-info\">试卷总分：{{paper.score}}</span>\n          <span class=\"paper-base-info\">考试时长：{{paper.limitTime}} 分钟</span>\n        </div>\n        <div class=\"paper-question-list\">\n          <div class=\"paper-question\" v-for=\"(item, index) in paper.questionList\" :key=\"index\" :ref=\"el => { if (el) questionNavList[index] = el }\">\n            <div class=\"title\">\n              {{index + 1}}. {{formatTitle(item)}}\n            </div>\n            <div class=\"question-body\">\n              <div v-if=\"item.type === 'subjective'\">\n                <el-input :readonly=\"true\" type=\"textarea\" @blur=\"answerChangeHandle(index, item)\" :rows=\"10\" v-model=\"answerMap[item.type + '_' + item.id]\"/>\n              </div>\n              <div v-if=\"item.type === 'fill_blank'\">\n                <div v-for=\"i in item.blankCount\" :key=\"i\" style=\"display: flex;margin: 10px 0;\">\n                  <div style=\"width: 20px;padding: 0 10px;\">{{i}}.</div>\n                  <el-input :readonly=\"true\" @blur=\"answerChangeHandle(index, item)\" size=\"small\" v-model=\"answerMap[item.type + '_' + item.id + '_' + i]\"/>\n                </div>\n              </div>\n              <div v-else-if=\"item.options\">\n                <el-checkbox-group v-if=\"item.type === 'multi_choice'\" v-model=\"answerMap[item.type + '_' + item.id]\" @change=\"answerChangeHandle(index, item)\">\n                  <el-checkbox :disabled=\"true\" :label=\"o.key\" v-for=\"o in JSON.parse(item.options)\" :key=\"o.key\">{{o.key}}. {{o.value}}</el-checkbox>\n                </el-checkbox-group>\n                <div v-else v-for=\"o in JSON.parse(item.options)\" :key=\"o.key\">\n                  <el-radio :disabled=\"true\" @change=\"answerChangeHandle(index, item)\" v-model=\"answerMap[item.type + '_' + item.id]\" :label=\"o.key\">{{o.key}}. {{o.value}}</el-radio>\n                </div>\n              </div>\n              <div class=\"answer-box\">\n                <div class=\"answer-item\">\n                  <div class=\"answer-info-label\">结果：</div>\n                  <div class=\"answer-info-value\">\n                    <el-button style=\"padding: 3px 10px;\" v-if=\"item.result\" size=\"small\" type=\"success\">对</el-button>\n                    <el-button style=\"padding: 3px 10px;\" v-else size=\"small\" type=\"danger\">错</el-button>\n                  </div>\n                </div>\n                <div class=\"answer-item\">\n                  <div class=\"answer-info-label\">得分：</div>\n                  <div class=\"answer-info-value\" style=\"color: green;font-size: 20px;font-weight: 500;\">{{item.scored || 0}}</div>\n                </div>\n                <div class=\"answer-item\">\n                  <div class=\"answer-info-label\">分数：</div>\n                  <div class=\"answer-info-value\">{{item.score}}</div>\n                </div>\n                <div class=\"answer-item\">\n                  <div class=\"answer-info-label\">难度：</div>\n                  <div class=\"answer-info-value\">\n                    <el-rate :disabled=\"true\" v-model=\"item.difficulty\" :colors=\"colors\"></el-rate>\n                  </div>\n                </div>\n                <div class=\"answer-item\">\n                  <div class=\"answer-info-label\" style=\"vertical-align: top;\">正确答案：</div>\n                  <div class=\"answer-info-value\">\n                    <div v-if=\"item.type === 'fill_blank'\">\n                      <div v-for=\"(blank, i) in item.referenceAnswer.split('[_]')\" :key=\"i\">\n                        填空 {{i + 1}}. {{blank}}\n                      </div>\n                    </div>\n                    <div v-else>\n                      {{item.referenceAnswer}}\n                    </div>\n                  </div>\n                </div>\n                <div class=\"answer-item\">\n                  <div class=\"answer-info-label\">解析：</div>\n                  <div class=\"answer-info-value\">\n                    {{item.referenceAnswerNote}}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </el-main>\n    </el-container>\n  </div>\n</template>\n\n<script>\nimport {nextTick, ref} from \"vue\"\nimport {getPaper, getRecord} from \"@/api/exam/paper\"\nexport default {\n  name: \"PaperDetail\",\n  props: {\n    examId: {\n      type: Number\n    },\n    signUpId: {\n      type: Number\n    },\n    examChapterSectionId: {\n      type: Number\n    },\n  },\n  setup(props) {\n    const clientHeight = ref(document.documentElement.clientHeight)\n    window.onresize = () => {\n      return (() => {\n        clientHeight.value = document.documentElement.clientHeight;\n      })()\n    }\n    const questionNavList = ref([])\n    const questionNavBtnList = ref([])\n    const mainRef = ref(null)\n    const headerRef = ref(null)\n    const position = (i) => {\n      const anchor = questionNavList.value[i]\n      const scrollTop = anchor.offsetTop - headerRef.value.$el.offsetHeight\n      nextTick(() => {\n        mainRef.value.$el.scrollTop = scrollTop\n      })\n    }\n    const paper = ref({})\n    const paperLoading = ref(false)\n    const answerMap = ref({})\n    const answerChangeHandle = (index, item) => {\n      if (item.type === \"subjective\") {\n        if (answerMap.value[item.type + \"_\" + item.id]) {\n          questionNavBtnList.value[index].style.background = \"#415fff\";\n        } else {\n          questionNavBtnList.value[index].style.background = \"#cccccc\";\n        }\n      } else if (item.type === \"multi_choice\") {\n        if (answerMap.value[item.type + \"_\" + item.id].length >= 2) {\n          questionNavBtnList.value[index].style.background = \"#415fff\";\n        } else if (answerMap.value[item.type + \"_\" + item.id].length === 1) {\n          questionNavBtnList.value[index].style.background = \"#fdc90c\";\n        } else {\n          questionNavBtnList.value[index].style.background = \"#cccccc\";\n        }\n      } else if (item.type === \"fill_blank\") {\n        let hasEmpty = false;\n        let hasEmptyCount = 1;\n        for (let i = 0; i < item.blankCount; i++) {\n          if (!answerMap.value[item.type + \"_\" + item.id + \"_\" + (i + 1)]) {\n            hasEmpty = true;\n            hasEmptyCount++;\n          }\n        }\n        if (hasEmpty) {\n          if (hasEmptyCount === item.blankCount) {\n            questionNavBtnList.value[index].style.background = \"#fdc90c\";\n          } else {\n            questionNavBtnList.value[index].style.background = \"#cccccc\";\n          }\n        } else {\n          questionNavBtnList.value[index].style.background = \"#415fff\";\n        }\n      } else {\n        if (answerMap.value[item.type + \"_\" + item.id]) {\n          questionNavBtnList.value[index].style.background = \"#415fff\";\n        }\n      }\n    }\n    const record = ref({});\n    // 加载试卷\n    const loadPaper = () => {\n      paperLoading.value = true;\n      const p = {\n        examId: props.examId,\n        signUpId: props.signUpId,\n        examChapterSectionId: props.examChapterSectionId\n      }\n      getRecord(p, (res) => {\n        if (!(res && res.id)) {\n          record.value = res\n          getPaper(props.paperId, res => {\n            if (res.questionList && res.questionList.length) {\n              for (const question of res.questionList) {\n                if (question.type === \"multi_choice\") {\n                  answerMap.value[question.type + \"_\" + question.id] = []\n                } else if (question.type === \"fill_blank\") {\n                  let thisCount = 0;\n                  question.title.replace(/\\[_\\]/g, function () {\n                    thisCount++;\n                    answerMap.value[question.type + \"_\" + question.id + \"_\" + thisCount] = \"\"\n                    return \"[_]\"\n                  });\n                  question.blankCount = thisCount\n                } else {\n                  answerMap.value[question.type + \"_\" + question.id] = \"\"\n                }\n              }\n            }\n            paper.value = res\n            paperLoading.value = false;\n          })\n        } else {\n          record.value = res\n          answerMap.value = JSON.parse(res.answer)\n          console.log(answerMap.value)\n          paper.value = JSON.parse(res.paper)\n          paperLoading.value = false;\n          nextTick(() => {\n            // 题目导航颜色\n            let i = 0\n            for (const q of paper.value.questionList) {\n              answerChangeHandle(i, q);\n              i++;\n            }\n          })\n        }\n      })\n    }\n    loadPaper()\n    const formatTitle = (item) => {\n      if (item.type === \"fill_blank\") {\n        let title = item.title\n        for (let i = 0; i < item.blankCount; i++) {\n          title = title.replace(\"[_]\", \"[__\" + (i + 1) + \"__]\");\n        }\n        return title\n      } else {\n        return item.title\n      }\n    }\n    const colors = [\"#99A9BF\", \"#F7BA2A\", \"#FF9900\"]\n    return {\n      clientHeight,\n      questionNavList,\n      questionNavBtnList,\n      paper,\n      paperLoading,\n      answerMap,\n      position,\n      mainRef,\n      headerRef,\n      answerChangeHandle,\n      formatTitle,\n      colors,\n      record\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.exam-paper {\n  .exam-paper-header {\n    background: rgba(65, 95, 255, .1);\n    .question-menu-list {\n      .question-menu {\n        background: #cccccc;\n        padding: 3px 10px;\n        vertical-align: middle;\n        margin: 5px 10px 5px 0;\n        border-radius: 4px;\n        float: left;\n        cursor: pointer;\n        color: #ffffff;\n        &:not(.countdown):hover {\n          background: $--color-primary;\n        }\n      }\n      .countdown {\n        float: right;\n        background: none;\n        margin-right: 0;\n        cursor: text;\n        color: #222222;\n      }\n    }\n  }\n  .el-main {\n    background: #ffffff;\n    .paper-title {\n      font-size: 30px;\n      font-weight: 500;\n      text-align: center;\n    }\n    .paper-base {\n      text-align: center;\n      margin: 20px 0;\n      .paper-base-info {\n        padding: 0 20px;\n      }\n    }\n    .paper-question-list {\n      .paper-question {\n        padding: 20px 0;\n        line-height: 36px;\n        .question-body {\n          ::v-deep .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {\n            background-color: $--color-primary;\n          }\n          ::v-deep .el-radio__input.is-disabled.is-checked .el-radio__inner {\n            background-color: $--color-primary;\n          }\n          ::v-deep .el-checkbox-group {\n            .el-checkbox__label {\n              color: #333;\n            }\n          }\n          ::v-deep .el-radio__label {\n            color: #333;\n          }\n        }\n        .answer-box {\n          margin-top: 20px;\n          .answer-item {\n            .answer-info-label {\n              display: inline-block;\n            }\n            .answer-info-value {\n              display: inline-block;\n              ::v-deep .el-rate {\n                line-height: 16px;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"], "mappings": "AA6FA,SAAQA,QAAQ,EAAEC,GAAG,QAAO,KAAI;AAChC,SAAQC,QAAQ,EAAEC,SAAS,QAAO,kBAAiB;AACnD,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,KAAK,EAAE;IACLC,MAAM,EAAE;MACNC,IAAI,EAAEC;IACR,CAAC;IACDC,QAAQ,EAAE;MACRF,IAAI,EAAEC;IACR,CAAC;IACDE,oBAAoB,EAAE;MACpBH,IAAI,EAAEC;IACR;EACF,CAAC;EACDG,KAAKA,CAACN,KAAK,EAAE;IACX,MAAMO,YAAW,GAAIX,GAAG,CAACY,QAAQ,CAACC,eAAe,CAACF,YAAY;IAC9DG,MAAM,CAACC,QAAO,GAAI,MAAM;MACtB,OAAO,CAAC,MAAM;QACZJ,YAAY,CAACK,KAAI,GAAIJ,QAAQ,CAACC,eAAe,CAACF,YAAY;MAC5D,CAAC,GAAE;IACL;IACA,MAAMM,eAAc,GAAIjB,GAAG,CAAC,EAAE;IAC9B,MAAMkB,kBAAiB,GAAIlB,GAAG,CAAC,EAAE;IACjC,MAAMmB,OAAM,GAAInB,GAAG,CAAC,IAAI;IACxB,MAAMoB,SAAQ,GAAIpB,GAAG,CAAC,IAAI;IAC1B,MAAMqB,QAAO,GAAKC,CAAC,IAAK;MACtB,MAAMC,MAAK,GAAIN,eAAe,CAACD,KAAK,CAACM,CAAC;MACtC,MAAME,SAAQ,GAAID,MAAM,CAACE,SAAQ,GAAIL,SAAS,CAACJ,KAAK,CAACU,GAAG,CAACC,YAAW;MACpE5B,QAAQ,CAAC,MAAM;QACboB,OAAO,CAACH,KAAK,CAACU,GAAG,CAACF,SAAQ,GAAIA,SAAQ;MACxC,CAAC;IACH;IACA,MAAMI,KAAI,GAAI5B,GAAG,CAAC,CAAC,CAAC;IACpB,MAAM6B,YAAW,GAAI7B,GAAG,CAAC,KAAK;IAC9B,MAAM8B,SAAQ,GAAI9B,GAAG,CAAC,CAAC,CAAC;IACxB,MAAM+B,kBAAiB,GAAIA,CAACC,KAAK,EAAEC,IAAI,KAAK;MAC1C,IAAIA,IAAI,CAAC3B,IAAG,KAAM,YAAY,EAAE;QAC9B,IAAIwB,SAAS,CAACd,KAAK,CAACiB,IAAI,CAAC3B,IAAG,GAAI,GAAE,GAAI2B,IAAI,CAACC,EAAE,CAAC,EAAE;UAC9ChB,kBAAkB,CAACF,KAAK,CAACgB,KAAK,CAAC,CAACG,KAAK,CAACC,UAAS,GAAI,SAAS;QAC9D,OAAO;UACLlB,kBAAkB,CAACF,KAAK,CAACgB,KAAK,CAAC,CAACG,KAAK,CAACC,UAAS,GAAI,SAAS;QAC9D;MACF,OAAO,IAAIH,IAAI,CAAC3B,IAAG,KAAM,cAAc,EAAE;QACvC,IAAIwB,SAAS,CAACd,KAAK,CAACiB,IAAI,CAAC3B,IAAG,GAAI,GAAE,GAAI2B,IAAI,CAACC,EAAE,CAAC,CAACG,MAAK,IAAK,CAAC,EAAE;UAC1DnB,kBAAkB,CAACF,KAAK,CAACgB,KAAK,CAAC,CAACG,KAAK,CAACC,UAAS,GAAI,SAAS;QAC9D,OAAO,IAAIN,SAAS,CAACd,KAAK,CAACiB,IAAI,CAAC3B,IAAG,GAAI,GAAE,GAAI2B,IAAI,CAACC,EAAE,CAAC,CAACG,MAAK,KAAM,CAAC,EAAE;UAClEnB,kBAAkB,CAACF,KAAK,CAACgB,KAAK,CAAC,CAACG,KAAK,CAACC,UAAS,GAAI,SAAS;QAC9D,OAAO;UACLlB,kBAAkB,CAACF,KAAK,CAACgB,KAAK,CAAC,CAACG,KAAK,CAACC,UAAS,GAAI,SAAS;QAC9D;MACF,OAAO,IAAIH,IAAI,CAAC3B,IAAG,KAAM,YAAY,EAAE;QACrC,IAAIgC,QAAO,GAAI,KAAK;QACpB,IAAIC,aAAY,GAAI,CAAC;QACrB,KAAK,IAAIjB,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIW,IAAI,CAACO,UAAU,EAAElB,CAAC,EAAE,EAAE;UACxC,IAAI,CAACQ,SAAS,CAACd,KAAK,CAACiB,IAAI,CAAC3B,IAAG,GAAI,GAAE,GAAI2B,IAAI,CAACC,EAAC,GAAI,GAAE,IAAKZ,CAAA,GAAI,CAAC,CAAC,CAAC,EAAE;YAC/DgB,QAAO,GAAI,IAAI;YACfC,aAAa,EAAE;UACjB;QACF;QACA,IAAID,QAAQ,EAAE;UACZ,IAAIC,aAAY,KAAMN,IAAI,CAACO,UAAU,EAAE;YACrCtB,kBAAkB,CAACF,KAAK,CAACgB,KAAK,CAAC,CAACG,KAAK,CAACC,UAAS,GAAI,SAAS;UAC9D,OAAO;YACLlB,kBAAkB,CAACF,KAAK,CAACgB,KAAK,CAAC,CAACG,KAAK,CAACC,UAAS,GAAI,SAAS;UAC9D;QACF,OAAO;UACLlB,kBAAkB,CAACF,KAAK,CAACgB,KAAK,CAAC,CAACG,KAAK,CAACC,UAAS,GAAI,SAAS;QAC9D;MACF,OAAO;QACL,IAAIN,SAAS,CAACd,KAAK,CAACiB,IAAI,CAAC3B,IAAG,GAAI,GAAE,GAAI2B,IAAI,CAACC,EAAE,CAAC,EAAE;UAC9ChB,kBAAkB,CAACF,KAAK,CAACgB,KAAK,CAAC,CAACG,KAAK,CAACC,UAAS,GAAI,SAAS;QAC9D;MACF;IACF;IACA,MAAMK,MAAK,GAAIzC,GAAG,CAAC,CAAC,CAAC,CAAC;IACtB;IACA,MAAM0C,SAAQ,GAAIA,CAAA,KAAM;MACtBb,YAAY,CAACb,KAAI,GAAI,IAAI;MACzB,MAAM2B,CAAA,GAAI;QACRtC,MAAM,EAAED,KAAK,CAACC,MAAM;QACpBG,QAAQ,EAAEJ,KAAK,CAACI,QAAQ;QACxBC,oBAAoB,EAAEL,KAAK,CAACK;MAC9B;MACAP,SAAS,CAACyC,CAAC,EAAGC,GAAG,IAAK;QACpB,IAAI,EAAEA,GAAE,IAAKA,GAAG,CAACV,EAAE,CAAC,EAAE;UACpBO,MAAM,CAACzB,KAAI,GAAI4B,GAAE;UACjB3C,QAAQ,CAACG,KAAK,CAACyC,OAAO,EAAED,GAAE,IAAK;YAC7B,IAAIA,GAAG,CAACE,YAAW,IAAKF,GAAG,CAACE,YAAY,CAACT,MAAM,EAAE;cAC/C,KAAK,MAAMU,QAAO,IAAKH,GAAG,CAACE,YAAY,EAAE;gBACvC,IAAIC,QAAQ,CAACzC,IAAG,KAAM,cAAc,EAAE;kBACpCwB,SAAS,CAACd,KAAK,CAAC+B,QAAQ,CAACzC,IAAG,GAAI,GAAE,GAAIyC,QAAQ,CAACb,EAAE,IAAI,EAAC;gBACxD,OAAO,IAAIa,QAAQ,CAACzC,IAAG,KAAM,YAAY,EAAE;kBACzC,IAAI0C,SAAQ,GAAI,CAAC;kBACjBD,QAAQ,CAACE,KAAK,CAACC,OAAO,CAAC,QAAQ,EAAE,YAAY;oBAC3CF,SAAS,EAAE;oBACXlB,SAAS,CAACd,KAAK,CAAC+B,QAAQ,CAACzC,IAAG,GAAI,GAAE,GAAIyC,QAAQ,CAACb,EAAC,GAAI,GAAE,GAAIc,SAAS,IAAI,EAAC;oBACxE,OAAO,KAAI;kBACb,CAAC,CAAC;kBACFD,QAAQ,CAACP,UAAS,GAAIQ,SAAQ;gBAChC,OAAO;kBACLlB,SAAS,CAACd,KAAK,CAAC+B,QAAQ,CAACzC,IAAG,GAAI,GAAE,GAAIyC,QAAQ,CAACb,EAAE,IAAI,EAAC;gBACxD;cACF;YACF;YACAN,KAAK,CAACZ,KAAI,GAAI4B,GAAE;YAChBf,YAAY,CAACb,KAAI,GAAI,KAAK;UAC5B,CAAC;QACH,OAAO;UACLyB,MAAM,CAACzB,KAAI,GAAI4B,GAAE;UACjBd,SAAS,CAACd,KAAI,GAAImC,IAAI,CAACC,KAAK,CAACR,GAAG,CAACS,MAAM;UACvCC,OAAO,CAACC,GAAG,CAACzB,SAAS,CAACd,KAAK;UAC3BY,KAAK,CAACZ,KAAI,GAAImC,IAAI,CAACC,KAAK,CAACR,GAAG,CAAChB,KAAK;UAClCC,YAAY,CAACb,KAAI,GAAI,KAAK;UAC1BjB,QAAQ,CAAC,MAAM;YACb;YACA,IAAIuB,CAAA,GAAI;YACR,KAAK,MAAMkC,CAAA,IAAK5B,KAAK,CAACZ,KAAK,CAAC8B,YAAY,EAAE;cACxCf,kBAAkB,CAACT,CAAC,EAAEkC,CAAC,CAAC;cACxBlC,CAAC,EAAE;YACL;UACF,CAAC;QACH;MACF,CAAC;IACH;IACAoB,SAAS,EAAC;IACV,MAAMe,WAAU,GAAKxB,IAAI,IAAK;MAC5B,IAAIA,IAAI,CAAC3B,IAAG,KAAM,YAAY,EAAE;QAC9B,IAAI2C,KAAI,GAAIhB,IAAI,CAACgB,KAAI;QACrB,KAAK,IAAI3B,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIW,IAAI,CAACO,UAAU,EAAElB,CAAC,EAAE,EAAE;UACxC2B,KAAI,GAAIA,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,KAAI,IAAK5B,CAAA,GAAI,CAAC,IAAI,KAAK,CAAC;QACvD;QACA,OAAO2B,KAAI;MACb,OAAO;QACL,OAAOhB,IAAI,CAACgB,KAAI;MAClB;IACF;IACA,MAAMS,MAAK,GAAI,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;IAC/C,OAAO;MACL/C,YAAY;MACZM,eAAe;MACfC,kBAAkB;MAClBU,KAAK;MACLC,YAAY;MACZC,SAAS;MACTT,QAAQ;MACRF,OAAO;MACPC,SAAS;MACTW,kBAAkB;MAClB0B,WAAW;MACXC,MAAM;MACNjB;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}