{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode } from \"vue\";\nconst _hoisted_1 = {\n  key: 0\n};\nconst _hoisted_2 = [\"src\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Plus = _resolveComponent(\"Plus\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_upload = _resolveComponent(\"el-upload\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createVNode(_component_el_upload, {\n    action: $props.uploadUrl,\n    limit: $props.limit,\n    \"on-exceed\": $options.handleExceed,\n    \"file-list\": $props.files,\n    \"before-upload\": $props.onBeforeUpload,\n    \"on-error\": $options.onError,\n    \"on-success\": $options.onSuccess,\n    \"on-preview\": $options.handlePictureCardPreview,\n    \"on-remove\": $options.onRemove,\n    data: $props.formData,\n    \"on-change\": $options.onChange,\n    class: _normalizeClass({\n      hide: $setup.hideUpload || $props.files && $props.files.length === $props.limit\n    }),\n    \"show-file-list\": $props.showFileList,\n    headers: $options.headers,\n    multiple: \"\",\n    accept: \"accept\",\n    \"list-type\": $props.listType\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Plus)]),\n      _: 1\n    }), $props.listType !== 'picture-card' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_1, \"选择文件\")) : _createCommentVNode(\"\", true)]),\n    _: 1\n  }, 8, [\"action\", \"limit\", \"on-exceed\", \"file-list\", \"before-upload\", \"on-error\", \"on-success\", \"on-preview\", \"on-remove\", \"data\", \"on-change\", \"class\", \"show-file-list\", \"headers\", \"list-type\"]), _createVNode(_component_el_dialog, {\n    modelValue: $setup.previewVisible,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.previewVisible = $event)\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"img\", {\n      alt: \"\",\n      width: \"100%\",\n      src: $setup.previewImageUrl\n    }, null, 8, _hoisted_2)]),\n    _: 1\n  }, 8, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["_createElementBlock", "_createVNode", "_component_el_upload", "action", "$props", "uploadUrl", "limit", "$options", "handleExceed", "files", "onBeforeUpload", "onError", "onSuccess", "handlePictureCardPreview", "onRemove", "data", "formData", "onChange", "class", "_normalizeClass", "hide", "$setup", "hideUpload", "length", "showFileList", "headers", "multiple", "accept", "listType", "_component_el_icon", "_component_Plus", "_hoisted_1", "_component_el_dialog", "previewVisible", "$event", "_createElementVNode", "alt", "width", "src", "previewImageUrl"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/components/Uplaod/index.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-upload\n      :action=\"uploadUrl\"\n      :limit=\"limit\"\n      :on-exceed=\"handleExceed\"\n      :file-list=\"files\"\n      :before-upload=\"onBeforeUpload\"\n      :on-error=\"onError\"\n      :on-success=\"onSuccess\"\n      :on-preview=\"handlePictureCardPreview\"\n      :on-remove=\"onRemove\"\n      :data=\"formData\"\n      :on-change=\"onChange\"\n      :class=\"{hide: hideUpload || files && files.length === limit}\"\n      :show-file-list=\"showFileList\"\n      :headers=\"headers\"\n      multiple accept=\"accept\"\n      :list-type=\"listType\">\n      <el-icon><Plus /></el-icon>\n      <span v-if=\"listType !== 'picture-card'\">选择文件</span>\n    </el-upload>\n    <el-dialog v-model=\"previewVisible\">\n      <img alt=\"\" width=\"100%\" :src=\"previewImageUrl\"/>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import {deleteFile} from \"../../api/oss/oss\";\n  import {getToken} from \"../../util/tokenUtils\";\n  import {error} from \"../../util/tipsUtils\";\n  import {ref} from \"vue\";\n  export default {\n    name: \"UploadIndex\",\n    computed:{\n      headers(){\n        return {\n          \"Authorization\": \"Bearer \" + getToken()\n        }\n      }\n    },\n    props: {\n      // 上传地址\n      uploadUrl: {\n        type: String,\n        required: true\n      },\n      // 限制多少个文件\n      limit: {\n        type: Number,\n        default: 1\n      },\n      //限制格式 image/jpeg,image/gif,image/png\n      accept: {\n        type: String,\n        default: \"image/jpeg,image/gif,image/png\"\n      },\n      files: {\n        type: Array,\n        default: ()=>[]\n      },\n      onBeforeUpload: {\n        type: Function,\n        default: function () {\n\n        }\n      },\n      onUploadSuccess: {\n        type: Function,\n        default: function () {\n\n        }\n      },\n      onUploadRemove: {\n        type: Function,\n        default: function () {\n\n        }\n      },\n      onUploadError: {\n        type: Function,\n        default: function (res) {\n          error(\"上传失败\")\n          console.log(res)\n        }\n      },\n      formData: {\n        type: Object,\n        default: ()=>{}\n      },\n      showFileList: {\n        type: Boolean,\n        default: true\n      },\n      alwaysShowUpload: {\n        type: Boolean,\n        default: false\n      },\n      listType: {\n        type: String,\n        default: \"picture-card\"\n      }\n    },\n    setup() {\n      return {\n        previewImageUrl: ref(\"\"),\n        previewVisible: ref(false),\n        hideUpload: ref(false)\n      }\n    },\n    methods: {\n      // 上传数量超过限制\n      handleExceed() {\n        error(\"上传数量超过限制\")\n      },\n      // 查看大图\n      handlePictureCardPreview(file) {\n        this.previewImageUrl = file.url;\n        this.previewVisible = true;\n      },\n      onRemove(file, fileList) {\n        if (!this.alwaysShowUpload) {\n          this.hideUpload = fileList.length >= this.limit;\n        }\n        let url = file.url\n        if (file.response && file.response.data) {\n          url = file.response.data\n        }\n        if (url) {\n          deleteFile(url, () => {\n            this.$message.success(\"删除成功\")\n          })\n        }\n        this.onUploadRemove(file, fileList);\n      },\n      onChange(file, fileList) {\n        if (!this.alwaysShowUpload) {\n          this.hideUpload = fileList.length >= this.limit;\n        }\n      },\n      onError(err, file, fileList) {\n        if (!this.alwaysShowUpload) {\n          this.hideUpload = fileList.length >= this.limit;\n        }\n        this.onUploadError(err, file, fileList);\n      },\n      onSuccess(res, file, fileList) {\n        if (!this.alwaysShowUpload) {\n          this.hideUpload = fileList.length >= this.limit;\n        }\n        this.onUploadSuccess(res, file, fileList)\n      }\n    }\n  }\n</script>\n<style>\n  .hide div.el-upload--picture-card {\n    display: none;\n  }\n</style>\n"], "mappings": ";;;;;;;;;;uBACEA,mBAAA,CAwBM,cAvBJC,YAAA,CAmBYC,oBAAA;IAlBTC,MAAM,EAAEC,MAAA,CAAAC,SAAS;IACjBC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACZ,WAAS,EAAEC,QAAA,CAAAC,YAAY;IACvB,WAAS,EAAEJ,MAAA,CAAAK,KAAK;IAChB,eAAa,EAAEL,MAAA,CAAAM,cAAc;IAC7B,UAAQ,EAAEH,QAAA,CAAAI,OAAO;IACjB,YAAU,EAAEJ,QAAA,CAAAK,SAAS;IACrB,YAAU,EAAEL,QAAA,CAAAM,wBAAwB;IACpC,WAAS,EAAEN,QAAA,CAAAO,QAAQ;IACnBC,IAAI,EAAEX,MAAA,CAAAY,QAAQ;IACd,WAAS,EAAET,QAAA,CAAAU,QAAQ;IACnBC,KAAK,EAAAC,eAAA;MAAAC,IAAA,EAASC,MAAA,CAAAC,UAAU,IAAIlB,MAAA,CAAAK,KAAK,IAAIL,MAAA,CAAAK,KAAK,CAACc,MAAM,KAAKnB,MAAA,CAAAE;IAAK;IAC3D,gBAAc,EAAEF,MAAA,CAAAoB,YAAY;IAC5BC,OAAO,EAAElB,QAAA,CAAAkB,OAAO;IACjBC,QAAQ,EAAR,EAAQ;IAACC,MAAM,EAAC,QAAQ;IACvB,WAAS,EAAEvB,MAAA,CAAAwB;;sBACZ,MAA2B,CAA3B3B,YAAA,CAA2B4B,kBAAA;wBAAlB,MAAQ,CAAR5B,YAAA,CAAQ6B,eAAA,E;;QACL1B,MAAA,CAAAwB,QAAQ,uB,cAApB5B,mBAAA,CAAoD,QAAA+B,UAAA,EAAX,MAAI,K;;sMAE/C9B,YAAA,CAEY+B,oBAAA;gBAFQX,MAAA,CAAAY,cAAc;+DAAdZ,MAAA,CAAAY,cAAc,GAAAC,MAAA;;sBAChC,MAAiD,CAAjDC,mBAAA,CAAiD;MAA5CC,GAAG,EAAC,EAAE;MAACC,KAAK,EAAC,MAAM;MAAEC,GAAG,EAAEjB,MAAA,CAAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}