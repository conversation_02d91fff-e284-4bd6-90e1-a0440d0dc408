{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, normalizeStyle as _normalizeStyle, resolveDirective as _resolveDirective, withDirectives as _withDirectives, TransitionGroup as _TransitionGroup, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-04a6150f\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"app-container\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"base\"\n};\nconst _hoisted_3 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", {\n  class: \"upload-image-tips\"\n}, \"图片建议：尺寸 1920 x 1200 像素，大小7M以下\", -1 /* HOISTED */));\nconst _hoisted_4 = {\n  style: {\n    \"margin\": \"50px auto\",\n    \"text-align\": \"center\"\n  }\n};\nconst _hoisted_5 = {\n  key: 1,\n  class: \"content\"\n};\nconst _hoisted_6 = {\n  class: \"content-header\"\n};\nconst _hoisted_7 = {\n  style: {\n    \"margin-top\": \"20px\"\n  }\n};\nconst _hoisted_8 = {\n  key: 0,\n  class: \"tips\"\n};\nconst _hoisted_9 = {\n  class: \"clearfix\",\n  style: {\n    \"line-height\": \"28px\"\n  }\n};\nconst _hoisted_10 = {\n  class: \"opt-btn\"\n};\nconst _hoisted_11 = {\n  key: 0,\n  class: \"tips\"\n};\nconst _hoisted_12 = {\n  class: \"video-box\"\n};\nconst _hoisted_13 = [\"src\"];\nconst _hoisted_14 = [\"onClick\"];\nconst _hoisted_15 = {\n  class: \"opt-btn\"\n};\nconst _hoisted_16 = {\n  key: 2,\n  class: \"homework\"\n};\nconst _hoisted_17 = {\n  style: {\n    \"margin\": \"50px auto\",\n    \"text-align\": \"center\"\n  }\n};\nconst _hoisted_18 = {\n  key: 3,\n  class: \"certificate\"\n};\nconst _hoisted_19 = {\n  class: \"certificate-select\"\n};\nconst _hoisted_20 = {\n  class: \"certificate-select-main\"\n};\nconst _hoisted_21 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"certificate-select-label\"\n}, \"选择证书：\", -1 /* HOISTED */));\nconst _hoisted_22 = {\n  class: \"certificate-select-value\"\n};\nconst _hoisted_23 = {\n  class: \"certificate-select-btn\"\n};\nconst _hoisted_24 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_25 = {\n  style: {\n    \"margin\": \"50px auto\",\n    \"text-align\": \"center\"\n  }\n};\nconst _hoisted_26 = {\n  key: 4,\n  class: \"publish\"\n};\nconst _hoisted_27 = {\n  class: \"publish-box\"\n};\nconst _hoisted_28 = {\n  key: 0,\n  class: \"current-status\"\n};\nconst _hoisted_29 = {\n  class: \"btn-list\"\n};\nconst _hoisted_30 = {\n  class: \"step-list\"\n};\nconst _hoisted_31 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"title\"\n}, \" 步骤导航 \", -1 /* HOISTED */));\nconst _hoisted_32 = {\n  key: 0,\n  class: \"draggable\"\n};\nconst _hoisted_33 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"title\"\n}, \" 章节目录（拖动排序） \", -1 /* HOISTED */));\nconst _hoisted_34 = {\n  class: \"item-title\"\n};\nconst _hoisted_35 = {\n  key: 0,\n  class: \"sub-item-list\"\n};\nconst _hoisted_36 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_37 = [\"src\"];\nconst _hoisted_38 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_radio = _resolveComponent(\"el-radio\");\n  const _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  const _component_el_date_picker = _resolveComponent(\"el-date-picker\");\n  const _component_el_cascader = _resolveComponent(\"el-cascader\");\n  const _component_el_input_number = _resolveComponent(\"el-input-number\");\n  const _component_upload = _resolveComponent(\"upload\");\n  const _component_wang_editor = _resolveComponent(\"wang-editor\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_certificate_preview = _resolveComponent(\"certificate-preview\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_step = _resolveComponent(\"el-step\");\n  const _component_el_steps = _resolveComponent(\"el-steps\");\n  const _component_draggable = _resolveComponent(\"draggable\");\n  const _component_el_affix = _resolveComponent(\"el-affix\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_certificate_template_list = _resolveComponent(\"certificate-template-list\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_row, null, {\n    default: _withCtx(() => [_createVNode(_component_el_col, {\n      span: 20\n    }, {\n      default: _withCtx(() => [$setup.showStep === 'base' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createVNode(_component_el_form, {\n        model: $setup.lesson,\n        rules: $setup.lessonRules,\n        ref: \"lessonRef\",\n        \"label-width\": \"120px\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_form_item, {\n          label: \"名称：\",\n          prop: \"name\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_input, {\n            size: \"small\",\n            modelValue: $setup.lesson.name,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.lesson.name = $event),\n            placeholder: \"请输入标题\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, {\n          label: \"有效期：\",\n          prop: \"timeType\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_radio_group, {\n            modelValue: $setup.lesson.timeType,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.lesson.timeType = $event)\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_radio, {\n              label: \"infinite\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(\"永久有效\")]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_el_radio, {\n              label: \"customize\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(\"自定义\")]),\n              _: 1 /* STABLE */\n            })]),\n\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])]),\n          _: 1 /* STABLE */\n        }), $setup.lesson.timeType !== 'infinite' ? (_openBlock(), _createBlock(_component_el_form_item, {\n          key: 0,\n          label: \"开始时间：\",\n          prop: \"startTime\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_date_picker, {\n            modelValue: $setup.lesson.startTime,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.lesson.startTime = $event),\n            type: \"datetime\",\n            placeholder: \"选择开始时间\",\n            class: \"input-text\",\n            \"default-time\": new Date(2000, 0, 1, 0, 0, 0),\n            size: \"small\",\n            onChange: $setup.changeStartTime,\n            style: {\n              \"width\": \"100%\"\n            }\n          }, null, 8 /* PROPS */, [\"modelValue\", \"default-time\", \"onChange\"])]),\n          _: 1 /* STABLE */\n        })) : _createCommentVNode(\"v-if\", true), $setup.lesson.timeType !== 'infinite' ? (_openBlock(), _createBlock(_component_el_form_item, {\n          key: 1,\n          label: \"结束时间：\",\n          prop: \"endTime\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_date_picker, {\n            modelValue: $setup.lesson.endTime,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.lesson.endTime = $event),\n            type: \"datetime\",\n            placeholder: \"选择结束时间\",\n            class: \"input-text\",\n            \"default-time\": new Date(2000, 0, 1, 22, 0, 0),\n            size: \"small\",\n            onChange: $setup.changeEndTime,\n            style: {\n              \"width\": \"100%\"\n            }\n          }, null, 8 /* PROPS */, [\"modelValue\", \"default-time\", \"onChange\"])]),\n          _: 1 /* STABLE */\n        })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_form_item, {\n          label: \"分类：\",\n          prop: \"cidList\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_cascader, {\n            style: {\n              \"width\": \"100%\"\n            },\n            size: \"small\",\n            modelValue: $setup.selectCidList,\n            \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.selectCidList = $event),\n            props: {\n              multiple: true,\n              checkStrictly: true\n            },\n            options: $setup.categoryOptions,\n            onChange: $setup.changeCategory\n          }, null, 8 /* PROPS */, [\"modelValue\", \"options\", \"onChange\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, {\n          label: \"简介：\",\n          prop: \"phrase\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_input, {\n            size: \"small\",\n            modelValue: $setup.lesson.phrase,\n            \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.lesson.phrase = $event),\n            placeholder: \"请输入简介\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, {\n          label: \"价格：\",\n          prop: \"price\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_input_number, {\n            class: \"input-number\",\n            modelValue: $setup.lesson.price,\n            \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.lesson.price = $event),\n            placeholder: \"请输入价格\",\n            precision: 2,\n            step: 1,\n            min: 0\n          }, null, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_input_number, {\n            class: \"input-number\",\n            modelValue: $setup.lesson.originalPrice,\n            \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.lesson.originalPrice = $event),\n            placeholder: \"请输入原价\",\n            precision: 2,\n            step: 1,\n            min: 0\n          }, null, 8 /* PROPS */, [\"modelValue\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, {\n          label: \"海报：\",\n          prop: \"image\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_upload, {\n            class: _normalizeClass({\n              'no-plus': $setup.lesson.image\n            }),\n            \"on-upload-success\": $setup.onUploadImageSuccess,\n            \"on-upload-remove\": $setup.onUploadImageRemove,\n            files: $setup.uploadData.files,\n            \"upload-url\": $setup.uploadData.url,\n            limit: 1,\n            accept: \"image/jpeg,image/gif,image/png\"\n          }, null, 8 /* PROPS */, [\"class\", \"on-upload-success\", \"on-upload-remove\", \"files\", \"upload-url\"]), _hoisted_3]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, {\n          label: \"详情描述：\",\n          prop: \"introduction\"\n        }, {\n          default: _withCtx(() => [$setup.loadWangEditorFlag ? (_openBlock(), _createBlock(_component_wang_editor, {\n            key: 0,\n            modelValue: $setup.lesson.introduction,\n            \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.lesson.introduction = $event)\n          }, null, 8 /* PROPS */, [\"modelValue\"])) : _createCommentVNode(\"v-if\", true)]),\n          _: 1 /* STABLE */\n        }), _createElementVNode(\"div\", _hoisted_4, [$setup.lesson.id ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 0,\n          size: \"small\",\n          onClick: _cache[9] || (_cache[9] = $event => $setup.stepClick('content'))\n        }, {\n          default: _withCtx(() => [_createTextVNode(\"下一步\")]),\n          _: 1 /* STABLE */\n        })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_button, {\n          size: \"small\",\n          onClick: $setup.submitBaseInfo\n        }, {\n          default: _withCtx(() => [_createTextVNode(\"提交\")]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"onClick\"])])]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"model\", \"rules\"])])) : _createCommentVNode(\"v-if\", true), $setup.showStep === 'content' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[10] || (_cache[10] = $event => $setup.stepClick('base'))\n      }, {\n        default: _withCtx(() => [_createTextVNode(\"上一步\")]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[11] || (_cache[11] = $event => $setup.stepClick('homework'))\n      }, {\n        default: _withCtx(() => [_createTextVNode(\"下一步\")]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: $setup.showChapter\n      }, {\n        default: _withCtx(() => [_createTextVNode(\"新增章节\")]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onClick\"])]), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_table, {\n        ref: \"table\",\n        onExpandChange: $options.handleExpandChange,\n        \"default-expand-all\": false,\n        data: $setup.contentList,\n        \"show-header\": false,\n        \"highlight-current-row\": true,\n        style: {\n          \"width\": \"100%\"\n        }\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_table_column, {\n          type: \"expand\"\n        }, {\n          default: _withCtx(props => [props.row.phrase ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, _toDisplayString(props.row.phrase), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(props.row.chapterSectionList, section => {\n            return _openBlock(), _createBlock(_component_el_card, {\n              class: \"box-card\",\n              key: section.title\n            }, {\n              header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"span\", null, _toDisplayString(section.title), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_10, [_createVNode(_component_el_button, {\n                type: \"text\",\n                size: \"small\",\n                onClick: $event => section.isPreview = !section.isPreview\n              }, {\n                default: _withCtx(() => [_createTextVNode(\"预览\")]),\n                _: 2 /* DYNAMIC */\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n                type: \"text\",\n                size: \"small\",\n                onClick: $event => $setup.showChapterSection(props.row.id, section)\n              }, {\n                default: _withCtx(() => [_createTextVNode(\"修改\")]),\n                _: 2 /* DYNAMIC */\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n                type: \"text\",\n                size: \"small\",\n                onClick: $event => $setup.deleteChapterSection(section.id)\n              }, {\n                default: _withCtx(() => [_createTextVNode(\"删除\")]),\n                _: 2 /* DYNAMIC */\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])])])]),\n              default: _withCtx(() => [_createElementVNode(\"div\", {\n                class: _normalizeClass([\"table-wrapper\", {\n                  'show': section.isPreview\n                }])\n              }, [section.phrase ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, _toDisplayString(section.phrase), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"video\", {\n                src: section.url,\n                controls: \"controls\",\n                style: _normalizeStyle({\n                  'margin-top:20px;': !!section.phrase\n                })\n              }, null, 12 /* STYLE, PROPS */, _hoisted_13)])], 2 /* CLASS */)]),\n\n              _: 2 /* DYNAMIC */\n            }, 1024 /* DYNAMIC_SLOTS */);\n          }), 128 /* KEYED_FRAGMENT */))]),\n\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_table_column, {\n          prop: \"title\",\n          label: \"标题\"\n        }, {\n          default: _withCtx(({\n            row\n          }) => [_createElementVNode(\"div\", {\n            class: \"expandable-cell\",\n            onClick: $event => $options.toggleRowExpansion(row)\n          }, _toDisplayString(row.title), 9 /* TEXT, PROPS */, _hoisted_14)]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_table_column, {\n          label: \"操作\"\n        }, {\n          default: _withCtx(r => [_createElementVNode(\"span\", _hoisted_15, [_createVNode(_component_el_button, {\n            type: \"text\",\n            onClick: $event => $setup.showChapterSection(r.row.id),\n            size: \"small\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(\"新增章节内容\")]),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n            type: \"text\",\n            onClick: $event => $setup.showChapter(r.row),\n            size: \"small\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(\"修改\")]),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n            type: \"text\",\n            onClick: $event => $setup.deleteChapter(r.row.id),\n            size: \"small\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(\"删除\")]),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])])]),\n          _: 1 /* STABLE */\n        })]),\n\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onExpandChange\", \"data\"])])])) : _createCommentVNode(\"v-if\", true), $setup.showStep === 'homework' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [_createVNode(_component_el_form, {\n        model: $setup.homework,\n        rules: $setup.homeworkRules,\n        ref: \"homeworkRef\",\n        \"label-width\": \"120px\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_form_item, {\n          label: \"作业内容：\",\n          prop: \"content\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_input, {\n            size: \"small\",\n            type: \"textarea\",\n            modelValue: $setup.homework.content,\n            \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $setup.homework.content = $event),\n            rows: 20,\n            placeholder: \"请输入作业内容\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_form_item, {\n          label: \"作业附件：\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_upload, {\n            \"list-type\": \"text\",\n            \"on-upload-success\": $setup.onUploadHomeworkAttachmentSuccess,\n            \"on-upload-remove\": $setup.onUploadHomeworkAttachmentRemove,\n            files: $setup.uploadHomeworkData.files,\n            \"upload-url\": $setup.uploadHomeworkData.url,\n            limit: 1,\n            accept: \"image/*,video/*,audio/*,application/*\"\n          }, null, 8 /* PROPS */, [\"on-upload-success\", \"on-upload-remove\", \"files\", \"upload-url\"])]),\n          _: 1 /* STABLE */\n        }), _createElementVNode(\"div\", _hoisted_17, [_createVNode(_component_el_button, {\n          size: \"small\",\n          onClick: _cache[13] || (_cache[13] = $event => $setup.stepClick('content'))\n        }, {\n          default: _withCtx(() => [_createTextVNode(\"上一步\")]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_button, {\n          size: \"small\",\n          onClick: _cache[14] || (_cache[14] = $event => $setup.stepClick('certificate'))\n        }, {\n          default: _withCtx(() => [_createTextVNode(\"下一步\")]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_button, {\n          size: \"small\",\n          onClick: $setup.submitHomework\n        }, {\n          default: _withCtx(() => [_createTextVNode(\"提交\")]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"onClick\"])])]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"model\", \"rules\"])])) : _createCommentVNode(\"v-if\", true), $setup.showStep === 'certificate' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_hoisted_21, _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_22, [_createTextVNode(_toDisplayString($setup.certificateTemplate.id ? $setup.certificateTemplate.name : '未选择'), 1 /* TEXT */)])), [[_directive_loading, $setup.baseLoading]]), _createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", null, [_createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: $setup.showCertificateTemplate\n      }, {\n        default: _withCtx(() => [_createTextVNode(\"选择\")]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onClick\"])]), _createElementVNode(\"div\", null, [_createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: $setup.showPreview\n      }, {\n        default: _withCtx(() => [_createTextVNode(\"预览\")]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onClick\"])])])]), _createVNode(_component_el_dialog, {\n        style: {\n          \"min-width\": \"840px\"\n        },\n        title: \"证书预览\",\n        modelValue: $setup.showPreviewViewFlag,\n        \"onUpdate:modelValue\": _cache[15] || (_cache[15] = $event => $setup.showPreviewViewFlag = $event),\n        \"before-close\": $setup.hidePreview\n      }, {\n        footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_24, [_createVNode(_component_el_button, {\n          size: \"small\",\n          onClick: $setup.hidePreview\n        }, {\n          default: _withCtx(() => [_createTextVNode(\"取 消\")]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"onClick\"])])]),\n        default: _withCtx(() => [_createElementVNode(\"div\", null, [$setup.showPreviewViewFlag ? (_openBlock(), _createBlock(_component_certificate_preview, {\n          key: 0,\n          download: false,\n          certificate: $setup.certificateTemplate\n        }, null, 8 /* PROPS */, [\"certificate\"])) : _createCommentVNode(\"v-if\", true)])]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\", \"before-close\"])]), _createElementVNode(\"div\", _hoisted_25, [_createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[16] || (_cache[16] = $event => $setup.stepClick('homework'))\n      }, {\n        default: _withCtx(() => [_createTextVNode(\"上一步\")]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[17] || (_cache[17] = $event => $setup.stepClick('publish'))\n      }, {\n        default: _withCtx(() => [_createTextVNode(\"下一步\")]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: $setup.submitCertificateTemplate\n      }, {\n        default: _withCtx(() => [_createTextVNode(\"提交\")]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onClick\"])])])) : _createCommentVNode(\"v-if\", true), $setup.showStep === 'publish' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [$setup.lesson.status ? (_openBlock(), _createElementBlock(\"div\", _hoisted_28, [$setup.lesson.status === 'published' ? (_openBlock(), _createBlock(_component_el_alert, {\n        key: 0,\n        title: $setup.statusMap[$setup.lesson.status],\n        effect: \"dark\",\n        type: \"success\",\n        closable: false,\n        \"show-icon\": \"\"\n      }, null, 8 /* PROPS */, [\"title\"])) : $setup.lesson.status === 'unpublished' ? (_openBlock(), _createBlock(_component_el_alert, {\n        key: 1,\n        title: $setup.statusMap[$setup.lesson.status],\n        effect: \"dark\",\n        type: \"warning\",\n        closable: false,\n        \"show-icon\": \"\"\n      }, null, 8 /* PROPS */, [\"title\"])) : (_openBlock(), _createBlock(_component_el_alert, {\n        key: 2,\n        title: $setup.statusMap[$setup.lesson.status],\n        effect: \"dark\",\n        type: \"error\",\n        closable: false,\n        \"show-icon\": \"\"\n      }, null, 8 /* PROPS */, [\"title\"]))])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_29, [_createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[18] || (_cache[18] = $event => $setup.stepClick('certificate'))\n      }, {\n        default: _withCtx(() => [_createTextVNode(\"上一步\")]),\n        _: 1 /* STABLE */\n      }), $setup.lesson.status === 'unpublished' ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 0,\n        size: \"small\",\n        onClick: $setup.publish\n      }, {\n        default: _withCtx(() => [_createTextVNode(\"马上发布\")]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), $setup.lesson.status === 'published' ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 1,\n        size: \"small\",\n        onClick: $setup.unPublish\n      }, {\n        default: _withCtx(() => [_createTextVNode(\"移入草稿\")]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])])])) : _createCommentVNode(\"v-if\", true)]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_col, {\n      span: 4,\n      style: {\n        \"position\": \"relative\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_affix, {\n        offset: 60,\n        class: \"affix\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_30, [_hoisted_31, _createVNode(_component_el_steps, {\n          class: \"steps\",\n          \"finish-status\": \"success\",\n          direction: \"vertical\",\n          active: $setup.stepActive\n        }, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.steps, step => {\n            return _openBlock(), _createBlock(_component_el_step, {\n              key: step.key,\n              onClick: $event => $setup.stepClick(step.key),\n              class: _normalizeClass({\n                'step-active': $setup.showStep === step.key\n              }),\n              title: step.name\n            }, null, 8 /* PROPS */, [\"onClick\", \"class\", \"title\"]);\n          }), 128 /* KEYED_FRAGMENT */))]),\n\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"active\"])]), $setup.showStep === 'content' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_32, [_hoisted_33, _createVNode(_component_draggable, {\n          class: \"item-list\",\n          modelValue: $setup.contentList,\n          \"onUpdate:modelValue\": _cache[19] || (_cache[19] = $event => $setup.contentList = $event),\n          \"chosen-class\": \"chosen\",\n          \"force-fallback\": \"true\",\n          group: \"item\",\n          animation: \"1000\",\n          onChange: $setup.onDraggableChange\n        }, {\n          default: _withCtx(() => [_createVNode(_TransitionGroup, null, {\n            default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.contentList, item => {\n              return _openBlock(), _createElementBlock(\"div\", {\n                class: \"item\",\n                key: item.id\n              }, [_createElementVNode(\"div\", _hoisted_34, _toDisplayString(item.title), 1 /* TEXT */), item.chapterSectionList && item.chapterSectionList.length ? (_openBlock(), _createElementBlock(\"div\", _hoisted_35, [_createVNode(_component_draggable, {\n                modelValue: item.chapterSectionList,\n                \"onUpdate:modelValue\": $event => item.chapterSectionList = $event,\n                \"chosen-class\": \"chosen\",\n                \"force-fallback\": \"true\",\n                group: \"sub-item\",\n                animation: \"1000\",\n                onChange: $setup.onDraggableChange\n              }, {\n                default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.chapterSectionList, subItem => {\n                  return _openBlock(), _createElementBlock(\"div\", {\n                    class: \"sub-item\",\n                    key: subItem.id\n                  }, _toDisplayString(subItem.title), 1 /* TEXT */);\n                }), 128 /* KEYED_FRAGMENT */))]),\n\n                _: 2 /* DYNAMIC */\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\", \"onChange\"])])) : _createCommentVNode(\"v-if\", true)]);\n            }), 128 /* KEYED_FRAGMENT */))]),\n\n            _: 1 /* STABLE */\n          })]),\n\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])])) : _createCommentVNode(\"v-if\", true)]),\n        _: 1 /* STABLE */\n      })]),\n\n      _: 1 /* STABLE */\n    })]),\n\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_dialog, {\n    title: \"编辑章节\",\n    modelValue: $setup.showChapterDialog,\n    \"onUpdate:modelValue\": _cache[22] || (_cache[22] = $event => $setup.showChapterDialog = $event),\n    \"before-close\": $setup.hideChapter\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_36, [_createVNode(_component_el_button, {\n      size: \"small\",\n      onClick: $setup.hideChapter\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"取 消\")]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      size: \"small\",\n      type: \"primary\",\n      onClick: $setup.submitChapter\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"确 定\")]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.lessonChapter,\n      rules: $setup.lessonChapterRules,\n      ref: \"lessonChapterRef\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"标题：\",\n        \"label-width\": \"120px\",\n        prop: \"title\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          size: \"small\",\n          modelValue: $setup.lessonChapter.title,\n          \"onUpdate:modelValue\": _cache[20] || (_cache[20] = $event => $setup.lessonChapter.title = $event),\n          placeholder: \"请输入标题\",\n          autocomplete: \"off\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"简介：\",\n        \"label-width\": \"120px\",\n        prop: \"phrase\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          size: \"small\",\n          modelValue: $setup.lessonChapter.phrase,\n          \"onUpdate:modelValue\": _cache[21] || (_cache[21] = $event => $setup.lessonChapter.phrase = $event),\n          type: \"textarea\",\n          rows: 4,\n          placeholder: \"请输入简介\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\", \"rules\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"before-close\"]), _createVNode(_component_el_dialog, {\n    title: \"编辑章节内容\",\n    modelValue: $setup.showChapterSectionDialog,\n    \"onUpdate:modelValue\": _cache[29] || (_cache[29] = $event => $setup.showChapterSectionDialog = $event),\n    \"before-close\": $setup.hideChapterSection\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_38, [_createVNode(_component_el_button, {\n      size: \"small\",\n      onClick: $setup.hideChapterSection\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"取 消\")]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      size: \"small\",\n      type: \"primary\",\n      onClick: $setup.submitChapterSection\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"确 定\")]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.lessonChapterSection,\n      rules: $setup.lessonChapterSectionRules,\n      ref: \"lessonChapterSectionRef\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"标题：\",\n        \"label-width\": \"120px\",\n        prop: \"title\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          size: \"small\",\n          modelValue: $setup.lessonChapterSection.title,\n          \"onUpdate:modelValue\": _cache[23] || (_cache[23] = $event => $setup.lessonChapterSection.title = $event),\n          placeholder: \"请输入标题\",\n          autocomplete: \"off\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"视频方式：\",\n        \"label-width\": \"120px\",\n        prop: \"type\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_radio, {\n          modelValue: $setup.lessonChapterSection.type,\n          \"onUpdate:modelValue\": _cache[24] || (_cache[24] = $event => $setup.lessonChapterSection.type = $event),\n          label: \"link\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(\"视频链接\")]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_radio, {\n          modelValue: $setup.lessonChapterSection.type,\n          \"onUpdate:modelValue\": _cache[25] || (_cache[25] = $event => $setup.lessonChapterSection.type = $event),\n          label: \"upload\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(\"视频上传\")]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), $setup.lessonChapterSection.type === 'link' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 0,\n        label: \"视频链接：\",\n        \"label-width\": \"120px\",\n        prop: \"url\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          size: \"small\",\n          onBlur: $setup.urlBlur,\n          modelValue: $setup.lessonChapterSection.url,\n          \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $setup.lessonChapterSection.url = $event),\n          placeholder: \"请输入视频地址\",\n          autocomplete: \"off\"\n        }, null, 8 /* PROPS */, [\"onBlur\", \"modelValue\"]), _createElementVNode(\"video\", {\n          ref: \"linkVideo\",\n          style: {\n            \"display\": \"none\"\n          },\n          src: $setup.lessonChapterSection.url\n        }, null, 8 /* PROPS */, _hoisted_37)]),\n        _: 1 /* STABLE */\n      })) : (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 1,\n        label: \"视频上传：\",\n        \"label-width\": \"120px\",\n        prop: \"url\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_upload, {\n          \"on-before-upload\": $setup.onBeforeUploadVideo,\n          \"on-upload-success\": $setup.onUploadVideoSuccess,\n          \"on-upload-remove\": $setup.onUploadVideoRemove,\n          files: $setup.uploadVideoData.files,\n          \"upload-url\": $setup.uploadVideoData.url,\n          limit: 1,\n          listType: \"text\",\n          accept: \"audio/mp4,video/mp4\"\n        }, null, 8 /* PROPS */, [\"on-before-upload\", \"on-upload-success\", \"on-upload-remove\", \"files\", \"upload-url\"])]),\n        _: 1 /* STABLE */\n      })), _createVNode(_component_el_form_item, {\n        label: \"视频时长：\",\n        \"label-width\": \"120px\",\n        prop: \"totalTime\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          size: \"small\",\n          modelValue: $setup.lessonChapterSection.totalTime,\n          \"onUpdate:modelValue\": _cache[27] || (_cache[27] = $event => $setup.lessonChapterSection.totalTime = $event),\n          placeholder: \"请输入时长\",\n          autocomplete: \"off\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"简介：\",\n        \"label-width\": \"120px\",\n        prop: \"phrase\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          size: \"small\",\n          modelValue: $setup.lessonChapterSection.phrase,\n          \"onUpdate:modelValue\": _cache[28] || (_cache[28] = $event => $setup.lessonChapterSection.phrase = $event),\n          type: \"textarea\",\n          rows: 4,\n          placeholder: \"请输入简介\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\", \"rules\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"before-close\"]), _createVNode(_component_el_dialog, {\n    \"custom-class\": \"custom-dialog\",\n    title: \"选择证书\",\n    modelValue: $setup.showCertificateTemplateFlag,\n    \"onUpdate:modelValue\": _cache[30] || (_cache[30] = $event => $setup.showCertificateTemplateFlag = $event),\n    \"before-close\": $setup.hideCertificateTemplate,\n    width: \"80%\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_certificate_template_list, {\n      \"cancel-callback\": $setup.hideCertificateTemplate,\n      \"select-callback\": $setup.selectCertificateTemplate,\n      \"is-component\": true\n    }, null, 8 /* PROPS */, [\"cancel-callback\", \"select-callback\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"before-close\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementVNode", "style", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_row", "_component_el_col", "span", "$setup", "showStep", "_hoisted_2", "_component_el_form", "model", "lesson", "rules", "lessonRules", "ref", "_component_el_form_item", "label", "prop", "_component_el_input", "size", "name", "$event", "placeholder", "_component_el_radio_group", "timeType", "_component_el_radio", "_createBlock", "_component_el_date_picker", "startTime", "type", "Date", "onChange", "changeStartTime", "endTime", "changeEndTime", "_component_el_cascader", "selectCidList", "props", "multiple", "checkStrictly", "options", "categoryOptions", "changeCategory", "phrase", "_component_el_input_number", "price", "precision", "step", "min", "originalPrice", "_component_upload", "_normalizeClass", "image", "onUploadImageSuccess", "onUploadImageRemove", "files", "uploadData", "url", "limit", "accept", "_hoisted_3", "loadWangEditorFlag", "_component_wang_editor", "introduction", "_hoisted_4", "id", "_component_el_button", "onClick", "_cache", "step<PERSON>lick", "submitBaseInfo", "_hoisted_5", "_hoisted_6", "showChapter", "_hoisted_7", "_component_el_table", "onExpandChange", "$options", "handleExpandChange", "data", "contentList", "_component_el_table_column", "default", "_withCtx", "row", "_hoisted_8", "_toDisplayString", "_Fragment", "_renderList", "chapterSectionList", "section", "_component_el_card", "key", "title", "header", "_hoisted_9", "_hoisted_10", "isPreview", "showChapterSection", "deleteChapterSection", "_hoisted_11", "_hoisted_12", "src", "controls", "_normalizeStyle", "toggleRowExpansion", "_hoisted_14", "r", "_hoisted_15", "deleteChapter", "_hoisted_16", "homework", "homeworkRules", "content", "rows", "onUploadHomeworkAttachmentSuccess", "onUploadHomeworkAttachmentRemove", "uploadHomeworkData", "_hoisted_17", "submitHomework", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "certificateTemplate", "baseLoading", "_hoisted_23", "showCertificateTemplate", "showPreview", "_component_el_dialog", "showPreviewViewFlag", "hidePreview", "footer", "_hoisted_24", "_component_certificate_preview", "download", "certificate", "_hoisted_25", "submitCertificateTemplate", "_hoisted_26", "_hoisted_27", "status", "_hoisted_28", "_component_el_alert", "statusMap", "effect", "closable", "_hoisted_29", "publish", "unPublish", "_component_el_affix", "offset", "_hoisted_30", "_hoisted_31", "_component_el_steps", "direction", "active", "stepActive", "steps", "_component_el_step", "_hoisted_32", "_hoisted_33", "_component_draggable", "group", "animation", "onDraggableChange", "_TransitionGroup", "item", "_hoisted_34", "length", "_hoisted_35", "subItem", "showChapterDialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_hoisted_36", "submitChapter", "lessonChapter", "lessonChapterRules", "autocomplete", "showChapterSectionDialog", "hideChapterSection", "_hoisted_38", "submitChapterSection", "lessonChapterSection", "lessonChapterSectionRules", "onBlur", "url<PERSON>lur", "onBeforeUploadVideo", "onUploadVideoSuccess", "onUploadVideoRemove", "uploadVideoData", "listType", "totalTime", "showCertificateTemplateFlag", "hideCertificateTemplate", "width", "_component_certificate_template_list", "selectCertificateTemplate"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/learn/lesson/edit/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row>\n      <el-col :span=\"20\">\n        <div v-if=\"showStep === 'base'\" class=\"base\">\n          <el-form :model=\"lesson\" :rules=\"lessonRules\" ref=\"lessonRef\" label-width=\"120px\">\n            <el-form-item label=\"名称：\" prop=\"name\">\n              <el-input size=\"small\" v-model=\"lesson.name\" placeholder=\"请输入标题\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"有效期：\" prop=\"timeType\">\n              <el-radio-group v-model=\"lesson.timeType\">\n                <el-radio label=\"infinite\">永久有效</el-radio>\n                <el-radio label=\"customize\">自定义</el-radio>\n              </el-radio-group>\n            </el-form-item>\n            <el-form-item label=\"开始时间：\" prop=\"startTime\" v-if=\"lesson.timeType !== 'infinite'\">\n              <el-date-picker\n                v-model=\"lesson.startTime\"\n                type=\"datetime\"\n                placeholder=\"选择开始时间\"\n                class=\"input-text\"\n                :default-time=\"new Date(2000, 0, 1, 0, 0, 0)\"\n                size=\"small\"\n                @change=\"changeStartTime\"\n                style=\"width: 100%;\"></el-date-picker>\n            </el-form-item>\n            <el-form-item label=\"结束时间：\" prop=\"endTime\" v-if=\"lesson.timeType !== 'infinite'\">\n              <el-date-picker\n                v-model=\"lesson.endTime\"\n                type=\"datetime\"\n                placeholder=\"选择结束时间\"\n                class=\"input-text\"\n                :default-time=\"new Date(2000, 0, 1, 22, 0, 0)\"\n                size=\"small\"\n                @change=\"changeEndTime\"\n                style=\"width: 100%;\"></el-date-picker>\n            </el-form-item>\n            <el-form-item label=\"分类：\" prop=\"cidList\">\n              <el-cascader style=\"width: 100%;\"\n                           size=\"small\"\n                           v-model=\"selectCidList\"\n                           :props=\"{ multiple: true, checkStrictly: true }\"\n                           :options=\"categoryOptions\"\n                           @change=\"changeCategory\">\n              </el-cascader>\n            </el-form-item>\n            <el-form-item label=\"简介：\" prop=\"phrase\">\n              <el-input size=\"small\" v-model=\"lesson.phrase\" placeholder=\"请输入简介\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"价格：\" prop=\"price\">\n              <el-input-number class=\"input-number\" v-model=\"lesson.price\" placeholder=\"请输入价格\" :precision=\"2\" :step=\"1\" :min=\"0\"></el-input-number>\n              <el-input-number class=\"input-number\" v-model=\"lesson.originalPrice\" placeholder=\"请输入原价\" :precision=\"2\" :step=\"1\" :min=\"0\"></el-input-number>\n            </el-form-item>\n            <el-form-item label=\"海报：\" prop=\"image\">\n              <upload\n                :class=\"{'no-plus': lesson.image}\"\n                :on-upload-success=\"onUploadImageSuccess\"\n                :on-upload-remove=\"onUploadImageRemove\"\n                :files=\"uploadData.files\"\n                :upload-url=\"uploadData.url\"\n                :limit=\"1\"\n                accept=\"image/jpeg,image/gif,image/png\">\n              </upload>\n              <span class=\"upload-image-tips\">图片建议：尺寸 1920 x 1200 像素，大小7M以下</span>\n            </el-form-item>\n            <el-form-item label=\"详情描述：\" prop=\"introduction\">\n              <wang-editor v-if=\"loadWangEditorFlag\" v-model=\"lesson.introduction\"></wang-editor>\n            </el-form-item>\n            <div style=\"margin:50px auto;text-align: center;\">\n              <el-button size=\"small\" @click=\"stepClick('content')\" v-if=\"lesson.id\">下一步</el-button>\n              <el-button size=\"small\" @click=\"submitBaseInfo\">提交</el-button>\n            </div>\n          </el-form>\n        </div>\n        <div v-if=\"showStep === 'content'\" class=\"content\">\n          <div class=\"content-header\">\n            <el-button size=\"small\" @click=\"stepClick('base')\">上一步</el-button>\n            <el-button size=\"small\" @click=\"stepClick('homework')\">下一步</el-button>\n            <el-button size=\"small\" @click=\"showChapter\">新增章节</el-button>\n          </div>\n          <div style=\"margin-top: 20px;\">\n            <el-table ref=\"table\" @expand-change=\"handleExpandChange\" :default-expand-all=\"false\" :data=\"contentList\" :show-header=\"false\" :highlight-current-row=\"true\" style=\"width: 100%\">\n              <el-table-column type=\"expand\">\n                <template #default=\"props\">\n                  <div v-if=\"props.row.phrase\" class=\"tips\">{{props.row.phrase}}</div>\n                  <el-card class=\"box-card\" v-for=\"section in props.row.chapterSectionList\" :key=\"section.title\">\n                    <template #header>\n                      <div class=\"clearfix\" style=\"line-height: 28px;\">\n                        <span>{{section.title}}</span>\n                        <span class=\"opt-btn\">\n                          <el-button type=\"text\" size=\"small\" @click=\"section.isPreview = !section.isPreview\">预览</el-button>\n                          <el-button type=\"text\" size=\"small\" @click=\"showChapterSection(props.row.id, section)\">修改</el-button>\n                          <el-button type=\"text\" size=\"small\" @click=\"deleteChapterSection(section.id)\">删除</el-button>\n                        </span>\n                      </div>\n                    </template>\n                    <div class=\"table-wrapper\" :class=\"{'show': section.isPreview}\">\n                      <div v-if=\"section.phrase\" class=\"tips\">{{section.phrase}}</div>\n                      <div class=\"video-box\">\n                        <video :src=\"section.url\" controls=\"controls\" :style=\"{'margin-top:20px;': !!section.phrase}\"></video>\n                      </div>\n                    </div>\n                  </el-card>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"title\" label=\"标题\">\n                <template #default=\"{ row }\">\n                  <div\n                    class=\"expandable-cell\"\n                    @click=\"toggleRowExpansion(row)\"\n                  >\n                    {{ row.title }}\n                  </div>\n                </template>\n              </el-table-column>\n              <el-table-column label=\"操作\">\n                <template #default=\"r\">\n                  <span class=\"opt-btn\">\n                    <el-button type=\"text\" @click=\"showChapterSection(r.row.id)\" size=\"small\">新增章节内容</el-button>\n                    <el-button type=\"text\" @click=\"showChapter(r.row)\" size=\"small\">修改</el-button>\n                    <el-button type=\"text\" @click=\"deleteChapter(r.row.id)\" size=\"small\">删除</el-button>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n        </div>\n        <div v-if=\"showStep === 'homework'\" class=\"homework\">\n          <el-form :model=\"homework\" :rules=\"homeworkRules\" ref=\"homeworkRef\" label-width=\"120px\">\n            <el-form-item label=\"作业内容：\" prop=\"content\">\n              <el-input size=\"small\" type=\"textarea\" v-model=\"homework.content\" :rows=\"20\" placeholder=\"请输入作业内容\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"作业附件：\">\n              <upload\n                list-type=\"text\"\n                :on-upload-success=\"onUploadHomeworkAttachmentSuccess\"\n                :on-upload-remove=\"onUploadHomeworkAttachmentRemove\"\n                :files=\"uploadHomeworkData.files\"\n                :upload-url=\"uploadHomeworkData.url\"\n                :limit=\"1\"\n                accept=\"image/*,video/*,audio/*,application/*\">\n              </upload>\n            </el-form-item>\n            <div style=\"margin:50px auto;text-align: center;\">\n              <el-button size=\"small\" @click=\"stepClick('content')\">上一步</el-button>\n              <el-button size=\"small\" @click=\"stepClick('certificate')\">下一步</el-button>\n              <el-button size=\"small\" @click=\"submitHomework\">提交</el-button>\n            </div>\n          </el-form>\n        </div>\n        <div v-if=\"showStep === 'certificate'\" class=\"certificate\">\n          <div class=\"certificate-select\">\n            <div class=\"certificate-select-main\">\n              <div class=\"certificate-select-label\">选择证书：</div>\n              <div class=\"certificate-select-value\" v-loading=\"baseLoading\">{{certificateTemplate.id ? certificateTemplate.name : '未选择'}}</div>\n              <div class=\"certificate-select-btn\">\n                <div>\n                  <el-button size=\"small\" @click=\"showCertificateTemplate\">选择</el-button>\n                </div>\n                <div>\n                  <el-button size=\"small\" @click=\"showPreview\">预览</el-button>\n                </div>\n              </div>\n            </div>\n            <el-dialog style=\"min-width: 840px\" title=\"证书预览\" v-model=\"showPreviewViewFlag\" :before-close=\"hidePreview\">\n              <div>\n                <certificate-preview v-if=\"showPreviewViewFlag\" :download=\"false\" :certificate=\"certificateTemplate\" />\n              </div>\n              <template #footer>\n                <div class=\"dialog-footer\">\n                  <el-button size=\"small\" @click=\"hidePreview\">取 消</el-button>\n                </div>\n              </template>\n            </el-dialog>\n          </div>\n          <div style=\"margin:50px auto;text-align: center;\">\n            <el-button size=\"small\" @click=\"stepClick('homework')\">上一步</el-button>\n            <el-button size=\"small\" @click=\"stepClick('publish')\">下一步</el-button>\n            <el-button size=\"small\" @click=\"submitCertificateTemplate\">提交</el-button>\n          </div>\n        </div>\n        <div v-if=\"showStep === 'publish'\" class=\"publish\">\n          <div class=\"publish-box\">\n            <div class=\"current-status\" v-if=\"lesson.status\">\n              <el-alert :title=\"statusMap[lesson.status]\" effect=\"dark\" type=\"success\" :closable=\"false\" show-icon v-if=\"lesson.status === 'published'\"></el-alert>\n              <el-alert :title=\"statusMap[lesson.status]\" effect=\"dark\" type=\"warning\" :closable=\"false\" show-icon v-else-if=\"lesson.status === 'unpublished'\"> </el-alert>\n              <el-alert :title=\"statusMap[lesson.status]\" effect=\"dark\" type=\"error\" :closable=\"false\" show-icon v-else> </el-alert>\n            </div>\n            <div class=\"btn-list\">\n              <el-button size=\"small\" @click=\"stepClick('certificate')\">上一步</el-button>\n              <el-button size=\"small\" @click=\"publish\" v-if=\"lesson.status === 'unpublished'\">马上发布</el-button>\n              <el-button size=\"small\" @click=\"unPublish\" v-if=\"lesson.status === 'published'\">移入草稿</el-button>\n            </div>\n          </div>\n        </div>\n      </el-col>\n      <el-col :span=\"4\" style=\"position: relative;\">\n        <el-affix :offset=\"60\" class=\"affix\">\n          <div class=\"step-list\">\n            <div class=\"title\">\n              步骤导航\n            </div>\n            <el-steps class=\"steps\" finish-status=\"success\" direction=\"vertical\" :active=\"stepActive\">\n              <el-step v-for=\"(step) in steps\" :key=\"step.key\" @click=\"stepClick(step.key)\" :class=\"{'step-active': showStep === step.key}\" :title=\"step.name\"></el-step>\n            </el-steps>\n          </div>\n          <div class=\"draggable\" v-if=\"showStep === 'content'\">\n            <div class=\"title\">\n              章节目录（拖动排序）\n            </div>\n            <draggable class=\"item-list\" v-model=\"contentList\" chosen-class=\"chosen\" force-fallback=\"true\" group=\"item\" animation=\"1000\" @change=\"onDraggableChange\">\n              <transition-group>\n                <div class=\"item\" v-for=\"item in contentList\" :key=\"item.id\">\n                  <div class=\"item-title\">{{item.title}}</div>\n                  <div class=\"sub-item-list\" v-if=\"item.chapterSectionList && item.chapterSectionList.length\">\n                    <draggable v-model=\"item.chapterSectionList\" chosen-class=\"chosen\" force-fallback=\"true\" group=\"sub-item\" animation=\"1000\" @change=\"onDraggableChange\">\n                      <div class=\"sub-item\" v-for=\"subItem in item.chapterSectionList\" :key=\"subItem.id\">{{subItem.title}}</div>\n                    </draggable>\n                  </div>\n                </div>\n              </transition-group>\n            </draggable>\n          </div>\n        </el-affix>\n      </el-col>\n    </el-row>\n    <el-dialog title=\"编辑章节\" v-model=\"showChapterDialog\" :before-close=\"hideChapter\">\n      <el-form :model=\"lessonChapter\" :rules=\"lessonChapterRules\" ref=\"lessonChapterRef\">\n        <el-form-item label=\"标题：\" label-width=\"120px\" prop=\"title\">\n          <el-input size=\"small\" v-model=\"lessonChapter.title\" placeholder=\"请输入标题\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"简介：\" label-width=\"120px\" prop=\"phrase\">\n          <el-input size=\"small\" v-model=\"lessonChapter.phrase\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入简介\"></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button size=\"small\" @click=\"hideChapter\">取 消</el-button>\n          <el-button size=\"small\" type=\"primary\" @click=\"submitChapter\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n    <el-dialog title=\"编辑章节内容\" v-model=\"showChapterSectionDialog\" :before-close=\"hideChapterSection\">\n      <el-form :model=\"lessonChapterSection\" :rules=\"lessonChapterSectionRules\" ref=\"lessonChapterSectionRef\">\n        <el-form-item label=\"标题：\" label-width=\"120px\" prop=\"title\">\n          <el-input size=\"small\" v-model=\"lessonChapterSection.title\" placeholder=\"请输入标题\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"视频方式：\" label-width=\"120px\" prop=\"type\">\n          <el-radio v-model=\"lessonChapterSection.type\" label=\"link\">视频链接</el-radio>\n          <el-radio v-model=\"lessonChapterSection.type\" label=\"upload\">视频上传</el-radio>\n        </el-form-item>\n        <el-form-item label=\"视频链接：\" label-width=\"120px\" prop=\"url\" v-if=\"lessonChapterSection.type === 'link'\">\n          <el-input size=\"small\" @blur=\"urlBlur\" v-model=\"lessonChapterSection.url\" placeholder=\"请输入视频地址\" autocomplete=\"off\"></el-input>\n          <video ref=\"linkVideo\" style=\"display: none;\" :src=\"lessonChapterSection.url\"></video>\n        </el-form-item>\n        <el-form-item label=\"视频上传：\" label-width=\"120px\" prop=\"url\" v-else>\n          <upload\n            :on-before-upload=\"onBeforeUploadVideo\"\n            :on-upload-success=\"onUploadVideoSuccess\"\n            :on-upload-remove=\"onUploadVideoRemove\"\n            :files=\"uploadVideoData.files\"\n            :upload-url=\"uploadVideoData.url\"\n            :limit=\"1\"\n            listType=\"text\"\n            accept=\"audio/mp4,video/mp4\">\n          </upload>\n        </el-form-item>\n        <el-form-item label=\"视频时长：\" label-width=\"120px\" prop=\"totalTime\">\n          <el-input size=\"small\" v-model=\"lessonChapterSection.totalTime\" placeholder=\"请输入时长\" autocomplete=\"off\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"简介：\" label-width=\"120px\" prop=\"phrase\">\n          <el-input size=\"small\" v-model=\"lessonChapterSection.phrase\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入简介\"></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button size=\"small\" @click=\"hideChapterSection\">取 消</el-button>\n          <el-button size=\"small\" type=\"primary\" @click=\"submitChapterSection\">确 定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n    <el-dialog custom-class=\"custom-dialog\" title=\"选择证书\" v-model=\"showCertificateTemplateFlag\" :before-close=\"hideCertificateTemplate\" width=\"80%\">\n      <certificate-template-list :cancel-callback=\"hideCertificateTemplate\" :select-callback=\"selectCertificateTemplate\" :is-component=\"true\"/>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport router from \"@/router\"\nimport WangEditor from \"@/components/WangEditor/index.vue\"\nimport Upload from \"@/components/Uplaod\"\nimport {ref} from \"vue\"\nimport {useRoute} from \"vue-router\"\nimport {VueDraggableNext} from \"vue-draggable-next\"\nimport {success, confirm, error} from \"@/util/tipsUtils\"\nimport {findCategoryList, toTree, getAllParent} from \"@/api/learn/category\"\nimport {saveBaseInfo, updateBaseInfo, getBaseInfo, publishLesson, unPublishLesson,\n    saveLessonChapter, updateLessonChapter, deleteLessonChapter, getLessonChapterList, updateSortOrder,\n    saveLessonChapterSection, updateLessonChapterSection, deleteLessonChapterSection, saveHomework, updateHomework, getHomework} from \"@/api/learn/lesson\"\nimport CertificateTemplateList from \"@/views/certificate/template/index.vue\";\nimport CertificatePreview from \"@/views/certificate/preview/index.vue\";\n\n  export default {\n  name: \"LearnLessonEdit\",\n    components:{\n      CertificatePreview,\n      CertificateTemplateList,\n      Upload,\n      WangEditor,\n      draggable: VueDraggableNext\n    },\n    data() {\n      return {\n        expandedFlag: false,\n        expandedRows: [], // 用于存储已展开的行\n      };\n    },\n    methods: {\n      // 自定义切换行展开\n      toggleRowExpansion(row) {\n        const index = this.expandedRows.indexOf(row);\n        console.log(\"111111\", index)\n        if (index === -1) {\n          this.expandedRows.push(row);\n          this.$refs.table.toggleRowExpansion(row, true);\n          console.log(\"222222\", index, this.expandedRows.length)\n        } else {\n          for (let i = 0; i < this.expandedRows.length; i++) {\n            const index = this.expandedRows.indexOf(row);\n            console.log(\"33333333\", index)\n            if (index === -1) {\n              break;\n            }\n            this.expandedRows.splice(index, 1);\n          }\n          console.log(\"444444\", this.expandedRows)\n          this.$refs.table.toggleRowExpansion(row, false);\n        }\n        console.log(\"xx expandedRows\", this.expandedRows)\n      },\n      handleExpandChange(row, expanded) {\n        if (expanded) {\n          this.expandedRows.push(row);\n        } else {\n          // const index = this.expandedRows.indexOf(row);\n          for (let i = 0; i < this.expandedRows.length; i++) {\n            const index = this.expandedRows.indexOf(row);\n            if (index === -1) {\n              break;\n            }\n            this.expandedRows.splice(index, 1);\n          }\n        }\n        console.log(\"expandedRows\", this.expandedRows)\n      }\n    },\n    setup() {\n      const loadWangEditorFlag = ref(false)\n      const route = useRoute()\n      let isUpdate = !!route.query.id\n      let showStep = ref(\"\")\n      const steps = [\n        {key: \"base\", name: \"基础信息\"},\n        {key: \"content\", name: \"章节内容\"},\n        {key: \"homework\", name: \"课后作业\"},\n        {key: \"certificate\", name: \"关联证书\"},\n        {key: \"publish\", name: \"发布状态\"},\n      ]\n      const stepActive = ref(0)\n      const loadStepActiveArray = () => {\n        const stepActiveArray = [];\n        for (let i = 0; i < steps.length; i++) {\n          const step = steps[i];\n          stepActiveArray.push(step.key);\n          if (step.key === showStep.value) {\n            stepActive.value = i;\n            break;\n          }\n        }\n        if (isUpdate) {\n          stepActive.value = steps.length;\n        }\n        return stepActiveArray;\n      }\n      // 基本信息\n      const uploadData = ref({\n        url: process.env.VUE_APP_BASE_API + \"/oss/learn/lesson/image\",\n        files: []\n      })\n      function getCurrentHour() {\n        const now = new Date();\n        const year = now.getFullYear();\n        const month = String(now.getMonth() + 1).padStart(2, '0');\n        const date = String(now.getDate()).padStart(2, '0');\n        const hour = String(now.getHours()).padStart(2, '0');\n        return `${year}-${month}-${date} ${hour}:00:00`;\n      }\n      const infiniteDate = \"2037-12-31 23:59:59\";\n      const categoryOptions = ref([])\n      const selectCidList = ref([])\n      const lesson = ref({\n        id: \"\",\n        name: \"\",\n        startTime: getCurrentHour(),\n        endTime: infiniteDate,\n        price: 0,\n        originalPrice: 0,\n        image: \"\",\n        cidList: [],\n        phrase: \"\",\n        introduction: \"\",\n        timeType: \"infinite\"\n      })\n      const lessonRules = {\n        name: [{ required: true, message: \"请输入标题\", trigger: \"blur\" }],\n        startTime: [{ required: true, message: \"请选择时间\", trigger: \"change\" }],\n        endTime: [{ required: true, message: \"请选择时间\", trigger: \"change\" }],\n        phrase: [{ required: true, message: \"请输入简介\", trigger: \"blur\" }],\n        price: [{ required: true, message: \"请输入价格\", trigger: \"blur\" }],\n        cidList: [{ required: true, message: \"请选择分类\", trigger: \"change\" }],\n        introduction: [{ required: true, message: \"请输入描述\", trigger: \"blur\" }],\n        image: [{ required: true, message: \"请选择海报\", trigger: \"change\" }],\n      }\n      const baseLoading = ref(true)\n      // 加载基本信息\n      const loadBaseInfo = () => {\n        let id = route.query.id;\n        if (!id) {\n          loadWangEditorFlag.value = true;\n          return;\n        }\n        getBaseInfo(id, function (res) {\n          baseLoading.value = false\n          lesson.value = res;\n          if (res && res.endTime) {\n            if (res.endTime === new Date(infiniteDate)) {\n              lesson.value.timeType = 'infinite'\n            } else {\n              lesson.value.timeType = 'customize'\n            }\n          }\n          selectCidList.value = getAllParent(categoryOptions.value, res.cidList);\n          lesson.value.cidList = []\n          uploadData.value.files = [\n            {\n              name: \"海报\",\n              url: lesson.value.image\n            }\n          ]\n          for (const valElement of selectCidList.value) {\n            lesson.value.cidList.push(valElement[valElement.length - 1])\n          }\n          loadWangEditorFlag.value = true;\n\n          // 获取证书\n          certificateTemplate.value = lesson.value.certificate\n\n        })\n      }\n      // 获取分类\n      const loadCategory = () => {\n        findCategoryList(0, true, (res) => {\n          if (res && res.length) {\n            categoryOptions.value = toTree(res);\n            loadBaseInfo();\n          }\n        })\n      }\n      // 选择分类\n      const changeCategory = (val) => {\n        lesson.value.cidList = []\n        for (const valElement of val) {\n          lesson.value.cidList.push(valElement[valElement.length - 1])\n        }\n      }\n      // 选择时间\n      const changeStartTime = (val) => {\n        lesson.value.startTime = val\n      }\n      // 选择时间\n      const changeEndTime = (val) => {\n        lesson.value.endTime = val\n      }\n      // 上传图片成功\n      const onUploadImageSuccess = (res) => {\n        lesson.value.image = res.data\n      }\n      // 删除图片\n      const onUploadImageRemove = () => {\n        lesson.value.image = \"\"\n        uploadData.value.files = []\n      }\n      // 提交基本信息\n      const lessonRef = ref(null)\n      const submitBaseInfo = () => {\n        lessonRef.value.validate((valid) => {\n          if (!valid) { return false }\n          if (isUpdate) {\n            if(typeof lesson.value.startTime == \"string\") {\n              lesson.value.startTime = new Date(lesson.value.startTime);\n            }\n            if(typeof lesson.value.endTime == \"string\") {\n              lesson.value.endTime = new Date(lesson.value.endTime);\n            }\n            updateBaseInfo(lesson.value, function (res) {\n              if (res && res.id) {\n                lesson.value = res;\n                success(\"编辑成功\")\n                showStep.value = \"content\";\n                loadStepActiveArray()\n                let path = route.fullPath;\n                router.push({path, query: {id: lesson.value.id, step: \"content\"} });\n              }\n            })\n          } else {\n            if (lesson.value.timeType === 'infinite') {\n              lesson.value.startTime = new Date(getCurrentHour());\n              lesson.value.endTime = new Date(infiniteDate);\n            }\n            saveBaseInfo(lesson.value, function (res) {\n              if (res && res.id) {\n                lesson.value = res;\n                success(\"新增成功\")\n                showStep.value = \"content\";\n                loadStepActiveArray()\n                let path = route.fullPath;\n                router.push({path, query: {id: lesson.value.id, step: \"content\"} });\n              }\n            })\n          }\n        })\n      }\n\n      // 内容\n      const contentList = ref([])\n      const showChapterDialog = ref(false)\n      const lessonChapter = ref({\n        id: \"\",\n        lessonId: \"\",\n        title: \"\",\n        phrase: \"\"\n      })\n      const lessonChapterRules = {\n        title: [{ required: true, message: \"请输入标题\", trigger: \"blur\" }],\n      }\n      const showChapterSectionDialog = ref(false)\n      const lessonChapterSection = ref({\n        id: \"\",\n        lessonChapterId: \"\",\n        type: \"link\",\n        title: \"\",\n        url: \"\",\n        phrase: \"\",\n        totalTime: \"\"\n      })\n      const lessonChapterSectionRules = ref({\n        title: [{ required: true, message: \"请输入标题\", trigger: \"blur\" }],\n        url: [{ required: true, message: \"请输入视频地址\", trigger: \"blur\" }],\n        type: [{ required: true, message: \"请选择类型\", trigger: \"change\" }],\n        totalTime: [{ required: true, message: \"请输入时长\", trigger: \"blur\" }]\n      })\n      const homework = ref({\n        lessonId: \"\",\n        content: \"\",\n        attachment: \"\",\n      })\n      const uploadHomeworkData = ref({\n        url: process.env.VUE_APP_BASE_API + \"/oss/learn/homework/file\",\n        files: []\n      })\n      const loadContent = () => {\n        let id = route.query.id;\n        if (!id) { return; }\n        getLessonChapterList({lessonId: id}, (res) => {\n          if (res && res.list) {\n            contentList.value = res.list;\n          }\n        })\n        getHomework({lessonId: route.query.id}, (res) => {\n          homework.value = res\n          if (homework.value.url) {\n            uploadHomeworkData.value.files = [\n              {\n                name: \"作业附件\",\n                url: homework.value.url\n              }\n            ]\n          }\n        })\n      }\n      const showChapter = (chapter) => {\n        showChapterDialog.value = true;\n        if (chapter && chapter.id) {\n          lessonChapter.value = chapter;\n        } else {\n          lessonChapter.value = {\n            lessonId: lesson.value.id,\n            id: \"\",\n            title: \"\",\n            phrase: \"\"\n          }\n        }\n      }\n      const hideChapter = () => {\n        showChapterDialog.value = false;\n        lessonChapter.value = {id: \"\", lessonId: \"\", title: \"\", phrase: \"\"}\n      }\n      const uploadVideoData = ref({\n        url: process.env.VUE_APP_BASE_API + \"/oss/learn/lesson/video\",\n        files: []\n      })\n      let videoLoaded = false;\n      const showChapterSection = (lessonChapterId, chapterSection) => {\n        showChapterSectionDialog.value = true;\n        if (chapterSection && chapterSection.id) {\n          lessonChapterSection.value = chapterSection;\n          uploadVideoData.value.files = [\n            {\n              name: lessonChapterSection.value.title + \".mp4\",\n              url: lessonChapterSection.value.url\n            }\n          ]\n        } else {\n          videoLoaded = false\n          lessonChapterSection.value = {\n            lessonChapterId: lessonChapterId,\n            id: \"\",\n            title: \"\",\n            url: \"\",\n            phrase: \"\",\n            type: \"link\",\n            totalTime: \"\"\n          }\n        }\n      }\n      const hideChapterSection = () => {\n        videoLoaded = false\n        showChapterSectionDialog.value = false;\n        lessonChapterSection.value = {\n          id: \"\",\n          lessonChapterId: \"\",\n          title: \"\",\n          url: \"\",\n          phrase: \"\",\n          type: \"link\",\n          totalTime: \"\"\n        }\n      }\n      const deleteChapter = (id) => {\n        confirm(\"确认删除吗？\", \"提示\", () => {\n          deleteLessonChapter({id: id}, () => {\n            success(\"删除成功\")\n            loadContent()\n          })\n        })\n      }\n      const deleteChapterSection = (id) => {\n        confirm(\"确认删除吗？\", \"提示\", () => {\n          deleteLessonChapterSection({id: id}, () => {\n            success(\"删除成功\")\n            loadContent()\n          })\n        })\n      }\n      const lessonChapterRef = ref(null)\n      const submitChapter = () => {\n        lessonChapterRef.value.validate((valid) => {\n          if (!valid) { return false }\n          if (lessonChapter.value.id) {\n            updateLessonChapter(lessonChapter.value, function () {\n              success(\"编辑成功\")\n              hideChapter()\n              loadContent()\n            })\n          } else {\n            saveLessonChapter(lessonChapter.value, function () {\n              success(\"新增成功\")\n              hideChapter()\n              loadContent()\n              stepActive.value = steps.length;\n              isUpdate = true;\n            })\n          }\n        })\n      }\n      const linkVideo = ref(null)\n      const urlBlur = () => {\n        if (lessonChapterSection.value.type === \"link\") {\n          linkVideo.value.addEventListener(\"loadedmetadata\", () => {\n            //时长为秒，小数，182.36\n            lessonChapterSection.value.totalTime = linkVideo.value.duration;\n            videoLoaded = true\n          });\n        }\n      }\n      const lessonChapterSectionRef = ref(null)\n      const submitChapterSection = () => {\n        if (lessonChapterSection.value.type === \"link\") {\n          if (!lessonChapterSection.value.id && !videoLoaded) {\n            error(\"正在计算视频时长，请稍后再试\");\n          }\n        }\n        lessonChapterSectionRef.value.validate((valid) => {\n          if (!valid) { return false }\n          if (lessonChapterSection.value.id) {\n            updateLessonChapterSection(lessonChapterSection.value, function () {\n              success(\"编辑成功\")\n              hideChapterSection()\n              loadContent()\n            })\n          } else {\n            saveLessonChapterSection(lessonChapterSection.value, function () {\n              success(\"新增成功\")\n              hideChapterSection()\n              loadContent()\n            })\n          }\n        })\n      }\n      // 上传视频成功\n      const onUploadVideoSuccess = (res) => {\n        lessonChapterSection.value.url = res.data\n        uploadVideoData.value.files = [\n            {\n              name: lessonChapterSection.value.title + \".mp4\",\n              url: res.data\n            }\n        ]\n      }\n      // 删除视频\n      const onUploadVideoRemove = () => {\n        lessonChapterSection.value.url = \"\"\n        uploadVideoData.value.files = []\n      }\n      const onBeforeUploadVideo = (file) => {\n        let videoUrl = URL.createObjectURL(file);\n        let audioElement = new Audio(videoUrl);\n        audioElement.addEventListener(\"loadedmetadata\", () => {\n          //时长为秒，小数，182.36\n          lessonChapterSection.value.totalTime = audioElement.duration;\n        });\n      }\n      // 拖拽事件\n      const onDraggableChange = () => {\n        console.log(contentList.value)\n        const chapterList = []\n        for (const content of contentList.value) {\n          const subData = []\n          if (content.chapterSectionList && content.chapterSectionList.length) {\n            for (const sub of content.chapterSectionList) {\n              subData.push({id: sub.id, list: []})\n            }\n          }\n          chapterList.push({id: content.id, list: subData});\n        }\n        const params = {id: lesson.value.id, list: chapterList}\n        updateSortOrder(params, () => {\n          success(\"排序更新成功\")\n        })\n        console.log(params)\n      }\n      // 作业\n      const homeworkRef = ref(null)\n      const homeworkRules = ref({\n        content: [{ required: true, message: \"请输入作业内容\", trigger: \"blur\" }],\n      })\n      // 上传附件成功\n      const onUploadHomeworkAttachmentSuccess = (res) => {\n        homework.value.attachment = res.data\n      }\n      // 删除附件成功\n      const onUploadHomeworkAttachmentRemove = () => {\n        homework.value.attachment = \"\"\n        uploadHomeworkData.value.files = []\n      }\n      const submitHomework = () => {\n        homework.value.lessonId = route.query.id || lesson.value.id\n        homeworkRef.value.validate((valid) => {\n          if (!valid) {return false}\n          if (homework.value.id) {\n            updateHomework(homework.value, () => {\n              success(\"编辑成功\")\n              showStep.value = \"publish\";\n              let path = route.fullPath;\n              router.push({path, query: {id: lesson.value.id, step: \"publish\"} });\n            })\n          } else {\n            saveHomework(homework.value, (res) => {\n              homework.value = res\n              success(\"编辑成功\")\n              showStep.value = \"publish\";\n              let path = route.fullPath;\n              router.push({path, query: {id: lesson.value.id, step: \"publish\"} });\n            })\n          }\n        })\n      }\n      // 发布页面\n      const statusMap = {\n        unpublished: \"草稿箱\",\n        published: \"已发布\",\n        deleted: \"已删除\"\n      }\n      const publish = () => {\n        publishLesson({id: lesson.value.id}, () => {\n          success(\"发布成功\")\n          lesson.value.status = \"published\"\n        })\n      }\n      const unPublish = () => {\n        unPublishLesson({id: lesson.value.id}, () => {\n          success(\"取消发布成功\")\n          lesson.value.status = \"unpublished\"\n        })\n      }\n      // 步骤条\n      const init = () => {\n        // 初始化加载\n        if (route.query.step) {\n          showStep.value = route.query.step;\n        } else {\n          showStep.value = \"base\"\n        }\n        lesson.value.id = route.query.id || \"\"\n        loadCategory();\n        loadContent();\n      }\n      init()\n      // 步骤条点击切换\n      const stepClick = (key) => {\n        if (!isUpdate && loadStepActiveArray().indexOf(key) < 0) {\n          return;\n        }\n        showStep.value = key;\n        let path = route.fullPath;\n        router.push({path, query: {id: lesson.value.id, step: key} });\n      }\n      loadStepActiveArray();\n\n      // 证书\n      const certificateTemplate = ref({})\n      const showCertificateTemplateFlag = ref(false)\n      const showCertificateTemplate = () => {\n        showCertificateTemplateFlag.value = true\n      }\n      const hideCertificateTemplate = () => {\n        showCertificateTemplateFlag.value = false\n      }\n      const selectCertificateTemplate = (val) => {\n        console.log(\"val\", val)\n        if (val.length > 1) {\n          error(\"只能选择一个证书\");\n          return\n        }\n        if (val.length > 0) {\n          certificateTemplate.value = val[0]\n          if (certificateTemplate.value) {\n            lesson.value.certificateId = certificateTemplate.value.id\n          }\n        }\n        console.log(\"lesson.value.certificateId\", lesson.value.certificateId)\n        hideCertificateTemplate()\n      }\n      const showPreviewViewFlag = ref(false);\n      const showPreview = () => {\n        if (!certificateTemplate.value.id) {\n          error(\"请先选择证书\")\n          return\n        }\n        showPreviewViewFlag.value = true;\n      }\n      const hidePreview = () => {\n        showPreviewViewFlag.value = false;\n      }\n      const submitCertificateTemplate = () => {\n        if (!certificateTemplate.value.id) {\n          error(\"请先选择证书\")\n          return;\n        }\n        if(typeof lesson.value.startTime == \"string\") {\n          lesson.value.startTime = new Date(lesson.value.startTime);\n        }\n        if(typeof lesson.value.endTime == \"string\") {\n          lesson.value.endTime = new Date(lesson.value.endTime);\n        }\n        lesson.value.certificateId = certificateTemplate.value.id;\n        updateBaseInfo(lesson.value, function (res) {\n          if (res && res.id) {\n            lesson.value = res;\n            success(\"关联证书成功\")\n          }\n        })\n      }\n      // 返回参数与方法\n      return {\n        // 证书\n        baseLoading,\n        hidePreview,\n        showPreview,\n        showPreviewViewFlag,\n        certificateTemplate,\n        showCertificateTemplateFlag,\n        showCertificateTemplate,\n        hideCertificateTemplate,\n        selectCertificateTemplate,\n        submitCertificateTemplate,\n        // 基本信息\n        uploadData,\n        categoryOptions,\n        lesson,\n        selectCidList,\n        lessonRules,\n        lessonRef,\n        changeCategory,\n        changeStartTime,\n        changeEndTime,\n        onUploadImageSuccess,\n        onUploadImageRemove,\n        submitBaseInfo,\n        // 内容列表\n        contentList,\n        showChapterDialog,\n        lessonChapter,\n        lessonChapterRules,\n        showChapterSectionDialog,\n        lessonChapterSection,\n        lessonChapterSectionRules,\n        lessonChapterRef,\n        lessonChapterSectionRef,\n        showChapter,\n        hideChapter,\n        showChapterSection,\n        hideChapterSection,\n        deleteChapter,\n        deleteChapterSection,\n        submitChapter,\n        submitChapterSection,\n        uploadVideoData,\n        linkVideo,\n        urlBlur,\n        onBeforeUploadVideo,\n        onUploadVideoSuccess,\n        onUploadVideoRemove,\n        onDraggableChange,\n        // 作业\n        homework,\n        homeworkRef,\n        homeworkRules,\n        uploadHomeworkData,\n        submitHomework,\n        onUploadHomeworkAttachmentSuccess,\n        onUploadHomeworkAttachmentRemove,\n        // 发布页面\n        statusMap,\n        publish,\n        unPublish,\n        // 步骤条\n        steps,\n        stepActive,\n        showStep,\n        stepClick,\n        loadWangEditorFlag\n      };\n    }\n  }\n</script>\n<style scoped lang=\"scss\">\n  .app-container {\n    margin: 20px;\n    .base {\n      .upload-image-tips {\n        font-size: 12px;\n        color: #999999;\n      }\n      ::v-deep .el-upload--picture-card,\n      ::v-deep .el-upload-list--picture-card .el-upload-list__item {\n        //width: 100%;\n        height: 62.5%;\n        border: none;\n        display: flex;\n        margin: 0;\n        min-height: 146px;\n        justify-content: center;\n        flex-direction: column;\n        max-height: 400px;\n        background-color: #ffffff;\n      }\n      .no-plus {\n        ::v-deep .el-upload--picture-card {\n          min-height: inherit;\n          justify-content: inherit;\n          flex-direction: inherit;\n          display: none;\n        }\n        img {\n          max-height: 460px;\n        }\n      }\n      .input-number {\n        margin-right: 20px;\n      }\n    }\n    .content {\n      position: relative;\n      min-height: 500px;\n      .content-header {\n        text-align: right;\n        ::v-deep .el-button {\n          border-color: #f3f5f8;\n        }\n      }\n      .tips {\n        font-size: 12px;\n        color: #999999;\n        padding: 15px 20px;\n      }\n    }\n    .publish {\n      .publish-box {\n        margin: 50px auto;\n        text-align: center;\n        .current-status {\n          margin: 0 auto 20px;\n          width: 180px;\n        }\n        .btn-list{\n          margin: 0 auto;\n          width: 180px;\n          text-align: center;\n        }\n      }\n    }\n  }\n  ::v-deep .el-input__inner, ::v-deep .el-input-number {\n    height: 34px;\n    line-height: 34px;\n    font-size: 12px;\n    border-color: #f3f5f8;\n    //border: none;\n    &:focus, &:hover {\n      border-color: #f3f5f8;\n    }\n    .el-input-number__decrease, .el-input-number__increase {\n      background: #FFFFFF;\n      line-height: 32px;\n      border: none;\n      &:focus, &:hover {\n        border-color: #f3f5f8;\n      }\n    }\n  }\n  ::v-deep .el-textarea__inner {\n    border-color: #f3f5f8;\n    &:focus, &:hover {\n      border-color: #f3f5f8;\n    }\n  }\n  ::v-deep .el-cascader .el-input .el-input__inner:focus {\n    border-color: #f3f5f8;\n  }\n  ::v-deep .el-input__icon {\n    line-height: 34px;\n    cursor: pointer;\n    &:hover {\n      color: $--color-primary;\n    }\n  }\n  ::v-deep .el-form-item__label {\n    font-size: 12px;\n  }\n  ::v-deep .el-table th,\n  ::v-deep .el-table td {\n    padding: 5px 0;\n    font-size: 12px;\n    color: #000000;\n  }\n  ::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td {\n    background-color: #FFFFFF;\n  }\n  ::v-deep .el-table__body tr.current-row > td {\n    background-color: #FFFFFF;\n  }\n  ::v-deep .el-button--text {\n    color: #999999;\n    font-size: 12px;\n    &:hover {\n      color: $--color-primary;\n    }\n  }\n  ::v-deep .el-cascader:not(.is-disabled):hover .el-input__inner {\n    cursor: pointer;\n    border-color: #f3f5f8;\n  }\n  .box-card {\n    padding: 0 30px 10px;\n    .el-card {\n      box-shadow: none;\n    }\n    ::v-deep .el-card__header {\n      padding: 5px 20px;\n      font-size: 12px;\n      border: 0;\n    }\n    ::v-deep .el-card__body {\n      padding: 0;\n      .table-wrapper {\n        display: none;\n        .video-box {\n          padding: 0 20px 15px;\n          display: flex;\n          justify-content: center;\n          video {\n            background: #000;\n            width: 320px;\n            height: 240px;\n          }\n        }\n      }\n      .show {\n        display: block;\n      }\n    }\n  }\n  .opt-btn {\n    float: right;\n    ::v-deep .el-button {\n      margin: 0;\n      padding: 5px;\n    }\n  }\n  .affix {\n    min-height: 720px;\n    .step-list {\n      padding: 10px 20px;\n      .title {\n        padding: 0 0 20px 0;\n        font-size: 12px;\n      }\n      .steps {\n        height: 120px;\n        padding-left: 10px;\n        ::v-deep .el-step__title {\n          font-size: 14px;\n        }\n        ::v-deep .el-step__icon {\n          width: 20px;\n          height: 20px;\n        }\n        ::v-deep .el-step.is-vertical .el-step__head {\n          width: 20px;\n        }\n        ::v-deep .el-step.is-vertical .el-step__title{\n          cursor:pointer;\n        }\n        ::v-deep .el-step.is-vertical .el-step__line {\n          width: 1px;\n          left: 10px;\n          top: 2px;\n        }\n        ::v-deep .el-step__icon.is-text {\n          border-width: 1px;\n          cursor:pointer;\n        }\n        ::v-deep .step-active .el-step__head.is-finish {\n          color: red;\n        }\n      }\n    }\n    .draggable {\n      padding: 10px 0 10px 10px;\n      .title {\n        padding: 10px 0 10px;\n        font-size: 12px;\n      }\n      .item-list {\n        padding: 0 0 0 10px;\n        .item {\n          font-size: 12px;\n          line-height: 20px;\n          padding: 5px 0;\n          .sub-item-list {\n            background: #ffffff;\n            padding: 0 10px;\n            border-radius: 4px;\n            margin-top: 5px;\n            .sub-item {\n              line-height: 20px;\n              padding: 5px 0;\n              color: #666666;\n              &:first-child {\n                padding-top: 10px;\n              }\n              &:last-child {\n                padding-bottom: 10px;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  ::v-deep .el-upload--text {\n    font-size: 12px;\n  }\n  ::v-deep .el-affix--fixed {\n    z-index: 98!important;\n  }\n  ::v-deep .el-table__empty-block {\n    line-height: 400px;\n    .el-table__empty-text {\n      line-height: 400px;\n    }\n  }\n  .certificate {\n    .certificate-select {\n      margin: 40px 0 20px;\n      .certificate-select-main {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        .certificate-select-label {\n\n        }\n        .certificate-select-value {\n\n        }\n        .certificate-select-btn {\n          display: flex;\n          margin-left: 20px;\n        }\n      }\n    }\n  }\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAe;;;EAGYA,KAAK,EAAC;;gEA2DhCC,mBAAA,CAAoE;EAA9DD,KAAK,EAAC;AAAmB,GAAC,+BAA6B;;EAK1DE,KAA4C,EAA5C;IAAA;IAAA;EAAA;AAA4C;;;EAMlBF,KAAK,EAAC;;;EAClCA,KAAK,EAAC;AAAgB;;EAKtBE,KAAyB,EAAzB;IAAA;EAAA;AAAyB;;;EAIOF,KAAK,EAAC;;;EAG1BA,KAAK,EAAC,UAAU;EAACE,KAA0B,EAA1B;IAAA;EAAA;;;EAEdF,KAAK,EAAC;AAAS;;;EAQIA,KAAK,EAAC;;;EAC5BA,KAAK,EAAC;AAAW;;;;EAmBpBA,KAAK,EAAC;AAAS;;;EAUKA,KAAK,EAAC;;;EAgBjCE,KAA4C,EAA5C;IAAA;IAAA;EAAA;AAA4C;;;EAOdF,KAAK,EAAC;;;EACtCA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAyB;iEAClCC,mBAAA,CAAiD;EAA5CD,KAAK,EAAC;AAA0B,GAAC,OAAK;;EACtCA,KAAK,EAAC;AAA0B;;EAChCA,KAAK,EAAC;AAAwB;;EAc5BA,KAAK,EAAC;AAAe;;EAM3BE,KAA4C,EAA5C;IAAA;IAAA;EAAA;AAA4C;;;EAMhBF,KAAK,EAAC;;;EAClCA,KAAK,EAAC;AAAa;;;EACjBA,KAAK,EAAC;;;EAKNA,KAAK,EAAC;AAAU;;EAUlBA,KAAK,EAAC;AAAW;iEACpBC,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAO,GAAC,QAEnB;;;EAKGA,KAAK,EAAC;;iEACTC,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAO,GAAC,cAEnB;;EAIWA,KAAK,EAAC;AAAY;;;EAClBA,KAAK,EAAC;;;EAsBhBA,KAAK,EAAC;AAAe;;;EAuCrBA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAlRhCG,mBAAA,CA2RM,OA3RNC,UA2RM,GA1RJC,YAAA,CA+NSC,iBAAA;sBA9NP,MAgMS,CAhMTD,YAAA,CAgMSE,iBAAA;MAhMAC,IAAI,EAAE;IAAE;wBACf,MAqEM,CArEKC,MAAA,CAAAC,QAAQ,e,cAAnBP,mBAAA,CAqEM,OArENQ,UAqEM,GApEJN,YAAA,CAmEUO,kBAAA;QAnEAC,KAAK,EAAEJ,MAAA,CAAAK,MAAM;QAAGC,KAAK,EAAEN,MAAA,CAAAO,WAAW;QAAEC,GAAG,EAAC,WAAW;QAAC,aAAW,EAAC;;0BACxE,MAEe,CAFfZ,YAAA,CAEea,uBAAA;UAFDC,KAAK,EAAC,KAAK;UAACC,IAAI,EAAC;;4BAC7B,MAA4E,CAA5Ef,YAAA,CAA4EgB,mBAAA;YAAlEC,IAAI,EAAC,OAAO;wBAAUb,MAAA,CAAAK,MAAM,CAACS,IAAI;uEAAXd,MAAA,CAAAK,MAAM,CAACS,IAAI,GAAAC,MAAA;YAAEC,WAAW,EAAC;;;YAE3DpB,YAAA,CAKea,uBAAA;UALDC,KAAK,EAAC,MAAM;UAACC,IAAI,EAAC;;4BAC9B,MAGiB,CAHjBf,YAAA,CAGiBqB,yBAAA;wBAHQjB,MAAA,CAAAK,MAAM,CAACa,QAAQ;uEAAflB,MAAA,CAAAK,MAAM,CAACa,QAAQ,GAAAH,MAAA;;8BACtC,MAA0C,CAA1CnB,YAAA,CAA0CuB,mBAAA;cAAhCT,KAAK,EAAC;YAAU;gCAAC,MAAI,C,iBAAJ,MAAI,E;;gBAC/Bd,YAAA,CAA0CuB,mBAAA;cAAhCT,KAAK,EAAC;YAAW;gCAAC,MAAG,C,iBAAH,KAAG,E;;;;;;;YAGgBV,MAAA,CAAAK,MAAM,CAACa,QAAQ,mB,cAAlEE,YAAA,CAUeX,uBAAA;;UAVDC,KAAK,EAAC,OAAO;UAACC,IAAI,EAAC;;4BAC/B,MAQwC,CARxCf,YAAA,CAQwCyB,yBAAA;wBAP7BrB,MAAA,CAAAK,MAAM,CAACiB,SAAS;uEAAhBtB,MAAA,CAAAK,MAAM,CAACiB,SAAS,GAAAP,MAAA;YACzBQ,IAAI,EAAC,UAAU;YACfP,WAAW,EAAC,QAAQ;YACpBzB,KAAK,EAAC,YAAY;YACjB,cAAY,MAAMiC,IAAI;YACvBX,IAAI,EAAC,OAAO;YACXY,QAAM,EAAEzB,MAAA,CAAA0B,eAAe;YACxBjC,KAAoB,EAApB;cAAA;YAAA;;;iDAE6CO,MAAA,CAAAK,MAAM,CAACa,QAAQ,mB,cAAhEE,YAAA,CAUeX,uBAAA;;UAVDC,KAAK,EAAC,OAAO;UAACC,IAAI,EAAC;;4BAC/B,MAQwC,CARxCf,YAAA,CAQwCyB,yBAAA;wBAP7BrB,MAAA,CAAAK,MAAM,CAACsB,OAAO;uEAAd3B,MAAA,CAAAK,MAAM,CAACsB,OAAO,GAAAZ,MAAA;YACvBQ,IAAI,EAAC,UAAU;YACfP,WAAW,EAAC,QAAQ;YACpBzB,KAAK,EAAC,YAAY;YACjB,cAAY,MAAMiC,IAAI;YACvBX,IAAI,EAAC,OAAO;YACXY,QAAM,EAAEzB,MAAA,CAAA4B,aAAa;YACtBnC,KAAoB,EAApB;cAAA;YAAA;;;iDAEJG,YAAA,CAQea,uBAAA;UARDC,KAAK,EAAC,KAAK;UAACC,IAAI,EAAC;;4BAC7B,MAMc,CANdf,YAAA,CAMciC,sBAAA;YANDpC,KAAoB,EAApB;cAAA;YAAA,CAAoB;YACpBoB,IAAI,EAAC,OAAO;wBACHb,MAAA,CAAA8B,aAAa;uEAAb9B,MAAA,CAAA8B,aAAa,GAAAf,MAAA;YACrBgB,KAAK,EAAE;cAAAC,QAAA;cAAAC,aAAA;YAAA,CAAuC;YAC9CC,OAAO,EAAElC,MAAA,CAAAmC,eAAe;YACxBV,QAAM,EAAEzB,MAAA,CAAAoC;;;YAGxBxC,YAAA,CAEea,uBAAA;UAFDC,KAAK,EAAC,KAAK;UAACC,IAAI,EAAC;;4BAC7B,MAA8E,CAA9Ef,YAAA,CAA8EgB,mBAAA;YAApEC,IAAI,EAAC,OAAO;wBAAUb,MAAA,CAAAK,MAAM,CAACgC,MAAM;uEAAbrC,MAAA,CAAAK,MAAM,CAACgC,MAAM,GAAAtB,MAAA;YAAEC,WAAW,EAAC;;;YAE7DpB,YAAA,CAGea,uBAAA;UAHDC,KAAK,EAAC,KAAK;UAACC,IAAI,EAAC;;4BAC7B,MAAqI,CAArIf,YAAA,CAAqI0C,0BAAA;YAApH/C,KAAK,EAAC,cAAc;wBAAUS,MAAA,CAAAK,MAAM,CAACkC,KAAK;uEAAZvC,MAAA,CAAAK,MAAM,CAACkC,KAAK,GAAAxB,MAAA;YAAEC,WAAW,EAAC,OAAO;YAAEwB,SAAS,EAAE,CAAC;YAAGC,IAAI,EAAE,CAAC;YAAGC,GAAG,EAAE;mDAChH9C,YAAA,CAA6I0C,0BAAA;YAA5H/C,KAAK,EAAC,cAAc;wBAAUS,MAAA,CAAAK,MAAM,CAACsC,aAAa;uEAApB3C,MAAA,CAAAK,MAAM,CAACsC,aAAa,GAAA5B,MAAA;YAAEC,WAAW,EAAC,OAAO;YAAEwB,SAAS,EAAE,CAAC;YAAGC,IAAI,EAAE,CAAC;YAAGC,GAAG,EAAE;;;YAE1H9C,YAAA,CAWea,uBAAA;UAXDC,KAAK,EAAC,KAAK;UAACC,IAAI,EAAC;;4BAC7B,MAQS,CARTf,YAAA,CAQSgD,iBAAA;YAPNrD,KAAK,EAAAsD,eAAA;cAAA,WAAc7C,MAAA,CAAAK,MAAM,CAACyC;YAAK;YAC/B,mBAAiB,EAAE9C,MAAA,CAAA+C,oBAAoB;YACvC,kBAAgB,EAAE/C,MAAA,CAAAgD,mBAAmB;YACrCC,KAAK,EAAEjD,MAAA,CAAAkD,UAAU,CAACD,KAAK;YACvB,YAAU,EAAEjD,MAAA,CAAAkD,UAAU,CAACC,GAAG;YAC1BC,KAAK,EAAE,CAAC;YACTC,MAAM,EAAC;8GAETC,UAAoE,C;;YAEtE1D,YAAA,CAEea,uBAAA;UAFDC,KAAK,EAAC,OAAO;UAACC,IAAI,EAAC;;4BAC/B,MAAmF,CAAhEX,MAAA,CAAAuD,kBAAkB,I,cAArCnC,YAAA,CAAmFoC,sBAAA;;wBAAnCxD,MAAA,CAAAK,MAAM,CAACoD,YAAY;uEAAnBzD,MAAA,CAAAK,MAAM,CAACoD,YAAY,GAAA1C,MAAA;;;YAErEvB,mBAAA,CAGM,OAHNkE,UAGM,GAFwD1D,MAAA,CAAAK,MAAM,CAACsD,EAAE,I,cAArEvC,YAAA,CAAsFwC,oBAAA;;UAA3E/C,IAAI,EAAC,OAAO;UAAEgD,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA/C,MAAA,IAAEf,MAAA,CAAA+D,SAAS;;4BAA8B,MAAG,C,iBAAH,KAAG,E;;iDAC1EnE,YAAA,CAA8DgE,oBAAA;UAAnD/C,IAAI,EAAC,OAAO;UAAEgD,OAAK,EAAE7D,MAAA,CAAAgE;;4BAAgB,MAAE,C,iBAAF,IAAE,E;;;;oFAI7ChE,MAAA,CAAAC,QAAQ,kB,cAAnBP,mBAAA,CAoDM,OApDNuE,UAoDM,GAnDJzE,mBAAA,CAIM,OAJN0E,UAIM,GAHJtE,YAAA,CAAkEgE,oBAAA;QAAvD/C,IAAI,EAAC,OAAO;QAAEgD,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA/C,MAAA,IAAEf,MAAA,CAAA+D,SAAS;;0BAAU,MAAG,C,iBAAH,KAAG,E;;UACtDnE,YAAA,CAAsEgE,oBAAA;QAA3D/C,IAAI,EAAC,OAAO;QAAEgD,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA/C,MAAA,IAAEf,MAAA,CAAA+D,SAAS;;0BAAc,MAAG,C,iBAAH,KAAG,E;;UAC1DnE,YAAA,CAA6DgE,oBAAA;QAAlD/C,IAAI,EAAC,OAAO;QAAEgD,OAAK,EAAE7D,MAAA,CAAAmE;;0BAAa,MAAI,C,iBAAJ,MAAI,E;;wCAEnD3E,mBAAA,CA6CM,OA7CN4E,UA6CM,GA5CJxE,YAAA,CA2CWyE,mBAAA;QA3CD7D,GAAG,EAAC,OAAO;QAAE8D,cAAa,EAAEC,QAAA,CAAAC,kBAAkB;QAAG,oBAAkB,EAAE,KAAK;QAAGC,IAAI,EAAEzE,MAAA,CAAA0E,WAAW;QAAG,aAAW,EAAE,KAAK;QAAG,uBAAqB,EAAE,IAAI;QAAEjF,KAAmB,EAAnB;UAAA;QAAA;;0BAC3J,MAsBkB,CAtBlBG,YAAA,CAsBkB+E,0BAAA;UAtBDpD,IAAI,EAAC;QAAQ;UACjBqD,OAAO,EAAAC,QAAA,CAAE9C,KAAK,KACZA,KAAK,CAAC+C,GAAG,CAACzC,MAAM,I,cAA3B3C,mBAAA,CAAoE,OAApEqF,UAAoE,EAAAC,gBAAA,CAAxBjD,KAAK,CAAC+C,GAAG,CAACzC,MAAM,oB,sDAC5D3C,mBAAA,CAiBUuF,SAAA,QAAAC,WAAA,CAjBkCnD,KAAK,CAAC+C,GAAG,CAACK,kBAAkB,EAAvCC,OAAO;iCAAxChE,YAAA,CAiBUiE,kBAAA;cAjBD9F,KAAK,EAAC,UAAU;cAAkD+F,GAAG,EAAEF,OAAO,CAACG;;cAC3EC,MAAM,EAAAX,QAAA,CACf,MAOM,CAPNrF,mBAAA,CAOM,OAPNiG,UAOM,GANJjG,mBAAA,CAA8B,cAAAwF,gBAAA,CAAtBI,OAAO,CAACG,KAAK,kBACrB/F,mBAAA,CAIO,QAJPkG,WAIO,GAHL9F,YAAA,CAAkGgE,oBAAA;gBAAvFrC,IAAI,EAAC,MAAM;gBAACV,IAAI,EAAC,OAAO;gBAAEgD,OAAK,EAAA9C,MAAA,IAAEqE,OAAO,CAACO,SAAS,IAAIP,OAAO,CAACO;;kCAAW,MAAE,C,iBAAF,IAAE,E;;gEACtF/F,YAAA,CAAqGgE,oBAAA;gBAA1FrC,IAAI,EAAC,MAAM;gBAACV,IAAI,EAAC,OAAO;gBAAEgD,OAAK,EAAA9C,MAAA,IAAEf,MAAA,CAAA4F,kBAAkB,CAAC7D,KAAK,CAAC+C,GAAG,CAACnB,EAAE,EAAEyB,OAAO;;kCAAG,MAAE,C,iBAAF,IAAE,E;;gEACzFxF,YAAA,CAA4FgE,oBAAA;gBAAjFrC,IAAI,EAAC,MAAM;gBAACV,IAAI,EAAC,OAAO;gBAAEgD,OAAK,EAAA9C,MAAA,IAAEf,MAAA,CAAA6F,oBAAoB,CAACT,OAAO,CAACzB,EAAE;;kCAAG,MAAE,C,iBAAF,IAAE,E;;;gCAItF,MAKM,CALNnE,mBAAA,CAKM;gBALDD,KAAK,EAAAsD,eAAA,EAAC,eAAe;kBAAA,QAAkBuC,OAAO,CAACO;gBAAS;kBAChDP,OAAO,CAAC/C,MAAM,I,cAAzB3C,mBAAA,CAAgE,OAAhEoG,WAAgE,EAAAd,gBAAA,CAAtBI,OAAO,CAAC/C,MAAM,oB,mCACxD7C,mBAAA,CAEM,OAFNuG,WAEM,GADJvG,mBAAA,CAAsG;gBAA9FwG,GAAG,EAAEZ,OAAO,CAACjC,GAAG;gBAAE8C,QAAQ,EAAC,UAAU;gBAAExG,KAAK,EAAAyG,eAAA;kBAAA,sBAAyBd,OAAO,CAAC/C;gBAAM;;;;;;;;YAMrGzC,YAAA,CASkB+E,0BAAA;UATDhE,IAAI,EAAC,OAAO;UAACD,KAAK,EAAC;;UACvBkE,OAAO,EAAAC,QAAA,CAChB,CAKM;YANcC;UAAG,OACvBtF,mBAAA,CAKM;YAJJD,KAAK,EAAC,iBAAiB;YACtBsE,OAAK,EAAA9C,MAAA,IAAEwD,QAAA,CAAA4B,kBAAkB,CAACrB,GAAG;8BAE3BA,GAAG,CAACS,KAAK,wBAAAa,WAAA,E;;YAIlBxG,YAAA,CAQkB+E,0BAAA;UARDjE,KAAK,EAAC;QAAI;UACdkE,OAAO,EAAAC,QAAA,CAAEwB,CAAC,KACnB7G,mBAAA,CAIO,QAJP8G,WAIO,GAHL1G,YAAA,CAA4FgE,oBAAA;YAAjFrC,IAAI,EAAC,MAAM;YAAEsC,OAAK,EAAA9C,MAAA,IAAEf,MAAA,CAAA4F,kBAAkB,CAACS,CAAC,CAACvB,GAAG,CAACnB,EAAE;YAAG9C,IAAI,EAAC;;8BAAQ,MAAM,C,iBAAN,QAAM,E;;4DAChFjB,YAAA,CAA8EgE,oBAAA;YAAnErC,IAAI,EAAC,MAAM;YAAEsC,OAAK,EAAA9C,MAAA,IAAEf,MAAA,CAAAmE,WAAW,CAACkC,CAAC,CAACvB,GAAG;YAAGjE,IAAI,EAAC;;8BAAQ,MAAE,C,iBAAF,IAAE,E;;4DAClEjB,YAAA,CAAmFgE,oBAAA;YAAxErC,IAAI,EAAC,MAAM;YAAEsC,OAAK,EAAA9C,MAAA,IAAEf,MAAA,CAAAuG,aAAa,CAACF,CAAC,CAACvB,GAAG,CAACnB,EAAE;YAAG9C,IAAI,EAAC;;8BAAQ,MAAE,C,iBAAF,IAAE,E;;;;;;;8FAOxEb,MAAA,CAAAC,QAAQ,mB,cAAnBP,mBAAA,CAsBM,OAtBN8G,WAsBM,GArBJ5G,YAAA,CAoBUO,kBAAA;QApBAC,KAAK,EAAEJ,MAAA,CAAAyG,QAAQ;QAAGnG,KAAK,EAAEN,MAAA,CAAA0G,aAAa;QAAElG,GAAG,EAAC,aAAa;QAAC,aAAW,EAAC;;0BAC9E,MAEe,CAFfZ,YAAA,CAEea,uBAAA;UAFDC,KAAK,EAAC,OAAO;UAACC,IAAI,EAAC;;4BAC/B,MAA8G,CAA9Gf,YAAA,CAA8GgB,mBAAA;YAApGC,IAAI,EAAC,OAAO;YAACU,IAAI,EAAC,UAAU;wBAAUvB,MAAA,CAAAyG,QAAQ,CAACE,OAAO;yEAAhB3G,MAAA,CAAAyG,QAAQ,CAACE,OAAO,GAAA5F,MAAA;YAAG6F,IAAI,EAAE,EAAE;YAAE5F,WAAW,EAAC;;;YAE3FpB,YAAA,CAUea,uBAAA;UAVDC,KAAK,EAAC;QAAO;4BACzB,MAQS,CARTd,YAAA,CAQSgD,iBAAA;YAPP,WAAS,EAAC,MAAM;YACf,mBAAiB,EAAE5C,MAAA,CAAA6G,iCAAiC;YACpD,kBAAgB,EAAE7G,MAAA,CAAA8G,gCAAgC;YAClD7D,KAAK,EAAEjD,MAAA,CAAA+G,kBAAkB,CAAC9D,KAAK;YAC/B,YAAU,EAAEjD,MAAA,CAAA+G,kBAAkB,CAAC5D,GAAG;YAClCC,KAAK,EAAE,CAAC;YACTC,MAAM,EAAC;;;YAGX7D,mBAAA,CAIM,OAJNwH,WAIM,GAHJpH,YAAA,CAAqEgE,oBAAA;UAA1D/C,IAAI,EAAC,OAAO;UAAEgD,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA/C,MAAA,IAAEf,MAAA,CAAA+D,SAAS;;4BAAa,MAAG,C,iBAAH,KAAG,E;;YACzDnE,YAAA,CAAyEgE,oBAAA;UAA9D/C,IAAI,EAAC,OAAO;UAAEgD,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA/C,MAAA,IAAEf,MAAA,CAAA+D,SAAS;;4BAAiB,MAAG,C,iBAAH,KAAG,E;;YAC7DnE,YAAA,CAA8DgE,oBAAA;UAAnD/C,IAAI,EAAC,OAAO;UAAEgD,OAAK,EAAE7D,MAAA,CAAAiH;;4BAAgB,MAAE,C,iBAAF,IAAE,E;;;;oFAI7CjH,MAAA,CAAAC,QAAQ,sB,cAAnBP,mBAAA,CA8BM,OA9BNwH,WA8BM,GA7BJ1H,mBAAA,CAuBM,OAvBN2H,WAuBM,GAtBJ3H,mBAAA,CAWM,OAXN4H,WAWM,GAVJC,WAAiD,E,+BACjD3H,mBAAA,CAAiI,OAAjI4H,WAAiI,G,kCAAjEtH,MAAA,CAAAuH,mBAAmB,CAAC5D,EAAE,GAAG3D,MAAA,CAAAuH,mBAAmB,CAACzG,IAAI,yB,0BAAhEd,MAAA,CAAAwH,WAAW,E,GAC5DhI,mBAAA,CAOM,OAPNiI,WAOM,GANJjI,mBAAA,CAEM,cADJI,YAAA,CAAuEgE,oBAAA;QAA5D/C,IAAI,EAAC,OAAO;QAAEgD,OAAK,EAAE7D,MAAA,CAAA0H;;0BAAyB,MAAE,C,iBAAF,IAAE,E;;wCAE7DlI,mBAAA,CAEM,cADJI,YAAA,CAA2DgE,oBAAA;QAAhD/C,IAAI,EAAC,OAAO;QAAEgD,OAAK,EAAE7D,MAAA,CAAA2H;;0BAAa,MAAE,C,iBAAF,IAAE,E;;4CAIrD/H,YAAA,CASYgI,oBAAA;QATDnI,KAAwB,EAAxB;UAAA;QAAA,CAAwB;QAAC8F,KAAK,EAAC,MAAM;oBAAUvF,MAAA,CAAA6H,mBAAmB;qEAAnB7H,MAAA,CAAA6H,mBAAmB,GAAA9G,MAAA;QAAG,cAAY,EAAEf,MAAA,CAAA8H;;QAIjFC,MAAM,EAAAlD,QAAA,CACf,MAEM,CAFNrF,mBAAA,CAEM,OAFNwI,WAEM,GADJpI,YAAA,CAA4DgE,oBAAA;UAAjD/C,IAAI,EAAC,OAAO;UAAEgD,OAAK,EAAE7D,MAAA,CAAA8H;;4BAAa,MAAG,C,iBAAH,KAAG,E;;;0BALpD,MAEM,CAFNtI,mBAAA,CAEM,cADuBQ,MAAA,CAAA6H,mBAAmB,I,cAA9CzG,YAAA,CAAuG6G,8BAAA;;UAAtDC,QAAQ,EAAE,KAAK;UAAGC,WAAW,EAAEnI,MAAA,CAAAuH;;;2DAStF/H,mBAAA,CAIM,OAJN4I,WAIM,GAHJxI,YAAA,CAAsEgE,oBAAA;QAA3D/C,IAAI,EAAC,OAAO;QAAEgD,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA/C,MAAA,IAAEf,MAAA,CAAA+D,SAAS;;0BAAc,MAAG,C,iBAAH,KAAG,E;;UAC1DnE,YAAA,CAAqEgE,oBAAA;QAA1D/C,IAAI,EAAC,OAAO;QAAEgD,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA/C,MAAA,IAAEf,MAAA,CAAA+D,SAAS;;0BAAa,MAAG,C,iBAAH,KAAG,E;;UACzDnE,YAAA,CAAyEgE,oBAAA;QAA9D/C,IAAI,EAAC,OAAO;QAAEgD,OAAK,EAAE7D,MAAA,CAAAqI;;0BAA2B,MAAE,C,iBAAF,IAAE,E;;+EAGtDrI,MAAA,CAAAC,QAAQ,kB,cAAnBP,mBAAA,CAaM,OAbN4I,WAaM,GAZJ9I,mBAAA,CAWM,OAXN+I,WAWM,GAV8BvI,MAAA,CAAAK,MAAM,CAACmI,MAAM,I,cAA/C9I,mBAAA,CAIM,OAJN+I,WAIM,GAHuGzI,MAAA,CAAAK,MAAM,CAACmI,MAAM,oB,cAAxHpH,YAAA,CAAqJsH,mBAAA;;QAA1InD,KAAK,EAAEvF,MAAA,CAAA2I,SAAS,CAAC3I,MAAA,CAAAK,MAAM,CAACmI,MAAM;QAAGI,MAAM,EAAC,MAAM;QAACrH,IAAI,EAAC,SAAS;QAAEsH,QAAQ,EAAE,KAAK;QAAE,WAAS,EAAT;4CACqB7I,MAAA,CAAAK,MAAM,CAACmI,MAAM,sB,cAA7HpH,YAAA,CAA6JsH,mBAAA;;QAAlJnD,KAAK,EAAEvF,MAAA,CAAA2I,SAAS,CAAC3I,MAAA,CAAAK,MAAM,CAACmI,MAAM;QAAGI,MAAM,EAAC,MAAM;QAACrH,IAAI,EAAC,SAAS;QAAEsH,QAAQ,EAAE,KAAK;QAAE,WAAS,EAAT;2DAC3FzH,YAAA,CAAsHsH,mBAAA;;QAA3GnD,KAAK,EAAEvF,MAAA,CAAA2I,SAAS,CAAC3I,MAAA,CAAAK,MAAM,CAACmI,MAAM;QAAGI,MAAM,EAAC,MAAM;QAACrH,IAAI,EAAC,OAAO;QAAEsH,QAAQ,EAAE,KAAK;QAAE,WAAS,EAAT;kFAE3FrJ,mBAAA,CAIM,OAJNsJ,WAIM,GAHJlJ,YAAA,CAAyEgE,oBAAA;QAA9D/C,IAAI,EAAC,OAAO;QAAEgD,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA/C,MAAA,IAAEf,MAAA,CAAA+D,SAAS;;0BAAiB,MAAG,C,iBAAH,KAAG,E;;UACd/D,MAAA,CAAAK,MAAM,CAACmI,MAAM,sB,cAA5DpH,YAAA,CAAgGwC,oBAAA;;QAArF/C,IAAI,EAAC,OAAO;QAAEgD,OAAK,EAAE7D,MAAA,CAAA+I;;0BAAgD,MAAI,C,iBAAJ,MAAI,E;;2EACnC/I,MAAA,CAAAK,MAAM,CAACmI,MAAM,oB,cAA9DpH,YAAA,CAAgGwC,oBAAA;;QAArF/C,IAAI,EAAC,OAAO;QAAEgD,OAAK,EAAE7D,MAAA,CAAAgJ;;0BAAgD,MAAI,C,iBAAJ,MAAI,E;;;;QAK5FpJ,YAAA,CA4BSE,iBAAA;MA5BAC,IAAI,EAAE,CAAC;MAAEN,KAA2B,EAA3B;QAAA;MAAA;;wBAChB,MA0BW,CA1BXG,YAAA,CA0BWqJ,mBAAA;QA1BAC,MAAM,EAAE,EAAE;QAAE3J,KAAK,EAAC;;0BAC3B,MAOM,CAPNC,mBAAA,CAOM,OAPN2J,WAOM,GANJC,WAEM,EACNxJ,YAAA,CAEWyJ,mBAAA;UAFD9J,KAAK,EAAC,OAAO;UAAC,eAAa,EAAC,SAAS;UAAC+J,SAAS,EAAC,UAAU;UAAEC,MAAM,EAAEvJ,MAAA,CAAAwJ;;4BACnE,MAAuB,E,kBAAhC9J,mBAAA,CAA2JuF,SAAA,QAAAC,WAAA,CAAjIlF,MAAA,CAAAyJ,KAAK,EAAdhH,IAAI;iCAArBrB,YAAA,CAA2JsI,kBAAA;cAAzHpE,GAAG,EAAE7C,IAAI,CAAC6C,GAAG;cAAGzB,OAAK,EAAA9C,MAAA,IAAEf,MAAA,CAAA+D,SAAS,CAACtB,IAAI,CAAC6C,GAAG;cAAI/F,KAAK,EAAAsD,eAAA;gBAAA,eAAkB7C,MAAA,CAAAC,QAAQ,KAAKwC,IAAI,CAAC6C;cAAG;cAAIC,KAAK,EAAE9C,IAAI,CAAC3B;;;;;yCAGlHd,MAAA,CAAAC,QAAQ,kB,cAArCP,mBAAA,CAgBM,OAhBNiK,WAgBM,GAfJC,WAEM,EACNhK,YAAA,CAWYiK,oBAAA;UAXDtK,KAAK,EAAC,WAAW;sBAAUS,MAAA,CAAA0E,WAAW;uEAAX1E,MAAA,CAAA0E,WAAW,GAAA3D,MAAA;UAAE,cAAY,EAAC,QAAQ;UAAC,gBAAc,EAAC,MAAM;UAAC+I,KAAK,EAAC,MAAM;UAACC,SAAS,EAAC,MAAM;UAAEtI,QAAM,EAAEzB,MAAA,CAAAgK;;4BACpI,MASmB,CATnBpK,YAAA,CASmBqK,gBAAA;8BARC,MAA2B,E,kBAA7CvK,mBAAA,CAOMuF,SAAA,QAAAC,WAAA,CAP2BlF,MAAA,CAAA0E,WAAW,EAAnBwF,IAAI;mCAA7BxK,mBAAA,CAOM;gBAPDH,KAAK,EAAC,MAAM;gBAA8B+F,GAAG,EAAE4E,IAAI,CAACvG;kBACvDnE,mBAAA,CAA4C,OAA5C2K,WAA4C,EAAAnF,gBAAA,CAAlBkF,IAAI,CAAC3E,KAAK,kBACH2E,IAAI,CAAC/E,kBAAkB,IAAI+E,IAAI,CAAC/E,kBAAkB,CAACiF,MAAM,I,cAA1F1K,mBAAA,CAIM,OAJN2K,WAIM,GAHJzK,YAAA,CAEYiK,oBAAA;4BAFQK,IAAI,CAAC/E,kBAAkB;iDAAvB+E,IAAI,CAAC/E,kBAAkB,GAAApE,MAAA;gBAAE,cAAY,EAAC,QAAQ;gBAAC,gBAAc,EAAC,MAAM;gBAAC+I,KAAK,EAAC,UAAU;gBAACC,SAAS,EAAC,MAAM;gBAAEtI,QAAM,EAAEzB,MAAA,CAAAgK;;kCAC5G,MAA0C,E,kBAAhEtK,mBAAA,CAA0GuF,SAAA,QAAAC,WAAA,CAAlEgF,IAAI,CAAC/E,kBAAkB,EAAlCmF,OAAO;uCAApC5K,mBAAA,CAA0G;oBAArGH,KAAK,EAAC,UAAU;oBAA6C+F,GAAG,EAAEgF,OAAO,CAAC3G;sCAAM2G,OAAO,CAAC/E,KAAK;;;;;;;;;;;;;;;;;;;MAUpH3F,YAAA,CAeYgI,oBAAA;IAfDrC,KAAK,EAAC,MAAM;gBAAUvF,MAAA,CAAAuK,iBAAiB;iEAAjBvK,MAAA,CAAAuK,iBAAiB,GAAAxJ,MAAA;IAAG,cAAY,EAAEf,MAAA,CAAAwK;;IAStDzC,MAAM,EAAAlD,QAAA,CACf,MAGM,CAHNrF,mBAAA,CAGM,OAHNiL,WAGM,GAFJ7K,YAAA,CAA4DgE,oBAAA;MAAjD/C,IAAI,EAAC,OAAO;MAAEgD,OAAK,EAAE7D,MAAA,CAAAwK;;wBAAa,MAAG,C,iBAAH,KAAG,E;;oCAChD5K,YAAA,CAA6EgE,oBAAA;MAAlE/C,IAAI,EAAC,OAAO;MAACU,IAAI,EAAC,SAAS;MAAEsC,OAAK,EAAE7D,MAAA,CAAA0K;;wBAAe,MAAG,C,iBAAH,KAAG,E;;;sBAXrE,MAOU,CAPV9K,YAAA,CAOUO,kBAAA;MAPAC,KAAK,EAAEJ,MAAA,CAAA2K,aAAa;MAAGrK,KAAK,EAAEN,MAAA,CAAA4K,kBAAkB;MAAEpK,GAAG,EAAC;;wBAC9D,MAEe,CAFfZ,YAAA,CAEea,uBAAA;QAFDC,KAAK,EAAC,KAAK;QAAC,aAAW,EAAC,OAAO;QAACC,IAAI,EAAC;;0BACjD,MAAuG,CAAvGf,YAAA,CAAuGgB,mBAAA;UAA7FC,IAAI,EAAC,OAAO;sBAAUb,MAAA,CAAA2K,aAAa,CAACpF,KAAK;uEAAnBvF,MAAA,CAAA2K,aAAa,CAACpF,KAAK,GAAAxE,MAAA;UAAEC,WAAW,EAAC,OAAO;UAAC6J,YAAY,EAAC;;;UAExFjL,YAAA,CAEea,uBAAA;QAFDC,KAAK,EAAC,KAAK;QAAC,aAAW,EAAC,OAAO;QAACC,IAAI,EAAC;;0BACjD,MAA+G,CAA/Gf,YAAA,CAA+GgB,mBAAA;UAArGC,IAAI,EAAC,OAAO;sBAAUb,MAAA,CAAA2K,aAAa,CAACtI,MAAM;uEAApBrC,MAAA,CAAA2K,aAAa,CAACtI,MAAM,GAAAtB,MAAA;UAAEQ,IAAI,EAAC,UAAU;UAAEqF,IAAI,EAAE,CAAC;UAAE5F,WAAW,EAAC;;;;;;;;qDAUlGpB,YAAA,CAsCYgI,oBAAA;IAtCDrC,KAAK,EAAC,QAAQ;gBAAUvF,MAAA,CAAA8K,wBAAwB;iEAAxB9K,MAAA,CAAA8K,wBAAwB,GAAA/J,MAAA;IAAG,cAAY,EAAEf,MAAA,CAAA+K;;IAgC/DhD,MAAM,EAAAlD,QAAA,CACf,MAGM,CAHNrF,mBAAA,CAGM,OAHNwL,WAGM,GAFJpL,YAAA,CAAmEgE,oBAAA;MAAxD/C,IAAI,EAAC,OAAO;MAAEgD,OAAK,EAAE7D,MAAA,CAAA+K;;wBAAoB,MAAG,C,iBAAH,KAAG,E;;oCACvDnL,YAAA,CAAoFgE,oBAAA;MAAzE/C,IAAI,EAAC,OAAO;MAACU,IAAI,EAAC,SAAS;MAAEsC,OAAK,EAAE7D,MAAA,CAAAiL;;wBAAsB,MAAG,C,iBAAH,KAAG,E;;;sBAlC5E,MA8BU,CA9BVrL,YAAA,CA8BUO,kBAAA;MA9BAC,KAAK,EAAEJ,MAAA,CAAAkL,oBAAoB;MAAG5K,KAAK,EAAEN,MAAA,CAAAmL,yBAAyB;MAAE3K,GAAG,EAAC;;wBAC5E,MAEe,CAFfZ,YAAA,CAEea,uBAAA;QAFDC,KAAK,EAAC,KAAK;QAAC,aAAW,EAAC,OAAO;QAACC,IAAI,EAAC;;0BACjD,MAA8G,CAA9Gf,YAAA,CAA8GgB,mBAAA;UAApGC,IAAI,EAAC,OAAO;sBAAUb,MAAA,CAAAkL,oBAAoB,CAAC3F,KAAK;uEAA1BvF,MAAA,CAAAkL,oBAAoB,CAAC3F,KAAK,GAAAxE,MAAA;UAAEC,WAAW,EAAC,OAAO;UAAC6J,YAAY,EAAC;;;UAE/FjL,YAAA,CAGea,uBAAA;QAHDC,KAAK,EAAC,OAAO;QAAC,aAAW,EAAC,OAAO;QAACC,IAAI,EAAC;;0BACnD,MAA0E,CAA1Ef,YAAA,CAA0EuB,mBAAA;sBAAvDnB,MAAA,CAAAkL,oBAAoB,CAAC3J,IAAI;uEAAzBvB,MAAA,CAAAkL,oBAAoB,CAAC3J,IAAI,GAAAR,MAAA;UAAEL,KAAK,EAAC;;4BAAO,MAAI,C,iBAAJ,MAAI,E;;2CAC/Dd,YAAA,CAA4EuB,mBAAA;sBAAzDnB,MAAA,CAAAkL,oBAAoB,CAAC3J,IAAI;uEAAzBvB,MAAA,CAAAkL,oBAAoB,CAAC3J,IAAI,GAAAR,MAAA;UAAEL,KAAK,EAAC;;4BAAS,MAAI,C,iBAAJ,MAAI,E;;;;UAEFV,MAAA,CAAAkL,oBAAoB,CAAC3J,IAAI,e,cAA1FH,YAAA,CAGeX,uBAAA;;QAHDC,KAAK,EAAC,OAAO;QAAC,aAAW,EAAC,OAAO;QAACC,IAAI,EAAC;;0BACnD,MAA8H,CAA9Hf,YAAA,CAA8HgB,mBAAA;UAApHC,IAAI,EAAC,OAAO;UAAEuK,MAAI,EAAEpL,MAAA,CAAAqL,OAAO;sBAAWrL,MAAA,CAAAkL,oBAAoB,CAAC/H,GAAG;uEAAxBnD,MAAA,CAAAkL,oBAAoB,CAAC/H,GAAG,GAAApC,MAAA;UAAEC,WAAW,EAAC,SAAS;UAAC6J,YAAY,EAAC;2DAC7GrL,mBAAA,CAAsF;UAA/EgB,GAAG,EAAC,WAAW;UAACf,KAAsB,EAAtB;YAAA;UAAA,CAAsB;UAAEuG,GAAG,EAAEhG,MAAA,CAAAkL,oBAAoB,CAAC/H;;;2BAE3E/B,YAAA,CAWeX,uBAAA;;QAXDC,KAAK,EAAC,OAAO;QAAC,aAAW,EAAC,OAAO;QAACC,IAAI,EAAC;;0BACnD,MASS,CATTf,YAAA,CASSgD,iBAAA;UARN,kBAAgB,EAAE5C,MAAA,CAAAsL,mBAAmB;UACrC,mBAAiB,EAAEtL,MAAA,CAAAuL,oBAAoB;UACvC,kBAAgB,EAAEvL,MAAA,CAAAwL,mBAAmB;UACrCvI,KAAK,EAAEjD,MAAA,CAAAyL,eAAe,CAACxI,KAAK;UAC5B,YAAU,EAAEjD,MAAA,CAAAyL,eAAe,CAACtI,GAAG;UAC/BC,KAAK,EAAE,CAAC;UACTsI,QAAQ,EAAC,MAAM;UACfrI,MAAM,EAAC;;;WAGXzD,YAAA,CAEea,uBAAA;QAFDC,KAAK,EAAC,OAAO;QAAC,aAAW,EAAC,OAAO;QAACC,IAAI,EAAC;;0BACnD,MAAkH,CAAlHf,YAAA,CAAkHgB,mBAAA;UAAxGC,IAAI,EAAC,OAAO;sBAAUb,MAAA,CAAAkL,oBAAoB,CAACS,SAAS;uEAA9B3L,MAAA,CAAAkL,oBAAoB,CAACS,SAAS,GAAA5K,MAAA;UAAEC,WAAW,EAAC,OAAO;UAAC6J,YAAY,EAAC;;;UAEnGjL,YAAA,CAEea,uBAAA;QAFDC,KAAK,EAAC,KAAK;QAAC,aAAW,EAAC,OAAO;QAACC,IAAI,EAAC;;0BACjD,MAAsH,CAAtHf,YAAA,CAAsHgB,mBAAA;UAA5GC,IAAI,EAAC,OAAO;sBAAUb,MAAA,CAAAkL,oBAAoB,CAAC7I,MAAM;uEAA3BrC,MAAA,CAAAkL,oBAAoB,CAAC7I,MAAM,GAAAtB,MAAA;UAAEQ,IAAI,EAAC,UAAU;UAAEqF,IAAI,EAAE,CAAC;UAAE5F,WAAW,EAAC;;;;;;;;qDAUzGpB,YAAA,CAEYgI,oBAAA;IAFD,cAAY,EAAC,eAAe;IAACrC,KAAK,EAAC,MAAM;gBAAUvF,MAAA,CAAA4L,2BAA2B;iEAA3B5L,MAAA,CAAA4L,2BAA2B,GAAA7K,MAAA;IAAG,cAAY,EAAEf,MAAA,CAAA6L,uBAAuB;IAAEC,KAAK,EAAC;;sBACvI,MAAyI,CAAzIlM,YAAA,CAAyImM,oCAAA;MAA7G,iBAAe,EAAE/L,MAAA,CAAA6L,uBAAuB;MAAG,iBAAe,EAAE7L,MAAA,CAAAgM,yBAAyB;MAAG,cAAY,EAAE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}