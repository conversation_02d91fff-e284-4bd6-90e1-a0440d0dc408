{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref } from \"vue\";\nimport { useRoute } from \"vue-router\";\nimport router from \"../../../router\";\nimport { saveNews, updateNews, getNews } from \"../../../api/content/news\";\nimport Upload from \"../../../components/Uplaod\";\nimport WangEditor from \"@/components/WangEditor/index.vue\";\nimport { success } from \"../../../util/tipsUtils\";\nexport default {\n  name: \"NewsContentEdit\",\n  components: {\n    Upload,\n    WangEditor\n  },\n  setup() {\n    const loadWangEditorFlag = ref(false);\n    const route = useRoute();\n    const isUpdate = !!route.query.id;\n    // 基本信息\n    const uploadData = ref({\n      url: process.env.VUE_APP_BASE_API + \"/oss/content/news/image\",\n      files: []\n    });\n    const news = ref({\n      id: \"\",\n      title: \"\",\n      image: \"\",\n      status: \"published\",\n      tags: \"\",\n      keywords: \"\",\n      content: \"\",\n      description: \"\"\n    });\n    const newsRules = {\n      title: [{\n        required: true,\n        message: \"请输入标题\",\n        trigger: \"blur\"\n      }],\n      content: [{\n        required: true,\n        message: \"请输入内容\",\n        trigger: \"blur\"\n      }],\n      description: [{\n        required: true,\n        message: \"请输入导语\",\n        trigger: \"blur\"\n      }],\n      image: [{\n        required: true,\n        message: \"请选择海报\",\n        trigger: \"change\"\n      }]\n    };\n    const tags = ref([]);\n    const tag = ref(\"\");\n    const tagsVisible = ref(false);\n    const tagsRef = ref(null);\n    const showTagsInput = () => {\n      tagsVisible.value = true;\n    };\n    const tagsInputConfirm = () => {\n      if (tag.value) {\n        tags.value.push(tag.value);\n        tag.value = \"\";\n      }\n      tagsVisible.value = false;\n    };\n    const delTag = index => {\n      tags.value.splice(index, 1);\n    };\n    const keywords = ref([]);\n    const keyword = ref(\"\");\n    const keywordsVisible = ref(false);\n    const keywordsRef = ref(null);\n    const showKeywordsInput = () => {\n      keywordsVisible.value = true;\n    };\n    const keywordsInputConfirm = () => {\n      if (keyword.value) {\n        keywords.value.push(keyword.value);\n        keyword.value = \"\";\n      }\n      keywordsVisible.value = false;\n    };\n    const delKeyword = index => {\n      keywords.value.splice(index, 1);\n    };\n    // 加载基本信息\n    const load = () => {\n      let id = route.query.id;\n      if (!id) {\n        loadWangEditorFlag.value = true;\n        return;\n      }\n      getNews(id, function (res) {\n        news.value = res;\n        if (res && res.tags) {\n          tags.value = res.tags.split(\",\");\n        }\n        if (res && res.keywords) {\n          keywords.value = res.keywords.split(\",\");\n        }\n        uploadData.value.files = [{\n          name: \"海报\",\n          url: news.value.image\n        }];\n        loadWangEditorFlag.value = true;\n      });\n    };\n    load();\n    // 上传图片成功\n    const onUploadImageSuccess = res => {\n      news.value.image = res.data;\n    };\n    // 删除图片\n    const onUploadImageRemove = () => {\n      news.value.image = \"\";\n      uploadData.value.files = [];\n    };\n    // 提交基本信息\n    const newsRef = ref(null);\n    const submitNews = () => {\n      newsRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (tags.value && tags.value.length) {\n          news.value.tags = tags.value.join(\",\");\n        }\n        if (keywords.value && keywords.value.length) {\n          news.value.keywords = keywords.value.join(\",\");\n        }\n        if (isUpdate) {\n          updateNews(news.value, function (res) {\n            if (res && res.id) {\n              news.value.id = res.id;\n              success(\"编辑成功\");\n              router.push({\n                path: \"/news/list\"\n              });\n            }\n          });\n        } else {\n          saveNews(news.value, function (res) {\n            if (res && res.id) {\n              news.value.id = res.id;\n              success(\"新增成功\");\n              router.push({\n                path: \"/news/list\"\n              });\n            }\n          });\n        }\n      });\n    };\n    const submitNewsDraft = () => {\n      news.value.status = \"draft\";\n      submitNews();\n    };\n    return {\n      uploadData,\n      news,\n      newsRules,\n      newsRef,\n      onUploadImageSuccess,\n      onUploadImageRemove,\n      submitNews,\n      submitNewsDraft,\n      tags,\n      tag,\n      tagsVisible,\n      tagsRef,\n      showTagsInput,\n      tagsInputConfirm,\n      delTag,\n      keywords,\n      keyword,\n      keywordsVisible,\n      keywordsRef,\n      showKeywordsInput,\n      keywordsInputConfirm,\n      delKeyword,\n      loadWangEditorFlag\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "useRoute", "router", "saveNews", "updateNews", "getNews", "Upload", "WangEditor", "success", "name", "components", "setup", "loadWangEditorFlag", "route", "isUpdate", "query", "id", "uploadData", "url", "process", "env", "VUE_APP_BASE_API", "files", "news", "title", "image", "status", "tags", "keywords", "content", "description", "newsRules", "required", "message", "trigger", "tag", "tagsVisible", "tagsRef", "showTagsInput", "value", "tagsInputConfirm", "push", "delTag", "index", "splice", "keyword", "keywordsVisible", "keywordsRef", "showKeywordsInput", "keywordsInputConfirm", "delKeyword", "load", "res", "split", "onUploadImageSuccess", "data", "onUploadImageRemove", "newsRef", "submitNews", "validate", "valid", "length", "join", "path", "submitNewsDraft"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/news/content/edit.vue"], "sourcesContent": ["<template>\n  <div class=\"news-edit-wrap\">\n    <el-form :model=\"news\" :rules=\"newsRules\" ref=\"newsRef\" label-width=\"120px\">\n      <el-form-item label=\"标题：\" prop=\"title\">\n        <el-input size=\"mini\" v-model=\"news.title\" placeholder=\"请输入标题\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"导语：\" prop=\"description\">\n        <el-input size=\"mini\" v-model=\"news.description\" placeholder=\"请输入导语\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"内容：\" prop=\"content\">\n        <wang-editor v-if=\"loadWangEditorFlag\" v-model=\"news.content\"></wang-editor>\n      </el-form-item>\n      <el-form-item label=\"封面：\" prop=\"image\">\n        <upload\n          :on-upload-success=\"onUploadImageSuccess\"\n          :on-upload-remove=\"onUploadImageRemove\"\n          :files=\"uploadData.files\"\n          :upload-url=\"uploadData.url\"\n          :limit=\"1\"\n          accept=\"image/jpeg,image/gif,image/png\">\n        </upload>\n        <span class=\"upload-image-tips\">图片建议：尺寸 1920 x 1200 像素，大小7M以下</span>\n      </el-form-item>\n      <el-form-item label=\"标签：\">\n        <el-tag size=\"mini\" :key=\"tag\" v-for=\"(tag, index) in tags\" closable :disable-transitions=\"false\" @close=\"delTag(index)\">{{tag}}</el-tag>\n        <el-input size=\"mini\" class=\"input-new-tag\" v-if=\"tagsVisible\" v-model=\"tag\" ref=\"tagsRef\" @blur=\"tagsInputConfirm\" placeholder=\"请输入标签\"></el-input>\n        <el-button v-else class=\"button-new-tag\" size=\"mini\" @click=\"showTagsInput\">+ 新增标签</el-button>\n      </el-form-item>\n      <el-form-item label=\"关键字：\">\n        <el-tag size=\"mini\" :key=\"keyword\" v-for=\"(keyword, index) in keywords\" closable :disable-transitions=\"false\" @close=\"delKeyword(index)\">{{keyword}}</el-tag>\n        <el-input size=\"mini\" class=\"input-new-tag\" v-if=\"keywordsVisible\" v-model=\"keyword\" ref=\"keywordsRef\" @blur=\"keywordsInputConfirm\" placeholder=\"请输入关键字\"></el-input>\n        <el-button v-else class=\"button-new-tag\" size=\"mini\" @click=\"showKeywordsInput\">+ 新增关键字</el-button>\n      </el-form-item>\n      <el-form-item style=\"text-align: center\">\n        <el-button size=\"mini\" @click=\"submitNewsDraft\">存草稿</el-button>\n        <el-button size=\"mini\" @click=\"submitNews\">发布</el-button>\n      </el-form-item>\n    </el-form>\n  </div>\n</template>\n<script>\n  import {ref} from \"vue\"\n  import {useRoute} from \"vue-router\"\n  import router from \"../../../router\"\n  import {saveNews, updateNews, getNews} from \"../../../api/content/news\"\n  import Upload from \"../../../components/Uplaod\"\n  import WangEditor from \"@/components/WangEditor/index.vue\"\n  import {success} from \"../../../util/tipsUtils\";\n\n  export default {\n    name: \"NewsContentEdit\",\n    components:{\n      Upload,\n      WangEditor\n    },\n    setup() {\n      const loadWangEditorFlag = ref(false)\n      const route = useRoute()\n      const isUpdate = !!route.query.id\n      // 基本信息\n      const uploadData = ref({\n        url: process.env.VUE_APP_BASE_API + \"/oss/content/news/image\",\n        files: []\n      })\n      const news = ref({\n        id: \"\",\n        title: \"\",\n        image: \"\",\n        status: \"published\",\n        tags: \"\",\n        keywords: \"\",\n        content: \"\",\n        description: \"\"\n      })\n      const newsRules = {\n        title: [{ required: true, message: \"请输入标题\", trigger: \"blur\" }],\n        content: [{ required: true, message: \"请输入内容\", trigger: \"blur\" }],\n        description: [{ required: true, message: \"请输入导语\", trigger: \"blur\" }],\n        image: [{ required: true, message: \"请选择海报\", trigger: \"change\" }],\n      }\n      const tags = ref([])\n      const tag = ref(\"\")\n      const tagsVisible = ref(false)\n      const tagsRef = ref(null)\n      const showTagsInput = () => {\n        tagsVisible.value = true\n      }\n      const tagsInputConfirm = () => {\n        if (tag.value) {\n          tags.value.push(tag.value)\n          tag.value = \"\"\n        }\n        tagsVisible.value = false\n      }\n      const delTag = (index) => {\n        tags.value.splice(index, 1)\n      }\n      const keywords = ref([])\n      const keyword = ref(\"\")\n      const keywordsVisible = ref(false)\n      const keywordsRef = ref(null)\n      const showKeywordsInput = () => {\n        keywordsVisible.value = true\n      }\n      const keywordsInputConfirm = () => {\n        if (keyword.value) {\n          keywords.value.push(keyword.value)\n          keyword.value = \"\"\n        }\n        keywordsVisible.value = false\n      }\n      const delKeyword = (index) => {\n        keywords.value.splice(index, 1)\n      }\n      // 加载基本信息\n      const load = () => {\n        let id = route.query.id;\n        if (!id) {\n          loadWangEditorFlag.value = true;\n          return;\n        }\n        getNews(id, function (res) {\n          news.value = res;\n          if (res && res.tags) {\n            tags.value = res.tags.split(\",\")\n          }\n          if (res && res.keywords) {\n            keywords.value = res.keywords.split(\",\")\n          }\n          uploadData.value.files = [{name: \"海报\", url: news.value.image}]\n          loadWangEditorFlag.value = true;\n        })\n      }\n      load()\n      // 上传图片成功\n      const onUploadImageSuccess = (res) => {\n        news.value.image = res.data\n      }\n      // 删除图片\n      const onUploadImageRemove = () => {\n        news.value.image = \"\"\n        uploadData.value.files = []\n      }\n      // 提交基本信息\n      const newsRef = ref(null)\n      const submitNews = () => {\n        newsRef.value.validate((valid) => {\n          if (!valid) { return false }\n          if (tags.value && tags.value.length) {\n            news.value.tags = tags.value.join(\",\");\n          }\n          if (keywords.value && keywords.value.length) {\n            news.value.keywords = keywords.value.join(\",\");\n          }\n          if (isUpdate) {\n            updateNews(news.value, function (res) {\n              if (res && res.id) {\n                news.value.id = res.id;\n                success(\"编辑成功\")\n                router.push({path: \"/news/list\"});\n              }\n            })\n          } else {\n            saveNews(news.value, function (res) {\n              if (res && res.id) {\n                news.value.id = res.id;\n                success(\"新增成功\")\n                router.push({path: \"/news/list\"});\n              }\n            })\n          }\n        })\n      }\n      const submitNewsDraft = () => {\n        news.value.status = \"draft\"\n        submitNews()\n      }\n      return {\n        uploadData,\n        news,\n        newsRules,\n        newsRef,\n        onUploadImageSuccess,\n        onUploadImageRemove,\n        submitNews,\n        submitNewsDraft,\n        tags,\n        tag,\n        tagsVisible,\n        tagsRef,\n        showTagsInput,\n        tagsInputConfirm,\n        delTag,\n        keywords,\n        keyword,\n        keywordsVisible,\n        keywordsRef,\n        showKeywordsInput,\n        keywordsInputConfirm,\n        delKeyword,\n        loadWangEditorFlag\n      };\n    }\n  }\n</script>\n<style scoped>\n  .news-edit-wrap {\n    padding: 40px 0;\n  }\n  .upload-image-tips {\n    font-size: 12px;\n    color: #999999;\n  }\n  .el-form-item {\n    width: 96%;\n  }\n  .el-tag {\n    margin-right: 10px;\n  }\n  .el-upload--picture-card, .el-upload-list--picture-card .el-upload-list__item {\n    width: 100%;\n    height: 62.5%;\n  }\n  .tips {\n    font-size: 12px;\n    color: #999999;\n  }\n</style>\n"], "mappings": ";AAyCE,SAAQA,GAAG,QAAO,KAAI;AACtB,SAAQC,QAAQ,QAAO,YAAW;AAClC,OAAOC,MAAK,MAAO,iBAAgB;AACnC,SAAQC,QAAQ,EAAEC,UAAU,EAAEC,OAAO,QAAO,2BAA0B;AACtE,OAAOC,MAAK,MAAO,4BAA2B;AAC9C,OAAOC,UAAS,MAAO,mCAAkC;AACzD,SAAQC,OAAO,QAAO,yBAAyB;AAE/C,eAAe;EACbC,IAAI,EAAE,iBAAiB;EACvBC,UAAU,EAAC;IACTJ,MAAM;IACNC;EACF,CAAC;EACDI,KAAKA,CAAA,EAAG;IACN,MAAMC,kBAAiB,GAAIZ,GAAG,CAAC,KAAK;IACpC,MAAMa,KAAI,GAAIZ,QAAQ,EAAC;IACvB,MAAMa,QAAO,GAAI,CAAC,CAACD,KAAK,CAACE,KAAK,CAACC,EAAC;IAChC;IACA,MAAMC,UAAS,GAAIjB,GAAG,CAAC;MACrBkB,GAAG,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAe,GAAI,yBAAyB;MAC7DC,KAAK,EAAE;IACT,CAAC;IACD,MAAMC,IAAG,GAAIvB,GAAG,CAAC;MACfgB,EAAE,EAAE,EAAE;MACNQ,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,EAAE;MACXC,WAAW,EAAE;IACf,CAAC;IACD,MAAMC,SAAQ,GAAI;MAChBP,KAAK,EAAE,CAAC;QAAEQ,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAC9DL,OAAO,EAAE,CAAC;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAChEJ,WAAW,EAAE,CAAC;QAAEE,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MACpET,KAAK,EAAE,CAAC;QAAEO,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC;IACjE;IACA,MAAMP,IAAG,GAAI3B,GAAG,CAAC,EAAE;IACnB,MAAMmC,GAAE,GAAInC,GAAG,CAAC,EAAE;IAClB,MAAMoC,WAAU,GAAIpC,GAAG,CAAC,KAAK;IAC7B,MAAMqC,OAAM,GAAIrC,GAAG,CAAC,IAAI;IACxB,MAAMsC,aAAY,GAAIA,CAAA,KAAM;MAC1BF,WAAW,CAACG,KAAI,GAAI,IAAG;IACzB;IACA,MAAMC,gBAAe,GAAIA,CAAA,KAAM;MAC7B,IAAIL,GAAG,CAACI,KAAK,EAAE;QACbZ,IAAI,CAACY,KAAK,CAACE,IAAI,CAACN,GAAG,CAACI,KAAK;QACzBJ,GAAG,CAACI,KAAI,GAAI,EAAC;MACf;MACAH,WAAW,CAACG,KAAI,GAAI,KAAI;IAC1B;IACA,MAAMG,MAAK,GAAKC,KAAK,IAAK;MACxBhB,IAAI,CAACY,KAAK,CAACK,MAAM,CAACD,KAAK,EAAE,CAAC;IAC5B;IACA,MAAMf,QAAO,GAAI5B,GAAG,CAAC,EAAE;IACvB,MAAM6C,OAAM,GAAI7C,GAAG,CAAC,EAAE;IACtB,MAAM8C,eAAc,GAAI9C,GAAG,CAAC,KAAK;IACjC,MAAM+C,WAAU,GAAI/C,GAAG,CAAC,IAAI;IAC5B,MAAMgD,iBAAgB,GAAIA,CAAA,KAAM;MAC9BF,eAAe,CAACP,KAAI,GAAI,IAAG;IAC7B;IACA,MAAMU,oBAAmB,GAAIA,CAAA,KAAM;MACjC,IAAIJ,OAAO,CAACN,KAAK,EAAE;QACjBX,QAAQ,CAACW,KAAK,CAACE,IAAI,CAACI,OAAO,CAACN,KAAK;QACjCM,OAAO,CAACN,KAAI,GAAI,EAAC;MACnB;MACAO,eAAe,CAACP,KAAI,GAAI,KAAI;IAC9B;IACA,MAAMW,UAAS,GAAKP,KAAK,IAAK;MAC5Bf,QAAQ,CAACW,KAAK,CAACK,MAAM,CAACD,KAAK,EAAE,CAAC;IAChC;IACA;IACA,MAAMQ,IAAG,GAAIA,CAAA,KAAM;MACjB,IAAInC,EAAC,GAAIH,KAAK,CAACE,KAAK,CAACC,EAAE;MACvB,IAAI,CAACA,EAAE,EAAE;QACPJ,kBAAkB,CAAC2B,KAAI,GAAI,IAAI;QAC/B;MACF;MACAlC,OAAO,CAACW,EAAE,EAAE,UAAUoC,GAAG,EAAE;QACzB7B,IAAI,CAACgB,KAAI,GAAIa,GAAG;QAChB,IAAIA,GAAE,IAAKA,GAAG,CAACzB,IAAI,EAAE;UACnBA,IAAI,CAACY,KAAI,GAAIa,GAAG,CAACzB,IAAI,CAAC0B,KAAK,CAAC,GAAG;QACjC;QACA,IAAID,GAAE,IAAKA,GAAG,CAACxB,QAAQ,EAAE;UACvBA,QAAQ,CAACW,KAAI,GAAIa,GAAG,CAACxB,QAAQ,CAACyB,KAAK,CAAC,GAAG;QACzC;QACApC,UAAU,CAACsB,KAAK,CAACjB,KAAI,GAAI,CAAC;UAACb,IAAI,EAAE,IAAI;UAAES,GAAG,EAAEK,IAAI,CAACgB,KAAK,CAACd;QAAK,CAAC;QAC7Db,kBAAkB,CAAC2B,KAAI,GAAI,IAAI;MACjC,CAAC;IACH;IACAY,IAAI,EAAC;IACL;IACA,MAAMG,oBAAmB,GAAKF,GAAG,IAAK;MACpC7B,IAAI,CAACgB,KAAK,CAACd,KAAI,GAAI2B,GAAG,CAACG,IAAG;IAC5B;IACA;IACA,MAAMC,mBAAkB,GAAIA,CAAA,KAAM;MAChCjC,IAAI,CAACgB,KAAK,CAACd,KAAI,GAAI,EAAC;MACpBR,UAAU,CAACsB,KAAK,CAACjB,KAAI,GAAI,EAAC;IAC5B;IACA;IACA,MAAMmC,OAAM,GAAIzD,GAAG,CAAC,IAAI;IACxB,MAAM0D,UAAS,GAAIA,CAAA,KAAM;MACvBD,OAAO,CAAClB,KAAK,CAACoB,QAAQ,CAAEC,KAAK,IAAK;QAChC,IAAI,CAACA,KAAK,EAAE;UAAE,OAAO,KAAI;QAAE;QAC3B,IAAIjC,IAAI,CAACY,KAAI,IAAKZ,IAAI,CAACY,KAAK,CAACsB,MAAM,EAAE;UACnCtC,IAAI,CAACgB,KAAK,CAACZ,IAAG,GAAIA,IAAI,CAACY,KAAK,CAACuB,IAAI,CAAC,GAAG,CAAC;QACxC;QACA,IAAIlC,QAAQ,CAACW,KAAI,IAAKX,QAAQ,CAACW,KAAK,CAACsB,MAAM,EAAE;UAC3CtC,IAAI,CAACgB,KAAK,CAACX,QAAO,GAAIA,QAAQ,CAACW,KAAK,CAACuB,IAAI,CAAC,GAAG,CAAC;QAChD;QACA,IAAIhD,QAAQ,EAAE;UACZV,UAAU,CAACmB,IAAI,CAACgB,KAAK,EAAE,UAAUa,GAAG,EAAE;YACpC,IAAIA,GAAE,IAAKA,GAAG,CAACpC,EAAE,EAAE;cACjBO,IAAI,CAACgB,KAAK,CAACvB,EAAC,GAAIoC,GAAG,CAACpC,EAAE;cACtBR,OAAO,CAAC,MAAM;cACdN,MAAM,CAACuC,IAAI,CAAC;gBAACsB,IAAI,EAAE;cAAY,CAAC,CAAC;YACnC;UACF,CAAC;QACH,OAAO;UACL5D,QAAQ,CAACoB,IAAI,CAACgB,KAAK,EAAE,UAAUa,GAAG,EAAE;YAClC,IAAIA,GAAE,IAAKA,GAAG,CAACpC,EAAE,EAAE;cACjBO,IAAI,CAACgB,KAAK,CAACvB,EAAC,GAAIoC,GAAG,CAACpC,EAAE;cACtBR,OAAO,CAAC,MAAM;cACdN,MAAM,CAACuC,IAAI,CAAC;gBAACsB,IAAI,EAAE;cAAY,CAAC,CAAC;YACnC;UACF,CAAC;QACH;MACF,CAAC;IACH;IACA,MAAMC,eAAc,GAAIA,CAAA,KAAM;MAC5BzC,IAAI,CAACgB,KAAK,CAACb,MAAK,GAAI,OAAM;MAC1BgC,UAAU,EAAC;IACb;IACA,OAAO;MACLzC,UAAU;MACVM,IAAI;MACJQ,SAAS;MACT0B,OAAO;MACPH,oBAAoB;MACpBE,mBAAmB;MACnBE,UAAU;MACVM,eAAe;MACfrC,IAAI;MACJQ,GAAG;MACHC,WAAW;MACXC,OAAO;MACPC,aAAa;MACbE,gBAAgB;MAChBE,MAAM;MACNd,QAAQ;MACRiB,OAAO;MACPC,eAAe;MACfC,WAAW;MACXC,iBAAiB;MACjBC,oBAAoB;MACpBC,UAAU;MACVtC;IACF,CAAC;EACH;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}