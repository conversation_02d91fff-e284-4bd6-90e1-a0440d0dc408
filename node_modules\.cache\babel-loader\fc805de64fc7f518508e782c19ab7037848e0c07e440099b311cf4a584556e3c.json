{"ast": null, "code": "import { ref } from \"vue\";\nimport Page from \"../../../components/Page\";\nimport { getMemberUnauditedList, approvedMember, rejectMember } from \"@/api/member\";\nimport { confirm } from \"@/util/tipsUtils\";\nexport default {\n  name: \"MemeberUnauditedList\",\n  components: {\n    Page\n  },\n  setup() {\n    const stateMap = {\n      \"normal\": \"正常\",\n      \"black\": \"黑名单\",\n      \"lock\": \"锁定\",\n      \"deleted\": \"注销\"\n    };\n    const total = ref(0);\n    const memberList = ref([]);\n    const dataLoading = ref(true);\n    const param = ref({\n      current: 1,\n      size: 20,\n      keyword: \"\"\n    });\n    const loadMemberList = () => {\n      dataLoading.value = true;\n      getMemberUnauditedList(param.value, res => {\n        dataLoading.value = false;\n        memberList.value = res.list;\n        total.value = res.total;\n      });\n    };\n    loadMemberList();\n    // 页码改变\n    const currentChange = currentPage => {\n      param.value.current = currentPage;\n      loadMemberList();\n    };\n    // 页面显示数量改变\n    const sizeChange = size => {\n      param.value.size = size;\n      loadMemberList();\n    };\n    const search = () => {\n      loadMemberList();\n    };\n    const approved = function (id) {\n      confirm(\"确认解禁该会员【\" + item.name + \"】\", \"解禁\", () => {\n        approvedMember({\n          id: id\n        }, res => {\n          console.log(res);\n        });\n      });\n    };\n    const reject = function (id) {\n      confirm(\"确认解禁该会员【\" + item.name + \"】\", \"解禁\", () => {\n        rejectMember({\n          id: id\n        }, res => {\n          console.log(res);\n        });\n      });\n    };\n    return {\n      stateMap,\n      param,\n      total,\n      memberList,\n      currentChange,\n      sizeChange,\n      search,\n      dataLoading,\n      approved,\n      reject\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "Page", "getMemberUnauditedList", "approvedMember", "rejectMember", "confirm", "name", "components", "setup", "stateMap", "total", "memberList", "dataLoading", "param", "current", "size", "keyword", "loadMemberList", "value", "res", "list", "currentChange", "currentPage", "sizeChange", "search", "approved", "id", "item", "console", "log", "reject"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/member/unaudited/index.vue"], "sourcesContent": ["<template>\n  <div class=\"member-container\">\n    <div class=\"head\">\n      <el-input size=\"mini\" v-model=\"param.keyword\" clearable placeholder=\"输入名称搜索\" class=\"custom-input\" @keyup.enter=\"search\">\n        <template #append>\n          <el-button size=\"mini\" class=\"custom-btn\" icon=\"el-icon-search\" @click=\"search\">搜索</el-button>\n        </template>\n      </el-input>\n    </div>\n    <el-table v-loading=\"dataLoading\" :data=\"memberList\" size=\"small\" style=\"width: 100%;\">\n      <el-table-column type=\"expand\">\n        <template #default=\"props\">\n          <el-card class=\"box-card\">\n            <template #header>\n              <div>\n                <span>基础信息</span>\n              </div>\n            </template>\n            <div class=\"table-wrapper\">\n              <table class=\"fl-table\">\n                <tbody>\n                  <tr><td>编号</td><td>{{props.row.code}}</td></tr>\n                  <tr><td>姓名</td><td>{{props.row.name}}</td></tr>\n                  <tr><td>性别</td><td>{{props.row.gender}}</td></tr>\n                  <tr><td>出生日期</td><td>{{props.row.birthday}}</td></tr>\n                  <tr><td>人员状态</td><td>{{stateMap[props.row.status]}}</td></tr>\n                  <tr><td>注册时间</td><td>{{props.row.createTime}}</td></tr>\n                  <tr><td>到期时间</td><td>{{props.row.expireTime}}</td></tr>\n                  <tr><td>手机电话</td><td>{{props.row.mobile}}</td></tr>\n                  <tr><td>座机号码</td><td>{{props.row.telephone}}</td></tr>\n                  <tr><td>电子邮箱</td><td>{{props.row.email}}</td></tr>\n                  <tr><td>会员等级</td><td>{{props.row.level && props.row.level.name || \"无\"}}</td></tr>\n                </tbody>\n              </table>\n            </div>\n          </el-card>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"username\" label=\"账号\"/>\n      <el-table-column prop=\"name\" label=\"姓名\"/>\n      <el-table-column prop=\"mobile\" label=\"手机号码\"/>\n      <el-table-column :show-overflow-tooltip=\"true\" prop=\"email\" label=\"邮箱\"/>\n      <el-table-column label=\"会员等级\">\n        <template #default=\"scope\">\n          {{scope.row.level && scope.row.level.name || \"无\"}}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"状态\" align=\"center\">\n        <template #default=\"scope\">\n          {{stateMap[scope.row.status]}}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\">\n        <template #default=\"scope\">\n          <el-button size=\"mini\" type=\"text\" @click=\"approved(scope.row.id)\">拉黑</el-button>\n          <el-button size=\"mini\" type=\"text\" @click=\"reject(scope.row.id)\">通过</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <!--分页组件-->\n    <page :total=\"total\" @size-change=\"sizeChange\" @current-change=\"currentChange\" :page-size=\"param.size\"/>\n  </div>\n</template>\n\n<script>\n  import {ref} from \"vue\"\n  import Page from \"../../../components/Page\"\n  import {getMemberUnauditedList, approvedMember, rejectMember} from \"@/api/member\";\n  import {confirm} from \"@/util/tipsUtils\";\n  export default {\n    name: \"MemeberUnauditedList\",\n    components: {\n      Page\n    },\n    setup() {\n      const stateMap = {\"normal\": \"正常\", \"black\": \"黑名单\", \"lock\": \"锁定\", \"deleted\": \"注销\"}\n      const total = ref(0)\n      const memberList = ref([])\n      const dataLoading = ref(true)\n      const param = ref({\n        current: 1,\n        size: 20,\n        keyword: \"\"\n      })\n      const loadMemberList = () => {\n        dataLoading.value = true\n        getMemberUnauditedList(param.value, res => {\n          dataLoading.value = false\n          memberList.value = res.list\n          total.value = res.total\n        })\n      }\n      loadMemberList();\n      // 页码改变\n      const currentChange = (currentPage) => {\n        param.value.current = currentPage;\n        loadMemberList()\n      }\n      // 页面显示数量改变\n      const sizeChange = (size) => {\n        param.value.size = size;\n        loadMemberList()\n      }\n      const search = () => {\n        loadMemberList()\n      }\n      const approved = function (id) {\n        confirm(\"确认解禁该会员【\"+ item.name +\"】\",  \"解禁\", () => {\n          approvedMember({id: id}, res => {\n            console.log(res)\n          })\n        })\n      }\n      const reject = function (id) {\n        confirm(\"确认解禁该会员【\"+ item.name +\"】\",  \"解禁\", () => {\n          rejectMember({id: id}, res => {\n            console.log(res)\n          })\n        })\n      }\n      return {\n        stateMap,\n        param,\n        total,\n        memberList,\n        currentChange,\n        sizeChange,\n        search,\n        dataLoading,\n        approved,\n        reject\n      }\n    }\n  }\n</script>\n\n<style scoped lang=\"scss\">\n  .member-container {\n    margin: 20px;\n    .head {\n      margin-bottom: 10px;\n      .custom-input {\n        width: 50%;\n        min-width: 300px;\n        max-width: 400px;\n      }\n      .custom-btn {\n        &:hover {\n          color: $--color-primary;\n        }\n      }\n    }\n  }\n  .box-card {\n    max-width: 500px;\n  }\n  .fl-table {\n    border-radius: 5px;\n    font-size: 12px;\n    font-weight: normal;\n    border: none;\n    border-collapse: collapse;\n    width: 100%;\n    background-color: white;\n  }\n  .fl-table td {\n    border: 1px solid #f8f8f8;\n    font-size: 12px;\n    padding: 12px;\n  }\n  .fl-table tr td:nth-child(1) {\n    background: #F8F8F8;\n    width: 30%;\n    min-width: 100px;\n  }\n</style>\n"], "mappings": "AAiEE,SAAQA,GAAG,QAAO,KAAI;AACtB,OAAOC,IAAG,MAAO,0BAAyB;AAC1C,SAAQC,sBAAsB,EAAEC,cAAc,EAAEC,YAAY,QAAO,cAAc;AACjF,SAAQC,OAAO,QAAO,kBAAkB;AACxC,eAAe;EACbC,IAAI,EAAE,sBAAsB;EAC5BC,UAAU,EAAE;IACVN;EACF,CAAC;EACDO,KAAKA,CAAA,EAAG;IACN,MAAMC,QAAO,GAAI;MAAC,QAAQ,EAAE,IAAI;MAAE,OAAO,EAAE,KAAK;MAAE,MAAM,EAAE,IAAI;MAAE,SAAS,EAAE;IAAI;IAC/E,MAAMC,KAAI,GAAIV,GAAG,CAAC,CAAC;IACnB,MAAMW,UAAS,GAAIX,GAAG,CAAC,EAAE;IACzB,MAAMY,WAAU,GAAIZ,GAAG,CAAC,IAAI;IAC5B,MAAMa,KAAI,GAAIb,GAAG,CAAC;MAChBc,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC;IACD,MAAMC,cAAa,GAAIA,CAAA,KAAM;MAC3BL,WAAW,CAACM,KAAI,GAAI,IAAG;MACvBhB,sBAAsB,CAACW,KAAK,CAACK,KAAK,EAAEC,GAAE,IAAK;QACzCP,WAAW,CAACM,KAAI,GAAI,KAAI;QACxBP,UAAU,CAACO,KAAI,GAAIC,GAAG,CAACC,IAAG;QAC1BV,KAAK,CAACQ,KAAI,GAAIC,GAAG,CAACT,KAAI;MACxB,CAAC;IACH;IACAO,cAAc,EAAE;IAChB;IACA,MAAMI,aAAY,GAAKC,WAAW,IAAK;MACrCT,KAAK,CAACK,KAAK,CAACJ,OAAM,GAAIQ,WAAW;MACjCL,cAAc,EAAC;IACjB;IACA;IACA,MAAMM,UAAS,GAAKR,IAAI,IAAK;MAC3BF,KAAK,CAACK,KAAK,CAACH,IAAG,GAAIA,IAAI;MACvBE,cAAc,EAAC;IACjB;IACA,MAAMO,MAAK,GAAIA,CAAA,KAAM;MACnBP,cAAc,EAAC;IACjB;IACA,MAAMQ,QAAO,GAAI,SAAAA,CAAUC,EAAE,EAAE;MAC7BrB,OAAO,CAAC,UAAU,GAAEsB,IAAI,CAACrB,IAAG,GAAG,GAAG,EAAG,IAAI,EAAE,MAAM;QAC/CH,cAAc,CAAC;UAACuB,EAAE,EAAEA;QAAE,CAAC,EAAEP,GAAE,IAAK;UAC9BS,OAAO,CAACC,GAAG,CAACV,GAAG;QACjB,CAAC;MACH,CAAC;IACH;IACA,MAAMW,MAAK,GAAI,SAAAA,CAAUJ,EAAE,EAAE;MAC3BrB,OAAO,CAAC,UAAU,GAAEsB,IAAI,CAACrB,IAAG,GAAG,GAAG,EAAG,IAAI,EAAE,MAAM;QAC/CF,YAAY,CAAC;UAACsB,EAAE,EAAEA;QAAE,CAAC,EAAEP,GAAE,IAAK;UAC5BS,OAAO,CAACC,GAAG,CAACV,GAAG;QACjB,CAAC;MACH,CAAC;IACH;IACA,OAAO;MACLV,QAAQ;MACRI,KAAK;MACLH,KAAK;MACLC,UAAU;MACVU,aAAa;MACbE,UAAU;MACVC,MAAM;MACNZ,WAAW;MACXa,QAAQ;MACRK;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}