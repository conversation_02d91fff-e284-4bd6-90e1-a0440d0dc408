{"ast": null, "code": "// const vueConfig = require(\"../../vue.config.js\")\n\nexport default function getPageTitle(pageTitle) {\n  const title = \"知否知否管理后台\";\n  if (pageTitle) {\n    return `${pageTitle} | ${title}`;\n  }\n  return `${title}`;\n}", "map": {"version": 3, "names": ["getPageTitle", "pageTitle", "title"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/util/getPageTitle.js"], "sourcesContent": ["// const vueConfig = require(\"../../vue.config.js\")\n\nexport default function getPageTitle(pageTitle) {\n  const title = \"知否知否管理后台\"\n  if (pageTitle) {\n    return `${pageTitle} | ${title}`\n  }\n  return `${title}`\n}\n"], "mappings": "AAAA;;AAEA,eAAe,SAASA,YAAYA,CAACC,SAAS,EAAE;EAC9C,MAAMC,KAAK,GAAG,UAAU;EACxB,IAAID,SAAS,EAAE;IACb,OAAQ,GAAEA,SAAU,MAAKC,KAAM,EAAC;EAClC;EACA,OAAQ,GAAEA,KAAM,EAAC;AACnB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}