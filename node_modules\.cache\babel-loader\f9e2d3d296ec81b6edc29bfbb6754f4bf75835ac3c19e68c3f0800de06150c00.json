{"ast": null, "code": "import { ref } from \"vue\";\nimport { getUser } from \"@/util/userUtils\";\nimport { updateUserPwd } from \"@/api/organizational/user\";\nimport { confirm, error, success } from \"@/util/tipsUtils\";\nexport default {\n  name: \"AccountSecurityIndex\",\n  components: {},\n  setup() {\n    const curUser = getUser();\n    console.log(\"curUser:{}\", curUser);\n    const user = ref({\n      id: curUser.id,\n      oldPassword: '',\n      newPassword: '',\n      confirmPassword: ''\n    });\n    const userRules = {\n      oldPassword: [{\n        required: true,\n        message: \"请输入旧密码\",\n        trigger: \"blur\"\n      }],\n      newPassword: [{\n        required: true,\n        message: \"请输入新密码\",\n        trigger: \"blur\"\n      }],\n      confirmPassword: [{\n        required: true,\n        message: \"请输入确认密码\",\n        trigger: \"blur\"\n      }]\n    };\n    const userRef = ref(null);\n    const submit = () => {\n      userRef.value.validate(valid => {\n        if (!valid) {\n          return false;\n        }\n        if (!user.value.oldPassword) {\n          error(\"旧密码必填\");\n          return;\n        }\n        if (!user.value.newPassword) {\n          error(\"新密码必填\");\n          return;\n        }\n        if (!user.value.confirmPassword) {\n          error(\"确认密码必填\");\n          return;\n        }\n        if (user.value.newPassword !== user.value.confirmPassword) {\n          error(\"新密码与确认密码不一致\");\n          return;\n        }\n        confirm(\"确认修改密码？\", \"提示\", () => {\n          updateUserPwd(user, () => {\n            success(\"更新成功\");\n          });\n        });\n      });\n    };\n    return {\n      userRules,\n      userRef,\n      submit,\n      user\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "getUser", "updateUserPwd", "confirm", "error", "success", "name", "components", "setup", "curUser", "console", "log", "user", "id", "oldPassword", "newPassword", "confirmPassword", "userRules", "required", "message", "trigger", "userRef", "submit", "value", "validate", "valid"], "sources": ["/Users/<USER>/rongge/code/已售项目/********/front/admin/src/views/account/security/index.vue"], "sourcesContent": ["<template>\n  <div style=\"margin: 20px;\">\n    <el-form :model=\"user\" :rules=\"userRules\" ref=\"userRef\" label-width=\"120px\">\n      <el-form-item label=\"旧密码：\" prop=\"name\">\n        <el-input v-model=\"user.name\" placeholder=\"请输入旧密码\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"新密码：\" prop=\"username\">\n          <el-input v-model=\"user.username\" placeholder=\"请输入新密码\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"确认密码：\" prop=\"mobile\">\n        <el-input v-model=\"user.mobile\" placeholder=\"确认新密码\"></el-input>\n      </el-form-item>\n    </el-form>\n    <div style=\"margin:50px auto;text-align: center;\">\n      <el-button size=\"small\" @click=\"submit\">提交</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {ref} from \"vue\";\nimport {getUser} from \"@/util/userUtils\";\nimport {updateUserPwd} from \"@/api/organizational/user\";\nimport {confirm, error, success} from \"@/util/tipsUtils\";\n\nexport default {\n  name: \"AccountSecurityIndex\",\n  components: {},\n  setup() {\n\n    const curUser = getUser()\n    console.log(\"curUser:{}\", curUser)\n    const user = ref({\n      id: curUser.id,\n      oldPassword: '',\n      newPassword: '',\n      confirmPassword: ''\n    })\n\n    const userRules = {\n      oldPassword: [{ required: true, message: \"请输入旧密码\", trigger: \"blur\" }],\n      newPassword: [{ required: true, message: \"请输入新密码\", trigger: \"blur\" }],\n      confirmPassword: [{ required: true, message: \"请输入确认密码\", trigger: \"blur\" }]\n    }\n    const userRef = ref(null)\n\n    const submit = () => {\n      userRef.value.validate((valid) => {\n        if (!valid) {\n          return false\n        }\n\n        if (!user.value.oldPassword) {\n          error(\"旧密码必填\")\n          return\n        }\n        if (!user.value.newPassword) {\n          error(\"新密码必填\")\n          return\n        }\n        if (!user.value.confirmPassword) {\n          error(\"确认密码必填\")\n          return\n        }\n\n        if (user.value.newPassword !== user.value.confirmPassword) {\n          error(\"新密码与确认密码不一致\")\n          return\n        }\n\n        confirm(\"确认修改密码？\",  \"提示\",() => {\n          updateUserPwd(user, () => {\n            success(\"更新成功\")\n          })\n        })\n      })\n    }\n\n    return {\n      userRules,\n      userRef,\n      submit,\n      user\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n\n</style>\n"], "mappings": "AAoBA,SAAQA,GAAG,QAAO,KAAK;AACvB,SAAQC,OAAO,QAAO,kBAAkB;AACxC,SAAQC,aAAa,QAAO,2BAA2B;AACvD,SAAQC,OAAO,EAAEC,KAAK,EAAEC,OAAO,QAAO,kBAAkB;AAExD,eAAe;EACbC,IAAI,EAAE,sBAAsB;EAC5BC,UAAU,EAAE,CAAC,CAAC;EACdC,KAAKA,CAAA,EAAG;IAEN,MAAMC,OAAM,GAAIR,OAAO,EAAC;IACxBS,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEF,OAAO;IACjC,MAAMG,IAAG,GAAIZ,GAAG,CAAC;MACfa,EAAE,EAAEJ,OAAO,CAACI,EAAE;MACdC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE;IACnB,CAAC;IAED,MAAMC,SAAQ,GAAI;MAChBH,WAAW,EAAE,CAAC;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MACrEL,WAAW,EAAE,CAAC;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MACrEJ,eAAe,EAAE,CAAC;QAAEE,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC;IAC3E;IACA,MAAMC,OAAM,GAAIrB,GAAG,CAAC,IAAI;IAExB,MAAMsB,MAAK,GAAIA,CAAA,KAAM;MACnBD,OAAO,CAACE,KAAK,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAChC,IAAI,CAACA,KAAK,EAAE;UACV,OAAO,KAAI;QACb;QAEA,IAAI,CAACb,IAAI,CAACW,KAAK,CAACT,WAAW,EAAE;UAC3BV,KAAK,CAAC,OAAO;UACb;QACF;QACA,IAAI,CAACQ,IAAI,CAACW,KAAK,CAACR,WAAW,EAAE;UAC3BX,KAAK,CAAC,OAAO;UACb;QACF;QACA,IAAI,CAACQ,IAAI,CAACW,KAAK,CAACP,eAAe,EAAE;UAC/BZ,KAAK,CAAC,QAAQ;UACd;QACF;QAEA,IAAIQ,IAAI,CAACW,KAAK,CAACR,WAAU,KAAMH,IAAI,CAACW,KAAK,CAACP,eAAe,EAAE;UACzDZ,KAAK,CAAC,aAAa;UACnB;QACF;QAEAD,OAAO,CAAC,SAAS,EAAG,IAAI,EAAC,MAAM;UAC7BD,aAAa,CAACU,IAAI,EAAE,MAAM;YACxBP,OAAO,CAAC,MAAM;UAChB,CAAC;QACH,CAAC;MACH,CAAC;IACH;IAEA,OAAO;MACLY,SAAS;MACTI,OAAO;MACPC,MAAM;MACNV;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}