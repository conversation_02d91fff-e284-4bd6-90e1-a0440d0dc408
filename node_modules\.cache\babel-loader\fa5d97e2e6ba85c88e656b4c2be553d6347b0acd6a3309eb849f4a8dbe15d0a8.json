{"ast": null, "code": "import { createRouter, createWebHistory } from \"vue-router\";\nimport Layout from \"../components/Layout\";\nimport LayoutEmpty from \"../components/LayoutEmpty\";\nimport LayoutNotAside from \"../components/LayoutNotAside\";\nimport Login from \"@/views/login/Index\";\nimport workWeChatLogin from \"@/views/login/workWeChat\";\nimport dingTalkLogin from \"@/views/login/dingTalk\";\nimport Index from \"@/views/home/<USER>\";\nexport const routes = [{\n  path: \"/login\",\n  name: \"login\",\n  component: Login,\n  hidden: true\n}, {\n  path: \"/work-we-chat\",\n  name: \"workWeChatLogin\",\n  component: workWeChatLogin,\n  hidden: true\n}, {\n  path: \"/ding-talk\",\n  name: \"dingTalk\",\n  component: dingTalkLogin,\n  hidden: true\n}, {\n  path: \"\",\n  name: \"Index\",\n  component: LayoutNotAside,\n  meta: {\n    title: \"仪表盘\",\n    icon: \"el-icon-odometer\"\n  },\n  hidden: true,\n  children: [{\n    path: \"index\",\n    meta: {\n      title: \"总览\",\n      icon: \"el-icon-odometer\"\n    },\n    component: Index\n  }]\n}];\nexport const asyncRoutes = [{\n  path: \"/learn\",\n  name: \"learn\",\n  component: Layout,\n  meta: {\n    title: \"课程\",\n    icon: \"Notebook\",\n    authorities: [\"learning\"],\n    breadcrumb: false\n  },\n  children: [{\n    path: \"\",\n    name: \"learnIndex\",\n    component: () => import(\"@/views/learn/index\"),\n    meta: {\n      title: \"概览\",\n      name: \"learning\",\n      icon: \"\",\n      authorities: [\"learning\"]\n    },\n    children: []\n  }, {\n    path: \"lesson\",\n    name: \"lesson\",\n    component: LayoutEmpty,\n    meta: {\n      title: \"课程\",\n      icon: \"Document\",\n      authorities: [\"map\"]\n    },\n    children: [{\n      path: \"\",\n      name: \"learnLesson\",\n      component: () => import(\"@/views/learn/lesson/index\"),\n      meta: {\n        title: \"在线课程\",\n        icon: \"Tickets\",\n        authorities: [\"learning_list\"]\n      },\n      children: []\n    }, {\n      path: \"category\",\n      name: \"learnCategory\",\n      component: () => import(\"@/views/learn/category/index\"),\n      meta: {\n        title: \"课程分类\",\n        icon: \"Collection\",\n        authorities: [\"learning_category\"]\n      }\n    }, {\n      path: \"edit\",\n      name: \"learnLessonEdit\",\n      component: () => import(\"@/views/learn/lesson/edit/index\"),\n      meta: {\n        title: \"课程编辑\",\n        icon: \"Edit\",\n        activeMenu: \"/learn/lesson\"\n      },\n      hidden: true\n    }]\n  }, {\n    path: \"topic\",\n    name: \"topic\",\n    component: LayoutEmpty,\n    meta: {\n      title: \"专题\",\n      icon: \"Operation\",\n      authorities: [\"map\"]\n    },\n    children: [{\n      path: \"\",\n      name: \"learnTopic\",\n      component: () => import(\"@/views/learn/topic/index\"),\n      meta: {\n        title: \"专题管理\",\n        icon: \"DocumentCopy\",\n        authorities: [\"learn_map_topic\"]\n      }\n    }, {\n      path: \"edit\",\n      name: \"learnTopicEdit\",\n      component: () => import(\"@/views/learn/topic/edit/index\"),\n      meta: {\n        title: \"专题编辑\",\n        icon: \"Edit\",\n        activeMenu: \"/learn/topic\"\n      },\n      hidden: true\n    }, {\n      path: \"category\",\n      name: \"learnTopicCategory\",\n      component: () => import(\"@/views/learn/topic/category/index\"),\n      meta: {\n        title: \"专题分类\",\n        icon: \"Collection\",\n        authorities: [\"learn_map_topic_category\"]\n      }\n    }]\n  }, {\n    path: \"map\",\n    name: \"map\",\n    component: LayoutEmpty,\n    meta: {\n      title: \"学习地图\",\n      icon: \"MapLocation\",\n      authorities: [\"map\"]\n    },\n    children: [{\n      path: \"\",\n      name: \"learnMap\",\n      component: () => import(\"@/views/learn/map/index\"),\n      meta: {\n        title: \"地图管理\",\n        icon: \"Guide\",\n        authorities: [\"learn_map\"]\n      }\n    }, {\n      path: \"edit\",\n      name: \"learnMapEdit\",\n      component: () => import(\"@/views/learn/map/edit/index\"),\n      meta: {\n        title: \"地图编辑\",\n        icon: \"Edit\",\n        activeMenu: \"/learn/map\"\n      },\n      hidden: true\n    }]\n  }, {\n    path: \"order\",\n    name: \"order\",\n    component: LayoutEmpty,\n    meta: {\n      title: \"订单\",\n      icon: \"Notification\",\n      authorities: [\"map\"]\n    },\n    children: [{\n      path: \"\",\n      name: \"orderList\",\n      component: () => import(\"@/views/learn/order/index\"),\n      meta: {\n        title: \"订单列表\",\n        icon: \"CopyDocument\",\n        authorities: [\"learn_map\"]\n      }\n    }]\n  }, {\n    path: \"data\",\n    name: \"data\",\n    component: LayoutEmpty,\n    meta: {\n      title: \"数据中心\",\n      icon: \"ScaleToOriginal\",\n      authorities: [\"map\"]\n    },\n    children: [{\n      path: \"\",\n      name: \"learnData\",\n      component: () => import(\"@/views/learn/report/signup\"),\n      meta: {\n        title: \"课程报名统计\",\n        icon: \"PieChart\",\n        authorities: [\"learn_map\"]\n      }\n    }, {\n      path: \"edit\",\n      name: \"learnDataEdit\",\n      component: () => import(\"@/views/learn/report/lessonlearn\"),\n      meta: {\n        title: \"课程学习统计\",\n        icon: \"Odometer\",\n        authorities: [\"learn_map\"]\n      }\n    }, {\n      path: \"edit3\",\n      name: \"learnDataEdit3\",\n      component: () => import(\"@/views/learn/report/userlearn\"),\n      meta: {\n        title: \"用户学习统计\",\n        icon: \"Finished\",\n        authorities: [\"learn_map\"]\n      }\n    }]\n  }]\n}, {\n  path: \"/exam\",\n  name: \"exam\",\n  component: Layout,\n  meta: {\n    title: \"考试\",\n    icon: \"el-icon-video-camera\",\n    authorities: [\"exam\"],\n    breadcrumb: false\n  },\n  children: [{\n    path: \"\",\n    name: \"examIndex\",\n    component: () => import(\"@/views/exam/index\"),\n    meta: {\n      title: \"概览\",\n      name: \"exam\",\n      icon: \"\",\n      authorities: [\"exam\"]\n    },\n    children: []\n  }, {\n    path: \"question-lib\",\n    component: LayoutEmpty,\n    meta: {\n      title: \"题库\",\n      icon: \"Tickets\",\n      authorities: [\"exam\"]\n    },\n    children: [{\n      path: \"category\",\n      name: \"examQuestionLibCategory\",\n      component: () => import(\"@/views/exam/question-lib/category\"),\n      meta: {\n        title: \"题目分类\",\n        icon: \"Collection\",\n        authorities: [\"exam_question_lib\"]\n      }\n    }, {\n      path: \"\",\n      name: \"examQuestionLibList\",\n      component: () => import(\"@/views/exam/question-lib\"),\n      meta: {\n        title: \"题目管理\",\n        icon: \"DocumentCopy\",\n        authorities: [\"exam_question_lib\"]\n      }\n    }, {\n      path: \"single-choice\",\n      name: \"singleChoiceEdit\",\n      component: () => import(\"@/views/exam/question-lib/single-choice\"),\n      meta: {\n        title: \"单选题编辑\",\n        icon: \"CircleCheck\",\n        authorities: [\"exam_question_lib\"]\n      }\n    }, {\n      path: \"multi-choice\",\n      name: \"multiChoiceEdit\",\n      component: () => import(\"@/views/exam/question-lib/multi-choice\"),\n      meta: {\n        title: \"多选题编辑\",\n        icon: \"DocumentChecked\",\n        authorities: [\"exam_question_lib\"]\n      }\n    }, {\n      path: \"judgment\",\n      name: \"judgmentEdit\",\n      component: () => import(\"@/views/exam/question-lib/judgment\"),\n      meta: {\n        title: \"判断题编辑\",\n        icon: \"CircleClose\",\n        authorities: [\"exam_question_lib\"]\n      }\n    }, {\n      path: \"fill-blank\",\n      name: \"fillBlankEdit\",\n      component: () => import(\"@/views/exam/question-lib/fill-blank\"),\n      meta: {\n        title: \"填空题编辑\",\n        icon: \"MoreFilled\",\n        authorities: [\"exam_question_lib\"]\n      }\n    }, {\n      path: \"subjective\",\n      name: \"subjectiveEdit\",\n      component: () => import(\"@/views/exam/question-lib/subjective\"),\n      meta: {\n        title: \"简答题编辑\",\n        icon: \"Edit\",\n        authorities: [\"exam_question_lib\"]\n      }\n    }]\n  }, {\n    path: \"paper\",\n    component: LayoutEmpty,\n    meta: {\n      title: \"试卷\",\n      icon: \"el-icon-tickets\",\n      authorities: [\"learning\"]\n    },\n    children: [{\n      path: \"category\",\n      name: \"examPaperCategory\",\n      component: () => import(\"@/views/exam/paper/category\"),\n      meta: {\n        title: \"试卷分类\",\n        icon: \"Collection\",\n        authorities: [\"learning_category\"]\n      }\n    }, {\n      path: \"\",\n      name: \"examPaperList\",\n      component: () => import(\"@/views/exam/paper\"),\n      meta: {\n        title: \"试卷管理\",\n        icon: \"Memo\",\n        authorities: [\"learning_list\"]\n      }\n    }, {\n      path: \"normal\",\n      name: \"examPaperNormal\",\n      component: () => import(\"@/views/exam/paper/normal\"),\n      meta: {\n        title: \"静态试卷编辑\",\n        icon: \"Edit\",\n        authorities: [\"learning_list\"]\n      }\n    }, {\n      path: \"random\",\n      name: \"examPaperRandom\",\n      component: () => import(\"@/views/exam/paper/random\"),\n      meta: {\n        title: \"随机试卷编辑\",\n        icon: \"EditPen\",\n        authorities: [\"learning_list\"]\n      }\n    }\n    // {\n    //   path: \"mock\",\n    //   name: \"examPaperMock\",\n    //   component: () => import(\"@/views/exam/paper/mock\"),\n    //   meta: { title: \"模拟试卷编辑\", icon: \"el-icon-bank-card\", authorities: [\"learning_list\"] },\n    // },\n    ]\n  }, {\n    path: \"exam\",\n    component: LayoutEmpty,\n    meta: {\n      title: \"考试\",\n      icon: \"el-icon-c-scale-to-original\",\n      authorities: [\"comment\"]\n    },\n    children: [{\n      path: \"category\",\n      name: \"examCategory\",\n      component: () => import(\"@/views/exam/category\"),\n      meta: {\n        title: \"考试分类\",\n        icon: \"Collection\",\n        authorities: [\"comment_sensitive_setting\"]\n      }\n    }, {\n      path: \"\",\n      name: \"examList\",\n      component: () => import(\"@/views/exam/list\"),\n      meta: {\n        title: \"考试管理\",\n        icon: \"Tickets\",\n        authorities: [\"comment_list\"]\n      }\n    }, {\n      path: \"edit\",\n      name: \"examEdit\",\n      component: () => import(\"@/views/exam/list/edit\"),\n      meta: {\n        title: \"考试编辑\",\n        icon: \"Edit\",\n        authorities: [\"comment_sensitive_setting\"]\n      }\n    }]\n  }, {\n    path: \"answer\",\n    component: LayoutEmpty,\n    meta: {\n      title: \"答卷\",\n      icon: \"el-icon-files\",\n      authorities: [\"comment\"]\n    },\n    children: [{\n      path: \"\",\n      name: \"examAnswerList\",\n      component: () => import(\"@/views/exam/answer/list\"),\n      meta: {\n        title: \"答卷管理\",\n        icon: \"DocumentCopy\",\n        authorities: [\"comment_list\"]\n      }\n    }, {\n      path: \"mark\",\n      name: \"examAnswerMark\",\n      component: () => import(\"@/views/exam/answer/mark\"),\n      meta: {\n        title: \"答卷批改\",\n        icon: \"Check\",\n        authorities: [\"comment_sensitive_setting\"]\n      }\n    }]\n  }]\n}, {\n  path: \"/live\",\n  name: \"live\",\n  component: Layout,\n  meta: {\n    title: \"直播\",\n    icon: \"VideoCamera\",\n    authorities: [\"live\"],\n    breadcrumb: false\n  },\n  children: [{\n    path: \"\",\n    name: \"liveIndex\",\n    component: () => import(\"@/views/live/index\"),\n    meta: {\n      title: \"概览\",\n      name: \"live\",\n      icon: \"\",\n      authorities: [\"live\"]\n    },\n    children: []\n  }, {\n    path: \"channel\",\n    name: \"channelList\",\n    component: LayoutEmpty,\n    meta: {\n      title: \"频道\",\n      icon: \"VideoCamera\",\n      authorities: [\"live_channel\"]\n    },\n    children: [{\n      path: \"\",\n      name: \"channelList\",\n      component: () => import(\"@/views/live/channel/index\"),\n      meta: {\n        title: \"频道管理\",\n        icon: \"Grid\",\n        authorities: [\"live_channel\"]\n      }\n    }, {\n      path: \"edit\",\n      name: \"channelEdit\",\n      component: () => import(\"@/views/live/channel/edit\"),\n      meta: {\n        title: \"频道编辑\",\n        icon: \"Edit\",\n        activeMenu: \"/live/channel\"\n      },\n      hidden: true\n    }, {\n      path: \"category\",\n      name: \"channelCategory\",\n      component: () => import(\"@/views/live/category/index\"),\n      meta: {\n        title: \"频道分类\",\n        icon: \"Collection\",\n        authorities: [\"live_category\"]\n      }\n    }]\n  }, {\n    path: \"lecturer\",\n    name: \"lecturer\",\n    component: LayoutEmpty,\n    meta: {\n      title: \"讲师\",\n      icon: \"el-icon-s-custom\",\n      authorities: [\"lecturer\"]\n    },\n    children: [{\n      path: \"list\",\n      name: \"lecturerList\",\n      component: () => import(\"@/views/live/lecturer/index\"),\n      meta: {\n        title: \"讲师管理\",\n        icon: \"Avatar\",\n        authorities: [\"lecturer_list\"]\n      }\n    }, {\n      path: \"edit\",\n      name: \"lecturerEdit\",\n      component: () => import(\"@/views/live/lecturer/edit\"),\n      meta: {\n        title: \"讲师编辑\",\n        icon: \"Edit\",\n        activeMenu: \"/lecturer/list\"\n      },\n      hidden: true\n    }]\n  }]\n}, {\n  path: \"/news\",\n  component: Layout,\n  meta: {\n    title: \"新闻\",\n    icon: \"el-icon-news\",\n    authorities: [\"news\"],\n    breadcrumb: false\n  },\n  children: [{\n    path: \"\",\n    name: \"news\",\n    component: () => import(\"@/views/news/index\"),\n    meta: {\n      title: \"概览\",\n      icon: \"\",\n      authorities: [\"news\"]\n    },\n    children: []\n  }, {\n    path: \"list\",\n    name: \"newsList\",\n    component: () => import(\"@/views/news/content/index\"),\n    meta: {\n      title: \"新闻管理\",\n      icon: \"Tickets\",\n      authorities: [\"news_list\"]\n    },\n    children: []\n  }, {\n    path: \"edit\",\n    name: \"newsEdit\",\n    component: () => import(\"@/views/news/content/edit\"),\n    meta: {\n      title: \"新闻编辑\",\n      icon: \"Edit\",\n      activeMenu: \"/content/news\"\n    },\n    hidden: true\n  }]\n}, {\n  path: \"/article\",\n  component: Layout,\n  meta: {\n    title: \"文章\",\n    icon: \"el-icon-postcard\",\n    authorities: [\"article\"],\n    breadcrumb: false\n  },\n  children: [{\n    path: \"\",\n    component: () => import(\"@/views/article/index\"),\n    meta: {\n      title: \"概览\",\n      icon: \"\",\n      authorities: [\"article\"]\n    },\n    children: []\n  }, {\n    path: \"list\",\n    name: \"articleList\",\n    component: () => import(\"@/views/article/content/index\"),\n    meta: {\n      title: \"文章管理\",\n      icon: \"Tickets\",\n      authorities: [\"article_list\"]\n    }\n  }, {\n    path: \"category\",\n    name: \"contentCategory\",\n    component: () => import(\"@/views/article/category/index\"),\n    meta: {\n      title: \"文章分类\",\n      icon: \"Collection\",\n      authorities: [\"article_category\"]\n    }\n  }]\n}, {\n  path: \"/ask\",\n  component: Layout,\n  meta: {\n    title: \"问答\",\n    icon: \"el-icon-question\",\n    authorities: [\"ask\"],\n    breadcrumb: false\n  },\n  children: [{\n    path: \"\",\n    component: () => import(\"@/views/ask/index\"),\n    meta: {\n      title: \"概览\",\n      icon: \"\",\n      authorities: [\"ask\"]\n    },\n    children: []\n  }, {\n    path: \"question\",\n    name: \"questionList\",\n    component: () => import(\"@/views/ask/question/index\"),\n    meta: {\n      title: \"问题管理\",\n      icon: \"Tickets\",\n      authorities: [\"ask_question\"]\n    }\n  }, {\n    path: \"category\",\n    name: \"questionCategory\",\n    component: () => import(\"@/views/ask/category/index\"),\n    meta: {\n      title: \"问题分类\",\n      icon: \"Collection\",\n      authorities: [\"ask_question_category\"]\n    }\n  }]\n}, {\n  path: \"/circle\",\n  component: Layout,\n  meta: {\n    title: \"社区\",\n    icon: \"el-icon-discover\",\n    authorities: [\"circle\"],\n    breadcrumb: false\n  },\n  children: [{\n    path: \"\",\n    component: () => import(\"@/views/circle/index\"),\n    meta: {\n      title: \"概览\",\n      icon: \"\",\n      authorities: [\"circle\"]\n    },\n    children: []\n  }, {\n    path: \"dynamics\",\n    name: \"dynamicsList\",\n    component: () => import(\"@/views/circle/dynamic/index\"),\n    meta: {\n      title: \"动态列表\",\n      icon: \"ChatLineSquare\",\n      authorities: [\"circle_dynamic\"]\n    }\n  }, {\n    path: \"list\",\n    name: \"circleList\",\n    component: () => import(\"@/views/circle/list/index\"),\n    meta: {\n      title: \"社区管理\",\n      icon: \"ScaleToOriginal\",\n      authorities: [\"circle_list\"]\n    }\n  }, {\n    path: \"category\",\n    name: \"circleCategory\",\n    component: () => import(\"@/views/circle/category/index\"),\n    meta: {\n      title: \"社区分类\",\n      icon: \"Collection\",\n      authorities: [\"circle_category\"]\n    }\n  }]\n}, {\n  path: \"/resource\",\n  component: Layout,\n  meta: {\n    title: \"知识库\",\n    icon: \"el-icon-collection\",\n    authorities: [\"resource\"],\n    breadcrumb: false\n  },\n  children: [{\n    path: \"\",\n    component: () => import(\"@/views/resource/index\"),\n    meta: {\n      title: \"概览\",\n      icon: \"\",\n      authorities: [\"resource\"]\n    },\n    children: []\n  }, {\n    path: \"list\",\n    name: \"resourceList\",\n    component: () => import(\"@/views/resource/list/index\"),\n    meta: {\n      title: \"知识管理\",\n      icon: \"Tickets\",\n      authorities: [\"resource_list\"]\n    }\n  }, {\n    path: \"category\",\n    name: \"resourceCategory\",\n    component: () => import(\"@/views/resource/category/index\"),\n    meta: {\n      title: \"知识分类\",\n      icon: \"Collection\",\n      authorities: [\"resource_category\"]\n    }\n  }]\n}, {\n  path: \"/point\",\n  component: Layout,\n  meta: {\n    title: \"积分\",\n    icon: \"el-icon-goods\",\n    authorities: [\"point\"],\n    breadcrumb: false\n  },\n  children: [{\n    path: \"\",\n    component: () => import(\"@/views/point/index\"),\n    meta: {\n      title: \"概览\",\n      icon: \"\",\n      authorities: [\"point\"]\n    },\n    children: []\n  }, {\n    path: \"list\",\n    name: \"pointList\",\n    component: () => import(\"@/views/point/list/index\"),\n    meta: {\n      title: \"积分管理\",\n      icon: \"Coin\",\n      authorities: [\"point_list\"]\n    }\n  }, {\n    path: \"channel\",\n    name: \"pointChannel\",\n    component: () => import(\"@/views/point/channel/index\"),\n    meta: {\n      title: \"积分渠道\",\n      icon: \"Guide\",\n      authorities: [\"point_channel\"]\n    }\n  }, {\n    path: \"record\",\n    name: \"pointRecord\",\n    component: () => import(\"@/views/point/record/index\"),\n    meta: {\n      title: \"积分记录\",\n      icon: \"Tickets\",\n      authorities: [\"point_record\"]\n    }\n  }]\n}, {\n  path: \"/comment\",\n  component: Layout,\n  meta: {\n    title: \"评论\",\n    icon: \"el-icon-chat-dot-square\",\n    authorities: [\"comment\"],\n    breadcrumb: false\n  },\n  children: [{\n    path: \"\",\n    component: () => import(\"@/views/comment/index\"),\n    meta: {\n      title: \"概览\",\n      icon: \"\",\n      authorities: [\"comment\"]\n    },\n    children: []\n  }, {\n    path: \"list\",\n    name: \"commentList\",\n    component: () => import(\"@/views/comment/list/index\"),\n    meta: {\n      title: \"评论管理\",\n      icon: \"Tickets\",\n      authorities: [\"comment_list\"]\n    }\n  }, {\n    path: \"sensitive-word\",\n    name: \"commentSensitiveSetting\",\n    component: () => import(\"@/views/comment/sensitive-word/index\"),\n    meta: {\n      title: \"敏感词\",\n      icon: \"Warning\",\n      authorities: [\"comment_sensitive_setting\"]\n    }\n  }]\n}, {\n  path: \"/search\",\n  component: Layout,\n  meta: {\n    title: \"搜索\",\n    icon: \"el-icon-search\",\n    authorities: [\"search\"],\n    breadcrumb: false\n  },\n  children: [{\n    path: \"\",\n    component: () => import(\"@/views/search/index\"),\n    meta: {\n      title: \"概览\",\n      icon: \"\",\n      authorities: [\"search\"]\n    },\n    children: []\n  }, {\n    path: \"hot-word\",\n    name: \"searchHotWord\",\n    component: () => import(\"@/views/search/hot-word/index\"),\n    meta: {\n      title: \"热词管理\",\n      icon: \"HotWater\",\n      authorities: [\"search_hot_word\"]\n    }\n  }]\n}, {\n  path: \"/message\",\n  component: Layout,\n  meta: {\n    title: \"消息\",\n    icon: \"el-icon-chat-dot-round\",\n    authorities: [\"message\"],\n    breadcrumb: false\n  },\n  children: [{\n    path: \"\",\n    component: () => import(\"@/views/message/index\"),\n    meta: {\n      title: \"概览\",\n      icon: \"\",\n      authorities: [\"message\"]\n    },\n    children: []\n  }, {\n    path: \"announcement\",\n    name: \"announcement\",\n    component: () => import(\"@/views/message/announcement/index\"),\n    meta: {\n      title: \"公告管理\",\n      icon: \"Tickets\",\n      authorities: [\"message_announcement\"]\n    }\n  }]\n}, {\n  path: \"/member\",\n  component: Layout,\n  meta: {\n    title: \"会员\",\n    icon: \"el-icon-user\",\n    authorities: [\"member\"],\n    breadcrumb: false\n  },\n  children: [{\n    path: \"\",\n    component: () => import(\"@/views/member/index\"),\n    meta: {\n      title: \"概览\",\n      icon: \"\",\n      authorities: [\"member\"]\n    },\n    children: []\n  }, {\n    path: \"list\",\n    name: \"memberList\",\n    component: () => import(\"@/views/member/list/index\"),\n    meta: {\n      title: \"会员管理\",\n      icon: \"Tickets\",\n      authorities: [\"member_list\"]\n    },\n    children: []\n  }, {\n    path: \"level\",\n    name: \"memberLevel\",\n    component: () => import(\"@/views/member/level/index\"),\n    meta: {\n      title: \"会员等级\",\n      icon: \"DataLine\",\n      authorities: [\"member_level\"]\n    },\n    children: []\n  }, {\n    path: \"edit\",\n    name: \"memberEdit\",\n    component: () => import(\"@/views/member/list/index\"),\n    meta: {\n      title: \"会员编辑\",\n      icon: \"Edit\",\n      activeMenu: \"/member/list\"\n    },\n    hidden: true,\n    children: []\n  }]\n}, {\n  path: \"/auth\",\n  component: Layout,\n  meta: {\n    title: \"权限\",\n    icon: \"el-icon-unlock\",\n    authorities: [\"authority\"],\n    breadcrumb: false\n  },\n  children: [{\n    path: \"\",\n    component: () => import(\"@/views/auth/index\"),\n    meta: {\n      title: \"概览\",\n      icon: \"\",\n      authorities: [\"authority\"]\n    },\n    children: []\n  }, {\n    path: \"organizational\",\n    component: LayoutEmpty,\n    meta: {\n      title: \"组织架构\",\n      icon: \"el-icon-office-building\",\n      authorities: [\"organizational\"]\n    },\n    redirect: \"organizational/user\",\n    children: [{\n      path: \"user\",\n      name: \"organizationalUser\",\n      component: () => import(\"@/views/organizational/user/index\"),\n      meta: {\n        title: \"用户管理\",\n        icon: \"User\",\n        authorities: [\"organizational_user\"]\n      }\n    }, {\n      path: \"department\",\n      name: \"organizationalDepartment\",\n      component: () => import(\"@/views/organizational/department/index\"),\n      meta: {\n        title: \"组织管理\",\n        icon: \"School\",\n        authorities: [\"organizational_department\"]\n      }\n    }]\n  }, {\n    path: \"role\",\n    name: \"authorityRole\",\n    component: () => import(\"@/views/auth/role/index\"),\n    meta: {\n      title: \"角色管理\",\n      icon: \"LocationInformation\",\n      authorities: [\"authority_role\"]\n    }\n  }, {\n    path: \"authority\",\n    name: \"authorityAuthority\",\n    component: () => import(\"@/views/auth/authority/index\"),\n    meta: {\n      title: \"权限列表\",\n      icon: \"Lock\",\n      authorities: [\"authority_authority\"]\n    }\n  }]\n}, {\n  path: \"/setting\",\n  component: Layout,\n  meta: {\n    title: \"系统\",\n    icon: \"Setting\",\n    authorities: [\"setting\"],\n    breadcrumb: false\n  },\n  children: [{\n    path: \"\",\n    component: () => import(\"@/views/setting/index\"),\n    meta: {\n      title: \"概览\",\n      icon: \"\",\n      authorities: [\"setting\"]\n    },\n    children: []\n  }, {\n    path: \"carousel\",\n    name: \"settingCarousel\",\n    component: () => import(\"@/views/setting/carousel/index\"),\n    meta: {\n      title: \"轮播图管理\",\n      icon: \"Picture\",\n      authorities: [\"setting_carousel\"]\n    }\n  }, {\n    path: \"agreement\",\n    name: \"settingAgreement\",\n    component: () => import(\"@/views/setting/agreement/index\"),\n    meta: {\n      title: \"协议管理\",\n      icon: \"Postcard\",\n      authorities: [\"setting_agreement\"]\n    }\n  }]\n}, {\n  path: \"/account\",\n  component: Layout,\n  meta: {\n    title: \"账号中心\",\n    icon: \"el-icon-setting\",\n    authorities: [\"setting\"],\n    breadcrumb: false\n  },\n  hidden: true,\n  children: [{\n    path: \"\",\n    component: () => import(\"@/views/account/index\"),\n    meta: {\n      title: \"基本信息\",\n      icon: \"User\",\n      authorities: [\"setting\"]\n    },\n    children: []\n  }, {\n    path: \"carousel\",\n    name: \"sss\",\n    component: () => import(\"@/views/account/security/index\"),\n    meta: {\n      title: \"安全设置\",\n      icon: \"Lock\",\n      authorities: [\"setting_carousel\"]\n    }\n  }]\n}];\nlet routerOptions = {\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n};\nlet router = createRouter(routerOptions);\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "Layout", "LayoutEmpty", "LayoutNotAside", "<PERSON><PERSON>", "workWeChatLogin", "dingTalkLogin", "Index", "routes", "path", "name", "component", "hidden", "meta", "title", "icon", "children", "asyncRoutes", "authorities", "breadcrumb", "activeMenu", "redirect", "routerOptions", "history", "process", "env", "BASE_URL", "router"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/router/index.js"], "sourcesContent": ["import {createRouter, createWebHistory} from \"vue-router\";\nimport Layout from \"../components/Layout\";\nimport LayoutEmpty from \"../components/LayoutEmpty\";\nimport LayoutNotAside from \"../components/LayoutNotAside\";\nimport Login from \"@/views/login/Index\"\nimport workWeChatLogin from \"@/views/login/workWeChat\"\nimport dingTalkLogin from \"@/views/login/dingTalk\"\nimport Index from \"@/views/home/<USER>\"\n\nexport const routes = [\n  {\n    path: \"/login\",\n    name: \"login\",\n    component: Login,\n    hidden: true\n  },\n  {\n    path: \"/work-we-chat\",\n    name: \"workWeChatLogin\",\n    component: workWeChatLogin,\n    hidden: true\n  },\n  {\n    path: \"/ding-talk\",\n    name: \"dingTalk\",\n    component: dingTalkLogin,\n    hidden: true\n  },\n  {\n    path: \"\",\n    name: \"Index\",\n    component: LayoutNotAside,\n    meta: { title: \"仪表盘\", icon: \"el-icon-odometer\" },\n    hidden: true,\n    children: [\n      {\n        path: \"index\",\n        meta: { title: \"总览\", icon: \"el-icon-odometer\" },\n        component: Index,\n      }\n    ]\n  }\n];\n\nexport const asyncRoutes = [\n  {\n    path: \"/learn\",\n    name: \"learn\",\n    component: Layout,\n    meta: { title: \"课程\", icon: \"Notebook\", authorities: [\"learning\"], breadcrumb: false },\n    children: [\n      {\n        path: \"\",\n        name: \"learnIndex\",\n        component: () => import(\"@/views/learn/index\"),\n        meta: { title: \"概览\", name: \"learning\", icon: \"\", authorities: [\"learning\"] },\n        children: []\n      },\n      {\n        path: \"lesson\",\n        name: \"lesson\",\n        component: LayoutEmpty,\n        meta: { title: \"课程\", icon: \"Document\", authorities: [\"map\"] },\n        children: [\n          {\n            path: \"\",\n            name: \"learnLesson\",\n            component: () => import(\"@/views/learn/lesson/index\"),\n            meta: { title: \"在线课程\", icon: \"Tickets\", authorities: [\"learning_list\"] },\n            children: []\n          },\n          {\n            path: \"category\",\n            name: \"learnCategory\",\n            component: () => import(\"@/views/learn/category/index\"),\n            meta: { title: \"课程分类\", icon: \"Collection\", authorities: [\"learning_category\"] },\n          },\n          {\n            path: \"edit\",\n            name: \"learnLessonEdit\",\n            component: () => import(\"@/views/learn/lesson/edit/index\"),\n            meta: { title: \"课程编辑\", icon: \"Edit\", activeMenu: \"/learn/lesson\" },\n            hidden: true\n          },\n        ]\n      },\n      {\n        path: \"topic\",\n        name: \"topic\",\n        component: LayoutEmpty,\n        meta: { title: \"专题\", icon: \"Operation\", authorities: [\"map\"] },\n        children: [\n          {\n            path: \"\",\n            name: \"learnTopic\",\n            component: () => import(\"@/views/learn/topic/index\"),\n            meta: { title: \"专题管理\", icon: \"DocumentCopy\", authorities: [\"learn_map_topic\"] },\n          },\n          {\n            path: \"edit\",\n            name: \"learnTopicEdit\",\n            component: () => import(\"@/views/learn/topic/edit/index\"),\n            meta: { title: \"专题编辑\", icon: \"Edit\", activeMenu: \"/learn/topic\" },\n            hidden: true\n          },\n          {\n            path: \"category\",\n            name: \"learnTopicCategory\",\n            component: () => import(\"@/views/learn/topic/category/index\"),\n            meta: { title: \"专题分类\", icon: \"Collection\", authorities: [\"learn_map_topic_category\"] },\n          },\n        ]\n      },\n      {\n        path: \"map\",\n        name: \"map\",\n        component: LayoutEmpty,\n        meta: { title: \"学习地图\", icon: \"MapLocation\", authorities: [\"map\"] },\n        children: [\n          {\n            path: \"\",\n            name: \"learnMap\",\n            component: () => import(\"@/views/learn/map/index\"),\n            meta: { title: \"地图管理\", icon: \"Guide\", authorities: [\"learn_map\"] },\n          },\n          {\n            path: \"edit\",\n            name: \"learnMapEdit\",\n            component: () => import(\"@/views/learn/map/edit/index\"),\n            meta: { title: \"地图编辑\", icon: \"Edit\", activeMenu: \"/learn/map\" },\n            hidden: true\n          },\n        ]\n      },\n      {\n        path: \"order\",\n        name: \"order\",\n        component: LayoutEmpty,\n        meta: { title: \"订单\", icon: \"Notification\", authorities: [\"map\"] },\n        children: [\n          {\n            path: \"\",\n            name: \"orderList\",\n            component: () => import(\"@/views/learn/order/index\"),\n            meta: { title: \"订单列表\", icon: \"CopyDocument\", authorities: [\"learn_map\"] },\n          }\n        ]\n      },\n      {\n        path: \"data\",\n        name: \"data\",\n        component: LayoutEmpty,\n        meta: { title: \"数据中心\", icon: \"ScaleToOriginal\", authorities: [\"map\"] },\n        children: [\n          {\n            path: \"\",\n            name: \"learnData\",\n            component: () => import(\"@/views/learn/report/signup\"),\n            meta: { title: \"课程报名统计\", icon: \"PieChart\", authorities: [\"learn_map\"] },\n          },\n          {\n            path: \"edit\",\n            name: \"learnDataEdit\",\n            component: () => import(\"@/views/learn/report/lessonlearn\"),\n            meta: { title: \"课程学习统计\", icon: \"Odometer\", authorities: [\"learn_map\"] }\n          },\n          {\n            path: \"edit3\",\n            name: \"learnDataEdit3\",\n            component: () => import(\"@/views/learn/report/userlearn\"),\n            meta: { title: \"用户学习统计\", icon: \"Finished\", authorities: [\"learn_map\"] }\n          },\n        ]\n      },\n    ]\n  },\n  {\n    path: \"/exam\",\n    name: \"exam\",\n    component: Layout,\n    meta: { title: \"考试\", icon: \"el-icon-video-camera\", authorities: [\"exam\"], breadcrumb: false },\n    children: [\n      {\n        path: \"\",\n        name: \"examIndex\",\n        component: () => import(\"@/views/exam/index\"),\n        meta: { title: \"概览\", name: \"exam\", icon: \"\", authorities: [\"exam\"] },\n        children: []\n      },\n      {\n        path: \"question-lib\",\n        component: LayoutEmpty,\n        meta: { title: \"题库\", icon: \"Tickets\", authorities: [\"exam\"] },\n        children: [\n          {\n            path: \"category\",\n            name: \"examQuestionLibCategory\",\n            component: () => import(\"@/views/exam/question-lib/category\"),\n            meta: { title: \"题目分类\", icon: \"Collection\", authorities: [\"exam_question_lib\"] },\n          },\n          {\n            path: \"\",\n            name: \"examQuestionLibList\",\n            component: () => import(\"@/views/exam/question-lib\"),\n            meta: { title: \"题目管理\", icon: \"DocumentCopy\", authorities: [\"exam_question_lib\"] },\n          },\n          {\n            path: \"single-choice\",\n            name: \"singleChoiceEdit\",\n            component: () => import(\"@/views/exam/question-lib/single-choice\"),\n            meta: { title: \"单选题编辑\", icon: \"CircleCheck\", authorities: [\"exam_question_lib\"] },\n          },\n          {\n            path: \"multi-choice\",\n            name: \"multiChoiceEdit\",\n            component: () => import(\"@/views/exam/question-lib/multi-choice\"),\n            meta: { title: \"多选题编辑\", icon: \"DocumentChecked\", authorities: [\"exam_question_lib\"] },\n          },\n          {\n            path: \"judgment\",\n            name: \"judgmentEdit\",\n            component: () => import(\"@/views/exam/question-lib/judgment\"),\n            meta: { title: \"判断题编辑\", icon: \"CircleClose\", authorities: [\"exam_question_lib\"] },\n          },\n          {\n            path: \"fill-blank\",\n            name: \"fillBlankEdit\",\n            component: () => import(\"@/views/exam/question-lib/fill-blank\"),\n            meta: { title: \"填空题编辑\", icon: \"MoreFilled\", authorities: [\"exam_question_lib\"] },\n          },\n          {\n            path: \"subjective\",\n            name: \"subjectiveEdit\",\n            component: () => import(\"@/views/exam/question-lib/subjective\"),\n            meta: { title: \"简答题编辑\", icon: \"Edit\", authorities: [\"exam_question_lib\"] },\n          }\n        ]\n      },\n      {\n        path: \"paper\",\n        component: LayoutEmpty,\n        meta: { title: \"试卷\", icon: \"el-icon-tickets\", authorities: [\"learning\"] },\n        children: [\n          {\n            path: \"category\",\n            name: \"examPaperCategory\",\n            component: () => import(\"@/views/exam/paper/category\"),\n            meta: { title: \"试卷分类\", icon: \"Collection\", authorities: [\"learning_category\"] },\n          },\n          {\n            path: \"\",\n            name: \"examPaperList\",\n            component: () => import(\"@/views/exam/paper\"),\n            meta: { title: \"试卷管理\", icon: \"Memo\", authorities: [\"learning_list\"] },\n          },\n          {\n            path: \"normal\",\n            name: \"examPaperNormal\",\n            component: () => import(\"@/views/exam/paper/normal\"),\n            meta: { title: \"静态试卷编辑\", icon: \"Edit\", authorities: [\"learning_list\"] },\n          },\n          {\n            path: \"random\",\n            name: \"examPaperRandom\",\n            component: () => import(\"@/views/exam/paper/random\"),\n            meta: { title: \"随机试卷编辑\", icon: \"EditPen\", authorities: [\"learning_list\"] },\n          },\n          // {\n          //   path: \"mock\",\n          //   name: \"examPaperMock\",\n          //   component: () => import(\"@/views/exam/paper/mock\"),\n          //   meta: { title: \"模拟试卷编辑\", icon: \"el-icon-bank-card\", authorities: [\"learning_list\"] },\n          // },\n        ]\n      },\n      {\n        path: \"exam\",\n        component: LayoutEmpty,\n        meta: { title: \"考试\", icon: \"el-icon-c-scale-to-original\", authorities: [\"comment\"] },\n        children: [\n          {\n            path: \"category\",\n            name: \"examCategory\",\n            component: () => import(\"@/views/exam/category\"),\n            meta: { title: \"考试分类\", icon: \"Collection\", authorities: [\"comment_sensitive_setting\"] }\n          },\n          {\n            path: \"\",\n            name: \"examList\",\n            component: () => import(\"@/views/exam/list\"),\n            meta: { title: \"考试管理\", icon: \"Tickets\", authorities: [\"comment_list\"] },\n          },\n          {\n            path: \"edit\",\n            name: \"examEdit\",\n            component: () => import(\"@/views/exam/list/edit\"),\n            meta: { title: \"考试编辑\", icon: \"Edit\", authorities: [\"comment_sensitive_setting\"] },\n          }\n        ]\n      },\n      {\n        path: \"answer\",\n        component: LayoutEmpty,\n        meta: { title: \"答卷\", icon: \"el-icon-files\", authorities: [\"comment\"] },\n        children: [\n          {\n            path: \"\",\n            name: \"examAnswerList\",\n            component: () => import(\"@/views/exam/answer/list\"),\n            meta: { title: \"答卷管理\", icon: \"DocumentCopy\", authorities: [\"comment_list\"] }\n          },\n          {\n            path: \"mark\",\n            name: \"examAnswerMark\",\n            component: () => import(\"@/views/exam/answer/mark\"),\n            meta: { title: \"答卷批改\", icon: \"Check\", authorities: [\"comment_sensitive_setting\"] }\n          }\n        ]\n      }\n    ]\n  },\n  {\n    path: \"/live\",\n    name: \"live\",\n    component: Layout,\n    meta: { title: \"直播\", icon: \"VideoCamera\", authorities: [\"live\"], breadcrumb: false },\n    children: [\n      {\n        path: \"\",\n        name: \"liveIndex\",\n        component: () => import(\"@/views/live/index\"),\n        meta: { title: \"概览\", name: \"live\", icon: \"\", authorities: [\"live\"] },\n        children: []\n      },\n      {\n        path: \"channel\",\n        name: \"channelList\",\n        component: LayoutEmpty,\n        meta: { title: \"频道\", icon: \"VideoCamera\", authorities: [\"live_channel\"] },\n        children: [\n          {\n            path: \"\",\n            name: \"channelList\",\n            component: () => import(\"@/views/live/channel/index\"),\n            meta: {title: \"频道管理\", icon: \"Grid\", authorities: [\"live_channel\"]},\n          },\n          {\n            path: \"edit\",\n            name: \"channelEdit\",\n            component: () => import(\"@/views/live/channel/edit\"),\n            meta: { title: \"频道编辑\", icon: \"Edit\", activeMenu: \"/live/channel\" },\n            hidden: true\n          },\n          {\n            path: \"category\",\n            name: \"channelCategory\",\n            component: () => import(\"@/views/live/category/index\"),\n            meta: { title: \"频道分类\", icon: \"Collection\", authorities: [\"live_category\"] }\n          },\n        ]\n      },\n      {\n        path: \"lecturer\",\n        name: \"lecturer\",\n        component: LayoutEmpty,\n        meta: { title: \"讲师\", icon: \"el-icon-s-custom\", authorities: [\"lecturer\"] },\n        children: [\n          {\n            path: \"list\",\n            name: \"lecturerList\",\n            component: () => import(\"@/views/live/lecturer/index\"),\n            meta: { title: \"讲师管理\", icon: \"Avatar\", authorities: [\"lecturer_list\"] },\n          },\n          {\n            path: \"edit\",\n            name: \"lecturerEdit\",\n            component: () => import(\"@/views/live/lecturer/edit\"),\n            meta: { title: \"讲师编辑\", icon: \"Edit\", activeMenu: \"/lecturer/list\" },\n            hidden: true\n          }\n        ]\n      },\n    ]\n  },\n  {\n    path: \"/news\",\n    component: Layout,\n    meta: { title: \"新闻\", icon: \"el-icon-news\", authorities: [\"news\"], breadcrumb: false },\n    children: [\n      {\n        path: \"\",\n        name:\"news\",\n        component: () => import(\"@/views/news/index\"),\n        meta: { title: \"概览\", icon: \"\", authorities: [\"news\"] },\n        children: []\n      },\n      {\n        path: \"list\",\n        name: \"newsList\",\n        component: () => import(\"@/views/news/content/index\"),\n        meta: { title: \"新闻管理\", icon: \"Tickets\", authorities: [\"news_list\"] },\n        children: []\n      },\n      {\n        path: \"edit\",\n        name: \"newsEdit\",\n        component: () => import(\"@/views/news/content/edit\"),\n        meta: { title: \"新闻编辑\", icon: \"Edit\", activeMenu: \"/content/news\" },\n        hidden: true\n      }\n    ]\n  },\n  {\n    path: \"/article\",\n    component: Layout,\n    meta: { title: \"文章\", icon: \"el-icon-postcard\", authorities: [\"article\"], breadcrumb: false },\n    children: [\n      {\n        path: \"\",\n        component: () => import(\"@/views/article/index\"),\n        meta: { title: \"概览\", icon: \"\", authorities: [\"article\"] },\n        children: []\n      },\n      {\n        path: \"list\",\n        name: \"articleList\",\n        component: () => import(\"@/views/article/content/index\"),\n        meta: { title: \"文章管理\", icon: \"Tickets\", authorities: [\"article_list\"] },\n      },\n      {\n        path: \"category\",\n        name: \"contentCategory\",\n        component: () => import(\"@/views/article/category/index\"),\n        meta: { title: \"文章分类\", icon: \"Collection\", authorities: [\"article_category\"] },\n      }\n    ]\n  },\n  {\n    path: \"/ask\",\n    component: Layout,\n    meta: { title: \"问答\", icon: \"el-icon-question\", authorities: [\"ask\"], breadcrumb: false },\n    children: [\n      {\n        path: \"\",\n        component: () => import(\"@/views/ask/index\"),\n        meta: { title: \"概览\", icon: \"\", authorities: [\"ask\"] },\n        children: []\n      },\n      {\n        path: \"question\",\n        name: \"questionList\",\n        component: () => import(\"@/views/ask/question/index\"),\n        meta: { title: \"问题管理\", icon: \"Tickets\", authorities: [\"ask_question\"] },\n      },\n      {\n        path: \"category\",\n        name: \"questionCategory\",\n        component: () => import(\"@/views/ask/category/index\"),\n        meta: { title: \"问题分类\", icon: \"Collection\", authorities: [\"ask_question_category\"] },\n      },\n    ]\n  },\n  {\n    path: \"/circle\",\n    component: Layout,\n    meta: { title: \"社区\", icon: \"el-icon-discover\", authorities: [\"circle\"], breadcrumb: false },\n    children: [\n      {\n        path: \"\",\n        component: () => import(\"@/views/circle/index\"),\n        meta: { title: \"概览\", icon: \"\", authorities: [\"circle\"] },\n        children: []\n      },\n      {\n        path: \"dynamics\",\n        name: \"dynamicsList\",\n        component: () => import(\"@/views/circle/dynamic/index\"),\n        meta: { title: \"动态列表\", icon: \"ChatLineSquare\", authorities: [\"circle_dynamic\"] },\n      },\n      {\n        path: \"list\",\n        name: \"circleList\",\n        component: () => import(\"@/views/circle/list/index\"),\n        meta: { title: \"社区管理\", icon: \"ScaleToOriginal\", authorities: [\"circle_list\"] },\n      },\n      {\n        path: \"category\",\n        name: \"circleCategory\",\n        component: () => import(\"@/views/circle/category/index\"),\n        meta: { title: \"社区分类\", icon: \"Collection\", authorities: [\"circle_category\"] },\n      },\n    ]\n  },\n  {\n    path: \"/resource\",\n    component: Layout,\n    meta: { title: \"知识库\", icon: \"el-icon-collection\", authorities: [\"resource\"], breadcrumb: false },\n    children: [\n      {\n        path: \"\",\n        component: () => import(\"@/views/resource/index\"),\n        meta: { title: \"概览\", icon: \"\", authorities: [\"resource\"] },\n        children: []\n      },\n      {\n        path: \"list\",\n        name: \"resourceList\",\n        component: () => import(\"@/views/resource/list/index\"),\n        meta: { title: \"知识管理\", icon: \"Tickets\", authorities: [\"resource_list\"] },\n      },\n      {\n        path: \"category\",\n        name: \"resourceCategory\",\n        component: () => import(\"@/views/resource/category/index\"),\n        meta: { title: \"知识分类\", icon: \"Collection\", authorities: [\"resource_category\"] },\n      },\n    ]\n  },\n  {\n    path: \"/point\",\n    component: Layout,\n    meta: { title: \"积分\", icon: \"el-icon-goods\", authorities: [\"point\"], breadcrumb: false },\n    children: [\n      {\n        path: \"\",\n        component: () => import(\"@/views/point/index\"),\n        meta: { title: \"概览\", icon: \"\", authorities: [\"point\"] },\n        children: []\n      },\n      {\n        path: \"list\",\n        name: \"pointList\",\n        component: () => import(\"@/views/point/list/index\"),\n        meta: { title: \"积分管理\", icon: \"Coin\", authorities: [\"point_list\"] },\n      },\n      {\n        path: \"channel\",\n        name: \"pointChannel\",\n        component: () => import(\"@/views/point/channel/index\"),\n        meta: { title: \"积分渠道\", icon: \"Guide\", authorities: [\"point_channel\"] },\n      },\n      {\n        path: \"record\",\n        name: \"pointRecord\",\n        component: () => import(\"@/views/point/record/index\"),\n        meta: { title: \"积分记录\", icon: \"Tickets\", authorities: [\"point_record\"] },\n      }\n    ]\n  },\n  {\n    path: \"/comment\",\n    component: Layout,\n    meta: { title: \"评论\", icon: \"el-icon-chat-dot-square\", authorities: [\"comment\"], breadcrumb: false },\n    children: [\n      {\n        path: \"\",\n        component: () => import(\"@/views/comment/index\"),\n        meta: { title: \"概览\", icon: \"\", authorities: [\"comment\"] },\n        children: []\n      },\n      {\n        path: \"list\",\n        name: \"commentList\",\n        component: () => import(\"@/views/comment/list/index\"),\n        meta: { title: \"评论管理\", icon: \"Tickets\", authorities: [\"comment_list\"] }\n      },\n      {\n        path: \"sensitive-word\",\n        name: \"commentSensitiveSetting\",\n        component: () => import(\"@/views/comment/sensitive-word/index\"),\n        meta: { title: \"敏感词\", icon: \"Warning\", authorities: [\"comment_sensitive_setting\"] }\n      }\n    ]\n  },\n  {\n    path: \"/search\",\n    component: Layout,\n    meta: { title: \"搜索\", icon: \"el-icon-search\", authorities: [\"search\"], breadcrumb: false },\n    children: [\n      {\n        path: \"\",\n        component: () => import(\"@/views/search/index\"),\n        meta: { title: \"概览\", icon: \"\", authorities: [\"search\"] },\n        children: []\n      },\n      {\n        path: \"hot-word\",\n        name: \"searchHotWord\",\n        component: () => import(\"@/views/search/hot-word/index\"),\n        meta: { title: \"热词管理\", icon: \"HotWater\", authorities: [\"search_hot_word\"] }\n      }\n    ]\n  },\n  {\n    path: \"/message\",\n    component: Layout,\n    meta: { title: \"消息\", icon: \"el-icon-chat-dot-round\", authorities: [\"message\"], breadcrumb: false },\n    children: [\n      {\n        path: \"\",\n        component:  () => import(\"@/views/message/index\"),\n        meta: { title: \"概览\", icon: \"\", authorities: [\"message\"] },\n        children: []\n      },\n      {\n        path: \"announcement\",\n        name: \"announcement\",\n        component:  () => import(\"@/views/message/announcement/index\"),\n        meta: { title: \"公告管理\", icon: \"Tickets\", authorities: [\"message_announcement\"] }\n      }\n    ]\n  },\n  {\n    path: \"/member\",\n    component: Layout,\n    meta: { title: \"会员\", icon: \"el-icon-user\", authorities: [\"member\"], breadcrumb: false },\n    children: [\n      {\n        path: \"\",\n        component: () => import(\"@/views/member/index\"),\n        meta: { title: \"概览\", icon: \"\", authorities: [\"member\"] },\n        children: []\n      },\n      {\n        path: \"list\",\n        name: \"memberList\",\n        component: () => import(\"@/views/member/list/index\"),\n        meta: { title: \"会员管理\", icon: \"Tickets\", authorities: [\"member_list\"] },\n        children: []\n      },\n      {\n        path: \"level\",\n        name: \"memberLevel\",\n        component: () => import(\"@/views/member/level/index\"),\n        meta: { title: \"会员等级\", icon: \"DataLine\", authorities: [\"member_level\"] },\n        children: []\n      },\n      {\n        path: \"edit\",\n        name: \"memberEdit\",\n        component: () => import(\"@/views/member/list/index\"),\n        meta: { title: \"会员编辑\", icon: \"Edit\", activeMenu: \"/member/list\" },\n        hidden: true,\n        children: []\n      }\n    ]\n  },\n  {\n    path: \"/auth\",\n    component: Layout,\n    meta: { title: \"权限\", icon: \"el-icon-unlock\", authorities: [\"authority\"], breadcrumb: false },\n    children: [\n      {\n        path: \"\",\n        component: () => import(\"@/views/auth/index\"),\n        meta: { title: \"概览\", icon: \"\", authorities: [\"authority\"] },\n        children: []\n      },\n      {\n        path: \"organizational\",\n        component: LayoutEmpty,\n        meta: { title: \"组织架构\", icon: \"el-icon-office-building\", authorities: [\"organizational\"] },\n        redirect: \"organizational/user\",\n        children: [\n          {\n            path: \"user\",\n            name: \"organizationalUser\",\n            component: () => import(\"@/views/organizational/user/index\"),\n            meta: { title: \"用户管理\", icon: \"User\", authorities: [\"organizational_user\"] }\n          },\n          {\n            path: \"department\",\n            name: \"organizationalDepartment\",\n            component: () => import(\"@/views/organizational/department/index\"),\n            meta: { title: \"组织管理\", icon: \"School\", authorities: [\"organizational_department\"] }\n          }\n        ]\n      },\n      {\n        path: \"role\",\n        name: \"authorityRole\",\n        component: () => import(\"@/views/auth/role/index\"),\n        meta: { title: \"角色管理\", icon: \"LocationInformation\", authorities: [\"authority_role\"] }\n      },\n      {\n        path: \"authority\",\n        name: \"authorityAuthority\",\n        component: () => import(\"@/views/auth/authority/index\"),\n        meta: { title: \"权限列表\", icon: \"Lock\", authorities: [\"authority_authority\"] }\n      }\n    ]\n  },\n  {\n    path: \"/setting\",\n    component: Layout,\n    meta: { title: \"系统\", icon: \"Setting\", authorities: [\"setting\"], breadcrumb: false },\n    children: [\n      {\n        path: \"\",\n        component:  () => import(\"@/views/setting/index\"),\n        meta: { title: \"概览\", icon: \"\", authorities: [\"setting\"] },\n        children: []\n      },\n      {\n        path: \"carousel\",\n        name: \"settingCarousel\",\n        component: () => import(\"@/views/setting/carousel/index\"),\n        meta: { title: \"轮播图管理\", icon: \"Picture\", authorities: [\"setting_carousel\"] }\n      },\n      {\n        path: \"agreement\",\n        name: \"settingAgreement\",\n        component: () => import(\"@/views/setting/agreement/index\"),\n        meta: { title: \"协议管理\", icon: \"Postcard\", authorities: [\"setting_agreement\"] }\n      }\n    ]\n  },\n  {\n    path: \"/account\",\n    component: Layout,\n    meta: { title: \"账号中心\", icon: \"el-icon-setting\", authorities: [\"setting\"], breadcrumb: false },\n    hidden: true,\n    children: [\n      {\n        path: \"\",\n        component: () => import(\"@/views/account/index\"),\n        meta: { title: \"基本信息\", icon: \"User\", authorities: [\"setting\"] },\n        children: []\n      },\n      {\n        path: \"carousel\",\n        name: \"sss\",\n        component: () => import(\"@/views/account/security/index\"),\n        meta: { title: \"安全设置\", icon: \"Lock\", authorities: [\"setting_carousel\"] }\n      }\n    ]\n  }\n]\n\nlet routerOptions = {\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n};\nlet router = createRouter(routerOptions);\nexport default router;\n"], "mappings": "AAAA,SAAQA,YAAY,EAAEC,gBAAgB,QAAO,YAAY;AACzD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,KAAK,MAAM,oBAAoB;AAEtC,OAAO,MAAMC,MAAM,GAAG,CACpB;EACEC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEP,KAAK;EAChBQ,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEN,eAAe;EAC1BO,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEL,aAAa;EACxBM,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,EAAE;EACRC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAER,cAAc;EACzBU,IAAI,EAAE;IAAEC,KAAK,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAmB,CAAC;EAChDH,MAAM,EAAE,IAAI;EACZI,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,OAAO;IACbI,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAmB,CAAC;IAC/CJ,SAAS,EAAEJ;EACb,CAAC;AAEL,CAAC,CACF;AAED,OAAO,MAAMU,WAAW,GAAG,CACzB;EACER,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEV,MAAM;EACjBY,IAAI,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,UAAU;IAAEG,WAAW,EAAE,CAAC,UAAU,CAAC;IAAEC,UAAU,EAAE;EAAM,CAAC;EACrFH,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC;IAC9CE,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEJ,IAAI,EAAE,UAAU;MAAEK,IAAI,EAAE,EAAE;MAAEG,WAAW,EAAE,CAAC,UAAU;IAAE,CAAC;IAC5EF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAET,WAAW;IACtBW,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,UAAU;MAAEG,WAAW,EAAE,CAAC,KAAK;IAAE,CAAC;IAC7DF,QAAQ,EAAE,CACR;MACEP,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,aAAa;MACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC;MACrDE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,SAAS;QAAEG,WAAW,EAAE,CAAC,eAAe;MAAE,CAAC;MACxEF,QAAQ,EAAE;IACZ,CAAC,EACD;MACEP,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,eAAe;MACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC;MACvDE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,YAAY;QAAEG,WAAW,EAAE,CAAC,mBAAmB;MAAE;IAChF,CAAC,EACD;MACET,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,iBAAiB;MACvBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC;MAC1DE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,MAAM;QAAEK,UAAU,EAAE;MAAgB,CAAC;MAClER,MAAM,EAAE;IACV,CAAC;EAEL,CAAC,EACD;IACEH,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAET,WAAW;IACtBW,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,WAAW;MAAEG,WAAW,EAAE,CAAC,KAAK;IAAE,CAAC;IAC9DF,QAAQ,EAAE,CACR;MACEP,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,YAAY;MAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;MACpDE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,cAAc;QAAEG,WAAW,EAAE,CAAC,iBAAiB;MAAE;IAChF,CAAC,EACD;MACET,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,gBAAgB;MACtBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;MACzDE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,MAAM;QAAEK,UAAU,EAAE;MAAe,CAAC;MACjER,MAAM,EAAE;IACV,CAAC,EACD;MACEH,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,oBAAoB;MAC1BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC;MAC7DE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,YAAY;QAAEG,WAAW,EAAE,CAAC,0BAA0B;MAAE;IACvF,CAAC;EAEL,CAAC,EACD;IACET,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAET,WAAW;IACtBW,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,aAAa;MAAEG,WAAW,EAAE,CAAC,KAAK;IAAE,CAAC;IAClEF,QAAQ,EAAE,CACR;MACEP,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,UAAU;MAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC;MAClDE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,OAAO;QAAEG,WAAW,EAAE,CAAC,WAAW;MAAE;IACnE,CAAC,EACD;MACET,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,cAAc;MACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC;MACvDE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,MAAM;QAAEK,UAAU,EAAE;MAAa,CAAC;MAC/DR,MAAM,EAAE;IACV,CAAC;EAEL,CAAC,EACD;IACEH,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAET,WAAW;IACtBW,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,cAAc;MAAEG,WAAW,EAAE,CAAC,KAAK;IAAE,CAAC;IACjEF,QAAQ,EAAE,CACR;MACEP,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,WAAW;MACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;MACpDE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,cAAc;QAAEG,WAAW,EAAE,CAAC,WAAW;MAAE;IAC1E,CAAC;EAEL,CAAC,EACD;IACET,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAET,WAAW;IACtBW,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,iBAAiB;MAAEG,WAAW,EAAE,CAAC,KAAK;IAAE,CAAC;IACtEF,QAAQ,EAAE,CACR;MACEP,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,WAAW;MACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC;MACtDE,IAAI,EAAE;QAAEC,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE,UAAU;QAAEG,WAAW,EAAE,CAAC,WAAW;MAAE;IACxE,CAAC,EACD;MACET,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,eAAe;MACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;MAC3DE,IAAI,EAAE;QAAEC,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE,UAAU;QAAEG,WAAW,EAAE,CAAC,WAAW;MAAE;IACxE,CAAC,EACD;MACET,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,gBAAgB;MACtBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;MACzDE,IAAI,EAAE;QAAEC,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE,UAAU;QAAEG,WAAW,EAAE,CAAC,WAAW;MAAE;IACxE,CAAC;EAEL,CAAC;AAEL,CAAC,EACD;EACET,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEV,MAAM;EACjBY,IAAI,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,sBAAsB;IAAEG,WAAW,EAAE,CAAC,MAAM,CAAC;IAAEC,UAAU,EAAE;EAAM,CAAC;EAC7FH,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC;IAC7CE,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEJ,IAAI,EAAE,MAAM;MAAEK,IAAI,EAAE,EAAE;MAAEG,WAAW,EAAE,CAAC,MAAM;IAAE,CAAC;IACpEF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,cAAc;IACpBE,SAAS,EAAET,WAAW;IACtBW,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,SAAS;MAAEG,WAAW,EAAE,CAAC,MAAM;IAAE,CAAC;IAC7DF,QAAQ,EAAE,CACR;MACEP,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,yBAAyB;MAC/BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC;MAC7DE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,YAAY;QAAEG,WAAW,EAAE,CAAC,mBAAmB;MAAE;IAChF,CAAC,EACD;MACET,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,qBAAqB;MAC3BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;MACpDE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,cAAc;QAAEG,WAAW,EAAE,CAAC,mBAAmB;MAAE;IAClF,CAAC,EACD;MACET,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,kBAAkB;MACxBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,yCAAyC,CAAC;MAClEE,IAAI,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE,aAAa;QAAEG,WAAW,EAAE,CAAC,mBAAmB;MAAE;IAClF,CAAC,EACD;MACET,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,iBAAiB;MACvBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC;MACjEE,IAAI,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE,iBAAiB;QAAEG,WAAW,EAAE,CAAC,mBAAmB;MAAE;IACtF,CAAC,EACD;MACET,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,cAAc;MACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC;MAC7DE,IAAI,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE,aAAa;QAAEG,WAAW,EAAE,CAAC,mBAAmB;MAAE;IAClF,CAAC,EACD;MACET,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,eAAe;MACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC;MAC/DE,IAAI,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE,YAAY;QAAEG,WAAW,EAAE,CAAC,mBAAmB;MAAE;IACjF,CAAC,EACD;MACET,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,gBAAgB;MACtBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC;MAC/DE,IAAI,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE,MAAM;QAAEG,WAAW,EAAE,CAAC,mBAAmB;MAAE;IAC3E,CAAC;EAEL,CAAC,EACD;IACET,IAAI,EAAE,OAAO;IACbE,SAAS,EAAET,WAAW;IACtBW,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,iBAAiB;MAAEG,WAAW,EAAE,CAAC,UAAU;IAAE,CAAC;IACzEF,QAAQ,EAAE,CACR;MACEP,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,mBAAmB;MACzBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC;MACtDE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,YAAY;QAAEG,WAAW,EAAE,CAAC,mBAAmB;MAAE;IAChF,CAAC,EACD;MACET,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,eAAe;MACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC;MAC7CE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,MAAM;QAAEG,WAAW,EAAE,CAAC,eAAe;MAAE;IACtE,CAAC,EACD;MACET,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,iBAAiB;MACvBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;MACpDE,IAAI,EAAE;QAAEC,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE,MAAM;QAAEG,WAAW,EAAE,CAAC,eAAe;MAAE;IACxE,CAAC,EACD;MACET,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,iBAAiB;MACvBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;MACpDE,IAAI,EAAE;QAAEC,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE,SAAS;QAAEG,WAAW,EAAE,CAAC,eAAe;MAAE;IAC3E;IACA;IACA;IACA;IACA;IACA;IACA;IAAA;EAEJ,CAAC,EACD;IACET,IAAI,EAAE,MAAM;IACZE,SAAS,EAAET,WAAW;IACtBW,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,6BAA6B;MAAEG,WAAW,EAAE,CAAC,SAAS;IAAE,CAAC;IACpFF,QAAQ,EAAE,CACR;MACEP,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,cAAc;MACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC;MAChDE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,YAAY;QAAEG,WAAW,EAAE,CAAC,2BAA2B;MAAE;IACxF,CAAC,EACD;MACET,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,UAAU;MAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC;MAC5CE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,SAAS;QAAEG,WAAW,EAAE,CAAC,cAAc;MAAE;IACxE,CAAC,EACD;MACET,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,UAAU;MAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC;MACjDE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,MAAM;QAAEG,WAAW,EAAE,CAAC,2BAA2B;MAAE;IAClF,CAAC;EAEL,CAAC,EACD;IACET,IAAI,EAAE,QAAQ;IACdE,SAAS,EAAET,WAAW;IACtBW,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,eAAe;MAAEG,WAAW,EAAE,CAAC,SAAS;IAAE,CAAC;IACtEF,QAAQ,EAAE,CACR;MACEP,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,gBAAgB;MACtBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC;MACnDE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,cAAc;QAAEG,WAAW,EAAE,CAAC,cAAc;MAAE;IAC7E,CAAC,EACD;MACET,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,gBAAgB;MACtBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC;MACnDE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,OAAO;QAAEG,WAAW,EAAE,CAAC,2BAA2B;MAAE;IACnF,CAAC;EAEL,CAAC;AAEL,CAAC,EACD;EACET,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEV,MAAM;EACjBY,IAAI,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,aAAa;IAAEG,WAAW,EAAE,CAAC,MAAM,CAAC;IAAEC,UAAU,EAAE;EAAM,CAAC;EACpFH,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC;IAC7CE,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEJ,IAAI,EAAE,MAAM;MAAEK,IAAI,EAAE,EAAE;MAAEG,WAAW,EAAE,CAAC,MAAM;IAAE,CAAC;IACpEF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAET,WAAW;IACtBW,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,aAAa;MAAEG,WAAW,EAAE,CAAC,cAAc;IAAE,CAAC;IACzEF,QAAQ,EAAE,CACR;MACEP,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,aAAa;MACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC;MACrDE,IAAI,EAAE;QAACC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,MAAM;QAAEG,WAAW,EAAE,CAAC,cAAc;MAAC;IACnE,CAAC,EACD;MACET,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,aAAa;MACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;MACpDE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,MAAM;QAAEK,UAAU,EAAE;MAAgB,CAAC;MAClER,MAAM,EAAE;IACV,CAAC,EACD;MACEH,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,iBAAiB;MACvBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC;MACtDE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,YAAY;QAAEG,WAAW,EAAE,CAAC,eAAe;MAAE;IAC5E,CAAC;EAEL,CAAC,EACD;IACET,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAET,WAAW;IACtBW,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,kBAAkB;MAAEG,WAAW,EAAE,CAAC,UAAU;IAAE,CAAC;IAC1EF,QAAQ,EAAE,CACR;MACEP,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,cAAc;MACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC;MACtDE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,QAAQ;QAAEG,WAAW,EAAE,CAAC,eAAe;MAAE;IACxE,CAAC,EACD;MACET,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,cAAc;MACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC;MACrDE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,MAAM;QAAEK,UAAU,EAAE;MAAiB,CAAC;MACnER,MAAM,EAAE;IACV,CAAC;EAEL,CAAC;AAEL,CAAC,EACD;EACEH,IAAI,EAAE,OAAO;EACbE,SAAS,EAAEV,MAAM;EACjBY,IAAI,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,cAAc;IAAEG,WAAW,EAAE,CAAC,MAAM,CAAC;IAAEC,UAAU,EAAE;EAAM,CAAC;EACrFH,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,EAAE;IACRC,IAAI,EAAC,MAAM;IACXC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC;IAC7CE,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,EAAE;MAAEG,WAAW,EAAE,CAAC,MAAM;IAAE,CAAC;IACtDF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC;IACrDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,SAAS;MAAEG,WAAW,EAAE,CAAC,WAAW;IAAE,CAAC;IACpEF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;IACpDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAgB,CAAC;IAClER,MAAM,EAAE;EACV,CAAC;AAEL,CAAC,EACD;EACEH,IAAI,EAAE,UAAU;EAChBE,SAAS,EAAEV,MAAM;EACjBY,IAAI,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,kBAAkB;IAAEG,WAAW,EAAE,CAAC,SAAS,CAAC;IAAEC,UAAU,EAAE;EAAM,CAAC;EAC5FH,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,EAAE;IACRE,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC;IAChDE,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,EAAE;MAAEG,WAAW,EAAE,CAAC,SAAS;IAAE,CAAC;IACzDF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC;IACxDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,SAAS;MAAEG,WAAW,EAAE,CAAC,cAAc;IAAE;EACxE,CAAC,EACD;IACET,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,iBAAiB;IACvBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,YAAY;MAAEG,WAAW,EAAE,CAAC,kBAAkB;IAAE;EAC/E,CAAC;AAEL,CAAC,EACD;EACET,IAAI,EAAE,MAAM;EACZE,SAAS,EAAEV,MAAM;EACjBY,IAAI,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,kBAAkB;IAAEG,WAAW,EAAE,CAAC,KAAK,CAAC;IAAEC,UAAU,EAAE;EAAM,CAAC;EACxFH,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,EAAE;IACRE,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC;IAC5CE,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,EAAE;MAAEG,WAAW,EAAE,CAAC,KAAK;IAAE,CAAC;IACrDF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC;IACrDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,SAAS;MAAEG,WAAW,EAAE,CAAC,cAAc;IAAE;EACxE,CAAC,EACD;IACET,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC;IACrDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,YAAY;MAAEG,WAAW,EAAE,CAAC,uBAAuB;IAAE;EACpF,CAAC;AAEL,CAAC,EACD;EACET,IAAI,EAAE,SAAS;EACfE,SAAS,EAAEV,MAAM;EACjBY,IAAI,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,kBAAkB;IAAEG,WAAW,EAAE,CAAC,QAAQ,CAAC;IAAEC,UAAU,EAAE;EAAM,CAAC;EAC3FH,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,EAAE;IACRE,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC;IAC/CE,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,EAAE;MAAEG,WAAW,EAAE,CAAC,QAAQ;IAAE,CAAC;IACxDF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC;IACvDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,gBAAgB;MAAEG,WAAW,EAAE,CAAC,gBAAgB;IAAE;EACjF,CAAC,EACD;IACET,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;IACpDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,iBAAiB;MAAEG,WAAW,EAAE,CAAC,aAAa;IAAE;EAC/E,CAAC,EACD;IACET,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,gBAAgB;IACtBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC;IACxDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,YAAY;MAAEG,WAAW,EAAE,CAAC,iBAAiB;IAAE;EAC9E,CAAC;AAEL,CAAC,EACD;EACET,IAAI,EAAE,WAAW;EACjBE,SAAS,EAAEV,MAAM;EACjBY,IAAI,EAAE;IAAEC,KAAK,EAAE,KAAK;IAAEC,IAAI,EAAE,oBAAoB;IAAEG,WAAW,EAAE,CAAC,UAAU,CAAC;IAAEC,UAAU,EAAE;EAAM,CAAC;EAChGH,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,EAAE;IACRE,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC;IACjDE,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,EAAE;MAAEG,WAAW,EAAE,CAAC,UAAU;IAAE,CAAC;IAC1DF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC;IACtDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,SAAS;MAAEG,WAAW,EAAE,CAAC,eAAe;IAAE;EACzE,CAAC,EACD;IACET,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC;IAC1DE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,YAAY;MAAEG,WAAW,EAAE,CAAC,mBAAmB;IAAE;EAChF,CAAC;AAEL,CAAC,EACD;EACET,IAAI,EAAE,QAAQ;EACdE,SAAS,EAAEV,MAAM;EACjBY,IAAI,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,eAAe;IAAEG,WAAW,EAAE,CAAC,OAAO,CAAC;IAAEC,UAAU,EAAE;EAAM,CAAC;EACvFH,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,EAAE;IACRE,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC;IAC9CE,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,EAAE;MAAEG,WAAW,EAAE,CAAC,OAAO;IAAE,CAAC;IACvDF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC;IACnDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEG,WAAW,EAAE,CAAC,YAAY;IAAE;EACnE,CAAC,EACD;IACET,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC;IACtDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,OAAO;MAAEG,WAAW,EAAE,CAAC,eAAe;IAAE;EACvE,CAAC,EACD;IACET,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC;IACrDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,SAAS;MAAEG,WAAW,EAAE,CAAC,cAAc;IAAE;EACxE,CAAC;AAEL,CAAC,EACD;EACET,IAAI,EAAE,UAAU;EAChBE,SAAS,EAAEV,MAAM;EACjBY,IAAI,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,yBAAyB;IAAEG,WAAW,EAAE,CAAC,SAAS,CAAC;IAAEC,UAAU,EAAE;EAAM,CAAC;EACnGH,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,EAAE;IACRE,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC;IAChDE,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,EAAE;MAAEG,WAAW,EAAE,CAAC,SAAS;IAAE,CAAC;IACzDF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC;IACrDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,SAAS;MAAEG,WAAW,EAAE,CAAC,cAAc;IAAE;EACxE,CAAC,EACD;IACET,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,yBAAyB;IAC/BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC;IAC/DE,IAAI,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEG,WAAW,EAAE,CAAC,2BAA2B;IAAE;EACpF,CAAC;AAEL,CAAC,EACD;EACET,IAAI,EAAE,SAAS;EACfE,SAAS,EAAEV,MAAM;EACjBY,IAAI,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,gBAAgB;IAAEG,WAAW,EAAE,CAAC,QAAQ,CAAC;IAAEC,UAAU,EAAE;EAAM,CAAC;EACzFH,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,EAAE;IACRE,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC;IAC/CE,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,EAAE;MAAEG,WAAW,EAAE,CAAC,QAAQ;IAAE,CAAC;IACxDF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC;IACxDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,UAAU;MAAEG,WAAW,EAAE,CAAC,iBAAiB;IAAE;EAC5E,CAAC;AAEL,CAAC,EACD;EACET,IAAI,EAAE,UAAU;EAChBE,SAAS,EAAEV,MAAM;EACjBY,IAAI,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,wBAAwB;IAAEG,WAAW,EAAE,CAAC,SAAS,CAAC;IAAEC,UAAU,EAAE;EAAM,CAAC;EAClGH,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,EAAE;IACRE,SAAS,EAAGA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC;IACjDE,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,EAAE;MAAEG,WAAW,EAAE,CAAC,SAAS;IAAE,CAAC;IACzDF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAGA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC;IAC9DE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,SAAS;MAAEG,WAAW,EAAE,CAAC,sBAAsB;IAAE;EAChF,CAAC;AAEL,CAAC,EACD;EACET,IAAI,EAAE,SAAS;EACfE,SAAS,EAAEV,MAAM;EACjBY,IAAI,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,cAAc;IAAEG,WAAW,EAAE,CAAC,QAAQ,CAAC;IAAEC,UAAU,EAAE;EAAM,CAAC;EACvFH,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,EAAE;IACRE,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC;IAC/CE,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,EAAE;MAAEG,WAAW,EAAE,CAAC,QAAQ;IAAE,CAAC;IACxDF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;IACpDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,SAAS;MAAEG,WAAW,EAAE,CAAC,aAAa;IAAE,CAAC;IACtEF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC;IACrDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,UAAU;MAAEG,WAAW,EAAE,CAAC,cAAc;IAAE,CAAC;IACxEF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;IACpDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe,CAAC;IACjER,MAAM,EAAE,IAAI;IACZI,QAAQ,EAAE;EACZ,CAAC;AAEL,CAAC,EACD;EACEP,IAAI,EAAE,OAAO;EACbE,SAAS,EAAEV,MAAM;EACjBY,IAAI,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,gBAAgB;IAAEG,WAAW,EAAE,CAAC,WAAW,CAAC;IAAEC,UAAU,EAAE;EAAM,CAAC;EAC5FH,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,EAAE;IACRE,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC;IAC7CE,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,EAAE;MAAEG,WAAW,EAAE,CAAC,WAAW;IAAE,CAAC;IAC3DF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,gBAAgB;IACtBE,SAAS,EAAET,WAAW;IACtBW,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,yBAAyB;MAAEG,WAAW,EAAE,CAAC,gBAAgB;IAAE,CAAC;IACzFG,QAAQ,EAAE,qBAAqB;IAC/BL,QAAQ,EAAE,CACR;MACEP,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,oBAAoB;MAC1BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC;MAC5DE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,MAAM;QAAEG,WAAW,EAAE,CAAC,qBAAqB;MAAE;IAC5E,CAAC,EACD;MACET,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,0BAA0B;MAChCC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,yCAAyC,CAAC;MAClEE,IAAI,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,QAAQ;QAAEG,WAAW,EAAE,CAAC,2BAA2B;MAAE;IACpF,CAAC;EAEL,CAAC,EACD;IACET,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC;IAClDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,qBAAqB;MAAEG,WAAW,EAAE,CAAC,gBAAgB;IAAE;EACtF,CAAC,EACD;IACET,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,oBAAoB;IAC1BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC;IACvDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEG,WAAW,EAAE,CAAC,qBAAqB;IAAE;EAC5E,CAAC;AAEL,CAAC,EACD;EACET,IAAI,EAAE,UAAU;EAChBE,SAAS,EAAEV,MAAM;EACjBY,IAAI,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEG,WAAW,EAAE,CAAC,SAAS,CAAC;IAAEC,UAAU,EAAE;EAAM,CAAC;EACnFH,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,EAAE;IACRE,SAAS,EAAGA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC;IACjDE,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,EAAE;MAAEG,WAAW,EAAE,CAAC,SAAS;IAAE,CAAC;IACzDF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,iBAAiB;IACvBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDE,IAAI,EAAE;MAAEC,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE,SAAS;MAAEG,WAAW,EAAE,CAAC,kBAAkB;IAAE;EAC7E,CAAC,EACD;IACET,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC;IAC1DE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,UAAU;MAAEG,WAAW,EAAE,CAAC,mBAAmB;IAAE;EAC9E,CAAC;AAEL,CAAC,EACD;EACET,IAAI,EAAE,UAAU;EAChBE,SAAS,EAAEV,MAAM;EACjBY,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE,iBAAiB;IAAEG,WAAW,EAAE,CAAC,SAAS,CAAC;IAAEC,UAAU,EAAE;EAAM,CAAC;EAC7FP,MAAM,EAAE,IAAI;EACZI,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,EAAE;IACRE,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC;IAChDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEG,WAAW,EAAE,CAAC,SAAS;IAAE,CAAC;IAC/DF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDE,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEG,WAAW,EAAE,CAAC,kBAAkB;IAAE;EACzE,CAAC;AAEL,CAAC,CACF;AAED,IAAII,aAAa,GAAG;EAClBC,OAAO,EAAEvB,gBAAgB,CAACwB,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EAC/ClB;AACF,CAAC;AACD,IAAImB,MAAM,GAAG5B,YAAY,CAACuB,aAAa,CAAC;AACxC,eAAeK,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}