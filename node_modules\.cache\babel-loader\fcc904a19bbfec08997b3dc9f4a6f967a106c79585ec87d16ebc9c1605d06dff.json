{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, watch } from \"vue\";\nimport { useRoute } from \"vue-router\";\nimport router from \"../../../router\";\nimport { findCategoryList, toTree } from \"../../../api/live/category\";\nimport Page from \"../../../components/Page\";\nimport { findList, removeChannel } from \"../../../api/live/channel\";\nimport { confirm, success } from \"../../../util/tipsUtils\";\nimport commentDrawer from \"../../comment/commentDrawer\";\nexport default {\n  name: \"LiveChannelIndex\",\n  components: {\n    Page,\n    commentDrawer\n  },\n  setup() {\n    // 监听路由\n    const route = useRoute();\n    const routerAlive = ref(route.fullPath === \"/live/channel\");\n    watch(() => route.fullPath, () => {\n      routerAlive.value = route.fullPath === \"/live/channel\";\n    });\n    // 变量\n    const list = ref([]);\n    const total = ref(0);\n    const dataLoading = ref(true);\n    const selectCidList = ref([]);\n    const categoryOptions = ref([]);\n    const searchParam = ref({\n      keyword: \"\",\n      cid: \"\",\n      status: \"\",\n      size: 20,\n      current: 1\n    });\n    const statusMap = {\n      inactive: \"未开播\",\n      active: \"直播中\",\n      forbid: \"禁播中\",\n      deleted: \"已删除\"\n    };\n    // 加载分类\n    const loadCategory = () => {\n      findCategoryList(0, true, res => {\n        if (res) {\n          categoryOptions.value = toTree(res);\n        }\n      });\n    };\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true;\n      findList(searchParam.value, res => {\n        if (!res) {\n          return;\n        }\n        for (const listElement of res.list) {\n          listElement.chapterList = [];\n        }\n        list.value = res.list;\n        total.value = res.total;\n        dataLoading.value = false;\n      });\n    };\n    loadList();\n    loadCategory();\n    // 搜索\n    const search = () => {\n      if (selectCidList.value && selectCidList.value.length > 0) {\n        searchParam.value.cid = selectCidList.value[selectCidList.value.length - 1];\n      }\n      loadList();\n    };\n    // 编辑\n    const edit = id => {\n      routerAlive.value = false;\n      router.push({\n        path: \"/live/channel/edit\",\n        query: {\n          id: id\n        }\n      });\n    };\n    // 删除\n    const remove = item => {\n      confirm(\"确认删除 \" + item.name + \" ?\", \"提示\", () => {\n        removeChannel(item.id, () => {\n          success(\"删除成功\");\n          loadList();\n        });\n      }, () => {});\n    };\n    // 分页\n    const currentChange = currentPage => {\n      searchParam.value.current = currentPage;\n      loadList();\n    };\n    const sizeChange = s => {\n      searchParam.value.size = s;\n      loadList();\n    };\n    // 查看评论\n    const selectTopic = ref({});\n    const drawer = ref(false);\n    const drawerClose = done => {\n      drawer.value = false;\n      done();\n    };\n    const commentView = item => {\n      drawer.value = true;\n      selectTopic.value = item;\n    };\n    return {\n      routerAlive,\n      list,\n      total,\n      searchParam,\n      selectCidList,\n      categoryOptions,\n      statusMap,\n      search,\n      edit,\n      remove,\n      currentChange,\n      sizeChange,\n      dataLoading,\n      commentView,\n      selectTopic,\n      drawer,\n      drawerClose\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "watch", "useRoute", "router", "findCategoryList", "toTree", "Page", "findList", "removeChannel", "confirm", "success", "comment<PERSON>raw<PERSON>", "name", "components", "setup", "route", "routerAlive", "fullPath", "value", "list", "total", "dataLoading", "selectCidList", "categoryOptions", "searchParam", "keyword", "cid", "status", "size", "current", "statusMap", "inactive", "active", "forbid", "deleted", "loadCategory", "res", "loadList", "listElement", "chapterList", "search", "length", "edit", "id", "push", "path", "query", "remove", "item", "currentChange", "currentPage", "sizeChange", "s", "selectTopic", "drawer", "drawerClose", "done", "commentView"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/live/channel/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div v-if=\"routerAlive\">\n      <div class=\"header\">\n        <el-form :inline=\"true\" :model=\"searchParam\" class=\"demo-form-inline\">\n          <el-form-item label=\"\">\n            <el-input class=\"search-input\" size=\"mini\" v-model=\"searchParam.keyword\" placeholder=\"请输入名称关键字\"></el-input>\n            <el-button class=\"search-btn\" size=\"mini\" type=\"primary\" @click=\"search\">搜索</el-button>\n          </el-form-item>\n          <el-form-item label=\"状态\" class=\"status\">\n            <el-select size=\"mini\" v-model=\"searchParam.status\" @change=\"search\">\n              <el-option label=\"全部\" value=\"\"></el-option>\n              <el-option label=\"未开播\" value=\"inactive\"></el-option>\n              <el-option label=\"直播中\" value=\"active\"></el-option>\n              <el-option label=\"禁播中\" value=\"forbid\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"分类\">\n            <el-cascader size=\"mini\" v-model=\"selectCidList\" :options=\"categoryOptions\" :props=\"{ checkStrictly: true }\" @change=\"search\" clearable></el-cascader>\n          </el-form-item>\n          <el-form-item>\n            <el-button size=\"mini\" type=\"primary\" @click=\"edit()\">\n              <el-icon><Plus /></el-icon>\n              新增\n            </el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n      <div class=\"content\">\n        <el-table v-loading=\"dataLoading\" class=\"custom-table\" ref=\"multipleTable\" :show-header=\"false\" :data=\"list\" style=\"width: 100%\">\n          <el-table-column type=\"expand\">\n            <template #default=\"scope\">\n              <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n                <template #header>\n                  <div class=\"clearfix\">\n                    <span>直播流信息</span>\n                  </div>\n                </template>\n                <div class=\"table-wrapper\">\n                  <table class=\"fl-table\" style=\"width: 100%;\">\n                    <tr><td style=\"width: 120px;\">流名称：</td><td>{{scope.row.stream ? scope.row.stream.streamName : \"\"}}</td></tr>\n                    <tr><td style=\"width: 120px;\">推流地址：</td><td>{{scope.row.stream ? scope.row.stream.pushUrl : \"\"}}</td></tr>\n                    <tr><td style=\"width: 120px;\">拉流地址：</td><td>{{scope.row.stream ? scope.row.stream.pullUrl : \"\"}}</td></tr>\n                    <tr><td style=\"width: 120px;\">创建时间：</td><td>{{scope.row.createTime}}</td></tr>\n                  </table>\n                </div>\n              </el-card>\n              <el-card class=\"box-card\">\n                <template #header>\n                  <div class=\"clearfix\">\n                    <span>详情</span>\n                  </div>\n                </template>\n                <div class=\"table-wrapper\">\n                  <div v-html=\"scope.row.introduction\"></div>\n                </div>\n              </el-card>\n            </template>\n          </el-table-column>\n          <el-table-column>\n            <template #default=\"scope\">\n              <div class=\"content-item-warp\">\n                <a class=\"image\" v-if=\"scope.row.image && scope.row.image.trim()\">\n                  <img :src=\"scope.row.image\">\n                </a>\n                <div class=\"article-card-bone\">\n                  <div class=\"title-wrap\">\n                    <a class=\"title\">{{scope.row.name}}</a>\n                    <span class=\"label create-time\">{{scope.row.startTime}}</span>\n                  </div>\n                  <div class=\"abstruct\">\n                    <div class=\"status\">{{statusMap[scope.row.status]}}</div>\n                  </div>\n                  <div class=\"count-wrapper\">\n                    <ul class=\"count\">\n                      <li>预约 {{scope.row.subscriptionNum || 0}}</li>\n                      <li>观看 {{scope.row.watchNum || 0}}</li>\n                      <li>点赞 {{scope.row.likeNum || 0}}</li>\n                      <li>收藏 {{scope.row.favoriteNum || 0}}</li>\n                      <li>评论 {{scope.row.commentNum || 0}}</li>\n                    </ul>\n                    <div class=\"article-action-list\">\n                      <span class=\"icon-label\" @click=\"commentView(scope.row)\">查看评论</span>\n                      <span class=\"icon-label\" @click=\"edit(scope.row.id)\">编辑</span>\n                      <span class=\"icon-label\" @click=\"remove(scope.row)\">删除</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n      <comment-drawer topic-type=\"channel\" :drawer-close=\"drawerClose\" :show-drawer=\"drawer\" :topic=\"selectTopic\"/>\n      <page :total=\"total\" :current-change=\"currentChange\" :size-change=\"sizeChange\"></page>\n    </div>\n    <router-view v-if=\"!routerAlive\"/>\n  </div>\n</template>\n\n<script>\nimport {ref, watch} from \"vue\"\nimport {useRoute} from \"vue-router\"\nimport router from \"../../../router\"\nimport {findCategoryList, toTree} from \"../../../api/live/category\"\nimport Page from \"../../../components/Page\"\nimport {findList, removeChannel} from \"../../../api/live/channel\"\nimport {confirm, success} from \"../../../util/tipsUtils\";\nimport commentDrawer from \"../../comment/commentDrawer\"\n\nexport default {\n  name: \"LiveChannelIndex\",\n  components: {\n    Page,\n    commentDrawer\n  },\n  setup() {\n    // 监听路由\n    const route = useRoute()\n    const routerAlive = ref(route.fullPath === \"/live/channel\")\n    watch(() => route.fullPath, () => {\n      routerAlive.value = route.fullPath === \"/live/channel\";\n    })\n    // 变量\n    const list = ref([])\n    const total = ref(0)\n    const dataLoading = ref(true)\n    const selectCidList = ref([])\n    const categoryOptions = ref([])\n    const searchParam = ref({\n      keyword: \"\",\n      cid: \"\",\n      status: \"\",\n      size: 20,\n      current: 1\n    })\n    const statusMap = {\n      inactive : \"未开播\",\n      active : \"直播中\",\n      forbid : \"禁播中\",\n      deleted : \"已删除\"\n    }\n    // 加载分类\n    const loadCategory = () => {\n      findCategoryList(0, true, (res) => {if (res) { categoryOptions.value = toTree(res);}})\n    }\n    // 加载列表\n    const loadList = () => {\n      dataLoading.value = true\n      findList(searchParam.value, (res) => {\n        if (!res) {return;}\n        for (const listElement of res.list) {\n          listElement.chapterList = [];\n        }\n        list.value = res.list;\n        total.value = res.total;\n        dataLoading.value = false\n      })\n    }\n    loadList();\n    loadCategory();\n    // 搜索\n    const search = () => {\n      if (selectCidList.value && selectCidList.value.length > 0) {\n        searchParam.value.cid = selectCidList.value[selectCidList.value.length - 1];\n      }\n      loadList();\n    }\n    // 编辑\n    const edit = (id) => {\n      routerAlive.value = false\n      router.push({path: \"/live/channel/edit\", query: { id : id }})\n    }\n    // 删除\n    const remove = (item) => {\n      confirm(\"确认删除 \" + item.name + \" ?\", \"提示\", () => {\n        removeChannel(item.id, () => {\n          success(\"删除成功\")\n          loadList()\n        })\n      }, () => {\n      });\n    }\n    // 分页\n    const currentChange = (currentPage) => {\n      searchParam.value.current = currentPage;\n      loadList();\n    }\n    const sizeChange = (s) => {\n      searchParam.value.size = s;\n      loadList();\n    }\n    // 查看评论\n    const selectTopic = ref({})\n    const drawer = ref(false)\n    const drawerClose = (done) => {\n      drawer.value = false\n      done()\n    }\n    const commentView = (item) => {\n      drawer.value = true\n      selectTopic.value = item\n    }\n    return {\n      routerAlive,\n      list,\n      total,\n      searchParam,\n      selectCidList,\n      categoryOptions,\n      statusMap,\n      search,\n      edit,\n      remove,\n      currentChange,\n      sizeChange,\n      dataLoading,\n      commentView,\n      selectTopic,\n      drawer,\n      drawerClose,\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n  .app-container {\n    margin: 20px;\n    .content {\n      .content-item-warp {\n        position: relative;\n        display: flex;\n        .image {\n          width: 168px;\n          min-width: 168px;\n          height: 108px;\n          margin-right: 24px;\n          position: relative;\n          overflow: hidden;\n          border-radius: 4px;\n          border: 1px solid #e8e8e8;\n          cursor: default;\n          img {\n            width: 100%;\n            height: 100%;\n            transition: all .5s ease-out .1s;\n            -o-object-fit: cover;\n            object-fit: cover;\n            -o-object-position: center;\n            object-position: center;\n            &:hover {\n              transform: matrix(1.04,0,0,1.04,0,0);\n              -webkit-backface-visibility: hidden;\n              backface-visibility: hidden;\n            }\n          }\n        }\n        .article-card-bone {\n          width: 100%;\n          display: flex;\n          flex-direction: column;\n          min-width: 0;\n          .title-wrap {\n            display: flex;\n            justify-content: space-between;\n            margin-top: 0;\n            .title {\n              font-size: 16px;\n              overflow: hidden;\n              white-space: nowrap;\n              text-overflow: ellipsis;\n              line-height: 24px;\n              font-weight: 600;\n              display: block;\n              color: #222;\n              cursor: text;\n            }\n            .create-time {\n              color: #999;\n              line-height: 24px;\n              margin-left: 12px;\n              flex-shrink: 0;\n            }\n          }\n          .content {\n            word-break: break-word;\n            overflow-wrap: break-word;\n            margin: 8px 0 4px 0;\n            font-size: 12px;\n          }\n          .abstruct {\n            line-height: 20px;\n            margin-top: 20px;\n            height: 20px;\n            display: flex;\n            align-items: flex-end;\n            .status {\n              color: #999;\n              border: none;\n              background-color: #f5f5f5;\n              padding: 0 8px;\n              line-height: 20px;\n              font-size: 12px;\n              border-radius: 2px;\n              white-space: nowrap;\n              display: inline-block;\n              box-sizing: border-box;\n              transition: all .3s;\n              margin-right: 8px;\n            }\n            .article-card .byte-tag-simple {\n              margin-right: 8px;\n            }\n            .divider {\n              width: 1px;\n              height: 12px;\n              margin: 4px 10px 4px 4px;\n              background: #bfbfbf;\n            }\n            .icon {\n              margin-right: 8px;\n              svg {\n                vertical-align: bottom;\n                &:focus {\n                  outline: none;\n                }\n              }\n            }\n          }\n          .count-wrapper {\n            margin-top: 24px;\n            display: flex;\n            justify-content: space-between;\n            .count {\n              line-height: 20px;\n              position: relative;\n              li {\n                display: inline-block;\n                margin-right: 24px;\n                &:after {\n                  content: \"\\ff65\";\n                  font-size: 20px;\n                  margin: 0 8px;\n                  line-height: 0;\n                  position: absolute;\n                  top: 10px;\n                  color: #666;\n                }\n                &:last-child:after {\n                  content: \"\"\n                }\n              }\n            }\n            .article-action-list {\n              display: flex;\n              line-height: 20px;\n              flex: 1 0 auto;\n              justify-content: flex-end;\n              .icon-label {\n                cursor: pointer;\n                font-size: 14px;\n                line-height: 20px;\n                display: flex;\n                color: #222;\n                font-weight: 400;\n                margin-left: 24px;\n                &:first-child {\n                  margin-left: 0;\n                }\n                &:hover {\n                  color: $--color-primary;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    .el-table th.is-leaf, .el-table td {\n      border: 0!important;\n    }\n    .el-table th.is-leaf, .el-table td:nth-child(1) {\n      text-align: right;\n      min-width: 100px;\n    }\n    .image {\n      height: 60px;\n      display: inline-block;\n    }\n    .search-input {\n      width: 242px;\n    }\n    .el-table-column--selection .cell{\n      padding-left: 14px;\n      padding-right: 14px;\n    }\n    ::v-deep .el-table tbody tr:hover > td {\n      background-color: transparent;\n    }\n  }\n</style>\n<style lang=\"scss\">\n  .el-table.custom-table table tr:last-child {\n    td {\n      border: 0!important;\n    }\n  }\n  .el-table::before {\n    height: 0!important;\n  }\n</style>\n"], "mappings": ";AAqGA,SAAQA,GAAG,EAAEC,KAAK,QAAO,KAAI;AAC7B,SAAQC,QAAQ,QAAO,YAAW;AAClC,OAAOC,MAAK,MAAO,iBAAgB;AACnC,SAAQC,gBAAgB,EAAEC,MAAM,QAAO,4BAA2B;AAClE,OAAOC,IAAG,MAAO,0BAAyB;AAC1C,SAAQC,QAAQ,EAAEC,aAAa,QAAO,2BAA0B;AAChE,SAAQC,OAAO,EAAEC,OAAO,QAAO,yBAAyB;AACxD,OAAOC,aAAY,MAAO,6BAA4B;AAEtD,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,UAAU,EAAE;IACVP,IAAI;IACJK;EACF,CAAC;EACDG,KAAKA,CAAA,EAAG;IACN;IACA,MAAMC,KAAI,GAAIb,QAAQ,EAAC;IACvB,MAAMc,WAAU,GAAIhB,GAAG,CAACe,KAAK,CAACE,QAAO,KAAM,eAAe;IAC1DhB,KAAK,CAAC,MAAMc,KAAK,CAACE,QAAQ,EAAE,MAAM;MAChCD,WAAW,CAACE,KAAI,GAAIH,KAAK,CAACE,QAAO,KAAM,eAAe;IACxD,CAAC;IACD;IACA,MAAME,IAAG,GAAInB,GAAG,CAAC,EAAE;IACnB,MAAMoB,KAAI,GAAIpB,GAAG,CAAC,CAAC;IACnB,MAAMqB,WAAU,GAAIrB,GAAG,CAAC,IAAI;IAC5B,MAAMsB,aAAY,GAAItB,GAAG,CAAC,EAAE;IAC5B,MAAMuB,eAAc,GAAIvB,GAAG,CAAC,EAAE;IAC9B,MAAMwB,WAAU,GAAIxB,GAAG,CAAC;MACtByB,OAAO,EAAE,EAAE;MACXC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC;IACD,MAAMC,SAAQ,GAAI;MAChBC,QAAO,EAAI,KAAK;MAChBC,MAAK,EAAI,KAAK;MACdC,MAAK,EAAI,KAAK;MACdC,OAAM,EAAI;IACZ;IACA;IACA,MAAMC,YAAW,GAAIA,CAAA,KAAM;MACzB/B,gBAAgB,CAAC,CAAC,EAAE,IAAI,EAAGgC,GAAG,IAAK;QAAC,IAAIA,GAAG,EAAE;UAAEb,eAAe,CAACL,KAAI,GAAIb,MAAM,CAAC+B,GAAG,CAAC;QAAC;MAAC,CAAC;IACvF;IACA;IACA,MAAMC,QAAO,GAAIA,CAAA,KAAM;MACrBhB,WAAW,CAACH,KAAI,GAAI,IAAG;MACvBX,QAAQ,CAACiB,WAAW,CAACN,KAAK,EAAGkB,GAAG,IAAK;QACnC,IAAI,CAACA,GAAG,EAAE;UAAC;QAAO;QAClB,KAAK,MAAME,WAAU,IAAKF,GAAG,CAACjB,IAAI,EAAE;UAClCmB,WAAW,CAACC,WAAU,GAAI,EAAE;QAC9B;QACApB,IAAI,CAACD,KAAI,GAAIkB,GAAG,CAACjB,IAAI;QACrBC,KAAK,CAACF,KAAI,GAAIkB,GAAG,CAAChB,KAAK;QACvBC,WAAW,CAACH,KAAI,GAAI,KAAI;MAC1B,CAAC;IACH;IACAmB,QAAQ,EAAE;IACVF,YAAY,EAAE;IACd;IACA,MAAMK,MAAK,GAAIA,CAAA,KAAM;MACnB,IAAIlB,aAAa,CAACJ,KAAI,IAAKI,aAAa,CAACJ,KAAK,CAACuB,MAAK,GAAI,CAAC,EAAE;QACzDjB,WAAW,CAACN,KAAK,CAACQ,GAAE,GAAIJ,aAAa,CAACJ,KAAK,CAACI,aAAa,CAACJ,KAAK,CAACuB,MAAK,GAAI,CAAC,CAAC;MAC7E;MACAJ,QAAQ,EAAE;IACZ;IACA;IACA,MAAMK,IAAG,GAAKC,EAAE,IAAK;MACnB3B,WAAW,CAACE,KAAI,GAAI,KAAI;MACxBf,MAAM,CAACyC,IAAI,CAAC;QAACC,IAAI,EAAE,oBAAoB;QAAEC,KAAK,EAAE;UAAEH,EAAC,EAAIA;QAAG;MAAC,CAAC;IAC9D;IACA;IACA,MAAMI,MAAK,GAAKC,IAAI,IAAK;MACvBvC,OAAO,CAAC,OAAM,GAAIuC,IAAI,CAACpC,IAAG,GAAI,IAAI,EAAE,IAAI,EAAE,MAAM;QAC9CJ,aAAa,CAACwC,IAAI,CAACL,EAAE,EAAE,MAAM;UAC3BjC,OAAO,CAAC,MAAM;UACd2B,QAAQ,EAAC;QACX,CAAC;MACH,CAAC,EAAE,MAAM,CACT,CAAC,CAAC;IACJ;IACA;IACA,MAAMY,aAAY,GAAKC,WAAW,IAAK;MACrC1B,WAAW,CAACN,KAAK,CAACW,OAAM,GAAIqB,WAAW;MACvCb,QAAQ,EAAE;IACZ;IACA,MAAMc,UAAS,GAAKC,CAAC,IAAK;MACxB5B,WAAW,CAACN,KAAK,CAACU,IAAG,GAAIwB,CAAC;MAC1Bf,QAAQ,EAAE;IACZ;IACA;IACA,MAAMgB,WAAU,GAAIrD,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAMsD,MAAK,GAAItD,GAAG,CAAC,KAAK;IACxB,MAAMuD,WAAU,GAAKC,IAAI,IAAK;MAC5BF,MAAM,CAACpC,KAAI,GAAI,KAAI;MACnBsC,IAAI,EAAC;IACP;IACA,MAAMC,WAAU,GAAKT,IAAI,IAAK;MAC5BM,MAAM,CAACpC,KAAI,GAAI,IAAG;MAClBmC,WAAW,CAACnC,KAAI,GAAI8B,IAAG;IACzB;IACA,OAAO;MACLhC,WAAW;MACXG,IAAI;MACJC,KAAK;MACLI,WAAW;MACXF,aAAa;MACbC,eAAe;MACfO,SAAS;MACTU,MAAM;MACNE,IAAI;MACJK,MAAM;MACNE,aAAa;MACbE,UAAU;MACV9B,WAAW;MACXoC,WAAW;MACXJ,WAAW;MACXC,MAAM;MACNC;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}