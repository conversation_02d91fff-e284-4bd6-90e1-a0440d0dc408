{"ast": null, "code": "import { ref } from \"vue\";\nimport router from \"../../../router\";\nimport { useRoute } from \"vue-router\";\nimport { getCategory, removeCategory } from \"@/api/resource\";\nimport CategoryEdit from \"./edit\";\nimport CategoryTree from \"./tree\";\nimport { error, confirm, success, info } from \"@/util/tipsUtils\";\nexport default {\n  name: \"ResourceProductIndex\",\n  components: {\n    CategoryTree,\n    CategoryEdit\n  },\n  setup() {\n    let cardTitle = ref(\"基础信息\");\n    const type = ref(\"detail\");\n    const pid = ref(0);\n    const c = {\n      pid: 0,\n      name: \"\",\n      image: \"\",\n      sortOrder: 1,\n      isShow: true,\n      isShowIndex: true\n    };\n    let category = ref(c);\n    const handleNodeClick = data => {\n      getCategory(data.id, res => {\n        if (!res) {\n          error(\"没有找到该标签\");\n          return;\n        }\n        category.value = res;\n        type.value = \"detail\";\n      });\n    };\n    const route = useRoute();\n    const currentNodeKey = ref(0);\n    let id = route.query.id;\n    if (id) {\n      handleNodeClick({\n        id: id\n      });\n      currentNodeKey.value = parseInt(id);\n    }\n    let beforeCategoryId;\n    // 新增同级分类\n    const add = id => {\n      type.value = \"edit\";\n      cardTitle.value = \"新增同级分类\";\n      if (category.value.id) {\n        beforeCategoryId = category.value.id;\n      }\n      pid.value = id;\n      c.pid = id;\n      category.value = c;\n    };\n    // 新增子分类\n    const addChildren = id => {\n      type.value = \"edit\";\n      cardTitle.value = \"新增子分类\";\n      if (category.value.id) {\n        beforeCategoryId = category.value.id;\n      }\n      pid.value = id;\n      c.pid = id;\n      category.value = c;\n    };\n    // 编辑\n    const edit = (id, item) => {\n      type.value = \"edit\";\n      cardTitle.value = \"编辑\";\n      beforeCategoryId = item.id;\n      pid.value = id;\n      category.value = item;\n    };\n    // 删除\n    const remove = category => {\n      if (category.children) {\n        error(\"该类目下面存在子类目，不允许删除\");\n        return;\n      }\n      confirm(\"确定删除该目录?\", \"提示\", () => {\n        removeCategory(category.id, () => {\n          success(\"删除成功\");\n          router.go(0);\n        });\n      }, () => {\n        info(\"取消删除\");\n      });\n    };\n    const editSuccess = id => {\n      if (id) {\n        currentNodeKey.value = parseInt(id);\n        handleNodeClick({\n          id: id\n        });\n      }\n    };\n    const editCancel = () => {\n      if (beforeCategoryId) {\n        handleNodeClick({\n          id: beforeCategoryId\n        });\n      } else {\n        type.value = 'detail';\n      }\n      cardTitle.value = \"基础信息\";\n    };\n    return {\n      cardTitle,\n      type,\n      pid,\n      category,\n      currentNodeKey,\n      handleNodeClick,\n      add,\n      addChildren,\n      edit,\n      remove,\n      editSuccess,\n      editCancel\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "router", "useRoute", "getCategory", "removeCategory", "CategoryEdit", "CategoryTree", "error", "confirm", "success", "info", "name", "components", "setup", "cardTitle", "type", "pid", "c", "image", "sortOrder", "isShow", "isShowIndex", "category", "handleNodeClick", "data", "id", "res", "value", "route", "currentNodeKey", "query", "parseInt", "beforeCategoryId", "add", "add<PERSON><PERSON><PERSON><PERSON>", "edit", "item", "remove", "children", "go", "editSuccess", "editCancel"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/resource/tag/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row>\n      <el-col :span=\"6\">\n        <category-tree :current-node-key=\"currentNodeKey\" class=\"tree\" @node-click=\"handleNodeClick\"/>\n      </el-col>\n      <el-col :span=\"18\">\n        <el-card class=\"box-card\">\n          <template #header>\n            <div class=\"category-head clearfix\">\n              <span class=\"category-title\">{{cardTitle}}</span>\n              <el-button size=\"mini\" class=\"category-btn\" v-if=\"type !== 'edit' && category.id\" @click=\"edit(category.pid, category)\">编辑</el-button>\n              <el-button size=\"mini\" class=\"category-btn\" v-if=\"type !== 'edit' && category.id\" @click=\"remove(category)\">删除</el-button>\n              <el-button size=\"mini\" class=\"category-btn\" v-if=\"type !== 'edit'\" @click=\"add(category.pid)\">新增标签</el-button>\n            </div>\n          </template>\n          <!-- 详情 -->\n          <div class=\"table-wrapper\" v-if=\"type === 'detail'\">\n            <table class=\"fl-table\" v-if=\"!category.id\"><tbody><tr><td>请选择左边的标签查看详细信息</td></tr></tbody></table>\n            <table class=\"fl-table\" v-else>\n              <tbody>\n                <tr><td width=\"20%\">名称</td><td>{{category.name}}</td></tr>\n              </tbody>\n            </table>\n          </div>\n          <!-- 编辑 -->\n          <div class=\"table-wrapper\" v-else>\n            <category-edit :edit-success=\"editSuccess\" :edit-cancel=\"editCancel\" :data=\"category\" :pid=\"pid\"/>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\n  import {ref} from \"vue\";\n  import router from \"../../../router\";\n  import { useRoute } from \"vue-router\"\n  import {getCategory, removeCategory} from \"@/api/resource\";\n  import CategoryEdit from \"./edit\";\n  import CategoryTree from \"./tree\";\n  import {error, confirm, success, info} from \"@/util/tipsUtils\";\n  export default {\n    name: \"ResourceProductIndex\",\n    components: {\n      CategoryTree,\n      CategoryEdit\n    },\n    setup() {\n      let cardTitle = ref(\"基础信息\")\n      const type = ref(\"detail\")\n      const pid = ref(0)\n      const c = {\n        pid: 0,\n        name: \"\",\n        image: \"\",\n        sortOrder: 1,\n        isShow: true,\n        isShowIndex: true\n      }\n      let category = ref(c)\n      const handleNodeClick = (data) => {\n        getCategory(data.id, (res) => {\n          if (!res) {\n            error(\"没有找到该标签\")\n            return;\n          }\n          category.value = res;\n          type.value = \"detail\";\n        });\n      }\n      const route = useRoute();\n      const currentNodeKey = ref(0)\n      let id = route.query.id;\n      if (id) {\n        handleNodeClick({id: id});\n        currentNodeKey.value = parseInt(id)\n      }\n      let beforeCategoryId;\n      // 新增同级分类\n      const add = (id) => {\n        type.value = \"edit\";\n        cardTitle.value = \"新增同级分类\";\n        if (category.value.id) {\n          beforeCategoryId = category.value.id\n        }\n        pid.value = id;\n        c.pid = id\n        category.value = c;\n      }\n      // 新增子分类\n      const addChildren = (id) => {\n        type.value = \"edit\";\n        cardTitle.value = \"新增子分类\";\n        if (category.value.id) {\n          beforeCategoryId = category.value.id\n        }\n        pid.value = id;\n        c.pid = id\n        category.value = c;\n      }\n      // 编辑\n      const edit = (id, item) => {\n        type.value = \"edit\";\n        cardTitle.value = \"编辑\";\n        beforeCategoryId = item.id\n        pid.value = id;\n        category.value = item;\n      }\n      // 删除\n      const remove = (category) => {\n        if (category.children) {\n          error(\"该类目下面存在子类目，不允许删除\")\n          return;\n        }\n        confirm(\"确定删除该目录?\", \"提示\", () => {\n          removeCategory(category.id, () => {\n            success(\"删除成功\")\n            router.go(0);\n          })\n        }, () => {\n          info(\"取消删除\")\n        });\n      }\n      const editSuccess = (id) => {\n        if (id) {\n          currentNodeKey.value = parseInt(id)\n          handleNodeClick({id: id});\n        }\n      }\n      const editCancel = () => {\n        if (beforeCategoryId) {\n          handleNodeClick({id: beforeCategoryId});\n        } else {\n          type.value = 'detail'\n        }\n        cardTitle.value = \"基础信息\";\n      }\n      return {\n        cardTitle,\n        type,\n        pid,\n        category,\n        currentNodeKey,\n        handleNodeClick,\n        add,\n        addChildren,\n        edit,\n        remove,\n        editSuccess,\n        editCancel\n      }\n    }\n  };\n</script>\n<style scoped lang=\"scss\">\n  .app-container {\n    margin: 20px;\n    .tree {\n      padding: 0 10px 0 0;\n    }\n    .box-card {\n      .category-head {\n        line-height: 28px;\n      }\n      .category-btn {\n        float: right;\n        margin-left: 10px;\n      }\n    }\n  }\n  .fl-table {\n    border-radius: 5px;\n    font-size: 14px;\n    font-weight: normal;\n    border: none;\n    border-collapse: collapse;\n    width: 100%;\n    background-color: white;\n  }\n  .fl-table td {\n    border: 1px solid #EEEEEE;\n    font-size: 14px;\n    padding: 12px;\n  }\n  .fl-table tr td:nth-child(1) {\n    background: #F8F8F8;\n  }\n  .fl-table td img {\n    max-width: 500px;\n    max-height: 500px\n  }\n</style>\n<style>\n  .el-card__header {\n    padding: 10px 20px!important;\n  }\n</style>\n"], "mappings": "AAoCE,SAAQA,GAAG,QAAO,KAAK;AACvB,OAAOC,MAAK,MAAO,iBAAiB;AACpC,SAASC,QAAO,QAAS,YAAW;AACpC,SAAQC,WAAW,EAAEC,cAAc,QAAO,gBAAgB;AAC1D,OAAOC,YAAW,MAAO,QAAQ;AACjC,OAAOC,YAAW,MAAO,QAAQ;AACjC,SAAQC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,IAAI,QAAO,kBAAkB;AAC9D,eAAe;EACbC,IAAI,EAAE,sBAAsB;EAC5BC,UAAU,EAAE;IACVN,YAAY;IACZD;EACF,CAAC;EACDQ,KAAKA,CAAA,EAAG;IACN,IAAIC,SAAQ,GAAId,GAAG,CAAC,MAAM;IAC1B,MAAMe,IAAG,GAAIf,GAAG,CAAC,QAAQ;IACzB,MAAMgB,GAAE,GAAIhB,GAAG,CAAC,CAAC;IACjB,MAAMiB,CAAA,GAAI;MACRD,GAAG,EAAE,CAAC;MACNL,IAAI,EAAE,EAAE;MACRO,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,CAAC;MACZC,MAAM,EAAE,IAAI;MACZC,WAAW,EAAE;IACf;IACA,IAAIC,QAAO,GAAItB,GAAG,CAACiB,CAAC;IACpB,MAAMM,eAAc,GAAKC,IAAI,IAAK;MAChCrB,WAAW,CAACqB,IAAI,CAACC,EAAE,EAAGC,GAAG,IAAK;QAC5B,IAAI,CAACA,GAAG,EAAE;UACRnB,KAAK,CAAC,SAAS;UACf;QACF;QACAe,QAAQ,CAACK,KAAI,GAAID,GAAG;QACpBX,IAAI,CAACY,KAAI,GAAI,QAAQ;MACvB,CAAC,CAAC;IACJ;IACA,MAAMC,KAAI,GAAI1B,QAAQ,EAAE;IACxB,MAAM2B,cAAa,GAAI7B,GAAG,CAAC,CAAC;IAC5B,IAAIyB,EAAC,GAAIG,KAAK,CAACE,KAAK,CAACL,EAAE;IACvB,IAAIA,EAAE,EAAE;MACNF,eAAe,CAAC;QAACE,EAAE,EAAEA;MAAE,CAAC,CAAC;MACzBI,cAAc,CAACF,KAAI,GAAII,QAAQ,CAACN,EAAE;IACpC;IACA,IAAIO,gBAAgB;IACpB;IACA,MAAMC,GAAE,GAAKR,EAAE,IAAK;MAClBV,IAAI,CAACY,KAAI,GAAI,MAAM;MACnBb,SAAS,CAACa,KAAI,GAAI,QAAQ;MAC1B,IAAIL,QAAQ,CAACK,KAAK,CAACF,EAAE,EAAE;QACrBO,gBAAe,GAAIV,QAAQ,CAACK,KAAK,CAACF,EAAC;MACrC;MACAT,GAAG,CAACW,KAAI,GAAIF,EAAE;MACdR,CAAC,CAACD,GAAE,GAAIS,EAAC;MACTH,QAAQ,CAACK,KAAI,GAAIV,CAAC;IACpB;IACA;IACA,MAAMiB,WAAU,GAAKT,EAAE,IAAK;MAC1BV,IAAI,CAACY,KAAI,GAAI,MAAM;MACnBb,SAAS,CAACa,KAAI,GAAI,OAAO;MACzB,IAAIL,QAAQ,CAACK,KAAK,CAACF,EAAE,EAAE;QACrBO,gBAAe,GAAIV,QAAQ,CAACK,KAAK,CAACF,EAAC;MACrC;MACAT,GAAG,CAACW,KAAI,GAAIF,EAAE;MACdR,CAAC,CAACD,GAAE,GAAIS,EAAC;MACTH,QAAQ,CAACK,KAAI,GAAIV,CAAC;IACpB;IACA;IACA,MAAMkB,IAAG,GAAIA,CAACV,EAAE,EAAEW,IAAI,KAAK;MACzBrB,IAAI,CAACY,KAAI,GAAI,MAAM;MACnBb,SAAS,CAACa,KAAI,GAAI,IAAI;MACtBK,gBAAe,GAAII,IAAI,CAACX,EAAC;MACzBT,GAAG,CAACW,KAAI,GAAIF,EAAE;MACdH,QAAQ,CAACK,KAAI,GAAIS,IAAI;IACvB;IACA;IACA,MAAMC,MAAK,GAAKf,QAAQ,IAAK;MAC3B,IAAIA,QAAQ,CAACgB,QAAQ,EAAE;QACrB/B,KAAK,CAAC,kBAAkB;QACxB;MACF;MACAC,OAAO,CAAC,UAAU,EAAE,IAAI,EAAE,MAAM;QAC9BJ,cAAc,CAACkB,QAAQ,CAACG,EAAE,EAAE,MAAM;UAChChB,OAAO,CAAC,MAAM;UACdR,MAAM,CAACsC,EAAE,CAAC,CAAC,CAAC;QACd,CAAC;MACH,CAAC,EAAE,MAAM;QACP7B,IAAI,CAAC,MAAM;MACb,CAAC,CAAC;IACJ;IACA,MAAM8B,WAAU,GAAKf,EAAE,IAAK;MAC1B,IAAIA,EAAE,EAAE;QACNI,cAAc,CAACF,KAAI,GAAII,QAAQ,CAACN,EAAE;QAClCF,eAAe,CAAC;UAACE,EAAE,EAAEA;QAAE,CAAC,CAAC;MAC3B;IACF;IACA,MAAMgB,UAAS,GAAIA,CAAA,KAAM;MACvB,IAAIT,gBAAgB,EAAE;QACpBT,eAAe,CAAC;UAACE,EAAE,EAAEO;QAAgB,CAAC,CAAC;MACzC,OAAO;QACLjB,IAAI,CAACY,KAAI,GAAI,QAAO;MACtB;MACAb,SAAS,CAACa,KAAI,GAAI,MAAM;IAC1B;IACA,OAAO;MACLb,SAAS;MACTC,IAAI;MACJC,GAAG;MACHM,QAAQ;MACRO,cAAc;MACdN,eAAe;MACfU,GAAG;MACHC,WAAW;MACXC,IAAI;MACJE,MAAM;MACNG,WAAW;MACXC;IACF;EACF;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}