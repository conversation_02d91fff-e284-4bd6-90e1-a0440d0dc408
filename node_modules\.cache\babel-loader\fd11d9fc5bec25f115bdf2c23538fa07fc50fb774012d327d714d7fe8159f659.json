{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\");\n}", "map": {"version": 3, "names": ["_createElementBlock"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/login/workWeChat.vue"], "sourcesContent": ["<template>\n  <div/>\n</template>\n\n<script>\nimport {onMounted} from \"vue\"\nimport {useRoute} from \"vue-router\"\nimport router from \"../../router\"\nimport {loading, success} from \"../../util/tipsUtils\";\nimport {workWeChatLogin} from \"../../api/login\"\nimport {setToken} from \"../../util/tokenUtils\";\nexport default {\n  name: \"workWeChatLogin\",\n  setup() {\n    const route = useRoute()\n    onMounted(() => {\n      let loadingObj = loading(\"正在登录...\")\n      const code = route.query.code\n      const state = route.query.state\n      const appId = route.query.appid\n      console.log(code, state, appId)\n      workWeChatLogin({ code, state, appId }, (res) => {\n        success(\"登录成功\");\n        const accessToken = { expiresIn: res.expiresIn, value: res.value };\n        const refreshToken = res.refreshToken;\n        const data = { accessToken: accessToken, refreshToken: refreshToken };\n        // 保存登录信息\n        setToken(data)\n        loadingObj.close()\n        router.push(\"/index\")\n      }).catch((e) => {\n        console.log(e)\n        loadingObj.close()\n        router.push(\"/login\")\n      })\n    })\n    return {}\n  }\n}\n</script>\n"], "mappings": ";;uBACEA,mBAAA,CAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}