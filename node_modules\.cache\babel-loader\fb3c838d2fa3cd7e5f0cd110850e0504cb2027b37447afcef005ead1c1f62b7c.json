{"ast": null, "code": "import { ref } from \"vue\";\nimport Page from \"@/components/Page\";\nimport { Search } from \"@element-plus/icons-vue\";\nimport { findCategoryList, toTree } from \"@/api/learn/category\";\nimport { getCompanyStudyReport } from \"@/api/learn/lesson\";\nimport { formatSeconds } from \"@/util/dateUtils\";\nexport default {\n  name: \"LearnReportIndex\",\n  methods: {\n    formatSeconds\n  },\n  components: {\n    Search,\n    Page\n  },\n  setup() {\n    const loading = ref(true);\n    const total = ref(0);\n    const dataList = ref([]);\n    const c = {\n      current: 1,\n      size: 20\n    };\n    const params = ref(c);\n    const selectCidList = ref([]);\n    const categoryOptions = ref([]);\n    // 加载分类\n    const loadCategory = () => {\n      findCategoryList(0, true, res => {\n        if (res) {\n          categoryOptions.value = toTree(res);\n        }\n      });\n    };\n    loadCategory();\n    const loadList = () => {\n      loading.value = true;\n      getCompanyStudyReport(params.value, res => {\n        dataList.value = res.list;\n        total.value = res.total;\n        loading.value = false;\n      }).catch(() => {\n        loading.value = false;\n      });\n    };\n    loadList();\n    const currentChange = c => {\n      params.value.current = c;\n      loadList();\n    };\n    const sizeChange = s => {\n      params.value.size = s;\n      loadList();\n    };\n    const search = () => {\n      if (selectCidList.value && selectCidList.value.length > 0) {\n        params.value.cid = selectCidList.value[selectCidList.value.length - 1];\n      }\n      params.value.current = 1;\n      loadList();\n    };\n    const resetParams = () => {\n      params.value = c;\n    };\n    const customIndexFn = index => {\n      return (params.value.current - 1) * params.value.size + index + 1;\n    };\n    return {\n      customIndexFn,\n      loading,\n      dataList,\n      selectCidList,\n      categoryOptions,\n      params,\n      total,\n      currentChange,\n      sizeChange,\n      search,\n      resetParams\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "Page", "Search", "findCategoryList", "toTree", "getCompanyStudyReport", "formatSeconds", "name", "methods", "components", "setup", "loading", "total", "dataList", "c", "current", "size", "params", "selectCidList", "categoryOptions", "loadCategory", "res", "value", "loadList", "list", "catch", "currentChange", "sizeChange", "s", "search", "length", "cid", "resetParams", "customIndexFn", "index"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/learn/report/companystudy/index.vue"], "sourcesContent": ["<template>\n  <div class=\"report\">\n    <div class=\"header\">\n      <el-form :inline=\"true\" :model=\"params\" class=\"form-inline\">\n        <el-form-item label=\"课程名称\">\n          <el-input size=\"small\" @keydown.enter=\"search\" class=\"search-input\" v-model=\"params.name\" placeholder=\"请输入关键字\">\n            <template #suffix>\n              <i @click=\"search\" class=\"el-input__icon el-icon-search search-btn\"></i>\n            </template>\n          </el-input>\n        </el-form-item>\n        <el-form-item label=\"课程状态\" class=\"select\">\n          <el-select size=\"small\" v-model=\"params.status\" @change=\"search\">\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"未发布\" value=\"unpublished\"></el-option>\n            <el-option label=\"已发布\" value=\"published\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"课程分类\" class=\"select\">\n          <el-cascader size=\"small\" v-model=\"selectCidList\" :options=\"categoryOptions\" :props=\"{ checkStrictly: true }\" @change=\"search\" clearable></el-cascader>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" type=\"primary\" @click=\"search()\">\n            <el-icon style=\"vertical-align: middle\">\n              <Search />\n            </el-icon>\n            <span style=\"vertical-align: middle\">搜索</span>\n          </el-button>\n          <el-button size=\"small\" @click=\"resetParams()\">\n            <span style=\"vertical-align: middle\">重置</span>\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"report-main\">\n      <el-table :data=\"dataList\" v-loading=\"loading\">\n        <el-table-column label=\"序号\" type=\"index\" :index=\"customIndexFn\"></el-table-column>\n        <el-table-column label=\"公司\" prop=\"companyName\"></el-table-column>\n        <el-table-column label=\"年份\" prop=\"year\"></el-table-column>\n        <el-table-column label=\"报名会员数\" prop=\"signUpMemberQty\"></el-table-column>\n        <el-table-column label=\"报名次数\" prop=\"signUpQty\"></el-table-column>\n        <el-table-column label=\"已取得证书的会员数量\" prop=\"certificateMemberQty\"></el-table-column>\n        <el-table-column label=\"取得的证书数量\" prop=\"certificateQty\"></el-table-column>\n        <el-table-column label=\"公司总会员数\" prop=\"memberQty\"></el-table-column>\n      </el-table>\n      <page :total=\"total\" :size-change=\"sizeChange\" :current-change=\"currentChange\" :page-size=\"params.size\"/>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {ref} from \"vue\"\nimport Page from \"@/components/Page\";\nimport {Search} from \"@element-plus/icons-vue\";\nimport {findCategoryList, toTree} from \"@/api/learn/category\";\nimport {getCompanyStudyReport} from \"@/api/learn/lesson\";\nimport {formatSeconds} from \"@/util/dateUtils\";\nexport default {\n  name: \"LearnReportIndex\",\n  methods: {formatSeconds},\n  components: {Search, Page},\n  setup() {\n    const loading = ref(true)\n    const total = ref(0)\n    const dataList = ref([])\n    const c = {\n      current: 1,\n      size: 20\n    }\n    const params = ref(c)\n    const selectCidList = ref([])\n    const categoryOptions = ref([])\n    // 加载分类\n    const loadCategory = () => {\n      findCategoryList(0, true, (res) => {if (res) { categoryOptions.value = toTree(res);}})\n    }\n    loadCategory();\n\n    const loadList = () => {\n      loading.value = true\n      getCompanyStudyReport(params.value, res => {\n        dataList.value = res.list\n        total.value = res.total\n        loading.value = false\n      }).catch(() => {\n        loading.value = false\n      })\n    }\n    loadList()\n    const currentChange = (c) => {\n      params.value.current = c;\n      loadList();\n    }\n    const sizeChange = (s) => {\n      params.value.size = s;\n      loadList();\n    }\n    const search = () => {\n      if (selectCidList.value && selectCidList.value.length > 0) {\n        params.value.cid = selectCidList.value[selectCidList.value.length - 1];\n      }\n      params.value.current = 1\n      loadList()\n    }\n    const resetParams = () => {\n      params.value = c\n    }\n    const customIndexFn = (index) => {\n      return (params.value.current - 1) * params.value.size + index + 1;\n    }\n    return {\n      customIndexFn,\n      loading,\n      dataList,\n      selectCidList,\n      categoryOptions,\n      params,\n      total,\n      currentChange,\n      sizeChange,\n      search,\n      resetParams\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.report {\n  margin: 20px;\n  font-size: 12px;\n  .report-main {\n    ::v-deep .el-table {\n      font-size: 12px;\n      .el-table__empty-block {\n        line-height: 400px;\n        .el-table__empty-text {\n          line-height: 400px;\n        }\n      }\n      th, td {\n        padding: 6px 0;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": "AAmDA,SAAQA,GAAG,QAAO,KAAI;AACtB,OAAOC,IAAG,MAAO,mBAAmB;AACpC,SAAQC,MAAM,QAAO,yBAAyB;AAC9C,SAAQC,gBAAgB,EAAEC,MAAM,QAAO,sBAAsB;AAC7D,SAAQC,qBAAqB,QAAO,oBAAoB;AACxD,SAAQC,aAAa,QAAO,kBAAkB;AAC9C,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,OAAO,EAAE;IAACF;EAAa,CAAC;EACxBG,UAAU,EAAE;IAACP,MAAM;IAAED;EAAI,CAAC;EAC1BS,KAAKA,CAAA,EAAG;IACN,MAAMC,OAAM,GAAIX,GAAG,CAAC,IAAI;IACxB,MAAMY,KAAI,GAAIZ,GAAG,CAAC,CAAC;IACnB,MAAMa,QAAO,GAAIb,GAAG,CAAC,EAAE;IACvB,MAAMc,CAAA,GAAI;MACRC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE;IACR;IACA,MAAMC,MAAK,GAAIjB,GAAG,CAACc,CAAC;IACpB,MAAMI,aAAY,GAAIlB,GAAG,CAAC,EAAE;IAC5B,MAAMmB,eAAc,GAAInB,GAAG,CAAC,EAAE;IAC9B;IACA,MAAMoB,YAAW,GAAIA,CAAA,KAAM;MACzBjB,gBAAgB,CAAC,CAAC,EAAE,IAAI,EAAGkB,GAAG,IAAK;QAAC,IAAIA,GAAG,EAAE;UAAEF,eAAe,CAACG,KAAI,GAAIlB,MAAM,CAACiB,GAAG,CAAC;QAAC;MAAC,CAAC;IACvF;IACAD,YAAY,EAAE;IAEd,MAAMG,QAAO,GAAIA,CAAA,KAAM;MACrBZ,OAAO,CAACW,KAAI,GAAI,IAAG;MACnBjB,qBAAqB,CAACY,MAAM,CAACK,KAAK,EAAED,GAAE,IAAK;QACzCR,QAAQ,CAACS,KAAI,GAAID,GAAG,CAACG,IAAG;QACxBZ,KAAK,CAACU,KAAI,GAAID,GAAG,CAACT,KAAI;QACtBD,OAAO,CAACW,KAAI,GAAI,KAAI;MACtB,CAAC,CAAC,CAACG,KAAK,CAAC,MAAM;QACbd,OAAO,CAACW,KAAI,GAAI,KAAI;MACtB,CAAC;IACH;IACAC,QAAQ,EAAC;IACT,MAAMG,aAAY,GAAKZ,CAAC,IAAK;MAC3BG,MAAM,CAACK,KAAK,CAACP,OAAM,GAAID,CAAC;MACxBS,QAAQ,EAAE;IACZ;IACA,MAAMI,UAAS,GAAKC,CAAC,IAAK;MACxBX,MAAM,CAACK,KAAK,CAACN,IAAG,GAAIY,CAAC;MACrBL,QAAQ,EAAE;IACZ;IACA,MAAMM,MAAK,GAAIA,CAAA,KAAM;MACnB,IAAIX,aAAa,CAACI,KAAI,IAAKJ,aAAa,CAACI,KAAK,CAACQ,MAAK,GAAI,CAAC,EAAE;QACzDb,MAAM,CAACK,KAAK,CAACS,GAAE,GAAIb,aAAa,CAACI,KAAK,CAACJ,aAAa,CAACI,KAAK,CAACQ,MAAK,GAAI,CAAC,CAAC;MACxE;MACAb,MAAM,CAACK,KAAK,CAACP,OAAM,GAAI;MACvBQ,QAAQ,EAAC;IACX;IACA,MAAMS,WAAU,GAAIA,CAAA,KAAM;MACxBf,MAAM,CAACK,KAAI,GAAIR,CAAA;IACjB;IACA,MAAMmB,aAAY,GAAKC,KAAK,IAAK;MAC/B,OAAO,CAACjB,MAAM,CAACK,KAAK,CAACP,OAAM,GAAI,CAAC,IAAIE,MAAM,CAACK,KAAK,CAACN,IAAG,GAAIkB,KAAI,GAAI,CAAC;IACnE;IACA,OAAO;MACLD,aAAa;MACbtB,OAAO;MACPE,QAAQ;MACRK,aAAa;MACbC,eAAe;MACfF,MAAM;MACNL,KAAK;MACLc,aAAa;MACbC,UAAU;MACVE,MAAM;MACNG;IACF,CAAC;EACH;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}