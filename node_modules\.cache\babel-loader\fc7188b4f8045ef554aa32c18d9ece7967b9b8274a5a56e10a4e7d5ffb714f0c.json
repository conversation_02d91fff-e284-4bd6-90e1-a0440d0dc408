{"ast": null, "code": "import { resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-166673d9\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"app-container\"\n};\nconst _hoisted_2 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_cascader = _resolveComponent(\"el-cascader\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_switch = _resolveComponent(\"el-switch\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"departmentRef\",\n    rules: $setup.rules,\n    model: $setup.department,\n    \"label-width\": \"110px\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      label: \"上级组织\",\n      prop: \"pid\"\n    }, {\n      default: _withCtx(() => [$setup.parentDepartment.name ? (_openBlock(), _createBlock(_component_el_input, {\n        key: 0,\n        size: \"mini\",\n        type: \"text\",\n        class: \"input-text\",\n        disabled: \"\",\n        modelValue: $setup.parentDepartment.name,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.parentDepartment.name = $event)\n      }, null, 8, [\"modelValue\"])) : (_openBlock(), _createBlock(_component_el_cascader, {\n        key: 1,\n        size: \"mini\",\n        class: \"input-text\",\n        props: {\n          checkStrictly: true\n        },\n        modelValue: $setup.selectedPidList,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.selectedPidList = $event),\n        options: $setup.departmentOptions,\n        placeholder: \"请选择上级组织\",\n        onChange: $setup.changeParentDepartment\n      }, null, 8, [\"modelValue\", \"options\", \"onChange\"]))]),\n      _: 1\n    }), _createVNode(_component_el_form_item, {\n      label: \"组织名称\",\n      prop: \"name\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"mini\",\n        maxlength: \"15\",\n        \"show-word-limit\": \"\",\n        class: \"input-text\",\n        modelValue: $setup.department.name,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.department.name = $event)\n      }, null, 8, [\"modelValue\"])]),\n      _: 1\n    }), _createVNode(_component_el_form_item, {\n      label: \"是否显示\",\n      prop: \"enabled\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_switch, {\n        size: \"mini\",\n        id: \"enabled\",\n        \"active-color\": \"#13ce66\",\n        modelValue: $setup.department.enabled,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.department.enabled = $event)\n      }, null, 8, [\"modelValue\"])]),\n      _: 1\n    })]),\n    _: 1\n  }, 8, [\"rules\", \"model\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n    size: \"mini\",\n    onClick: _cache[4] || (_cache[4] = $event => $setup.cancel())\n  }, {\n    default: _withCtx(() => [_createTextVNode(\"取 消\")]),\n    _: 1\n  }), _createVNode(_component_el_button, {\n    size: \"mini\",\n    type: \"primary\",\n    onClick: _cache[5] || (_cache[5] = $event => $setup.submit())\n  }, {\n    default: _withCtx(() => [_createTextVNode(\"确 定\")]),\n    _: 1\n  })])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "rules", "$setup", "model", "department", "_component_el_form_item", "label", "prop", "parentDepartment", "name", "_createBlock", "_component_el_input", "size", "type", "disabled", "$event", "_component_el_cascader", "props", "checkStrictly", "selectedPidList", "options", "departmentOptions", "placeholder", "onChange", "changeParentDepartment", "maxlength", "_component_el_switch", "id", "enabled", "_createElementVNode", "_hoisted_2", "_component_el_button", "onClick", "_cache", "cancel", "submit"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/organizational/department/edit.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form ref=\"departmentRef\" :rules=\"rules\" :model=\"department\" label-width=\"110px\">\n      <el-form-item label=\"上级组织\" prop=\"pid\">\n        <el-input size=\"mini\" v-if=\"parentDepartment.name\" type=\"text\" class=\"input-text\" disabled v-model=\"parentDepartment.name\"></el-input>\n        <el-cascader size=\"mini\" v-else class=\"input-text\" :props=\"{checkStrictly: true}\" v-model=\"selectedPidList\" :options=\"departmentOptions\" placeholder=\"请选择上级组织\" @change=\"changeParentDepartment\"></el-cascader>\n      </el-form-item>\n      <el-form-item label=\"组织名称\" prop=\"name\">\n        <el-input size=\"mini\" maxlength=\"15\" show-word-limit class=\"input-text\" v-model=\"department.name\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"是否显示\" prop=\"enabled\">\n        <el-switch size=\"mini\" id=\"enabled\" active-color=\"#13ce66\" v-model=\"department.enabled\"></el-switch>\n      </el-form-item>\n    </el-form>\n    <div class=\"dialog-footer\">\n      <el-button size=\"mini\" @click=\"cancel()\">取 消</el-button>\n      <el-button size=\"mini\" type=\"primary\" @click=\"submit()\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\n  import {ref, watch} from \"vue\"\n  import router from \"../../../router\"\n  import {findDepartmentList, toTree, getDepartment, saveDepartment, updateDepartment} from \"@/api/organizational/department\"\n  import {success, error} from \"@/util/tipsUtils\";\n  export default {\n    name: \"departmentEdit\",\n    props: {\n      data: {\n        type: Object,\n        required: true\n      },\n      pid: {\n        type: Number,\n        required: true\n      },\n      editSuccess: {\n        type: Function\n      },\n      editCancel: {\n        type: Function\n      }\n    },\n    setup(props) {\n      let selectedPidList = ref([])\n      const departmentOptions = ref([])\n      const parentDepartment = ref({})\n      const rules = {\n        pid: [{ required: true, message: \"请选择上级组织\", trigger: \"blur\" }],\n        name: [{ required: true, message: \"请输入组织名称\", trigger: \"blur\" }],\n      }\n      let department = ref({\n        pid: 0,\n        name: \"\",\n        code: \"\",\n        enabled: true,\n      })\n      const init = (item, pid) => {\n        if (pid) {\n          getDepartment(pid, res => {\n            if (!res) {\n              error(\"没有找到该组织\")\n              return;\n            }\n            parentDepartment.value = res;\n          });\n        } else {\n          parentDepartment.value = {id: 0, name: \"全部\"};\n        }\n        if (item && item.id) {\n          department = ref(item);\n        }\n        department.value.pid = pid || 0;\n        selectedPidList.value.push(department.value.pid);\n      }\n      init(props.data, props.pid)\n      watch(() => props.data, (nv) => {\n        init(nv, nv.pid)\n        department = ref(nv)\n      })\n      const loadDepartment = () => {\n        findDepartmentList(0, true).then(function (response) {\n          if (response) {\n            departmentOptions.value = toTree(response);\n          }\n        });\n      }\n      loadDepartment();\n      const changeParentDepartment = () => {\n        if (department.value.selectedPidList && department.value.selectedPidList.length > 0) {\n          let id = selectedPidList.value[selectedPidList.value.length - 1];\n          if (id === department.value.id) {\n            error(\"不能选择自己为上级组织\")\n            return;\n          }\n          department.value.pid = id;\n        }\n      }\n      const cancel = () => {\n        props.editCancel && props.editCancel()\n      }\n      const departmentRef = ref(null)\n      const submit = () => {\n        departmentRef.value.validate(valid => {\n          if (!valid) {\n            return false;\n          }\n          if (!department.value.pid && department.value.pid !== 0) {\n            error(\"请选择上级组织\")\n            return false;\n          }\n          if (department.value.id) {\n            updateDepartment(department.value, (res) => {\n              success(\"编辑成功\")\n              router.push({path: \"/auth/organizational/department\", query:{ id: res[\"id\"]}});\n              props.editSuccess && props.editSuccess(res[\"id\"])\n            })\n          } else {\n            saveDepartment(department.value, (res) => {\n              success(\"新增成功\")\n              router.push({path: \"/auth/organizational/department\", query:{ id: res[\"id\"]}});\n              props.editSuccess && props.editSuccess(res[\"id\"])\n            })\n          }\n        });\n      }\n      return {\n        selectedPidList,\n        departmentOptions,\n        parentDepartment,\n        department,\n        rules,\n        departmentRef,\n        loadDepartment,\n        changeParentDepartment,\n        cancel,\n        submit\n      }\n    }\n  }\n</script>\n<style scoped lang=\"scss\">\n.dialog-footer {\n  text-align: center;\n}\n.input-text {\n  width: 80%;\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAe;;EAanBA,KAAK,EAAC;AAAe;;;;;;;;uBAb5BC,mBAAA,CAiBM,OAjBNC,UAiBM,GAhBJC,YAAA,CAWUC,kBAAA;IAXDC,GAAG,EAAC,eAAe;IAAEC,KAAK,EAAEC,MAAA,CAAAD,KAAK;IAAGE,KAAK,EAAED,MAAA,CAAAE,UAAU;IAAE,aAAW,EAAC;;sBAC1E,MAGe,CAHfN,YAAA,CAGeO,uBAAA;MAHDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAAsI,CAA1GL,MAAA,CAAAM,gBAAgB,CAACC,IAAI,I,cAAjDC,YAAA,CAAsIC,mBAAA;;QAA5HC,IAAI,EAAC,MAAM;QAA8BC,IAAI,EAAC,MAAM;QAAClB,KAAK,EAAC,YAAY;QAACmB,QAAQ,EAAR,EAAQ;oBAAUZ,MAAA,CAAAM,gBAAgB,CAACC,IAAI;mEAArBP,MAAA,CAAAM,gBAAgB,CAACC,IAAI,GAAAM,MAAA;oDACzHL,YAAA,CAA8MM,sBAAA;;QAAjMJ,IAAI,EAAC,MAAM;QAAQjB,KAAK,EAAC,YAAY;QAAEsB,KAAK,EAAE;UAAAC,aAAA;QAAA,CAAqB;oBAAWhB,MAAA,CAAAiB,eAAe;mEAAfjB,MAAA,CAAAiB,eAAe,GAAAJ,MAAA;QAAGK,OAAO,EAAElB,MAAA,CAAAmB,iBAAiB;QAAEC,WAAW,EAAC,SAAS;QAAEC,QAAM,EAAErB,MAAA,CAAAsB;;;QAE1K1B,YAAA,CAEeO,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAA6G,CAA7GT,YAAA,CAA6Ga,mBAAA;QAAnGC,IAAI,EAAC,MAAM;QAACa,SAAS,EAAC,IAAI;QAAC,iBAAe,EAAf,EAAe;QAAC9B,KAAK,EAAC,YAAY;oBAAUO,MAAA,CAAAE,UAAU,CAACK,IAAI;mEAAfP,MAAA,CAAAE,UAAU,CAACK,IAAI,GAAAM,MAAA;;;QAElGjB,YAAA,CAEeO,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MAAoG,CAApGT,YAAA,CAAoG4B,oBAAA;QAAzFd,IAAI,EAAC,MAAM;QAACe,EAAE,EAAC,SAAS;QAAC,cAAY,EAAC,SAAS;oBAAUzB,MAAA,CAAAE,UAAU,CAACwB,OAAO;mEAAlB1B,MAAA,CAAAE,UAAU,CAACwB,OAAO,GAAAb,MAAA;;;;;6BAG1Fc,mBAAA,CAGM,OAHNC,UAGM,GAFJhC,YAAA,CAAwDiC,oBAAA;IAA7CnB,IAAI,EAAC,MAAM;IAAEoB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAlB,MAAA,IAAEb,MAAA,CAAAgC,MAAM;;sBAAI,MAAG,C,iBAAH,KAAG,E;;MAC5CpC,YAAA,CAAuEiC,oBAAA;IAA5DnB,IAAI,EAAC,MAAM;IAACC,IAAI,EAAC,SAAS;IAAEmB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAlB,MAAA,IAAEb,MAAA,CAAAiC,MAAM;;sBAAI,MAAG,C,iBAAH,KAAG,E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}