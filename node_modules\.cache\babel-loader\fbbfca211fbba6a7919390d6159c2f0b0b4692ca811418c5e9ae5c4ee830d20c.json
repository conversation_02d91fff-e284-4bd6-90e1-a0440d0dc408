{"ast": null, "code": "import { get, put, post, del } from \"../../util/requestUtils\";\nexport function findList(params, success) {\n  return get(\"/ask/answer/list\", params, success);\n}\nexport function getQuestion(id, success) {\n  return get(\"/ask/public-api/question\", {\n    id: id\n  }, success);\n}\nexport function updateQuestion(data, success) {\n  return put(\"/ask/question\", data, success);\n}\nexport function saveQuestion(data, success) {\n  return post(\"/ask/question\", data, success);\n}\nexport function deleteQuestion(id, success) {\n  return del(\"/ask/question\", {\n    id: id\n  }, success);\n}", "map": {"version": 3, "names": ["get", "put", "post", "del", "findList", "params", "success", "getQuestion", "id", "updateQuestion", "data", "saveQuestion", "deleteQuestion"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/api/ask/answer.js"], "sourcesContent": ["import { get, put, post, del } from \"../../util/requestUtils\"\n\nexport function findList(params, success) {\n  return get(\"/ask/answer/list\", params, success)\n}\n\nexport function getQuestion(id, success) {\n  return get(\"/ask/public-api/question\", {id: id}, success)\n}\n\nexport function updateQuestion(data, success) {\n  return put(\"/ask/question\", data, success)\n}\n\nexport function saveQuestion(data, success) {\n  return post(\"/ask/question\", data, success)\n}\n\nexport function deleteQuestion(id, success) {\n  return del(\"/ask/question\", {id: id}, success)\n}\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,QAAQ,yBAAyB;AAE7D,OAAO,SAASC,QAAQA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACxC,OAAON,GAAG,CAAC,kBAAkB,EAAEK,MAAM,EAAEC,OAAO,CAAC;AACjD;AAEA,OAAO,SAASC,WAAWA,CAACC,EAAE,EAAEF,OAAO,EAAE;EACvC,OAAON,GAAG,CAAC,0BAA0B,EAAE;IAACQ,EAAE,EAAEA;EAAE,CAAC,EAAEF,OAAO,CAAC;AAC3D;AAEA,OAAO,SAASG,cAAcA,CAACC,IAAI,EAAEJ,OAAO,EAAE;EAC5C,OAAOL,GAAG,CAAC,eAAe,EAAES,IAAI,EAAEJ,OAAO,CAAC;AAC5C;AAEA,OAAO,SAASK,YAAYA,CAACD,IAAI,EAAEJ,OAAO,EAAE;EAC1C,OAAOJ,IAAI,CAAC,eAAe,EAAEQ,IAAI,EAAEJ,OAAO,CAAC;AAC7C;AAEA,OAAO,SAASM,cAAcA,CAACJ,EAAE,EAAEF,OAAO,EAAE;EAC1C,OAAOH,GAAG,CAAC,eAAe,EAAE;IAACK,EAAE,EAAEA;EAAE,CAAC,EAAEF,OAAO,CAAC;AAChD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}