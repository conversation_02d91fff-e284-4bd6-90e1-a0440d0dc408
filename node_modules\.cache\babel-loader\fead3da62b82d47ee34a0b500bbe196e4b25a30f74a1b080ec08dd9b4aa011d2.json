{"ast": null, "code": "import { ref } from \"vue\";\nimport Page from \"../../../components/Page\";\nimport { getMemberList, sealMember, unsealMember } from \"../../../api/member/index\";\nimport { confirm } from \"@/util/tipsUtils\";\nexport default {\n  name: \"MemeberList\",\n  components: {\n    Page\n  },\n  setup() {\n    const showUserDialogFlag = ref(false);\n    const stateMap = {\n      \"normal\": \"正常\",\n      \"black\": \"黑名单\",\n      \"lock\": \"锁定\",\n      \"deleted\": \"注销\"\n    };\n    const total = ref(0);\n    const memberList = ref([]);\n    const dataLoading = ref(true);\n    const param = ref({\n      current: 1,\n      size: 20,\n      keyword: \"\"\n    });\n    const member = ref({});\n    const loadMemberList = () => {\n      dataLoading.value = true;\n      getMemberList(param.value, res => {\n        dataLoading.value = false;\n        memberList.value = res.list;\n        total.value = res.total;\n      });\n    };\n    loadMemberList();\n    // 页码改变\n    const currentChange = currentPage => {\n      param.value.current = currentPage;\n      loadMemberList();\n    };\n    // 页面显示数量改变\n    const sizeChange = size => {\n      param.value.size = size;\n      loadMemberList();\n    };\n    const search = () => {\n      loadMemberList();\n    };\n    const seal = function (item) {\n      confirm(\"\", \"\", () => {\n        sealMember({\n          id: item.id\n        }, res => {\n          console.log(res);\n        });\n      });\n    };\n    const unseal = function () {\n      unsealMember({}, res => {\n        console.log(res);\n      });\n    };\n    const showUserDialog = function (item) {\n      showUserDialogFlag.value = true;\n      member.value = item;\n    };\n    const hideUserDialog = function () {\n      showUserDialogFlag.value = false;\n    };\n    return {\n      stateMap,\n      param,\n      total,\n      memberList,\n      currentChange,\n      sizeChange,\n      search,\n      dataLoading,\n      seal,\n      unseal,\n      showUserDialogFlag,\n      showUserDialog,\n      hideUserDialog,\n      member\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "Page", "getMemberList", "sealMember", "unsealMember", "confirm", "name", "components", "setup", "showUserDialogFlag", "stateMap", "total", "memberList", "dataLoading", "param", "current", "size", "keyword", "member", "loadMemberList", "value", "res", "list", "currentChange", "currentPage", "sizeChange", "search", "seal", "item", "id", "console", "log", "unseal", "showUserDialog", "hideUserDialog"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/member/list/index.vue"], "sourcesContent": ["<template>\n  <div class=\"member-container\">\n    <div class=\"head\">\n      <el-input size=\"mini\" v-model=\"param.keyword\" clearable placeholder=\"输入名称搜索\" class=\"custom-input\" @keyup.enter=\"search\">\n        <template #append>\n          <el-button size=\"mini\" class=\"custom-btn\" icon=\"el-icon-search\" @click=\"search\">搜索</el-button>\n        </template>\n      </el-input>\n    </div>\n    <el-table v-loading=\"dataLoading\" :data=\"memberList\" size=\"small\" style=\"width: 100%;\">\n      <el-table-column type=\"expand\">\n        <template #default=\"props\">\n          <el-card class=\"box-card\">\n            <template #header>\n              <div>\n                <span>基础信息</span>\n              </div>\n            </template>\n            <div class=\"table-wrapper\">\n              <table class=\"fl-table\">\n                <tbody>\n                  <tr><td>编号</td><td>{{props.row.code}}</td></tr>\n                  <tr><td>姓名</td><td>{{props.row.name}}</td></tr>\n                  <tr><td>性别</td><td>{{props.row.gender}}</td></tr>\n                  <tr><td>出生日期</td><td>{{props.row.birthday}}</td></tr>\n                  <tr><td>人员状态</td><td>{{stateMap[props.row.status]}}</td></tr>\n                  <tr><td>注册时间</td><td>{{props.row.createTime}}</td></tr>\n                  <tr><td>手机电话</td><td>{{props.row.mobile}}</td></tr>\n                  <tr><td>座机号码</td><td>{{props.row.telephone}}</td></tr>\n                  <tr><td>电子邮箱</td><td>{{props.row.email}}</td></tr>\n                  <tr><td>会员等级</td><td>{{props.row.level && props.row.level.name || \"无\"}}</td></tr>\n                </tbody>\n              </table>\n            </div>\n          </el-card>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"username\" label=\"账号\"/>\n      <el-table-column prop=\"name\" label=\"姓名\"/>\n      <el-table-column prop=\"mobile\" label=\"手机号码\"/>\n      <el-table-column :show-overflow-tooltip=\"true\" prop=\"email\" label=\"邮箱\"/>\n      <el-table-column label=\"会员等级\">\n        <template #default=\"scope\">\n          {{scope.row.level && scope.row.level.name || \"无\"}}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"状态\" align=\"center\">\n        <template #default=\"scope\">\n          {{stateMap[scope.row.status]}}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\">\n        <template #default=\"scope\">\n          <el-button size=\"mini\" type=\"text\" @click=\"showUserDialog(scope.row)\">编辑</el-button>\n          <el-button size=\"mini\" type=\"text\" @click=\"seal(scope.row.id)\">禁用</el-button>\n          <el-button size=\"mini\" type=\"text\" v-if=\"scope.row.status === 'black'\" @click=\"unseal\">解禁{{scope.row.id}}</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <!--分页组件-->\n    <page :total=\"total\" @size-change=\"sizeChange\" @current-change=\"currentChange\" :page-size=\"param.size\"/>\n    <!-- 编辑 -->\n    <el-dialog v-model=\"showUserDialogFlag\" :title=\"member.id ? '新增用户' : '编辑用户'\" append-to-body width=\"90%\" :before-close=\"hideUserDialog\">\n      <el-form :model=\"member\" :rules=\"userRules\" ref=\"userRef\" class=\"user-form\" label-width=\"150px\">\n        <el-form-item label=\"名字：\" prop=\"name\">\n          <el-input size=\"mini\" v-model=\"member.name\" placeholder=\"请输入名字\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"账号：\" prop=\"username\">\n          <el-input size=\"mini\" v-model=\"member.username\" placeholder=\"请输入账号\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"工号：\" prop=\"code\">\n          <el-input size=\"mini\" v-model=\"member.code\" placeholder=\"请输入工号\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"邮箱：\" prop=\"email\">\n          <el-input size=\"mini\" v-model=\"member.email\" placeholder=\"请输入导语\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"部门：\" prop=\"departmentId\">\n          <el-cascader style=\"width: 100%;\"\n                       size=\"mini\"\n                       v-model=\"selectDepartmentList\"\n                       :props=\"{ checkStrictly: true }\"\n                       :options=\"departmentOptionList\"\n                       @change=\"changeDepartment\"></el-cascader>\n        </el-form-item>\n        <el-form-item label=\"手机号码：\" prop=\"mobile\">\n          <el-input size=\"mini\" v-model=\"member.mobile\" placeholder=\"请输入导语\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"出生日期：\" prop=\"birthday\">\n          <el-date-picker style=\"width: 100%;\" size=\"mini\" v-model=\"member.birthday\" type=\"date\" placeholder=\"选择出生日期\"></el-date-picker>\n        </el-form-item>\n        <el-form-item label=\"性别：\" prop=\"gender\">\n          <el-radio size=\"mini\" v-model=\"member.gender\" label=\"男\">男</el-radio>\n          <el-radio size=\"mini\" v-model=\"member.gender\" label=\"女\">女</el-radio>\n        </el-form-item>\n        <el-form-item label=\"籍贯：\" prop=\"nativePlace\">\n          <el-input size=\"mini\" v-model=\"member.nativePlace\" placeholder=\"请输入籍贯\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"民族：\" prop=\"nation\">\n          <el-input size=\"mini\" v-model=\"member.nation\" placeholder=\"请输入民族\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"婚姻状态：\" prop=\"maritalStatus\">\n          <el-input size=\"mini\" v-model=\"member.maritalStatus\" placeholder=\"请输入身份证住址\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"身份证号：\" prop=\"idCard\">\n          <el-input size=\"mini\" v-model=\"member.idCard\" placeholder=\"请输入身份证号\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"身份证地址：\" prop=\"idCardAddress\">\n          <el-input size=\"mini\" v-model=\"member.idCardAddress\" placeholder=\"请输入身份证地址\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"当前住址：\" prop=\"currentAddress\">\n          <el-input size=\"mini\" v-model=\"member.currentAddress\" placeholder=\"请输入当前住址\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"办公电话：\" prop=\"telephone\">\n          <el-input size=\"mini\" v-model=\"member.telephone\" placeholder=\"请输入导语\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"合约开始时间：\" prop=\"contractStartDate\">\n          <el-date-picker style=\"width: 100%;\" size=\"mini\" v-model=\"member.contractStartDate\" type=\"date\" placeholder=\"选择合约开始时间\"></el-date-picker>\n        </el-form-item>\n        <el-form-item label=\"合约结束时间：\" prop=\"contractEndDate\">\n          <el-date-picker style=\"width: 100%;\" size=\"mini\" v-model=\"member.contractEndDate\" type=\"date\" placeholder=\"选择合约结束时间\"></el-date-picker>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div style=\"text-align: center;\">\n          <el-button size=\"mini\" @click=\"submit\">提交</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import {ref} from \"vue\"\n  import Page from \"../../../components/Page\"\n  import {getMemberList, sealMember, unsealMember} from \"../../../api/member/index\";\n  import {confirm} from \"@/util/tipsUtils\"\n  export default {\n    name: \"MemeberList\",\n    components: {\n      Page\n    },\n    setup() {\n      const showUserDialogFlag = ref(false)\n      const stateMap = {\"normal\": \"正常\", \"black\": \"黑名单\", \"lock\": \"锁定\", \"deleted\": \"注销\"}\n      const total = ref(0)\n      const memberList = ref([])\n      const dataLoading = ref(true)\n      const param = ref({\n        current: 1,\n        size: 20,\n        keyword: \"\",\n      })\n      const member = ref({})\n      const loadMemberList = () => {\n        dataLoading.value = true\n        getMemberList(param.value, res => {\n          dataLoading.value = false\n          memberList.value = res.list\n          total.value = res.total\n        })\n      }\n      loadMemberList();\n      // 页码改变\n      const currentChange = (currentPage) => {\n        param.value.current = currentPage;\n        loadMemberList()\n      }\n      // 页面显示数量改变\n      const sizeChange = (size) => {\n        param.value.size = size;\n        loadMemberList()\n      }\n      const search = () => {\n        loadMemberList()\n      }\n      const seal = function (item) {\n        confirm(\"\",  \"\", () => {\n          sealMember({id: item.id}, res => {\n            console.log(res)\n          })\n        })\n      }\n      const unseal = function () {\n        unsealMember({}, res => {\n          console.log(res)\n        })\n      }\n      const showUserDialog = function (item) {\n        showUserDialogFlag.value = true\n        member.value = item\n      }\n      const hideUserDialog = function () {\n        showUserDialogFlag.value = false\n      }\n      return {\n        stateMap,\n        param,\n        total,\n        memberList,\n        currentChange,\n        sizeChange,\n        search,\n        dataLoading,\n        seal,\n        unseal,\n        showUserDialogFlag,\n        showUserDialog,\n        hideUserDialog,\n        member\n      }\n    }\n  }\n</script>\n\n<style scoped lang=\"scss\">\n  .member-container {\n    margin: 20px;\n    .head {\n      margin-bottom: 10px;\n      .custom-input {\n        width: 50%;\n        min-width: 300px;\n        max-width: 400px;\n      }\n      .custom-btn {\n        &:hover {\n          color: $--color-primary;\n        }\n      }\n    }\n  }\n  .box-card {\n    max-width: 500px;\n  }\n  .fl-table {\n    border-radius: 5px;\n    font-size: 12px;\n    font-weight: normal;\n    border: none;\n    border-collapse: collapse;\n    width: 100%;\n    background-color: white;\n  }\n  .fl-table td {\n    border: 1px solid #f8f8f8;\n    font-size: 12px;\n    padding: 12px;\n  }\n  .fl-table tr td:nth-child(1) {\n    background: #F8F8F8;\n    width: 30%;\n    min-width: 100px;\n  }\n  .user-form {\n    display: inline-block;\n    .el-form-item {\n      width: 50%;\n      float: left;\n    }\n  }\n</style>\n"], "mappings": "AAoIE,SAAQA,GAAG,QAAO,KAAI;AACtB,OAAOC,IAAG,MAAO,0BAAyB;AAC1C,SAAQC,aAAa,EAAEC,UAAU,EAAEC,YAAY,QAAO,2BAA2B;AACjF,SAAQC,OAAO,QAAO,kBAAiB;AACvC,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE;IACVN;EACF,CAAC;EACDO,KAAKA,CAAA,EAAG;IACN,MAAMC,kBAAiB,GAAIT,GAAG,CAAC,KAAK;IACpC,MAAMU,QAAO,GAAI;MAAC,QAAQ,EAAE,IAAI;MAAE,OAAO,EAAE,KAAK;MAAE,MAAM,EAAE,IAAI;MAAE,SAAS,EAAE;IAAI;IAC/E,MAAMC,KAAI,GAAIX,GAAG,CAAC,CAAC;IACnB,MAAMY,UAAS,GAAIZ,GAAG,CAAC,EAAE;IACzB,MAAMa,WAAU,GAAIb,GAAG,CAAC,IAAI;IAC5B,MAAMc,KAAI,GAAId,GAAG,CAAC;MAChBe,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC;IACD,MAAMC,MAAK,GAAIlB,GAAG,CAAC,CAAC,CAAC;IACrB,MAAMmB,cAAa,GAAIA,CAAA,KAAM;MAC3BN,WAAW,CAACO,KAAI,GAAI,IAAG;MACvBlB,aAAa,CAACY,KAAK,CAACM,KAAK,EAAEC,GAAE,IAAK;QAChCR,WAAW,CAACO,KAAI,GAAI,KAAI;QACxBR,UAAU,CAACQ,KAAI,GAAIC,GAAG,CAACC,IAAG;QAC1BX,KAAK,CAACS,KAAI,GAAIC,GAAG,CAACV,KAAI;MACxB,CAAC;IACH;IACAQ,cAAc,EAAE;IAChB;IACA,MAAMI,aAAY,GAAKC,WAAW,IAAK;MACrCV,KAAK,CAACM,KAAK,CAACL,OAAM,GAAIS,WAAW;MACjCL,cAAc,EAAC;IACjB;IACA;IACA,MAAMM,UAAS,GAAKT,IAAI,IAAK;MAC3BF,KAAK,CAACM,KAAK,CAACJ,IAAG,GAAIA,IAAI;MACvBG,cAAc,EAAC;IACjB;IACA,MAAMO,MAAK,GAAIA,CAAA,KAAM;MACnBP,cAAc,EAAC;IACjB;IACA,MAAMQ,IAAG,GAAI,SAAAA,CAAUC,IAAI,EAAE;MAC3BvB,OAAO,CAAC,EAAE,EAAG,EAAE,EAAE,MAAM;QACrBF,UAAU,CAAC;UAAC0B,EAAE,EAAED,IAAI,CAACC;QAAE,CAAC,EAAER,GAAE,IAAK;UAC/BS,OAAO,CAACC,GAAG,CAACV,GAAG;QACjB,CAAC;MACH,CAAC;IACH;IACA,MAAMW,MAAK,GAAI,SAAAA,CAAA,EAAY;MACzB5B,YAAY,CAAC,CAAC,CAAC,EAAEiB,GAAE,IAAK;QACtBS,OAAO,CAACC,GAAG,CAACV,GAAG;MACjB,CAAC;IACH;IACA,MAAMY,cAAa,GAAI,SAAAA,CAAUL,IAAI,EAAE;MACrCnB,kBAAkB,CAACW,KAAI,GAAI,IAAG;MAC9BF,MAAM,CAACE,KAAI,GAAIQ,IAAG;IACpB;IACA,MAAMM,cAAa,GAAI,SAAAA,CAAA,EAAY;MACjCzB,kBAAkB,CAACW,KAAI,GAAI,KAAI;IACjC;IACA,OAAO;MACLV,QAAQ;MACRI,KAAK;MACLH,KAAK;MACLC,UAAU;MACVW,aAAa;MACbE,UAAU;MACVC,MAAM;MACNb,WAAW;MACXc,IAAI;MACJK,MAAM;MACNvB,kBAAkB;MAClBwB,cAAc;MACdC,cAAc;MACdhB;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}