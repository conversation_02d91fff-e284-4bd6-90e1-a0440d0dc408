{"ast": null, "code": "import { resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  return _openBlock(), _createBlock(_component_el_pagination, {\n    total: $props.total,\n    \"page-size\": $props.pageSize,\n    class: \"page-bar\",\n    layout: \"total, prev, pager, next, sizes\",\n    onSizeChange: $props.sizeChange,\n    onCurrentChange: $props.currentChange\n  }, null, 8 /* PROPS */, [\"total\", \"page-size\", \"onSizeChange\", \"onCurrentChange\"]);\n}", "map": {"version": 3, "names": ["_createBlock", "_component_el_pagination", "total", "$props", "pageSize", "class", "layout", "onSizeChange", "sizeChange", "onCurrentChange", "currentChange"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/components/Page/index.vue"], "sourcesContent": ["<template>\n  <el-pagination\n    :total=\"total\"\n    :page-size=\"pageSize\"\n    class=\"page-bar\"\n    layout=\"total, prev, pager, next, sizes\"\n    @size-change=\"sizeChange\"\n    @current-change=\"currentChange\"/>\n</template>\n\n<script>\n  export default {\n    name: \"Page\",\n    props: {\n      // 数据总条数\n      total: {\n        type: Number,\n        default: 0\n      },\n      // 每页显示条数\n      size: {\n        type: Number,\n        default: 10\n      },\n      // 当前页码\n      sizeChange: {\n        type: Function\n      },\n      // 分页条数\n      currentChange: {\n        type: Function\n      },\n      pageSize: {\n        type: Number,\n        default: 20\n      }\n    }\n  }\n</script>\n\n<style scoped=\"scope\" lang=\"scss\">\n.page-bar {\n  margin-top: 10px;\n}\n</style>\n"], "mappings": ";;;uBACEA,YAAA,CAMmCC,wBAAA;IALhCC,KAAK,EAAEC,MAAA,CAAAD,KAAK;IACZ,WAAS,EAAEC,MAAA,CAAAC,QAAQ;IACpBC,KAAK,EAAC,UAAU;IAChBC,MAAM,EAAC,iCAAiC;IACvCC,YAAW,EAAEJ,MAAA,CAAAK,UAAU;IACvBC,eAAc,EAAEN,MAAA,CAAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}