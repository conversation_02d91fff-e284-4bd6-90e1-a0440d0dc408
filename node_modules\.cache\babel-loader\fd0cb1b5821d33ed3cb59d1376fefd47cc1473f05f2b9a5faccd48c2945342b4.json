{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-59c74d3e\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"question-box\"\n};\nconst _hoisted_2 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"div\", {\n  style: {\n    \"font-size\": \"12px\",\n    \"color\": \"#999999\"\n  }\n}, \"tips: 填空位置使用 [_] 表示\", -1));\nconst _hoisted_3 = {\n  style: {\n    \"color\": \"#666666\"\n  }\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_cascader = _resolveComponent(\"el-cascader\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_rate = _resolveComponent(\"el-rate\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    model: $setup.question,\n    rules: $setup.questionRules,\n    ref: \"questionRef\",\n    \"label-width\": \"120px\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      label: \"分类：\",\n      prop: \"cidList\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_cascader, {\n        size: \"small\",\n        style: {\n          \"width\": \"100%\"\n        },\n        modelValue: $setup.selectCidList,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.selectCidList = $event),\n        props: {\n          multiple: true,\n          checkStrictly: true\n        },\n        options: $setup.categoryOptions,\n        onChange: $setup.changeCategory\n      }, null, 8, [\"modelValue\", \"options\", \"onChange\"])]),\n      _: 1\n    }), _createVNode(_component_el_form_item, {\n      label: \"题干：\",\n      prop: \"title\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"small\",\n        modelValue: $setup.question.title,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.question.title = $event),\n        placeholder: \"请输入题干\",\n        onChange: $setup.titleChange,\n        onBlur: $setup.titleChange\n      }, null, 8, [\"modelValue\", \"onChange\", \"onBlur\"]), _hoisted_2]),\n      _: 1\n    }), _createVNode(_component_el_form_item, {\n      label: \"描述：\",\n      prop: \"note\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"small\",\n        type: \"textarea\",\n        rows: 5,\n        modelValue: $setup.question.note,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.question.note = $event),\n        placeholder: \"请输入题干描述\"\n      }, null, 8, [\"modelValue\"])]),\n      _: 1\n    }), $setup.question.referenceAnswerList && $setup.question.referenceAnswerList.length ? (_openBlock(), _createBlock(_component_el_form_item, {\n      key: 0,\n      label: \"参考答案：\",\n      prop: \"referenceAnswer\"\n    }, {\n      default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.question.referenceAnswerList, (o, index) => {\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: o,\n          class: \"text item\"\n        }, [_createElementVNode(\"span\", _hoisted_3, \"填空 \" + _toDisplayString(index + 1) + \". \", 1), _createVNode(_component_el_input, {\n          modelValue: o.value,\n          \"onUpdate:modelValue\": $event => o.value = $event,\n          size: \"small\"\n        }, null, 8, [\"modelValue\", \"onUpdate:modelValue\"])]);\n      }), 128))]),\n      _: 1\n    })) : _createCommentVNode(\"\", true), _createVNode(_component_el_form_item, {\n      label: \"答案解析：\",\n      prop: \"referenceAnswerNote\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"small\",\n        type: \"textarea\",\n        rows: 5,\n        modelValue: $setup.question.referenceAnswerNote,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.question.referenceAnswerNote = $event),\n        placeholder: \"请输入答案解析\"\n      }, null, 8, [\"modelValue\"])]),\n      _: 1\n    }), _createVNode(_component_el_form_item, {\n      label: \"分数：\",\n      prop: \"score\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        size: \"small\",\n        modelValue: $setup.question.score,\n        \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.question.score = $event),\n        placeholder: \"请输入试题分数\"\n      }, null, 8, [\"modelValue\"])]),\n      _: 1\n    }), _createVNode(_component_el_form_item, {\n      label: \"难度：\",\n      prop: \"difficulty\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_rate, {\n        style: {\n          \"line-height\": \"48px\"\n        },\n        modelValue: $setup.question.difficulty,\n        \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.question.difficulty = $event),\n        colors: $setup.colors\n      }, null, 8, [\"modelValue\", \"colors\"])]),\n      _: 1\n    })]),\n    _: 1\n  }, 8, [\"model\", \"rules\"]), _createVNode(_component_el_button, {\n    size: \"small\",\n    style: {\n      \"display\": \"block\",\n      \"margin\": \"50px auto\"\n    },\n    onClick: $setup.submitBaseInfo\n  }, {\n    default: _withCtx(() => [_createTextVNode(\"提交\")]),\n    _: 1\n  }, 8, [\"onClick\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementVNode", "style", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "model", "$setup", "question", "rules", "questionRules", "ref", "_component_el_form_item", "label", "prop", "_component_el_cascader", "size", "selectCidList", "$event", "props", "multiple", "checkStrictly", "options", "categoryOptions", "onChange", "changeCategory", "_component_el_input", "title", "placeholder", "titleChange", "onBlur", "_hoisted_2", "type", "rows", "note", "referenceAnswerList", "length", "_createBlock", "_Fragment", "_renderList", "o", "index", "key", "_hoisted_3", "_toDisplayString", "value", "referenceAnswerNote", "score", "_component_el_rate", "difficulty", "colors", "_component_el_button", "onClick", "submitBaseInfo"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/exam/question-lib/fill-blank/index.vue"], "sourcesContent": ["<template>\n  <div class=\"question-box\">\n    <el-form :model=\"question\" :rules=\"questionRules\" ref=\"questionRef\" label-width=\"120px\">\n      <el-form-item label=\"分类：\" prop=\"cidList\">\n        <el-cascader size=\"small\" style=\"width: 100%;\"\n                     v-model=\"selectCidList\"\n                     :props=\"{ multiple: true, checkStrictly: true }\"\n                     :options=\"categoryOptions\"\n                     @change=\"changeCategory\">\n        </el-cascader>\n      </el-form-item>\n      <el-form-item label=\"题干：\" prop=\"title\">\n        <el-input size=\"small\" v-model=\"question.title\" placeholder=\"请输入题干\" @change=\"titleChange\" @blur=\"titleChange\"></el-input>\n        <div style=\"font-size: 12px;color: #999999;\">tips: 填空位置使用 [_] 表示</div>\n      </el-form-item>\n      <el-form-item label=\"描述：\" prop=\"note\">\n        <el-input size=\"small\" type=\"textarea\" :rows=\"5\" v-model=\"question.note\" placeholder=\"请输入题干描述\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"参考答案：\"  prop=\"referenceAnswer\" v-if=\"question.referenceAnswerList && question.referenceAnswerList.length\">\n        <div v-for=\"(o, index) in question.referenceAnswerList\" :key=\"o\" class=\"text item\">\n          <span style=\"color: #666666;\">填空 {{index + 1}}. </span>\n          <el-input v-model=\"o.value\" size=\"small\"></el-input>\n        </div>\n      </el-form-item>\n      <el-form-item label=\"答案解析：\" prop=\"referenceAnswerNote\">\n        <el-input size=\"small\" type=\"textarea\" :rows=\"5\" v-model=\"question.referenceAnswerNote\" placeholder=\"请输入答案解析\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"分数：\" prop=\"score\">\n        <el-input size=\"small\" v-model=\"question.score\" placeholder=\"请输入试题分数\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"难度：\" prop=\"difficulty\">\n        <el-rate style=\"line-height: 48px;\" v-model=\"question.difficulty\" :colors=\"colors\"></el-rate>\n      </el-form-item>\n    </el-form>\n    <el-button size=\"small\" style=\"display:block;margin:50px auto;\" @click=\"submitBaseInfo\">提交</el-button>\n  </div>\n</template>\n<script>\n  import {ref} from \"vue\"\n  import {findCategoryList, toTree, getAllParent} from \"@/api/exam/question-lib/category\"\n  import {saveBaseInfo, updateBaseInfo, getBaseInfo} from \"@/api/exam/question-lib/question\"\n  import {useRoute} from \"vue-router\";\n  import {error, success} from \"@/util/tipsUtils\";\n  import router from \"@/router\";\n\n  export default {\n    name: \"ExamQuestionLibFillBlank\",\n    setup() {\n      const route = useRoute()\n      const colors = [\"#99A9BF\", \"#F7BA2A\", \"#FF9900\"]\n      const question = ref({\n        id: \"\",\n        title: \"\",\n        note: \"\",\n        type: \"fill_blank\",\n        score: \"\",\n        difficulty: 2,\n        referenceAnswer: \"\",\n        referenceAnswerNote: \"\",\n        options: \"\",\n        cidList: []\n      })\n      const questionRules = {\n        title: [{ required: true, message: \"请输入题干\", trigger: \"blur\" }],\n        score: [{ required: true, message: \"请输入分数\", trigger: \"blur\" }],\n        cidList: [{ required: true, message: \"请选择分类\", trigger: \"change\" }],\n        //referenceAnswer: [{ required: true, message: \"请选择参考答案\", trigger: \"change\" }],\n        referenceAnswerNote: [{ required: true, message: \"请输入答案解析\", trigger: \"blur\" }],\n        options: [{ required: true, message: \"请添加选项\", trigger: \"blur\" }],\n      }\n      const serialNumber = [\"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\", \"H\", \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\", \"R\", \"S\", \"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\"]\n      const optionList = ref([])\n      const categoryOptions = ref([])\n      const selectCidList = ref([])\n      // 获取分类\n      findCategoryList(0, true, (res) => {\n        if (res && res.length) {\n          categoryOptions.value = toTree(res);\n          categoryOptions.value.splice(0, 1);\n          if (route.query.id) {\n            // 获取试题信息\n            getBaseInfo(route.query.id, function (res) {\n              console.log(res)\n              question.value = res;\n              question.value.referenceAnswerList = [];\n              const answerList = res.referenceAnswer.split(\"[_]\")\n              for (const a of answerList) {\n                question.value.referenceAnswerList.push({value: a})\n              }\n              selectCidList.value = getAllParent(categoryOptions.value, res.cidList);\n              question.value.cidList = []\n              for (const valElement of selectCidList.value) {\n                question.value.cidList.push(valElement[valElement.length - 1])\n              }\n            })\n          }\n        }\n      })\n      // 选择分类\n      const changeCategory = (val) => {\n        question.value.cidList = []\n        for (const valElement of val) {\n          question.value.cidList.push(valElement[valElement.length - 1])\n        }\n      }\n      let optionIndex = -1;\n      const option = ref(\"\")\n      const showAddOptionInput = ref(false)\n      const addOption = () => {\n        showAddOptionInput.value = true\n      }\n      const optionBlur = () => {\n        showAddOptionInput.value = false\n        if (!option.value) {\n          return\n        }\n        if (optionIndex > -1) {\n          optionList.value[optionIndex].value = option.value\n        } else {\n          optionList.value.push({value: option.value, key: serialNumber[optionList.value.length]})\n        }\n        question.value.options = JSON.stringify(optionList.value)\n        option.value = \"\"\n        optionIndex = -1;\n      }\n      const editOption = (index) => {\n        const o = optionList.value[index];\n        option.value = o.value;\n        optionIndex = index;\n        showAddOptionInput.value = true\n      }\n      const deleteOption = (index) => {\n        if (optionList.value && optionList.value.length) {\n          optionList.value.splice(index, 1);\n          optionList.value.forEach((item, index) => {\n            item.key = serialNumber[index]\n          })\n          question.value.options = JSON.stringify(optionList.value)\n        } else {\n          question.value.options = \"\"\n        }\n      }\n      const questionRef = ref();\n      const submitBaseInfo = () => {\n        questionRef.value.validate((valid) => {\n          if (!valid) { return false }\n          const answerList = []\n          if (question.value.referenceAnswerList && question.value.referenceAnswerList.length) {\n            for (const answer of question.value.referenceAnswerList) {\n              if (!answer.value) {\n                error(\"参考答案为必填项\")\n                return false;\n              }\n              answerList.push(answer.value)\n            }\n          } else {\n            error(\"参考答案为必填项\")\n            return\n          }\n          question.value.referenceAnswer = answerList.join(\"[_]\")\n          if (question.value.id) {\n            updateBaseInfo(question.value, function () {\n              success(\"编辑成功\")\n              router.push({path: \"/exam/question-lib\"})\n            })\n          } else {\n            saveBaseInfo(question.value, function () {\n              success(\"新增成功\")\n              router.push({path: \"/exam/question-lib\"})\n            })\n          }\n        })\n      }\n      const titleChange = () => {\n        let thisCount = 0;\n        question.value.title.replace(/\\[_\\]/g, function () {\n          thisCount++;\n          console.log(thisCount, \"次\")\n          return \"[_]\"\n        });\n        if (!question.value.referenceAnswerList) {\n          question.value.referenceAnswerList = []\n        }\n        let count = 0;\n        if (question.value.referenceAnswerList.length <= thisCount) {\n          count = thisCount - question.value.referenceAnswerList.length\n          for (let i = 0; i < count; i++) {\n            question.value.referenceAnswerList.push({value: \"\"})\n          }\n        } else {\n          question.value.referenceAnswerList.splice(thisCount, question.value.referenceAnswerList.length)\n        }\n      }\n      return {\n        colors,\n        question,\n        questionRules,\n        categoryOptions,\n        selectCidList,\n        serialNumber,\n        option,\n        optionList,\n        showAddOptionInput,\n        questionRef,\n        changeCategory,\n        addOption,\n        optionBlur,\n        editOption,\n        deleteOption,\n        submitBaseInfo,\n        titleChange\n      }\n    }\n  }\n</script>\n<style scoped lang=\"scss\">\n.question-box {\n  margin: 20px;\n  .option-delete {\n    margin-left: 20px;\n    cursor: pointer;\n  }\n  .option-delete:hover {\n    color: $--color-primary;\n  }\n  ::v-deep .el-card__header{\n    padding: 0!important;\n  }\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAc;gEAYnBC,mBAAA,CAAsE;EAAjEC,KAAuC,EAAvC;IAAA;IAAA;EAAA;AAAuC,GAAC,qBAAmB;;EAOxDA,KAAuB,EAAvB;IAAA;EAAA;AAAuB;;;;;;;;uBAnBrCC,mBAAA,CAkCM,OAlCNC,UAkCM,GAjCJC,YAAA,CA+BUC,kBAAA;IA/BAC,KAAK,EAAEC,MAAA,CAAAC,QAAQ;IAAGC,KAAK,EAAEF,MAAA,CAAAG,aAAa;IAAEC,GAAG,EAAC,aAAa;IAAC,aAAW,EAAC;;sBAC9E,MAOe,CAPfP,YAAA,CAOeQ,uBAAA;MAPDC,KAAK,EAAC,KAAK;MAACC,IAAI,EAAC;;wBAC7B,MAKc,CALdV,YAAA,CAKcW,sBAAA;QALDC,IAAI,EAAC,OAAO;QAACf,KAAoB,EAApB;UAAA;QAAA,CAAoB;oBACxBM,MAAA,CAAAU,aAAa;mEAAbV,MAAA,CAAAU,aAAa,GAAAC,MAAA;QACrBC,KAAK,EAAE;UAAAC,QAAA;UAAAC,aAAA;QAAA,CAAuC;QAC9CC,OAAO,EAAEf,MAAA,CAAAgB,eAAe;QACxBC,QAAM,EAAEjB,MAAA,CAAAkB;;;QAGxBrB,YAAA,CAGeQ,uBAAA;MAHDC,KAAK,EAAC,KAAK;MAACC,IAAI,EAAC;;wBAC7B,MAAyH,CAAzHV,YAAA,CAAyHsB,mBAAA;QAA/GV,IAAI,EAAC,OAAO;oBAAUT,MAAA,CAAAC,QAAQ,CAACmB,KAAK;mEAAdpB,MAAA,CAAAC,QAAQ,CAACmB,KAAK,GAAAT,MAAA;QAAEU,WAAW,EAAC,OAAO;QAAEJ,QAAM,EAAEjB,MAAA,CAAAsB,WAAW;QAAGC,MAAI,EAAEvB,MAAA,CAAAsB;yDACjGE,UAAsE,C;;QAExE3B,YAAA,CAEeQ,uBAAA;MAFDC,KAAK,EAAC,KAAK;MAACC,IAAI,EAAC;;wBAC7B,MAA0G,CAA1GV,YAAA,CAA0GsB,mBAAA;QAAhGV,IAAI,EAAC,OAAO;QAACgB,IAAI,EAAC,UAAU;QAAEC,IAAI,EAAE,CAAC;oBAAW1B,MAAA,CAAAC,QAAQ,CAAC0B,IAAI;mEAAb3B,MAAA,CAAAC,QAAQ,CAAC0B,IAAI,GAAAhB,MAAA;QAAEU,WAAW,EAAC;;;QAE7BrB,MAAA,CAAAC,QAAQ,CAAC2B,mBAAmB,IAAI5B,MAAA,CAAAC,QAAQ,CAAC2B,mBAAmB,CAACC,MAAM,I,cAA7HC,YAAA,CAKezB,uBAAA;;MALDC,KAAK,EAAC,OAAO;MAAEC,IAAI,EAAC;;wBAC3B,MAAkD,E,kBAAvDZ,mBAAA,CAGMoC,SAAA,QAAAC,WAAA,CAHoBhC,MAAA,CAAAC,QAAQ,CAAC2B,mBAAmB,GAAzCK,CAAC,EAAEC,KAAK;6BAArBvC,mBAAA,CAGM;UAHmDwC,GAAG,EAAEF,CAAC;UAAEzC,KAAK,EAAC;YACrEC,mBAAA,CAAuD,QAAvD2C,UAAuD,EAAzB,KAAG,GAAAC,gBAAA,CAAEH,KAAK,QAAM,IAAE,MAChDrC,YAAA,CAAoDsB,mBAAA;sBAAjCc,CAAC,CAACK,KAAK;2CAAPL,CAAC,CAACK,KAAK,GAAA3B,MAAA;UAAEF,IAAI,EAAC;;;;yCAGrCZ,YAAA,CAEeQ,uBAAA;MAFDC,KAAK,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAC/B,MAAyH,CAAzHV,YAAA,CAAyHsB,mBAAA;QAA/GV,IAAI,EAAC,OAAO;QAACgB,IAAI,EAAC,UAAU;QAAEC,IAAI,EAAE,CAAC;oBAAW1B,MAAA,CAAAC,QAAQ,CAACsC,mBAAmB;mEAA5BvC,MAAA,CAAAC,QAAQ,CAACsC,mBAAmB,GAAA5B,MAAA;QAAEU,WAAW,EAAC;;;QAEtGxB,YAAA,CAEeQ,uBAAA;MAFDC,KAAK,EAAC,KAAK;MAACC,IAAI,EAAC;;wBAC7B,MAAiF,CAAjFV,YAAA,CAAiFsB,mBAAA;QAAvEV,IAAI,EAAC,OAAO;oBAAUT,MAAA,CAAAC,QAAQ,CAACuC,KAAK;mEAAdxC,MAAA,CAAAC,QAAQ,CAACuC,KAAK,GAAA7B,MAAA;QAAEU,WAAW,EAAC;;;QAE9DxB,YAAA,CAEeQ,uBAAA;MAFDC,KAAK,EAAC,KAAK;MAACC,IAAI,EAAC;;wBAC7B,MAA6F,CAA7FV,YAAA,CAA6F4C,kBAAA;QAApF/C,KAA0B,EAA1B;UAAA;QAAA,CAA0B;oBAAUM,MAAA,CAAAC,QAAQ,CAACyC,UAAU;mEAAnB1C,MAAA,CAAAC,QAAQ,CAACyC,UAAU,GAAA/B,MAAA;QAAGgC,MAAM,EAAE3C,MAAA,CAAA2C;;;;;6BAG/E9C,YAAA,CAAsG+C,oBAAA;IAA3FnC,IAAI,EAAC,OAAO;IAACf,KAAuC,EAAvC;MAAA;MAAA;IAAA,CAAuC;IAAEmD,OAAK,EAAE7C,MAAA,CAAA8C;;sBAAgB,MAAE,C,iBAAF,IAAE,E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}