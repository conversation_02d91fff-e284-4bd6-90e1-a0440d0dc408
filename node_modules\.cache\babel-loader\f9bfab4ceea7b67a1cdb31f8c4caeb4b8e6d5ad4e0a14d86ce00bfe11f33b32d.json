{"ast": null, "code": "import { get, put, post, del } from \"@/util/requestUtils\";\nexport function findList(params, success) {\n  return get(\"/member/tag/list\", params, success);\n}\nexport function getTag(id, success) {\n  return get(\"/member/public-api/tag\", {\n    id: id\n  }, success);\n}\nexport function updateTag(data, success) {\n  return put(\"/member/tag\", data, success);\n}\nexport function saveTag(data, success) {\n  return post(\"/member/tag\", data, success);\n}\nexport function deleteTag(data, success) {\n  return del(\"/member/tag\", data, success);\n}", "map": {"version": 3, "names": ["get", "put", "post", "del", "findList", "params", "success", "getTag", "id", "updateTag", "data", "saveTag", "deleteTag"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/api/member/tag.js"], "sourcesContent": ["import {get, put, post, del} from \"@/util/requestUtils\"\n\nexport function findList(params, success) {\n  return get(\"/member/tag/list\", params, success)\n}\n\nexport function getTag(id, success) {\n  return get(\"/member/public-api/tag\", {id: id}, success)\n}\n\nexport function updateTag(data, success) {\n  return put(\"/member/tag\", data, success)\n}\n\nexport function saveTag(data, success) {\n  return post(\"/member/tag\", data, success)\n}\n\nexport function deleteTag(data, success) {\n  return del(\"/member/tag\", data, success)\n}\n"], "mappings": "AAAA,SAAQA,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,QAAO,qBAAqB;AAEvD,OAAO,SAASC,QAAQA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACxC,OAAON,GAAG,CAAC,kBAAkB,EAAEK,MAAM,EAAEC,OAAO,CAAC;AACjD;AAEA,OAAO,SAASC,MAAMA,CAACC,EAAE,EAAEF,OAAO,EAAE;EAClC,OAAON,GAAG,CAAC,wBAAwB,EAAE;IAACQ,EAAE,EAAEA;EAAE,CAAC,EAAEF,OAAO,CAAC;AACzD;AAEA,OAAO,SAASG,SAASA,CAACC,IAAI,EAAEJ,OAAO,EAAE;EACvC,OAAOL,GAAG,CAAC,aAAa,EAAES,IAAI,EAAEJ,OAAO,CAAC;AAC1C;AAEA,OAAO,SAASK,OAAOA,CAACD,IAAI,EAAEJ,OAAO,EAAE;EACrC,OAAOJ,IAAI,CAAC,aAAa,EAAEQ,IAAI,EAAEJ,OAAO,CAAC;AAC3C;AAEA,OAAO,SAASM,SAASA,CAACF,IAAI,EAAEJ,OAAO,EAAE;EACvC,OAAOH,GAAG,CAAC,aAAa,EAAEO,IAAI,EAAEJ,OAAO,CAAC;AAC1C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}