{"ast": null, "code": "export default {\n  name: \"MessageIndex\",\n  setup() {\n    return {};\n  }\n};", "map": {"version": 3, "names": ["name", "setup"], "sources": ["/Users/<USER>/rongge/code/已售项目/20340305/front/admin/src/views/message/index.vue"], "sourcesContent": ["<template>\n  <div style=\"margin: 20px;\">\n    概览\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"MessageIndex\",\n  setup() {\n    return {};\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n</style>\n"], "mappings": "AAOA,eAAe;EACbA,IAAI,EAAE,cAAc;EACpBC,KAAKA,CAAA,EAAG;IACN,OAAO,CAAC,CAAC;EACX;AACF,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}