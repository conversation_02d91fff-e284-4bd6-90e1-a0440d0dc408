{"ast": null, "code": "import { like } from \"../../api/comment/like\";\nimport { deleteComment, deleteReplyComment, saveReplyComment } from \"../../api/comment\";\nimport { confirm, success } from \"../../util/tipsUtils\";\nexport default {\n  name: \"commentItem\",\n  components: {},\n  props: {\n    item: {\n      type: Object\n    },\n    member: {\n      type: Object\n    },\n    commentId: {\n      type: Number\n    },\n    replyNum: {\n      type: Number,\n      default: 0\n    },\n    submitCallback: {\n      type: Function,\n      default: () => {}\n    },\n    deleteCallback: {\n      type: Function,\n      default: () => {}\n    }\n  },\n  setup(props) {\n    const topicType = {\n      lesson: \"课程\",\n      news: \"新闻\",\n      article: \"文章\",\n      question: \"问题\",\n      answer: \"回答\",\n      dynamic: \"动态\",\n      channel: \"直播\",\n      resource: \"知识\"\n    };\n    const commentLike = item => {\n      console.log(item);\n      if (item.toMember && item.toMember.id) {\n        like(item, \"reply_comment\", res => {\n          item.like = res.like;\n        });\n      } else {\n        like(item, \"comment\", res => {\n          item.like = res.like;\n        });\n      }\n    };\n    const showReply = item => {\n      console.log(item);\n      item.showReplyInput = !item.showReplyInput;\n    };\n    const replySubmitCallback = function (param, parentItem) {\n      if (!parentItem) {\n        return;\n      }\n      saveReplyComment({\n        commentId: param.commentId,\n        content: param.content,\n        replyCommentId: parentItem.id,\n        toMemberId: parentItem.member.id\n      }, () => {\n        parentItem.showReplyInput = false;\n        parentItem.showReply = true;\n        props.submitCallback();\n      });\n    };\n    const delComment = () => {\n      confirm(\"确定删除该评论?\", \"提示\", () => {\n        if (props.item.toMember && props.item.toMember.id) {\n          deleteReplyComment({\n            id: props.item.id\n          }, () => {\n            success(\"删除成功\");\n            props.submitCallback();\n          });\n        } else {\n          deleteComment({\n            id: props.item.id\n          }, () => {\n            success(\"删除成功\");\n            props.submitCallback();\n          });\n        }\n      });\n    };\n    return {\n      topicType,\n      commentLike,\n      showReply,\n      replySubmitCallback,\n      delComment\n    };\n  }\n};", "map": {"version": 3, "names": ["like", "deleteComment", "deleteReplyComment", "saveReplyComment", "confirm", "success", "name", "components", "props", "item", "type", "Object", "member", "commentId", "Number", "replyNum", "default", "submitCallback", "Function", "deleteCallback", "setup", "topicType", "lesson", "news", "article", "question", "answer", "dynamic", "channel", "resource", "commentLike", "console", "log", "toMember", "id", "res", "showReply", "showReplyInput", "replySubmit<PERSON><PERSON>back", "param", "parentItem", "content", "replyCommentId", "toMemberId", "delComment"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/views/comment/commentItem.vue"], "sourcesContent": ["<template>\n  <div class=\"comment-item\">\n    <div class=\"comment-item-header\">\n      <div class=\"comment-item-avatar\">\n        <div class=\"byte-avatar byte-avatar-circle\">\n          <span class=\"byte-avatar-image\">\n            <img :src=\"item.member && item.member.avatar\">\n          </span>\n        </div>\n      </div>\n      <span class=\"comment-item-title\">{{item.member && item.member.name}}</span>\n      <div class=\"comment-item-header-extra\" v-if=\"item['toMember'] && item['toMember'].id\">\n        {{'  回复  ' + item['toMember'].name}}\n      </div>\n      <div class=\"comment-item-header-extra\" v-else>\n        评论了{{topicType[item.topicType]}}\n        <span class=\"extra-title\">\n          《\n          <a v-if=\"item.topic\" class=\"comment-item-header-extra-title\" :href=\"'/comment/list?topicId=' + item.topicId + '&type=' + item.topicType + '&topicName=' + item.topic.name\" :title=\"item.topic.name\">{{item.topic.name}}</a>\n          》\n        </span>\n      </div>\n    </div>\n    <div class=\"comment-item-body\">\n      <div class=\"comment-item-content-wrap\">\n        <div class=\"two-line-wrap\">\n          <div class=\"comment-item-content\">{{item.content}}</div>\n        </div>\n      </div>\n      <div class=\"comment-item-footer\">\n        <div class=\"comment-item-timer\">{{item.createTime}}</div>\n        <div class=\"comment-item-actions\">\n          <div class=\"comment-item-actions-item\">\n            <div class=\"byte-spin\">\n              <div class=\"byte-spin-container\">\n                <div class=\"byte-spin-content\" :class=\"{'show-active' : item.like && item.like.status}\">\n                  <i class=\"el-icon-thumb\"/> 赞 {{item.likeCount || 0}}\n                </div>\n              </div>\n            </div>\n          </div>\n          <div class=\"comment-item-actions-item\" v-if=\"item.member && member.id !== item.member.id && replyNum >= 0\">\n            <div class=\"byte-spin\">\n              <div class=\"byte-spin-container\">\n                <div class=\"byte-spin-content\" :class=\"{'show-active' : item.showReplyInput}\">\n                  <i class=\"el-icon-chat-dot-round\"/> 回复 {{replyNum || 0}}\n                </div>\n              </div>\n            </div>\n          </div>\n          <div class=\"comment-item-actions-item delete\">\n            <div class=\"byte-spin\">\n              <div class=\"byte-spin-container\">\n                <div class=\"byte-spin-content\" @click=\"delComment\">\n                  <i class=\"el-icon-delete\"/> 删除\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\n  import {like} from \"../../api/comment/like\";\n  import {deleteComment, deleteReplyComment, saveReplyComment} from \"../../api/comment\";\n  import {confirm, success} from \"../../util/tipsUtils\";\n  export default {\n    name: \"commentItem\",\n    components: {\n    },\n    props: {\n      item: {\n        type: Object\n      },\n      member: {\n        type: Object\n      },\n      commentId: {\n        type: Number\n      },\n      replyNum: {\n        type: Number,\n        default: 0\n      },\n      submitCallback: {\n        type: Function,\n        default: () => {}\n      },\n      deleteCallback: {\n        type: Function,\n        default: () => {}\n      }\n    },\n    setup(props) {\n      const topicType = {\n        lesson: \"课程\",\n        news: \"新闻\",\n        article: \"文章\",\n        question: \"问题\",\n        answer: \"回答\",\n        dynamic: \"动态\",\n        channel: \"直播\",\n        resource: \"知识\",\n      }\n      const commentLike = (item) => {\n        console.log(item)\n        if (item.toMember && item.toMember.id) {\n          like(item, \"reply_comment\", res => {\n            item.like = res.like\n          })\n        } else {\n          like(item, \"comment\", res => {\n            item.like = res.like\n          })\n        }\n      }\n      const showReply = (item) => {\n        console.log(item)\n        item.showReplyInput = !item.showReplyInput\n      }\n      const replySubmitCallback = function(param, parentItem) {\n        if (!parentItem) {\n          return\n        }\n        saveReplyComment({\n          commentId: param.commentId,\n          content: param.content,\n          replyCommentId: parentItem.id,\n          toMemberId: parentItem.member.id\n        }, () => {\n          parentItem.showReplyInput = false\n          parentItem.showReply = true\n          props.submitCallback()\n        })\n      }\n      const delComment = () => {\n        confirm(\"确定删除该评论?\", \"提示\", () => {\n          if (props.item.toMember && props.item.toMember.id) {\n            deleteReplyComment({id : props.item.id}, () => {\n              success(\"删除成功\")\n              props.submitCallback()\n            })\n          } else {\n            deleteComment({id : props.item.id}, () => {\n              success(\"删除成功\")\n              props.submitCallback()\n            })\n          }\n        })\n      }\n      return {\n        topicType,\n        commentLike,\n        showReply,\n        replySubmitCallback,\n        delComment\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .comment-item {\n    display: flex;\n    align-items: flex-start;\n    padding: 10px 20px;\n    text-align: left;\n    flex-direction: column;\n    .comment-item-header {\n      display: flex;\n      width: 100%;\n      overflow: hidden;\n      align-items: center;\n      font-size: 13px;\n      .comment-item-avatar {\n        display: inline-block;\n        width: 28px;\n        height: 28px;\n        border-radius: 50%;\n        flex-shrink: 0;\n        flex-basis: 28px;\n        cursor: pointer;\n        margin-right: 8px;\n        .byte-avatar {\n          display: inline-block;\n          position: relative;\n          background-color: #c2c6cc;\n          white-space: nowrap;\n          color: #fff;\n          overflow: hidden;\n          width: 28px;\n          height: 28px;\n          line-height: 0;\n          .byte-avatar-image {\n            display: inline-block;\n            img {\n              width: 28px;\n              height: 28px;\n            }\n          }\n        }\n        .byte-avatar-circle {\n          border-radius: 100%;\n        }\n      }\n      .comment-item-title {\n        color: #666666;\n        font-size: 13px;\n        white-space: nowrap;\n        cursor: pointer;\n      }\n      .comment-item-header-extra {\n        flex: 1;\n        color: #999999;\n        white-space: nowrap;\n        display: flex;\n        margin-left: 8px;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        .extra-title {\n          color: #666666;\n          margin-left: 4px;\n          display: flex;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          .comment-item-header-extra-title {\n            color: #666666;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            white-space: nowrap;\n            cursor: pointer;\n            &:hover {\n              color: $--color-primary;\n            }\n          }\n        }\n      }\n    }\n    .comment-item-body {\n      width: 100%;\n      flex: 1;\n      .comment-item-content-wrap {\n        display: flex;\n        flex-direction: column;\n        margin-top: 8px;\n        .two-line-wrap {\n          overflow: hidden;\n          text-overflow: ellipsis;\n          display: -webkit-box;\n          -webkit-line-clamp: 2;\n          -webkit-box-orient: vertical;\n          .comment-item-content {\n            font-size: 15px;\n            line-height: 24px;\n            color: #222222;\n            display: inline;\n          }\n        }\n      }\n      .comment-item-footer {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        margin-top: 12px;\n        .comment-item-timer {\n          font-size: 14px;\n          line-height: 20px;\n          color: #999999;\n          white-space: nowrap;\n        }\n        .comment-item-actions {\n          display: flex;\n          align-items: center;\n          color: #222222;\n          .comment-item-actions-item.delete {\n            cursor: pointer;\n            &:hover {\n              color: $--color-primary;\n            }\n          }\n          .comment-item-actions-item {\n            margin-right: 20px;\n            /*cursor: pointer;*/\n            white-space: nowrap;\n            height: 20px;\n            text-align: right;\n            /*&:hover {\n              color: $--color-primary;\n            }*/\n            &:last-child {\n              margin-right: 0;\n            }\n            .byte-spin {\n              line-height: 20px;\n              height: 20px;\n              display: flex;\n              align-items: center;\n              .byte-spin-container {\n                display: inline-block;\n                position: relative;\n                width: 100%;\n                .byte-spin-content {\n                  position: relative;\n                  span,\n                  div {\n                    display: flex;\n                    align-items: center;\n                  }\n                }\n                .byte-spin-content.show-active {\n                  color: $--color-primary;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n</style>\n"], "mappings": "AAkEE,SAAQA,IAAI,QAAO,wBAAwB;AAC3C,SAAQC,aAAa,EAAEC,kBAAkB,EAAEC,gBAAgB,QAAO,mBAAmB;AACrF,SAAQC,OAAO,EAAEC,OAAO,QAAO,sBAAsB;AACrD,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE,CACZ,CAAC;EACDC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC;IACR,CAAC;IACDC,MAAM,EAAE;MACNF,IAAI,EAAEC;IACR,CAAC;IACDE,SAAS,EAAE;MACTH,IAAI,EAAEI;IACR,CAAC;IACDC,QAAQ,EAAE;MACRL,IAAI,EAAEI,MAAM;MACZE,OAAO,EAAE;IACX,CAAC;IACDC,cAAc,EAAE;MACdP,IAAI,EAAEQ,QAAQ;MACdF,OAAO,EAAEA,CAAA,KAAM,CAAC;IAClB,CAAC;IACDG,cAAc,EAAE;MACdT,IAAI,EAAEQ,QAAQ;MACdF,OAAO,EAAEA,CAAA,KAAM,CAAC;IAClB;EACF,CAAC;EACDI,KAAKA,CAACZ,KAAK,EAAE;IACX,MAAMa,SAAQ,GAAI;MAChBC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE;IACZ;IACA,MAAMC,WAAU,GAAKrB,IAAI,IAAK;MAC5BsB,OAAO,CAACC,GAAG,CAACvB,IAAI;MAChB,IAAIA,IAAI,CAACwB,QAAO,IAAKxB,IAAI,CAACwB,QAAQ,CAACC,EAAE,EAAE;QACrClC,IAAI,CAACS,IAAI,EAAE,eAAe,EAAE0B,GAAE,IAAK;UACjC1B,IAAI,CAACT,IAAG,GAAImC,GAAG,CAACnC,IAAG;QACrB,CAAC;MACH,OAAO;QACLA,IAAI,CAACS,IAAI,EAAE,SAAS,EAAE0B,GAAE,IAAK;UAC3B1B,IAAI,CAACT,IAAG,GAAImC,GAAG,CAACnC,IAAG;QACrB,CAAC;MACH;IACF;IACA,MAAMoC,SAAQ,GAAK3B,IAAI,IAAK;MAC1BsB,OAAO,CAACC,GAAG,CAACvB,IAAI;MAChBA,IAAI,CAAC4B,cAAa,GAAI,CAAC5B,IAAI,CAAC4B,cAAa;IAC3C;IACA,MAAMC,mBAAkB,GAAI,SAAAA,CAASC,KAAK,EAAEC,UAAU,EAAE;MACtD,IAAI,CAACA,UAAU,EAAE;QACf;MACF;MACArC,gBAAgB,CAAC;QACfU,SAAS,EAAE0B,KAAK,CAAC1B,SAAS;QAC1B4B,OAAO,EAAEF,KAAK,CAACE,OAAO;QACtBC,cAAc,EAAEF,UAAU,CAACN,EAAE;QAC7BS,UAAU,EAAEH,UAAU,CAAC5B,MAAM,CAACsB;MAChC,CAAC,EAAE,MAAM;QACPM,UAAU,CAACH,cAAa,GAAI,KAAI;QAChCG,UAAU,CAACJ,SAAQ,GAAI,IAAG;QAC1B5B,KAAK,CAACS,cAAc,EAAC;MACvB,CAAC;IACH;IACA,MAAM2B,UAAS,GAAIA,CAAA,KAAM;MACvBxC,OAAO,CAAC,UAAU,EAAE,IAAI,EAAE,MAAM;QAC9B,IAAII,KAAK,CAACC,IAAI,CAACwB,QAAO,IAAKzB,KAAK,CAACC,IAAI,CAACwB,QAAQ,CAACC,EAAE,EAAE;UACjDhC,kBAAkB,CAAC;YAACgC,EAAC,EAAI1B,KAAK,CAACC,IAAI,CAACyB;UAAE,CAAC,EAAE,MAAM;YAC7C7B,OAAO,CAAC,MAAM;YACdG,KAAK,CAACS,cAAc,EAAC;UACvB,CAAC;QACH,OAAO;UACLhB,aAAa,CAAC;YAACiC,EAAC,EAAI1B,KAAK,CAACC,IAAI,CAACyB;UAAE,CAAC,EAAE,MAAM;YACxC7B,OAAO,CAAC,MAAM;YACdG,KAAK,CAACS,cAAc,EAAC;UACvB,CAAC;QACH;MACF,CAAC;IACH;IACA,OAAO;MACLI,SAAS;MACTS,WAAW;MACXM,SAAS;MACTE,mBAAmB;MACnBM;IACF;EACF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}