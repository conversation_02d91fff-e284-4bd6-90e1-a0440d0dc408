{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-ad0c3ba4\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"header\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_hamburger = _resolveComponent(\"hamburger\");\n  const _component_breadcrumb = _resolveComponent(\"breadcrumb\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_hamburger, {\n    \"is-active\": $setup.opened,\n    class: \"hamburger-container\",\n    onToggleClick: $setup.toggleSideBar\n  }, null, 8, [\"is-active\", \"onToggleClick\"]), _createVNode(_component_breadcrumb, {\n    class: \"breadcrumb-container\"\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_hamburger", "$setup", "opened", "onToggleClick", "toggleSideBar", "_component_breadcrumb"], "sources": ["/Users/<USER>/rongge/code/cloud-learning-enterprise-front/admin/src/components/Header.vue"], "sourcesContent": ["<template>\n  <div class=\"header\">\n    <hamburger :is-active=\"opened\" class=\"hamburger-container\" @toggleClick=\"toggleSideBar\"/>\n    <breadcrumb class=\"breadcrumb-container\"/>\n  </div>\n</template>\n\n<script>\nimport Hamburger from \"./Hamburger\";\nimport Breadcrumb from \"./Breadcrumb\";\nimport {ref} from \"vue\";\nimport store from \"../store/index\"\nexport default {\n  name: \"HeaderIndex\",\n  components: {\n    Hamburger,\n    Breadcrumb\n  },\n  setup() {\n    const opened = ref(store.getters.getAsideStatus);\n    const toggleSideBar = () => {\n      opened.value = !opened.value;\n      store.commit(\"toggleAsideStatus\");\n    }\n    return {\n      opened,\n      toggleSideBar\n    };\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.header {\n  background: #ffffff;\n  overflow: hidden;\n  position: relative;\n  /*box-shadow: 0 1px 4px rgba(0,21,41,.08);*/\n  .hamburger-container {\n    line-height: 38px;\n    height: 100%;\n    float: left;\n    cursor: pointer;\n    transition: background .3s;\n    -webkit-tap-highlight-color:transparent;\n    &:hover {\n      background: rgba(0, 0, 0, .025)\n    }\n  }\n  .breadcrumb-container {\n    float: left;\n  }\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAQ;;;;uBAAnBC,mBAAA,CAGM,OAHNC,UAGM,GAFJC,YAAA,CAAyFC,oBAAA;IAA7E,WAAS,EAAEC,MAAA,CAAAC,MAAM;IAAEN,KAAK,EAAC,qBAAqB;IAAEO,aAAW,EAAEF,MAAA,CAAAG;+CACzEL,YAAA,CAA0CM,qBAAA;IAA9BT,KAAK,EAAC;EAAsB,G"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}